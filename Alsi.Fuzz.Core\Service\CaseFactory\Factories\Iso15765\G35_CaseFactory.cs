using Alsi.Common.Utils;
using Alsi.Fuzz.Core.Service.CaseFactory.Core;
using Alsi.Fuzz.Core.Utils;
using System.Collections.Generic;

namespace Alsi.Fuzz.Core.Service.CaseFactory.Factories.Iso15765
{
    public class G35_CaseFactory : CaseFactoryBase
    {
        public override CaseMutation[] Generate(MutationOptions options)
        {
            var list = new List<CaseMutation>();

            var isCanFd = options.CommunicationType == App.Devices.Core.CommunicationType.CanFd;

            var dlc = 8;
            byte ffHighNibble = 1;
            // G3511
            {
                var byte1List = new byte[] { 0x21, 0x22, 0x23 };
                var ff_dl = (byte)random.Next(0x15, 0x1C);
                var caseMutationG3511 = CaseMutation.Create($"G3511")
                    .MutateDlc(dlc)
                    .MutateTpFfHighNibble(ffHighNibble)
                    .MutateTpFfDl12b(ff_dl)
                    .MutateTpParameters(GetTpParameters(options.TpParameters, ff_dl))
                    .MutateTpCfByte1List(byte1List);
                list.Add(caseMutationG3511);
            }

            // G3512
            byte[] snList = new byte[] { 1, 2, 3 };
            new LhsSampler().ShuffleArray(snList);
            var snA = snList[0];
            var snB = snList[1];
            var snC = snList[2];
            for (byte i = 0; i <= 5; i++)
            {
                if (i == 2)
                {
                    continue;
                }

                var byte1List = new byte[] { 0x21, 0x22, 0x23 };
                byte1List[snA - 1] = (byte)(i * 0x10 + byte1List[snA - 1] % 0x10);

                var ff_dl = random.Next(0x15, 0x1C);
                var caseMutation = CaseMutation.Create($"G3512-CF_{byte1List.ToHex()}")
                    .MutateDlc(dlc)
                    .MutateTpFfHighNibble(ffHighNibble)
                    .MutateTpFfDl12b(ff_dl)
                    .MutateTpParameters(GetTpParameters(options.TpParameters, ff_dl))
                    .MutateTpCfByte1List(byte1List);
                list.Add(caseMutation);
            }

            // G3513
            for (byte i = 6; i <= 10; i++)
            {
                var byte1List = new byte[] { 0x21, 0x22, 0x23 };
                byte1List[snB - 1] = (byte)(i * 0x10 + byte1List[snB - 1] % 0x10);

                var ff_dl = random.Next(0x15, 0x1C);
                var caseMutation = CaseMutation.Create($"G3513-CF_{byte1List.ToHex()}")
                    .MutateDlc(dlc)
                    .MutateTpFfHighNibble(ffHighNibble)
                    .MutateTpFfDl12b(ff_dl)
                    .MutateTpParameters(GetTpParameters(options.TpParameters, ff_dl))
                    .MutateTpCfByte1List(byte1List);
                list.Add(caseMutation);
            }

            // G3514
            for (byte i = 11; i <= 15; i++)
            {
                var byte1List = new byte[] { 0x21, 0x22, 0x23 };
                byte1List[snC - 1] = (byte)(i * 0x10 + byte1List[snC - 1] % 0x10);

                var invalidTpCfHighNibble = i;
                var ff_dl = random.Next(0x15, 0x1C);
                var caseMutation = CaseMutation.Create($"G3514-CF_{byte1List.ToHex()}")
                    .MutateDlc(dlc)
                    .MutateTpFfHighNibble(ffHighNibble)
                    .MutateTpFfDl12b(ff_dl)
                    .MutateTpParameters(GetTpParameters(options.TpParameters, ff_dl))
                    .MutateTpCfByte1List(byte1List);
                list.Add(caseMutation);
            }

            // G3521
            {
                byte invalidSnFirst0 = 0;
                var ff_dl = random.Next(0x15, 0x1C);

                var byte1List = new byte[] { 0x21, 0x22, 0x23 };
                byte1List[0] = 0x20;
                var caseMutationG3521 = CaseMutation.Create($"G3521-SN_First_{invalidSnFirst0:X}")
                    .MutateDlc(dlc)
                    .MutateTpFfHighNibble(ffHighNibble)
                    .MutateTpFfDl12b(ff_dl)
                    .MutateTpParameters(GetTpParameters(options.TpParameters, ff_dl))
                    .MutateTpCfByte1List(byte1List);
                list.Add(caseMutationG3521);
            }

            // G3522
            for (byte invalidSnFirst = 0x2; invalidSnFirst <= 0xF; invalidSnFirst++)
            {
                var ff_dl = random.Next(0x15, 0x1C);

                var byte1List = new byte[] { 0x21, 0x22, 0x23 };
                byte1List[0] = (byte)(0x20 + invalidSnFirst);
                var caseMutation = CaseMutation.Create($"G3522-SN_First_{invalidSnFirst:X}")
                    .MutateDlc(dlc)
                    .MutateTpFfHighNibble(ffHighNibble)
                    .MutateTpFfDl12b(ff_dl)
                    .MutateTpParameters(GetTpParameters(options.TpParameters, ff_dl))
                    .MutateTpCfByte1List(byte1List);
                list.Add(caseMutation);
            }

            // G3531
            {
                var ff_dl = random.Next(0x15, 0x1C);

                var byte1List = new byte[] { 0x21, 0x22, 0x23 };
                byte1List[1] = 0x21;
                var caseMutation = CaseMutation.Create($"G3531-FF_DL_{ff_dl:X}")
                    .MutateDlc(dlc)
                    .MutateTpFfHighNibble(ffHighNibble)
                    .MutateTpFfDl12b(ff_dl)
                    .MutateTpParameters(GetTpParameters(options.TpParameters, ff_dl))
                    .MutateTpCfByte1List(byte1List);
                list.Add(caseMutation);
            }

            // G3532
            {
                var sampleCount = 3;
                if (options.Coverage == CoverageType.High)
                {
                    sampleCount = 13;
                }
                var sampleBytes = UniformSampleBytes(0x3, 0xF, sampleCount);
                foreach (var sampleByte in sampleBytes)
                {
                    var ff_dl = random.Next(0x15, 0x1C);

                    var byte1List = new byte[] { 0x21, 0x22, 0x23 };
                    byte1List[1] = (byte)(0x20 + sampleByte);

                    var caseMutation = CaseMutation.Create($"G3532-FF_DL_{ff_dl:X}")
                        .MutateDlc(dlc)
                        .MutateTpFfHighNibble(ffHighNibble)
                        .MutateTpFfDl12b(ff_dl)
                        .MutateTpParameters(GetTpParameters(options.TpParameters, ff_dl))
                        .MutateTpCfByte1List(byte1List);
                    list.Add(caseMutation);
                }
            }

            // G3533
            {
                var ff_dl = random.Next(0x15, 0x1C);

                var byte1List = new byte[] { 0x21, 0x22, 0x23 };
                byte1List[2] = 0x22;

                var caseMutation = CaseMutation.Create($"G3533-FF_DL_{ff_dl:X}")
                    .MutateDlc(dlc)
                    .MutateTpFfHighNibble(ffHighNibble)
                    .MutateTpFfDl12b(ff_dl)
                    .MutateTpParameters(GetTpParameters(options.TpParameters, ff_dl))
                    .MutateTpCfByte1List(byte1List);
                list.Add(caseMutation);
            }

            // G3534
            {
                var sampleCount = 3;
                if (options.Coverage == CoverageType.High)
                {
                    sampleCount = 12;
                }
                var sampleBytes = UniformSampleBytes(0x4, 0xF, sampleCount);
                foreach (var sampleByte in sampleBytes)
                {
                    var byte1List = new byte[] { 0x21, 0x22, 0x23 };
                    byte1List[2] = (byte)(0x20 + sampleByte);

                    var ff_dl = random.Next(0x15, 0x1C);
                    var caseMutation = CaseMutation.Create($"G3534-FF_DL_{ff_dl:X}")
                        .MutateDlc(dlc)
                        .MutateTpFfHighNibble(ffHighNibble)
                        .MutateTpFfDl12b(ff_dl)
                        .MutateTpParameters(GetTpParameters(options.TpParameters, ff_dl))
                        .MutateTpCfByte1List(byte1List);

                    list.Add(caseMutation);
                }
            }

            byte[] calcCfByte1Array(int length)
            {
                var lastSn = 0x20;
                List<byte> sns = new List<byte>();
                length = length - 6;
                while (length > 0)
                {
                    length -= 7;

                    var sn = lastSn + 1;
                    if (sn > 0x2F)
                    {
                        sn = 0x20;
                    }
                    sns.Add((byte)sn);
                    lastSn = sn;
                }
                return sns.ToArray();
            }

            // G3535
            {
                var ff_dl = random.Next(112, 119);

                var byte1List = calcCfByte1Array(ff_dl);
                byte1List[16 - 1] = 0x21;

                var caseMutation = CaseMutation.Create($"G3535-FF_DL_{ff_dl:X}")
                    .MutateDlc(dlc)
                    .MutateTpFfHighNibble(ffHighNibble)
                    .MutateTpFfDl12b(ff_dl)
                    .MutateTpParameters(GetTpParameters(options.TpParameters, ff_dl))
                    .MutateTpCfByte1List(byte1List);
                list.Add(caseMutation);
            }

            // G3536
            {
                var sampleCountG3536 = 4;
                if (options.Coverage == CoverageType.High)
                {
                    sampleCountG3536 = 14;
                }
                var sampleBytes = UniformSampleBytes(0x22, 0x2F, sampleCountG3536);
                foreach (var sampleByte in sampleBytes)
                {
                    var ff_dl = random.Next(112, 119);

                    var byte1List = calcCfByte1Array(ff_dl);
                    byte1List[16 - 1] = sampleByte;

                    var caseMutation = CaseMutation.Create($"G3536-FF_DL_{ff_dl:X}")
                        .MutateDlc(dlc)
                        .MutateTpFfHighNibble(ffHighNibble)
                        .MutateTpFfDl12b(ff_dl)
                        .MutateTpParameters(GetTpParameters(options.TpParameters, ff_dl))
                        .MutateTpCfByte1List(byte1List);
                    list.Add(caseMutation);
                }
            }

            return list.ToArray();
        }
    }
}
