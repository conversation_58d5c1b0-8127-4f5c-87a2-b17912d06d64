using Alsi.Fuzz.Core.Service.CaseFactory.Core;
using Alsi.Fuzz.Core.Service.CaseFactory.Factories.Iso14229.Consts;
using System.Collections.Generic;
using System.Linq;

namespace Alsi.Fuzz.Core.Service.CaseFactory.Factories.Iso14229
{
    public class G42_CaseFactory : CaseFactoryBase
    {
        public override CaseMutation[] Generate(MutationOptions options)
        {
            var caseMutations = new List<CaseMutation>();

            var xmlServices = options.XmlServices;
            var supportedXmlServicesWithSubfunction = xmlServices
                // 仅遍历支持的服务
                .Where(x => x.SubfunctionId.HasValue && x.IsSupported)
                // 仅遍历有 Subfunction 的服务
                .Where(x => IsoUdsConsts.Services.FirstOrDefault(service => x.Id == service.Id)?.Subfunctions.Any() == true)
                .ToArray();

            foreach (var xmlServiceWithSubfunction in supportedXmlServicesWithSubfunction)
            {
                var sid = xmlServiceWithSubfunction.Id;
                var subfunctionId = xmlServiceWithSubfunction.SubfunctionId.Value;
                var parameter2k = xmlServiceWithSubfunction.Parameter2k;

                var payload = new List<byte> { sid, subfunctionId };
                payload.AddRange(parameter2k);

                foreach (var repeat in new[] { 2, 4, 8, 16, 64 })
                {
                    var caseMutation = CaseMutation.Create($"G421-Sid{sid:X2}")
                        .Mutate(MutationFieldType.UDS_Repeat_Frame, repeat.ToString())
                        .MutatePayload(payload.ToArray())
                        .MutatePayloadLength(payload.Count);
                    caseMutations.Add(caseMutation);
                }
            }

            return caseMutations.ToArray();
        }
    }
}
