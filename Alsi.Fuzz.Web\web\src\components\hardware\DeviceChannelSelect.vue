<template>
  <el-select 
    v-model="selectedValue" 
    :placeholder="placeholder" 
    class="device-channel-select"
    :popper-class="'device-channel-select-dropdown'"
    @change="handleChange">
    <el-option v-for="device in devices" :key="device.name" :label="device.name" :value="device.name"
      :disabled="!device.isConnected">
      <div class="device-option">
        <span class="device-name">{{ device.name }}</span>
        <el-tag size="small" :type="device.isConnected ? 'success' : 'danger'">
          {{ device.isConnected ? 'Connected' : 'Disconnected' }}
        </el-tag>
      </div>
    </el-option>
  </el-select>
</template>

<script lang="ts">
import { defineComponent, computed } from 'vue';

export default defineComponent({
  name: 'DeviceChannelSelect',
  
  props: {
    modelValue: {
      type: String,
      default: ''
    },
    devices: {
      type: Array,
      required: true,
      default: () => []
    },
    placeholder: {
      type: String,
      default: 'Select Device Channel'
    }
  },

  emits: ['update:modelValue', 'change'],

  setup(props, { emit }) {
    const selectedValue = computed({
      get: () => props.modelValue,
      set: (value) => emit('update:modelValue', value)
    });

    const handleChange = (value: string) => {
      emit('change', value);
    };

    return {
      selectedValue,
      handleChange
    };
  }
});
</script>

<style scoped>
.device-channel-select {
  width: 100%;
}

:global(.device-channel-select-dropdown) {
  min-width: 400px !important;
}

.device-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
}

.device-name {
  font-weight: 500;
  flex-grow: 1;
}
</style>
