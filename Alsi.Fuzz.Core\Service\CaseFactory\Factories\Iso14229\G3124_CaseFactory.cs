using Alsi.Fuzz.Core.Service.CaseFactory.Core;
using Alsi.Fuzz.Core.Service.CaseFactory.Factories.Iso14229.Consts;
using System.Collections.Generic;
using System.Linq;

namespace Alsi.Fuzz.Core.Service.CaseFactory.Factories.Iso14229
{
    public class G3124_CaseFactory : CaseFactoryBase
    {
        public override CaseMutation[] Generate(MutationOptions options)
        {
            var xmlServices = options.XmlServices;
            var supportedXmlServicesWithoutSubfunction = xmlServices
                // 仅遍历支持的服务
                .Where(x => !x.SubfunctionId.HasValue && x.IsSupported)
                // 仅遍历没有 Subfunction 的服务
                .Where(x => IsoUdsConsts.Services.FirstOrDefault(service => x.Id == service.Id)?.Subfunctions.Any() == false)
                .ToArray();

            var caseMutations = new List<CaseMutation>();

            foreach (var xmlService in supportedXmlServicesWithoutSubfunction)
            {
                var sid = xmlService.Id;

                foreach (var otherXmlService in supportedXmlServicesWithoutSubfunction)
                {
                    if (otherXmlService.Id == xmlService.Id)
                    {
                        continue;
                    }

                    var payload = new List<byte> { sid, };
                    // 别人的参数
                    payload.AddRange(otherXmlService.Parameter2k);

                    var caseMutation = CaseMutation.Create($"G3124-Sid{sid:X2}-OtherSid{otherXmlService.Id:X2}")
                        .MutatePayload(payload.ToArray())
                        .MutatePayloadLength(payload.Count);
                    caseMutations.Add(caseMutation);
                }
            }

            return caseMutations.ToArray();
        }
    }
}
