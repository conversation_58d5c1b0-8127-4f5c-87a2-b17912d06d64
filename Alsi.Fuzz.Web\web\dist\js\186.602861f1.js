"use strict";(self["webpackChunkfuzz_web"]=self["webpackChunkfuzz_web"]||[]).push([[186],{2186:function(e,a,s){s.r(a),s.d(a,{default:function(){return ie}});s(8111),s(2489),s(7588),s(1701),s(8237);var t=s(6768),n=s(144),l=s(1219),u=s(1021),r=(s(1148),s(3579),s(7642),s(8004),s(3853),s(5876),s(2475),s(5024),s(1698),s(4232)),i=s(5130),c=s(7477),o=s(9559),d=s(2971),g=s(9929);const v={class:"time-display"};var p=(0,t.pM)({__name:"TimeDisplay",props:{begin:{},end:{},showDuration:{type:<PERSON><PERSON><PERSON>}},setup(e){const a=e,s=e=>{const a=e.getFullYear(),s=String(e.getMonth()+1).padStart(2,"0"),t=String(e.getDate()).padStart(2,"0"),n=String(e.getHours()).padStart(2,"0"),l=String(e.getMinutes()).padStart(2,"0"),u=String(e.getSeconds()).padStart(2,"0"),r=String(e.getMilliseconds()).padStart(3,"0");return`${a}-${s}-${t} ${n}:${l}:${u}.${r}`},l=e=>{const a=String(e.getHours()).padStart(2,"0"),s=String(e.getMinutes()).padStart(2,"0"),t=String(e.getSeconds()).padStart(2,"0");return`${a}:${s}:${t}`},u=(0,t.EW)((()=>{if(!a.begin)return"";const e=new Date(a.begin),t=s(e);if(!a.end)return`Start: ${t}`;const n=new Date(a.end),l=s(n),u=n.getTime()-e.getTime();let r="";if(u<1e3)r=`${u} ms`;else{const e=Math.floor(u/1e3),a=Math.floor(e/60),s=Math.floor(a/60);r=s>0?`${s}h ${a%60}m ${e%60}s`:a>0?`${a}m ${e%60}s`:`${e}s`}return`Duration: ${r}<br>Start: ${t}<br>End: ${l}`})),i=(0,t.EW)((()=>{if(!a.begin)return"-";const e=new Date(a.begin),s=l(e);if(!a.end||!a.showDuration)return s;const t=new Date(a.end),n=t.getTime()-e.getTime();if(n<1e3)return`Started at ${s}, elapsed ${n}ms`;{const e=Math.round(n/1e3);return`Started at ${s}, elapsed ${e}s`}}));return(e,a)=>e.begin?((0,t.uX)(),(0,t.Wv)((0,n.R1)(g.R7),{key:0,content:u.value,placement:"top",effect:"dark","raw-content":""},{default:(0,t.k6)((()=>[(0,t.Lk)("span",v,(0,r.v_)(i.value),1)])),_:1},8,["content"])):(0,t.Q3)("",!0)}}),m=s(1241);const f=(0,m.A)(p,[["__scopeId","data-v-9d5dcc2c"]]);var k=f;const h={class:"result-panel"},b={key:0,class:"duplicate-warning"},y={key:1,class:"loading-indicator"},C={key:2,class:"empty-message"},_={key:3,class:"results-list"},w={class:"select-all-row"},S=["onClick"],E={class:"item-checkbox"},$={class:"item-name"},L={class:"item-status"},F={class:"item-duration"};var R=(0,t.pM)({__name:"InteroperationResultPanel",props:{results:{},loading:{type:Boolean},selectedSequences:{}},emits:["update:selectedSequences"],setup(e,{emit:a}){const s=e,l=a,u=(0,n.KR)([]);function g(){s.results.length>0&&(u.value=new Array(s.results.length).fill(!0),W())}(0,t.sV)((()=>{g()})),(0,t.wB)((()=>s.results),(()=>{g()}),{immediate:!1}),(0,t.wB)((()=>s.selectedSequences),(e=>{e&&s.results.length>0&&(u.value=s.results.map((a=>e.includes(a.sequenceName))))}),{deep:!0});const v=(0,t.EW)((()=>u.value.length>0&&u.value.every((e=>e)))),p=(0,t.EW)((()=>u.value.some((e=>e))&&!v.value)),m=e=>{u.value=new Array(s.results.length).fill(e),W()},f=e=>{u.value[e]=!u.value[e],W()},R=(e,a,s)=>{u.value[a]=e,W()},W=()=>{const e=s.results.filter(((e,a)=>u.value[a])).map((e=>e.sequenceName)),a=[...new Set(e)];l("update:selectedSequences",a)},q=(0,t.EW)((()=>{if(!s.results||0===s.results.length)return[];const e=new Map,a=new Set;return s.results.forEach((s=>{const t=e.get(s.sequenceName)||0;e.set(s.sequenceName,t+1),t>0&&a.add(s.sequenceName)})),Array.from(a)})),M=e=>q.value.includes(e);return(e,a)=>{const s=(0,t.g2)("el-icon"),l=(0,t.g2)("el-checkbox"),g=(0,t.g2)("el-tag");return(0,t.uX)(),(0,t.CE)("div",h,[a[7]||(a[7]=(0,t.Lk)("div",{class:"panel-header"},[(0,t.Lk)("h3",null,"Interoperation Results"),(0,t.Lk)("div",{class:"panel-subtitle"},"Showing only successful sequences")],-1)),q.value.length>0?((0,t.uX)(),(0,t.CE)("div",b,[(0,t.bF)((0,n.R1)(o.KR),{title:"Warning: Duplicate sequence names detected",type:"warning",closable:!1,"show-icon":""},{default:(0,t.k6)((()=>[(0,t.eW)(" Found duplicate names: "+(0,r.v_)(q.value.join(", "))+".",1),a[2]||(a[2]=(0,t.Lk)("br",null,null,-1)),a[3]||(a[3]=(0,t.eW)(" This may cause selection issues. "))])),_:1})])):(0,t.Q3)("",!0),e.loading?((0,t.uX)(),(0,t.CE)("div",y,[(0,t.bF)(s,{class:"is-loading"},{default:(0,t.k6)((()=>[(0,t.bF)((0,n.R1)(c.Loading))])),_:1}),a[4]||(a[4]=(0,t.eW)(" Loading... "))])):0===e.results.length?((0,t.uX)(),(0,t.CE)("div",C," No successful results available ")):((0,t.uX)(),(0,t.CE)("div",_,[(0,t.Lk)("div",w,[(0,t.bF)(l,{modelValue:v.value,"onUpdate:modelValue":a[0]||(a[0]=e=>v.value=e),indeterminate:p.value,onChange:m,size:"small"},{default:(0,t.k6)((()=>a[5]||(a[5]=[(0,t.eW)(" Select All ")]))),_:1},8,["modelValue","indeterminate"])]),((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(e.results,((e,s)=>((0,t.uX)(),(0,t.CE)("div",{key:e.id||`item-${s}`,class:(0,r.C4)(["list-item",{striped:s%2===1,duplicate:M(e.sequenceName)}]),onClick:(0,i.D$)((e=>f(s)),["stop"])},[(0,t.Lk)("div",E,[(0,t.bF)(l,{modelValue:u.value[s],"onUpdate:modelValue":e=>u.value[s]=e,size:"small",onClick:a[1]||(a[1]=(0,i.D$)((()=>{}),["stop"])),onChange:a=>R(a,s,e)},null,8,["modelValue","onUpdate:modelValue","onChange"])]),(0,t.Lk)("div",$,[(0,t.eW)((0,r.v_)(e.sequenceName)+" ",1),M(e.sequenceName)?((0,t.uX)(),(0,t.Wv)(g,{key:0,size:"small",type:"warning",effect:"dark",class:"duplicate-tag"},{default:(0,t.k6)((()=>a[6]||(a[6]=[(0,t.eW)(" Duplicate ")]))),_:1})):(0,t.Q3)("",!0)]),(0,t.Lk)("div",L,[(0,t.bF)(d.A,{state:e.state},null,8,["state"])]),(0,t.Lk)("div",F,[(0,t.bF)(k,{begin:e.begin,end:e.end,showDuration:!0},null,8,["begin","end"])])],10,S)))),128))]))])}}});const W=(0,m.A)(R,[["__scopeId","data-v-5b656733"]]);var q=W;const M={class:"case-panel"},X={class:"panel-header"},P={key:0,class:"case-info"},x={class:"case-count"},K={class:"estimated-time"},N={key:0,class:"loading-indicator"},T={key:1,class:"empty-message"},D={key:2,class:"results-list"},V=["onClick"],z={class:"group-info"},A={class:"group-name"},G={class:"group-count"},I={class:"group-time"},Q={key:0,class:"group-content"},B={key:0,class:"group-loading"},U={class:"item-header"},j={class:"sequence-group"},H={class:"sequence-name"},O={class:"group-badge"},Y=["title"];var J=(0,t.pM)({__name:"GeneratedCasesPanel",props:{cases:{},generating:{type:Boolean},showEmptyMessage:{type:Boolean},estimatedTime:{}},setup(e){const a=e,s=(0,n.KR)({}),l=(0,n.Kh)({}),u=(0,n.Kh)({}),i=(0,t.EW)((()=>{const e={};for(const s of a.cases){const a=(s.name||"").split("-"),t=a.length>0?a[0]:s.name;e[t]||(e[t]=0),e[t]++}return Object.entries(e).map((([e,a])=>({groupPrefix:e,count:a}))).sort(((e,a)=>e.groupPrefix.localeCompare(a.groupPrefix)))})),o=(e,s)=>{const t=a.estimatedTime/a.cases.length;return t*s},d=e=>{u[e]=!0,setTimeout((()=>{const s=a.cases.filter((a=>{const s=(a.name||"").split("-"),t=s.length>0?s[0]:a.name;return t===e}));l[e]=s,u[e]=!1}),100)},g=e=>{const a=s.value[e];s.value[e]=!a,a||l[e]||d(e)};(0,t.wB)((()=>a.cases),(e=>{console.log("Cases changed:",e.length),Object.keys(l).forEach((e=>{delete l[e]})),Object.keys(u).forEach((e=>{delete u[e]})),s.value={}}),{deep:!0});const v=e=>{if(e=Math.floor(e),e<1e3)return`${e}ms`;if(e<6e4){const a=Math.floor(e/1e3),s=Math.floor(e%1e3);return`${a}s ${s}ms`}if(e<36e5){const a=Math.floor(e/6e4),s=Math.floor(e%6e4/1e3);return`${a}m ${s}s`}{const a=Math.floor(e/36e5),s=Math.floor(e%36e5/6e4);return`${a}h ${s}m`}};return(e,a)=>{const d=(0,t.g2)("el-icon");return(0,t.uX)(),(0,t.CE)("div",M,[(0,t.Lk)("div",X,[a[0]||(a[0]=(0,t.Lk)("div",{class:"header-main"},[(0,t.Lk)("h3",null,"Generated Test Cases")],-1)),e.cases.length>0?((0,t.uX)(),(0,t.CE)("div",P,[(0,t.Lk)("span",x,"Generated: "+(0,r.v_)(e.cases.length)+" cases",1),(0,t.Lk)("span",K," Estimated Time: "+(0,r.v_)(v(e.estimatedTime)),1)])):(0,t.Q3)("",!0)]),e.generating?((0,t.uX)(),(0,t.CE)("div",N,[(0,t.bF)(d,{class:"is-loading"},{default:(0,t.k6)((()=>[(0,t.bF)((0,n.R1)(c.Loading))])),_:1}),a[1]||(a[1]=(0,t.eW)(" Generating... "))])):0===e.cases.length&&e.showEmptyMessage?((0,t.uX)(),(0,t.CE)("div",T," Click Generate to create test cases ")):e.cases.length>0?((0,t.uX)(),(0,t.CE)("div",D,[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(i.value,(e=>((0,t.uX)(),(0,t.CE)("div",{key:e.groupPrefix,class:"group-item"},[(0,t.Lk)("div",{class:"group-header",onClick:a=>g(e.groupPrefix)},[(0,t.Lk)("div",z,[(0,t.bF)(d,{class:(0,r.C4)(["expand-icon",{"is-expanded":s.value[e.groupPrefix]}])},{default:(0,t.k6)((()=>[(0,t.bF)((0,n.R1)(c.CaretRight))])),_:2},1032,["class"]),(0,t.Lk)("span",A,(0,r.v_)(e.groupPrefix),1),(0,t.Lk)("span",G,"("+(0,r.v_)(e.count)+" cases)",1),(0,t.Lk)("span",I," Estimated Time: "+(0,r.v_)(v(o(e.groupPrefix,e.count))),1)])],8,V),s.value[e.groupPrefix]?((0,t.uX)(),(0,t.CE)("div",Q,[u[e.groupPrefix]?((0,t.uX)(),(0,t.CE)("div",B,[(0,t.bF)(d,{class:"is-loading"},{default:(0,t.k6)((()=>[(0,t.bF)((0,n.R1)(c.Loading))])),_:1}),a[2]||(a[2]=(0,t.eW)(" Loading... "))])):l[e.groupPrefix]?((0,t.uX)(!0),(0,t.CE)(t.FK,{key:1},(0,t.pI)(l[e.groupPrefix],((e,a)=>((0,t.uX)(),(0,t.CE)("div",{key:e.id||a,class:(0,r.C4)(["list-item",{striped:a%2===1}])},[(0,t.Lk)("div",U,[(0,t.Lk)("div",j,[(0,t.Lk)("span",H,(0,r.v_)(e.id),1),(0,t.Lk)("span",O,(0,r.v_)(e.name),1)])]),(0,t.Lk)("div",{class:"parameter-text",title:`${e.parameter}`},(0,r.v_)(e.parameter),9,Y)],2)))),128)):(0,t.Q3)("",!0)])):(0,t.Q3)("",!0)])))),128))])):(0,t.Q3)("",!0)])}}});const Z=(0,m.A)(J,[["__scopeId","data-v-7dd937b5"]]);var ee=Z;const ae={class:"test-cases-container"},se={class:"toolbar"},te={class:"coverage-options"},ne={class:"action-buttons"},le={class:"content-area"};var ue=(0,t.pM)({__name:"TestCases",setup(e){const a=(0,n.KR)([]),s=(0,n.KR)([]),r=(0,n.KR)(!0),i=(0,n.KR)(!1),c=(0,n.KR)(!1),o=(0,n.KR)(u.Fp.Normal),d=(0,n.KR)(!1),g=(0,n.KR)([]),v=(0,n.KR)(!1),p=(0,t.EW)((()=>a.value.filter((e=>e.state===u.si.Success)))),m=(0,t.EW)((()=>v.value&&s.value.length>0&&!c.value));(0,t.wB)(p,(e=>{e.length>0&&0===g.value.length&&(g.value=e.map((e=>e.sequenceName)))}),{immediate:!0});const f=async()=>{r.value=!0;try{const e=await u.GQ.getLatestInteroperationCaseResults();a.value=e.data}catch(e){console.error("获取测试结果失败:",e),l.nk.error("Failed to fetch interoperation results")}finally{r.value=!1}},k=async()=>{if(0!==g.value.length){i.value=!0,d.value=!0,v.value=!1,s.value=[];try{const e=await u.GQ.generateCases(o.value,g.value);s.value=[...e.data],v.value=!0,l.nk.success(`Successfully generated ${e.data.length} test cases`),console.log("Generated cases:",s.value.length)}catch(e){console.error("生成测试用例失败:",e),l.nk.error("Failed to generate test cases")}finally{i.value=!1}}else l.nk.warning("Please select at least one sequence")},h=async()=>{if(v.value&&0!==s.value.length){c.value=!0;try{const e=await u.GQ.saveCases(o.value,g.value);l.nk.success(`Successfully saved ${e.data.length} test cases`),s.value=[...e.data],console.log("Saved cases:",s.value.length)}catch(e){console.error("保存测试用例失败:",e),l.nk.error("Failed to save test cases")}finally{c.value=!1}}else l.nk.warning("Please generate test cases first")},b=(0,t.EW)((()=>{if(!s.value.length||!a.value.length)return 0;const e=new Map;a.value.forEach((a=>{if(a.begin&&a.end&&a.sequenceName){const s=new Date(a.end).getTime()-new Date(a.begin).getTime();e.set(a.sequenceName,s)}}));const t=s.value.reduce(((a,s)=>{const t=e.get(s.sequenceName)||0;return a+t}),0);return t}));return(0,t.sV)((()=>{f()})),(e,a)=>{const l=(0,t.g2)("el-radio"),v=(0,t.g2)("el-radio-group"),f=(0,t.g2)("el-button");return(0,t.uX)(),(0,t.CE)("div",ae,[(0,t.Lk)("div",se,[(0,t.Lk)("div",te,[a[4]||(a[4]=(0,t.Lk)("span",null,"Coverage:",-1)),(0,t.bF)(v,{modelValue:o.value,"onUpdate:modelValue":a[0]||(a[0]=e=>o.value=e),size:"small"},{default:(0,t.k6)((()=>[(0,t.bF)(l,{label:(0,n.R1)(u.Fp).Normal},{default:(0,t.k6)((()=>a[2]||(a[2]=[(0,t.eW)("Normal")]))),_:1},8,["label"]),(0,t.bF)(l,{label:(0,n.R1)(u.Fp).High},{default:(0,t.k6)((()=>a[3]||(a[3]=[(0,t.eW)("High")]))),_:1},8,["label"])])),_:1},8,["modelValue"])]),(0,t.Lk)("div",ne,[(0,t.bF)(f,{type:"primary",onClick:k,loading:i.value,disabled:0===g.value.length,size:"small"},{default:(0,t.k6)((()=>a[5]||(a[5]=[(0,t.eW)(" Generate ")]))),_:1},8,["loading","disabled"]),(0,t.bF)(f,{type:"success",onClick:h,loading:c.value,disabled:!m.value,size:"small"},{default:(0,t.k6)((()=>a[6]||(a[6]=[(0,t.eW)(" Save ")]))),_:1},8,["loading","disabled"])])]),(0,t.Lk)("div",le,[(0,t.bF)(q,{results:p.value,loading:r.value,selectedSequences:g.value,"onUpdate:selectedSequences":a[1]||(a[1]=e=>g.value=e)},null,8,["results","loading","selectedSequences"]),(0,t.bF)(ee,{cases:s.value,generating:i.value,"show-empty-message":d.value,"estimated-time":b.value},null,8,["cases","generating","show-empty-message","estimated-time"])])])}}});const re=(0,m.A)(ue,[["__scopeId","data-v-6afd28e8"]]);var ie=re},2971:function(e,a,s){s.d(a,{A:function(){return i}});var t=s(6768),n=s(4232),l=s(1021),u=(0,t.pM)({__name:"CaseStateTag",props:{state:{}},setup(e){const a=e,s=(0,t.EW)((()=>{switch(a.state){case l.si.Success:return"success";case l.si.Running:return"warning";case l.si.Failure:return"danger";case l.si.Pending:default:return"info"}})),u=e=>{switch(e){case l.si.Running:return"Running";case l.si.Pending:return"Not Run";case l.si.Success:return"Passed";case l.si.Failure:return"Failed";default:return"Unknown"}},r=(0,t.EW)((()=>u(a.state)));return(e,a)=>{const l=(0,t.g2)("el-tag");return(0,t.uX)(),(0,t.Wv)(l,{type:s.value,size:"small",style:{"min-width":"60px"}},{default:(0,t.k6)((()=>[(0,t.eW)((0,n.v_)(r.value),1)])),_:1},8,["type"])}}});const r=u;var i=r}}]);
//# sourceMappingURL=186.602861f1.js.map