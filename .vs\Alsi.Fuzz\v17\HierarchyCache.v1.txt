﻿++解决方案 'Alsi.Fuzz' ‎ (12 个项目，共 12 个)
i:{00000000-0000-0000-0000-000000000000}:Alsi.Fuzz.sln
++Alsi.Fuzz.Tester
i:{00000000-0000-0000-0000-000000000000}:Alsi.Fuzz.Tester
++Properties
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.tester\properties\
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.web\properties\
i:{e2962ea3-dab3-4691-988d-a921002e6c16}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz\properties\
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\properties\
i:{11c6fa99-c7d1-43b0-bc29-3bbd3d4e0eb9}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.database\properties\
i:{cbf9675f-c055-4fde-b84a-a21bce58dff6}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.securityaccess.x86\properties\
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.desktop\properties\
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\properties\
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\properties\
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\properties\
++引用
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:
i:{e2962ea3-dab3-4691-988d-a921002e6c16}:
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:
i:{11c6fa99-c7d1-43b0-bc29-3bbd3d4e0eb9}:
i:{cbf9675f-c055-4fde-b84a-a21bce58dff6}:
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
i:{2bf46d86-9704-494a-8998-a478b601df80}:
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:
++Controllers
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.tester\controllers\
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.web\controllers\
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.desktop\controllers\
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\controllers\
++TesterController.cs
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.tester\controllers\testercontroller.cs
++Testers
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.tester\testers\
++CaseContext.cs
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.tester\testers\casecontext.cs
++DataCache.cs
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.tester\testers\datacache.cs
++DiagStepTester.cs
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.tester\testers\diagsteptester.cs
++IsotpStepTester.cs
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.tester\testers\isotpsteptester.cs
++PackageTester.cs
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.tester\testers\packagetester.cs
++StepTester.cs
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.tester\testers\steptester.cs
++App.config
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.tester\app.config
i:{e2962ea3-dab3-4691-988d-a921002e6c16}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz\app.config
i:{cbf9675f-c055-4fde-b84a-a21bce58dff6}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.securityaccess.x86\app.config
++FuzzTesterAssembly.cs
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.tester\fuzztesterassembly.cs
++packages.config
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.tester\packages.config
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.web\packages.config
i:{e2962ea3-dab3-4691-988d-a921002e6c16}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz\packages.config
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\packages.config
i:{11c6fa99-c7d1-43b0-bc29-3bbd3d4e0eb9}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.database\packages.config
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.desktop\packages.config
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\packages.config
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\packages.config
++Program.cs
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.tester\program.cs
i:{cbf9675f-c055-4fde-b84a-a21bce58dff6}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.securityaccess.x86\program.cs
++TesterEnv.cs
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.tester\testerenv.cs
++AssemblyInfo.cs
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.tester\properties\assemblyinfo.cs
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.web\properties\assemblyinfo.cs
i:{e2962ea3-dab3-4691-988d-a921002e6c16}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz\properties\assemblyinfo.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\properties\assemblyinfo.cs
i:{11c6fa99-c7d1-43b0-bc29-3bbd3d4e0eb9}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.database\properties\assemblyinfo.cs
i:{cbf9675f-c055-4fde-b84a-a21bce58dff6}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.securityaccess.x86\properties\assemblyinfo.cs
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.desktop\properties\assemblyinfo.cs
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\properties\assemblyinfo.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\properties\assemblyinfo.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\properties\assemblyinfo.cs
++Alsi.App
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:
i:{e2962ea3-dab3-4691-988d-a921002e6c16}:
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:>1565
i:{11c6fa99-c7d1-43b0-bc29-3bbd3d4e0eb9}:
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
i:{d36982c1-9f69-48a3-8af6-1f018cff1ee5}:Alsi.App
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:
++Alsi.App.Database
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:
i:{e2962ea3-dab3-4691-988d-a921002e6c16}:
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:>1581
i:{d36982c1-9f69-48a3-8af6-1f018cff1ee5}:Alsi.App.Database
++Alsi.App.Devices
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:
i:{e2962ea3-dab3-4691-988d-a921002e6c16}:
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:>1576
i:{d36982c1-9f69-48a3-8af6-1f018cff1ee5}:Alsi.App.Devices
++Alsi.Common.Utils
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:>1580
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:
i:{d36982c1-9f69-48a3-8af6-1f018cff1ee5}:Alsi.Common.Utils
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:
++Alsi.Fuzz.Core
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:
i:{e2962ea3-dab3-4691-988d-a921002e6c16}:
i:{00000000-0000-0000-0000-000000000000}:Alsi.Fuzz.Core
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:>1582
++FreeSql
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:
i:{e2962ea3-dab3-4691-988d-a921002e6c16}:
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:
i:{11c6fa99-c7d1-43b0-bc29-3bbd3d4e0eb9}:
++FreeSql.Provider.Sqlite
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:
i:{e2962ea3-dab3-4691-988d-a921002e6c16}:
i:{11c6fa99-c7d1-43b0-bc29-3bbd3d4e0eb9}:
++Microsoft.CSharp
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:
i:{e2962ea3-dab3-4691-988d-a921002e6c16}:
i:{11c6fa99-c7d1-43b0-bc29-3bbd3d4e0eb9}:
i:{cbf9675f-c055-4fde-b84a-a21bce58dff6}:
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
i:{2bf46d86-9704-494a-8998-a478b601df80}:
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:
++Microsoft.Owin
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
i:{2bf46d86-9704-494a-8998-a478b601df80}:
++Newtonsoft.Json
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
i:{2bf46d86-9704-494a-8998-a478b601df80}:
++Newtonsoft.Json.Bson
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
i:{2bf46d86-9704-494a-8998-a478b601df80}:
++Owin
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
i:{2bf46d86-9704-494a-8998-a478b601df80}:
++System
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:
i:{e2962ea3-dab3-4691-988d-a921002e6c16}:
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:
i:{11c6fa99-c7d1-43b0-bc29-3bbd3d4e0eb9}:
i:{cbf9675f-c055-4fde-b84a-a21bce58dff6}:
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
i:{2bf46d86-9704-494a-8998-a478b601df80}:
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\system.dll
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:
++System.Buffers
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
i:{2bf46d86-9704-494a-8998-a478b601df80}:
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:
++System.Core
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:
i:{e2962ea3-dab3-4691-988d-a921002e6c16}:
i:{11c6fa99-c7d1-43b0-bc29-3bbd3d4e0eb9}:
i:{cbf9675f-c055-4fde-b84a-a21bce58dff6}:
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
i:{2bf46d86-9704-494a-8998-a478b601df80}:
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\system.core.dll
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:
++System.Data
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:
i:{e2962ea3-dab3-4691-988d-a921002e6c16}:
i:{11c6fa99-c7d1-43b0-bc29-3bbd3d4e0eb9}:
i:{cbf9675f-c055-4fde-b84a-a21bce58dff6}:
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
i:{2bf46d86-9704-494a-8998-a478b601df80}:
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\system.data.dll
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:
++System.Data.DataSetExtensions
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:
i:{e2962ea3-dab3-4691-988d-a921002e6c16}:
i:{11c6fa99-c7d1-43b0-bc29-3bbd3d4e0eb9}:
i:{cbf9675f-c055-4fde-b84a-a21bce58dff6}:
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
i:{2bf46d86-9704-494a-8998-a478b601df80}:
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:
++System.Data.SQLite
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:
i:{e2962ea3-dab3-4691-988d-a921002e6c16}:
i:{11c6fa99-c7d1-43b0-bc29-3bbd3d4e0eb9}:
++System.Memory
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
i:{2bf46d86-9704-494a-8998-a478b601df80}:
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:
++System.Net.Http.Formatting
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
i:{2bf46d86-9704-494a-8998-a478b601df80}:
++System.Numerics
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
i:{2bf46d86-9704-494a-8998-a478b601df80}:
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\system.numerics.dll
++System.Numerics.Vectors
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
i:{2bf46d86-9704-494a-8998-a478b601df80}:
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:
++System.Runtime.CompilerServices.Unsafe
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
i:{2bf46d86-9704-494a-8998-a478b601df80}:
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:
++System.Threading.Tasks.Extensions
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
i:{2bf46d86-9704-494a-8998-a478b601df80}:
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:
++System.Web.Http
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
i:{2bf46d86-9704-494a-8998-a478b601df80}:
++System.Web.Http.Owin
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
i:{2bf46d86-9704-494a-8998-a478b601df80}:
++System.Xml
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:
i:{e2962ea3-dab3-4691-988d-a921002e6c16}:
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:
i:{11c6fa99-c7d1-43b0-bc29-3bbd3d4e0eb9}:
i:{cbf9675f-c055-4fde-b84a-a21bce58dff6}:
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
i:{2bf46d86-9704-494a-8998-a478b601df80}:
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\system.xml.dll
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:
++System.Xml.Linq
i:{b8eda0ae-6eb2-42d4-80d6-15fbd5cc8288}:
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:
i:{e2962ea3-dab3-4691-988d-a921002e6c16}:
i:{11c6fa99-c7d1-43b0-bc29-3bbd3d4e0eb9}:
i:{cbf9675f-c055-4fde-b84a-a21bce58dff6}:
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
i:{2bf46d86-9704-494a-8998-a478b601df80}:
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\system.xml.linq.dll
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:
++Alsi.Fuzz.Web
i:{00000000-0000-0000-0000-000000000000}:Alsi.Fuzz.Web
i:{e2962ea3-dab3-4691-988d-a921002e6c16}:
++Dto
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.web\dto\
++Mapping
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.web\mapping\
++app.config
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.web\app.config
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\app.config
i:{11c6fa99-c7d1-43b0-bc29-3bbd3d4e0eb9}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.database\app.config
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.desktop\app.config
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\app.config
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\app.config
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\app.config
++build-web.ps1
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.web\build-web.ps1
++FuzzWebAssembly.cs
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.web\fuzzwebassembly.cs
++MapperEnv.cs
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.web\mapperenv.cs
++web.zip
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.web\web.zip
++Alsi.App.Desktop
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:
i:{e2962ea3-dab3-4691-988d-a921002e6c16}:
i:{d36982c1-9f69-48a3-8af6-1f018cff1ee5}:Alsi.App.Desktop
++Alsi.Common.Parsers
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:
i:{d36982c1-9f69-48a3-8af6-1f018cff1ee5}:Alsi.Common.Parsers
++AutoMapper
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:
++CaseConfigController.cs
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.web\controllers\caseconfigcontroller.cs
++CaseController.cs
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.web\controllers\casecontroller.cs
++HardwareConfigController.cs
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.web\controllers\hardwareconfigcontroller.cs
++InteroperationController.cs
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.web\controllers\interoperationcontroller.cs
++SequenceConfigController.cs
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.web\controllers\sequenceconfigcontroller.cs
++TestPlanController.cs
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.web\controllers\testplancontroller.cs
++TestPlanHistoryController.cs
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.web\controllers\testplanhistorycontroller.cs
++TestSuiteController.cs
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.web\controllers\testsuitecontroller.cs
++WebControllerBase.cs
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.web\controllers\webcontrollerbase.cs
++CaseConfigDto.cs
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.web\dto\caseconfigdto.cs
++HardwareConfigDto.cs
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.web\dto\hardwareconfigdto.cs
++TestPlanDto.cs
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.web\dto\testplandto.cs
++TestPlanHistoryDto.cs
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.web\dto\testplanhistorydto.cs
++TestPlanManifestDto.cs
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.web\dto\testplanmanifestdto.cs
++MappingProfile.cs
i:{6579caa3-008c-4fc2-b6b3-f3d228dafb03}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.web\mapping\mappingprofile.cs
++Alsi.Fuzz
i:{00000000-0000-0000-0000-000000000000}:Alsi.Fuzz
++Views
i:{e2962ea3-dab3-4691-988d-a921002e6c16}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz\views\
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.desktop\views\
++app.manifest
i:{e2962ea3-dab3-4691-988d-a921002e6c16}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz\app.manifest
++App.xaml
i:{e2962ea3-dab3-4691-988d-a921002e6c16}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz\app.xaml
++favicon.ico
i:{e2962ea3-dab3-4691-988d-a921002e6c16}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz\favicon.ico
++Resources.resx
i:{e2962ea3-dab3-4691-988d-a921002e6c16}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz\properties\resources.resx
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.desktop\properties\resources.resx
++Settings.settings
i:{e2962ea3-dab3-4691-988d-a921002e6c16}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz\properties\settings.settings
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.desktop\properties\settings.settings
++PresentationCore
i:{e2962ea3-dab3-4691-988d-a921002e6c16}:
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
++PresentationFramework
i:{e2962ea3-dab3-4691-988d-a921002e6c16}:
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
++System.Xaml
i:{e2962ea3-dab3-4691-988d-a921002e6c16}:
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
++WindowsBase
i:{e2962ea3-dab3-4691-988d-a921002e6c16}:
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:
++MainView.xaml
i:{e2962ea3-dab3-4691-988d-a921002e6c16}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz\views\mainview.xaml
++App.xaml.cs
i:{e2962ea3-dab3-4691-988d-a921002e6c16}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz\app.xaml.cs
++Resources.Designer.cs
i:{e2962ea3-dab3-4691-988d-a921002e6c16}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz\properties\resources.designer.cs
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.desktop\properties\resources.designer.cs
++Settings.Designer.cs
i:{e2962ea3-dab3-4691-988d-a921002e6c16}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz\properties\settings.designer.cs
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.desktop\properties\settings.designer.cs
++MainView.xaml.cs
i:{e2962ea3-dab3-4691-988d-a921002e6c16}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz\views\mainview.xaml.cs
++Contracts
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\contracts\
++Models
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\arxml\models\
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\models\
++Resources
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\resources\
++Service
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\
++CaseFactory
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\
++Core
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\core\
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\core\
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\openissue\core\
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\td\core\
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\filters\core\
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\core\
++Factories
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\factories\
++Iso11898
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\factories\iso11898\
++Iso14229
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\factories\iso14229\
++Consts
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\factories\iso14229\consts\
++CaseFactoryBase.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\factories\iso14229\casefactorybase.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\factories\iso15765\casefactorybase.cs
++G111_G112_G113_CaseFactory.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\factories\iso14229\g111_g112_g113_casefactory.cs
++G114_CaseFactory.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\factories\iso14229\g114_casefactory.cs
++G115_G116_CaseFactory.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\factories\iso14229\g115_g116_casefactory.cs
++G117_CaseFactory.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\factories\iso14229\g117_casefactory.cs
++G121_G122_G123_CaseFactory.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\factories\iso14229\g121_g122_g123_casefactory.cs
++G124_CaseFactory.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\factories\iso14229\g124_casefactory.cs
++G125_CaseFactory.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\factories\iso14229\g125_casefactory.cs
++G131_G132_CaseFactory.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\factories\iso14229\g131_g132_casefactory.cs
++G141_G151_CaseFactory.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\factories\iso14229\g141_g151_casefactory.cs
++G211_G212_G213_G214_CaseFactory.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\factories\iso14229\g211_g212_g213_g214_casefactory.cs
++G221_G222_G223_CaseFactory.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\factories\iso14229\g221_g222_g223_casefactory.cs
++Iso15765
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\factories\iso15765\
++G31_G32_CaseFactory.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\factories\iso15765\g31_g32_casefactory.cs
++G33_G34_CaseFactory.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\factories\iso15765\g33_g34_casefactory.cs
++CaseFactoryUtils.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\casefactoryutils.cs
++Results
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\results\
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.unittests\results\
++Tester
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\tester\
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\contracts\tester\
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\tester\
++BuiltInTestSuiteService.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\builtintestsuiteservice.cs
++CaseService.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\caseservice.cs
++DeviceScanner.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\devicescanner.cs
++FileLocker.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\filelocker.cs
++HardwareService.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\hardwareservice.cs
++IHardwareService.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\ihardwareservice.cs
++ITestPlanHistoryService.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\itestplanhistoryservice.cs
++ITestPlanService.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\itestplanservice.cs
++ReportTemplateService.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\reporttemplateservice.cs
++StatusPollingService.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\statuspollingservice.cs
++TestPlanHistoryService.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\testplanhistoryservice.cs
++TestPlanManager.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\testplanmanager.cs
++TestPlanService.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\testplanservice.cs
++Storage
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\storage\
++Utils
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\utils\
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.unittests\utils\
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.desktop\utils\
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\utils\
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\utils\
++FuzzConsts.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\fuzzconsts.cs
++ICSharpCode.SharpZipLib
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:
i:{2bf46d86-9704-494a-8998-a478b601df80}:
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:
++History
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\history\
++TestPlans
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\testplans\
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.unittests\testplans\
++TestSuites
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\testsuites\
++report-template.html
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\resources\report-template.html
++sequences-11898.xml
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\resources\sequences-11898.xml
++sequences-14229.xml
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\resources\sequences-14229.xml
++sequences-15765.xml
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\resources\sequences-15765.xml
++CaseMutation.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\core\casemutation.cs
++ICaseFactory.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\core\icasefactory.cs
++IsoType.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\core\isotype.cs
++MutationFactoryDiscoverer.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\core\mutationfactorydiscoverer.cs
++MutationField.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\core\mutationfield.cs
++MutationFieldType.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\core\mutationfieldtype.cs
++MutationOptions.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\core\mutationoptions.cs
++DlcCaseFactory.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\factories\iso11898\dlccasefactory.cs
++LimitDataCaseFactory.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\factories\iso11898\limitdatacasefactory.cs
++RandomDataCaseFactory.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\factories\iso11898\randomdatacasefactory.cs
++IsoUdsConsts.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\factories\iso14229\consts\isoudsconsts.cs
++Subfunction.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\factories\iso14229\consts\subfunction.cs
++UdsService.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\casefactory\factories\iso14229\consts\udsservice.cs
++CaseResult.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\results\caseresult.cs
++CaseStep.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\results\casestep.cs
++ExecutionState.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\results\executionstate.cs
++ITestResultReaderService.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\results\itestresultreaderservice.cs
++ITestResultWriterService.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\results\itestresultwriterservice.cs
++ResultContext.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\results\resultcontext.cs
++Sequence.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\results\sequence.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\testsuites\sequence.cs
++TestResult.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\results\testresult.cs
++TestResultReaderService.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\results\testresultreaderservice.cs
++TestResultWriterService.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\results\testresultwriterservice.cs
++TestType.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\results\testtype.cs
++ApiResponse.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\tester\apiresponse.cs
++ITesterApiClient.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\tester\itesterapiclient.cs
++ITesterManager.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\tester\itestermanager.cs
++TesterApiClient.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\tester\testerapiclient.cs
++TesterConsts.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\tester\testerconsts.cs
++TesterManager.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\service\tester\testermanager.cs
++DataLogStorage.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\storage\datalogstorage.cs
++TestPlanStorage.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\storage\testplanstorage.cs
++LhsSampler.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\utils\lhssampler.cs
++SecurityAccessUtils.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\utils\securityaccessutils.cs
++SequencePackageUtils.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\utils\sequencepackageutils.cs
++SequenceUtils.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\utils\sequenceutils.cs
++ExecutionRequest.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\contracts\tester\executionrequest.cs
++TestPlanHistory.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\history\testplanhistory.cs
++EnvVars.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\tester\envvars.cs
++TesterProcess.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\tester\testerprocess.cs
++CaseConfig.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\testplans\caseconfig.cs
++HardwareConfig.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\testplans\hardwareconfig.cs
++SecurityConfig.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\testplans\securityconfig.cs
++SequenceConfig.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\testplans\sequenceconfig.cs
++SequenceConfigDto.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\testplans\sequenceconfigdto.cs
++TestPlan.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\testplans\testplan.cs
++TestPlanConfig.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\testplans\testplanconfig.cs
++TestPlanManifest.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\testplans\testplanmanifest.cs
++WhiteListFrame.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\testplans\whitelistframe.cs
++Steps
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\testsuites\steps\
++EnvVar.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\testsuites\envvar.cs
++SequencePackage.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\testsuites\sequencepackage.cs
++SetVar.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\testsuites\setvar.cs
++Store.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\testsuites\store.cs
++TestSuite.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\testsuites\testsuite.cs
++Diagnostic
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\testsuites\steps\diagnostic\
++Isotp
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\testsuites\steps\isotp\
++CalcKeyStep.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\testsuites\steps\calckeystep.cs
++Frame.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\testsuites\steps\frame.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\arxml\models\frame.cs
++MatchFrame.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\testsuites\steps\matchframe.cs
++PrintStep.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\testsuites\steps\printstep.cs
++ReceiveStep.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\testsuites\steps\receivestep.cs
++SendStep.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\testsuites\steps\sendstep.cs
++StepBase.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\testsuites\steps\stepbase.cs
++WaitStep.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\testsuites\steps\waitstep.cs
++MatchDiag.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\testsuites\steps\diagnostic\matchdiag.cs
++RecvDiagStep.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\testsuites\steps\diagnostic\recvdiagstep.cs
++SendDiagStep.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\testsuites\steps\diagnostic\senddiagstep.cs
++RecvIsotpStep.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\testsuites\steps\isotp\recvisotpstep.cs
++SendIsotpStep.cs
i:{9c5c66b4-b1fa-4f41-9daf-3779c5940c53}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.core\models\testsuites\steps\isotp\sendisotpstep.cs
++UnitTests
i:{00000000-0000-0000-0000-000000000000}:UnitTests
++Alsi.Fuzz.UnitTests
i:{4c1f4725-d3ce-4a1d-859a-f93e638bcc76}:Alsi.Fuzz.UnitTests
++依赖项
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:>1561
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:>1558
++Hardware
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.unittests\hardware\
++Log
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.unittests\log\
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\log\
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\log\
++Suites
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.unittests\suites\
++SerialUnitTestCollection.cs
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.unittests\serialunittestcollection.cs
++UnitTestAppContext.cs
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.unittests\unittestappcontext.cs
++UnitTestBase.cs
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.unittests\unittestbase.cs
++包
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:>1591
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:>1563
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:>1577
++分析器
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:>1583
++框架
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:>1589
++项目
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:>1562
++DataBusTests.cs
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.unittests\hardware\databustests.cs
++DeviceTests.cs
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.unittests\hardware\devicetests.cs
++BlfLogTests.cs
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.unittests\log\blflogtests.cs
++ResultContextTests.cs
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.unittests\results\resultcontexttests.cs
++TestResultServiceTests.cs
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.unittests\results\testresultservicetests.cs
++PackageSerializationTests.cs
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.unittests\suites\packageserializationtests.cs
++TestPlanTests.cs
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.unittests\testplans\testplantests.cs
++TestPlanUpdateTests.cs
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.unittests\testplans\testplanupdatetests.cs
++LhsSamplerTests.cs
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.unittests\utils\lhssamplertests.cs
++SequenceUtilsTests.cs
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.fuzz.unittests\utils\sequenceutilstests.cs
++coverlet.collector (3.2.0)
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:>1596
++FreeSql.Provider.Sqlite (3.5.106)
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:>1597
++Microsoft.NET.Test.Sdk (17.5.0)
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:>1593
++Shouldly (4.3.0)
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:>1592
++xunit (2.9.3)
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:>1594
++xunit.runner.visualstudio (3.0.2)
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:>1595
++Xunit.SkippableFact (1.5.23)
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:>1598
++Microsoft.CodeAnalysis.CSharp.NetAnalyzers
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.csharp.netanalyzers.dll
++Microsoft.CodeAnalysis.NetAnalyzers
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:c:\program files\dotnet\sdk\9.0.203\sdks\microsoft.net.sdk\analyzers\microsoft.codeanalysis.netanalyzers.dll
++System.Text.Json.SourceGeneration
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:c:\program files\dotnet\packs\microsoft.netcore.app.ref\6.0.36\analyzers\dotnet\cs\system.text.json.sourcegeneration.dll
++xunit.analyzers
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:d:\nuget_packages\xunit.analyzers\1.18.0\analyzers\dotnet\cs\xunit.analyzers.dll
++xunit.analyzers.fixes
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:d:\nuget_packages\xunit.analyzers\1.18.0\analyzers\dotnet\cs\xunit.analyzers.fixes.dll
++Microsoft.NETCore.App
i:{bb7cb51f-8fe5-473f-b731-c6bc3ae32be7}:>1590
++Common
i:{00000000-0000-0000-0000-000000000000}:Common
++Midwares
i:{11c6fa99-c7d1-43b0-bc29-3bbd3d4e0eb9}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.database\midwares\
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.desktop\midwares\
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\midwares\
++AppExtensions.cs
i:{11c6fa99-c7d1-43b0-bc29-3bbd3d4e0eb9}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.database\appextensions.cs
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.desktop\appextensions.cs
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\appextensions.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\appextensions.cs
++DbEnv.cs
i:{11c6fa99-c7d1-43b0-bc29-3bbd3d4e0eb9}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.database\dbenv.cs
++DbContext.cs
i:{11c6fa99-c7d1-43b0-bc29-3bbd3d4e0eb9}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.database\midwares\dbcontext.cs
++DbContextManager.cs
i:{11c6fa99-c7d1-43b0-bc29-3bbd3d4e0eb9}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.database\midwares\dbcontextmanager.cs
++FreeSqlMidware.cs
i:{11c6fa99-c7d1-43b0-bc29-3bbd3d4e0eb9}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.database\midwares\freesqlmidware.cs
++Alsi.App.SecurityAccess.x86
i:{d36982c1-9f69-48a3-8af6-1f018cff1ee5}:Alsi.App.SecurityAccess.x86
++System.Net.Http
i:{cbf9675f-c055-4fde-b84a-a21bce58dff6}:
i:{2bf46d86-9704-494a-8998-a478b601df80}:
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:
++Themes
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.desktop\themes\
++WindowEnv.cs
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.desktop\windowenv.cs
++CefSharp
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
++CefSharp.Core
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
++CefSharp.Wpf
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
++MahApps.Metro.IconPacks.Core
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
++MahApps.Metro.IconPacks.Material
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
++Ookii.Dialogs.Wpf
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
++System.Design
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
++System.Drawing
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\system.drawing.dll
++System.Security
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:
++ExplorerController.cs
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.desktop\controllers\explorercontroller.cs
++AppExceptionHandlerMidware.cs
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.desktop\midwares\appexceptionhandlermidware.cs
++DesktopMidware.cs
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.desktop\midwares\desktopmidware.cs
++ProcessMutexMidware.cs
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.desktop\midwares\processmutexmidware.cs
++Generic.xaml
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.desktop\themes\generic.xaml
++CefSharpUtils.cs
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.desktop\utils\cefsharputils.cs
++CefSharpUtils.CustomKeyboardHandler.cs
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.desktop\utils\cefsharputils.customkeyboardhandler.cs
++CefSharpUtils.EmptyContextMenuHandler.cs
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.desktop\utils\cefsharputils.emptycontextmenuhandler.cs
++UiUtils.cs
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.desktop\utils\uiutils.cs
++WindowUtils.cs
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.desktop\utils\windowutils.cs
++WindowUtils.RemoveIcon.cs
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.desktop\utils\windowutils.removeicon.cs
++CefBrowser.xaml
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.desktop\views\cefbrowser.xaml
++WindowEx.xaml
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.desktop\views\windowex.xaml
++CefBrowser.xaml.cs
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.desktop\views\cefbrowser.xaml.cs
++WindowEx.xaml.cs
i:{47a11109-4f55-41ce-9bd3-c74687d7a212}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app.desktop\views\windowex.xaml.cs
++AppEnv.cs
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\appenv.cs
++AppOptions.cs
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\appoptions.cs
++WebHostApp.cs
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\webhostapp.cs
++Microsoft.Owin.Host.HttpListener
i:{2bf46d86-9704-494a-8998-a478b601df80}:
++Microsoft.Owin.Hosting
i:{2bf46d86-9704-494a-8998-a478b601df80}:
++Serilog
i:{2bf46d86-9704-494a-8998-a478b601df80}:
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\midwares\serilog\
++Serilog.Sinks.Console
i:{2bf46d86-9704-494a-8998-a478b601df80}:
++Serilog.Sinks.File
i:{2bf46d86-9704-494a-8998-a478b601df80}:
++App
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\controllers\app\
++ControllerBase.cs
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\controllers\controllerbase.cs
++ErrorData.cs
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\controllers\errordata.cs
++AppMidwareManager.cs
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\core\appmidwaremanager.cs
++IApiAssembly.cs
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\core\iapiassembly.cs
++IAppMidware.cs
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\core\iappmidware.cs
++ILogger.cs
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\log\ilogger.cs
++LogCategory.cs
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\log\logcategory.cs
++LogConsts.cs
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\log\logconsts.cs
++LogEntry.cs
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\log\logentry.cs
++MemoryLogger.cs
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\log\memorylogger.cs
++ApiHost
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\midwares\apihost\
++ContentTypeUtils.cs
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\utils\contenttypeutils.cs
++PortUtils.cs
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\utils\portutils.cs
++AppController.cs
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\controllers\app\appcontroller.cs
++AppInfo.cs
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\controllers\app\appinfo.cs
++ApiHostMidware.cs
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\midwares\apihost\apihostmidware.cs
++ApiHostStartup.cs
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\midwares\apihost\apihoststartup.cs
++LoggingHandler.cs
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\midwares\apihost\logginghandler.cs
++SerilogLogger.cs
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\midwares\serilog\seriloglogger.cs
++SerilogMidware.cs
i:{2bf46d86-9704-494a-8998-a478b601df80}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.app\midwares\serilog\serilogmidware.cs
++Arxml
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\arxml\
++Dbc
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\
++Ldf
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\ldf\
++OpenIssue
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\openissue\
++Sddb
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\sddb\
++Td
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\td\
++Vbf
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\vbf\
++BouncyCastle.Cryptography
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:
++Enums.NET
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:
++MathNet.Numerics
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:
++Microsoft.IO.RecyclableMemoryStream
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:
++NPOI.Core
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:
++NPOI.OOXML
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:
++NPOI.OpenXml4Net
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:
++NPOI.OpenXmlFormats
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:
++SixLabors.Fonts
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:
++SixLabors.ImageSharp
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:
++System.ComponentModel.DataAnnotations
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:
++System.Configuration
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:
++System.Configuration.ConfigurationManager
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:
++System.Data.OracleClient
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:
++System.Net
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:
++System.Runtime.Serialization
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\system.runtime.serialization.dll
++System.Security.AccessControl
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:
++System.Security.Cryptography.Xml
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:
++System.Security.Permissions
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:
++System.Security.Principal.Windows
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:
++System.ServiceProcess
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:
++System.Text.Encoding.CodePages
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:
++System.Transactions
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:
++ModelsParsers
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\arxml\modelsparsers\
++ArxmlParser.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\arxml\arxmlparser.cs
++AUTOSAR_4-2-2.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\arxml\autosar_4-2-2.cs
++AutosarExtension.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\arxml\autosarextension.cs
++AutosarStaticExtension.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\arxml\autosarstaticextension.cs
++Parsers
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\parsers\
++DbcParser.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\dbcparser.cs
++Model
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\ldf\model\
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\vbf\model\
++LdfParser.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\ldf\ldfparser.cs
++OpenIssueParser.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\openissue\openissueparser.cs
++Project.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\sddb\project.cs
++SddbParser.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\sddb\sddbparser.cs
++TdParser.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\td\tdparser.cs
++TdResult.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\td\tdresult.cs
++ColumnExAttribute.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\utils\columnexattribute.cs
++ColumnExInfo.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\utils\columnexinfo.cs
++VbfParser.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\vbf\vbfparser.cs
++ClusterBusType.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\arxml\models\clusterbustype.cs
++CommunicationCluster.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\arxml\models\communicationcluster.cs
++Direction.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\arxml\models\direction.cs
++Pdu.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\arxml\models\pdu.cs
++Signal.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\arxml\models\signal.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\models\signal.cs
++CommunicationClusterParser.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\arxml\modelsparsers\communicationclusterparser.cs
++AttributeDef.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\models\attributedef.cs
++AttributeType.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\models\attributetype.cs
++CustomProperty.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\models\customproperty.cs
++DbcModel.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\models\dbcmodel.cs
++DbcValueType.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\models\dbcvaluetype.cs
++EnvAccessibility.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\models\envaccessibility.cs
++EnvDataType.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\models\envdatatype.cs
++EnvironmentVariable.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\models\environmentvariable.cs
++Message.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\models\message.cs
++Node.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\models\node.cs
++SignalExtension.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\models\signalextension.cs
++SignalGroup.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\models\signalgroup.cs
++StatisticInfo.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\models\statisticinfo.cs
++ValTable.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\models\valtable.cs
++AttributeDefDefParser.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\parsers\attributedefdefparser.cs
++AttributeDefParser.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\parsers\attributedefparser.cs
++AttributeParser.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\parsers\attributeparser.cs
++BaudrateParser.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\parsers\baudrateparser.cs
++CommentParser.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\parsers\commentparser.cs
++EnvironmentDataVariableParser.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\parsers\environmentdatavariableparser.cs
++EnvironmentVariableParser.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\parsers\environmentvariableparser.cs
++IParser.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\parsers\iparser.cs
++MessageParser.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\parsers\messageparser.cs
++NewSymbolParser.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\parsers\newsymbolparser.cs
++NodeParser.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\parsers\nodeparser.cs
++ParserContext.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\parsers\parsercontext.cs
++SignalGroupParser.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\parsers\signalgroupparser.cs
++SignalParser.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\parsers\signalparser.cs
++SignalValueTypeParser.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\parsers\signalvaluetypeparser.cs
++ValParser.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\parsers\valparser.cs
++ValTableParser.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\parsers\valtableparser.cs
++VersionParser.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\dbc\parsers\versionparser.cs
++LdfFrame.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\ldf\model\ldfframe.cs
++LdfModel.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\ldf\model\ldfmodel.cs
++LdfNode.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\ldf\model\ldfnode.cs
++LdfScheduleTable.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\ldf\model\ldfscheduletable.cs
++LdfSignal.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\ldf\model\ldfsignal.cs
++OpenIssueModel.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\openissue\core\openissuemodel.cs
++OpenIssueRow.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\openissue\core\openissuerow.cs
++TdModel.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\td\core\tdmodel.cs
++TdRow.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\td\core\tdrow.cs
++VbfBlock.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\vbf\model\vbfblock.cs
++VbfErase.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\vbf\model\vbferase.cs
++VbfModel.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\vbf\model\vbfmodel.cs
++VbfOutputModel.cs
i:{2306abe4-156f-48bf-a890-c67ef1c28c67}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.parsers\vbf\model\vbfoutputmodel.cs
++Autosar
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\autosar\
++Consoles
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\consoles\
++Cryptography
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\cryptography\
++Filters
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\filters\
++Linq
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\linq\
++Reflection
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\reflection\
++Threads
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\threads\
++Timers
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\timers\
++AppException.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\appexception.cs
++ArrayUtils.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\arrayutils.cs
++ByteUtils.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\byteutils.cs
++DirExtension.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\dirextension.cs
++DisplayAttribute.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\displayattribute.cs
++EnumUtils.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\enumutils.cs
++EnvUtils.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\envutils.cs
++HashUtils.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\hashutils.cs
++HexExtension.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\hexextension.cs
++JsonUtils.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\jsonutils.cs
++NativeDllUtils.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\nativedllutils.cs
++NumberOptions.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\numberoptions.cs
++NumberUtils.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\numberutils.cs
++PathUtils.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\pathutils.cs
++StringUtils.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\stringutils.cs
++ValidationUtils.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\validationutils.cs
++XmlUtils.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\xmlutils.cs
++net462
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:>1559
++netstandard2.0
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:>1560
++DlcUtils.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\autosar\dlcutils.cs
++ConsoleEx.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\consoles\consoleex.cs
++Option.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\consoles\option.cs
++AesUtils.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\cryptography\aesutils.cs
++CryptFile.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\cryptography\cryptfile.cs
++CryptFileIndex.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\cryptography\cryptfileindex.cs
++CryptFileUtils.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\cryptography\cryptfileutils.cs
++Sha256Utils.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\cryptography\sha256utils.cs
++FilterUtils.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\filters\filterutils.cs
++IFilter.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\filters\ifilter.cs
++LinqExtension.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\linq\linqextension.cs
++AssemblyLoader.CacheItem.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\reflection\assemblyloader.cacheitem.cs
++AssemblyLoader.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\reflection\assemblyloader.cs
++AssemblyLoader.Dependeny.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\reflection\assemblyloader.dependeny.cs
++TypeUtils.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\reflection\typeutils.cs
++AsyncRelayer.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\threads\asyncrelayer.cs
++ProcessUtils.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\threads\processutils.cs
++ThreadUtils.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\threads\threadutils.cs
++TimeWindow.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\threads\timewindow.cs
++TimeWindowCounter.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\threads\timewindowcounter.cs
++HighResolutionTimer.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\timers\highresolutiontimer.cs
++MultimediaTimer.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\timers\multimediatimer.cs
++NativeMethods.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\timers\nativemethods.cs
++程序集
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:>1566
++CaseIdFilter.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\filters\core\caseidfilter.cs
++FrameIdFilter.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\filters\core\frameidfilter.cs
++FrameIdRange.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\filters\core\frameidrange.cs
++NameFilter.cs
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:d:\src\005_tab\2、src\1、source code\alsi.common\alsi.common.utils\filters\core\namefilter.cs
++Newtonsoft.Json (13.0.3)
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:newtonsoft.json/13.0.3
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:>1578
++System.IO.Compression.FileSystem
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:c:\program files (x86)\reference assemblies\microsoft\framework\.netframework\v4.6.2\system.io.compression.filesystem.dll
++NETStandard.Library (2.0.3)
i:{f6626691-d7f8-483c-9423-ed1c6e9214f7}:>1579
++TsLibCan
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\tslibcan\
++Vector
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\vector\
++x64
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\x64\
++DeviceEnv.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\deviceenv.cs
++DeviceMidware.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\devicemidware.cs
++Interop.TsCANApi
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:
++vxlapi_NET
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:
++Channels
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\core\channels\
++TransportLayer
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\core\transportlayer\
++CanFrame.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\core\canframe.cs
++ChannelConfig.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\core\channelconfig.cs
++CommunicationType.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\core\communicationtype.cs
++DataBus.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\core\databus.cs
++DataBusTimer.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\core\databustimer.cs
++DeviceChannel.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\core\devicechannel.cs
++ICanChannel.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\core\icanchannel.cs
++Manufacturer.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\core\manufacturer.cs
++TimestampUtils.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\core\timestamputils.cs
++BlfLogReader.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\log\blflogreader.cs
++BlfLogWriter.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\log\blflogwriter.cs
++BlfStructs.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\log\blfstructs.cs
++EncapsulationLibTsCan.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\tslibcan\encapsulationlibtscan.cs
++TosunConsts.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\tslibcan\tosunconsts.cs
++TsLibApi.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\tslibcan\tslibapi.cs
++TsLibCanChannelInfo.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\tslibcan\tslibcanchannelinfo.cs
++EncapsulationVectorCan.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\vector\encapsulationvectorcan.cs
++VectorDeviceApi.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\vector\vectordeviceapi.cs
++VectorInfo.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\vector\vectorinfo.cs
++blf
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\x64\blf\
++libTsCan
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\x64\libtscan\
++vector
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\x64\vector\
++TsLibCanChannel.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\core\channels\tslibcanchannel.cs
++VectorCanChannel.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\core\channels\vectorcanchannel.cs
++Frames
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\core\transportlayer\frames\
++DiagParams.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\core\transportlayer\diagparams.cs
++NoFlowControlException.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\core\transportlayer\noflowcontrolexception.cs
++Request.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\core\transportlayer\request.cs
++Response.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\core\transportlayer\response.cs
++ResponseStore.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\core\transportlayer\responsestore.cs
++TpContext.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\core\transportlayer\tpcontext.cs
++TpService.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\core\transportlayer\tpservice.cs
++TpService.SendMultipleFrame.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\core\transportlayer\tpservice.sendmultipleframe.cs
++TpService.SendSingleFrame.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\core\transportlayer\tpservice.sendsingleframe.cs
++binlog.dll
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\x64\blf\binlog.dll
++OperateBlf.dll
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\x64\blf\operateblf.dll
++Interop.TsCANApi.dll
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\x64\libtscan\interop.tscanapi.dll
++libTSCAN.dll
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\x64\libtscan\libtscan.dll
++libTSH.dll
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\x64\libtscan\libtsh.dll
++vxlapi.dll
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\x64\vector\vxlapi.dll
++vxlapi_NET.dll
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\x64\vector\vxlapi_net.dll
++vxlapi64.dll
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\x64\vector\vxlapi64.dll
++ConsecutiveFrame.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\core\transportlayer\frames\consecutiveframe.cs
++FirstFrame.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\core\transportlayer\frames\firstframe.cs
++FlowControl.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\core\transportlayer\frames\flowcontrol.cs
++FlowState.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\core\transportlayer\frames\flowstate.cs
++FrameRecorder.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\core\transportlayer\frames\framerecorder.cs
++IDiagFrame.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\core\transportlayer\frames\idiagframe.cs
++MultipleFrame.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\core\transportlayer\frames\multipleframe.cs
++SingleFrame.cs
i:{b5bd0cc8-03eb-4319-8b67-0fa697b43553}:d:\src\005_tab\2、src\1、source code\alsi.fuzz\alsi.app.devices\core\transportlayer\frames\singleframe.cs
