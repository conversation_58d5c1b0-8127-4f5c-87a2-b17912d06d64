import axios from 'axios'
import { USE_MOCK, mockApi } from '@/mock/mockApi'

// 定义序列配置数据结构
export interface SequenceConfigData {
  testSuiteName: string;
  sequencePackageName: string;
  sequencePackageXml?: string;
}

export enum CommunicationType {
  Can = 0,
  Canfd = 1
}

const BASE_URL = '/api/sequenceConfig'

export const sequenceApi = {
  // 获取序列配置
  getSequenceConfig: () => {
    if (USE_MOCK) {
      return mockApi.sequence.getSequenceConfig();
    }
    return axios.get<SequenceConfigData>(`${BASE_URL}`);
  },

  // 更新序列配置
  updateSequenceConfig: (config: SequenceConfigData) => {
    if (USE_MOCK) {
      return mockApi.sequence.updateSequenceConfig(config);
    }
    return axios.post<SequenceConfigData>(`${BASE_URL}`, config);
  }
}

export default sequenceApi
