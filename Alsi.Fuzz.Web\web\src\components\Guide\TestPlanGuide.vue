<template>
  <div class="guide-content">
    <el-timeline>
      <el-timeline-item
        v-for="step in steps"
        :key="step.title"
        :type="'info'"
        :size="'normal'"
        :timestamp="``"
      >
        <div class="step-content">
          <div class="step-header">
            <span class="step-title">{{ step.title }}</span>
          </div>
          <p class="step-desc">{{ step.description }}</p>
        </div>
      </el-timeline-item>
    </el-timeline>
  </div>
</template>

<script setup lang="ts">

const steps = [
  {
    title: 'Basic Setting',
    description: 'Configure test plan name, description and select test suite type',
  },
  {
    title: 'Hardware Setting',
    description: 'Set up CAN/CANFD hardware type and configure baud rate parameters',
  },
  {
    title: 'Interoperation',
    description: 'Verify hardware connection and protocol compatibility',
  },
  {
    title: 'Test Cases',
    description: 'Select coverage intensity and generate test cases based on supported services',
  },
  {
    title: 'Test Run',
    description: 'Run test cases with start and stop control',
  },
  {
    title: 'Report',
    description: 'View test reports and analyze data with detailed results',
  }
]
</script>

<style scoped>
.guide-content {
  padding: 10px 16px;
  max-width: 800px;
  margin: 0 auto;
  max-height: 100vh;
}

:deep(.el-timeline) {
  padding: 0 15px;
}

:deep(.el-timeline-item__node) {
  background-color: var(--el-color-primary);
  top: 10px;
}

:deep(.el-timeline-item__tail) {
  top: 10px;
}

:deep(.el-timeline-item__node--primary) {
  background-color: var(--el-color-primary);
}

:deep(.el-timeline-item__node--success) {
  background-color: var(--el-color-success);
}

:deep(.el-timeline-item__timestamp) {
  color: var(--el-text-color-secondary);
  font-weight: bold;
}

:deep(.el-timeline-item__content) {
  position: relative;
  top: -4px; /* 微调内容垂直位置以保持对齐 */
}

:deep(.el-timeline-item) {
  padding-bottom: 15px; /* 减小项目间距 */
}

.step-content {
  background: var(--el-bg-color-page);
  border-radius: 4px;
  padding: 10px 20px;
  margin-bottom: 10px;
  box-shadow: var(--el-box-shadow-lighter);
}

.step-title {
  font-size: 16px; /* 减小标题字体大小 */
  font-weight: bold;
  color: var(--el-color-primary);
  margin-bottom: 5px; /* 减小底部外边距 */
  display: block;
}

.step-desc {
  color: var(--el-text-color-regular);
  margin: 5px 0 0; /* 减小上方外边距 */
  font-size: 14px;
  line-height: 1.4; /* 稍微减小行高 */
}

:deep(.el-timeline-item:last-child .el-timeline-item__tail) {
  display: none;
}
</style>
