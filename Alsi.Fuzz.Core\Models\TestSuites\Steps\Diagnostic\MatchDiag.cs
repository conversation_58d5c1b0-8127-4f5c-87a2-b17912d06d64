using Alsi.Fuzz.Core.Utils;
using System.Collections.Generic;
using System.Xml.Serialization;

namespace Alsi.Fuzz.Core.Models.TestSuites.Steps.Diagnostic
{
    public class MatchDiag
    {
        [XmlAttribute("hex-payload")]
        public string Payload { get; set; }

        public bool IsMatch(string expectedPayload, byte[] actualPayload)
        {
            if (!string.IsNullOrWhiteSpace(expectedPayload))
            {
                var matchBytes = SequenceUtils.ParseMatchBytes(expectedPayload);
                if (actualPayload.Length != matchBytes.Length)
                {
                    return false;

                }
                for (var i = 0; i < matchBytes.Length; i++)
                {
                    if (matchBytes[i].HasValue
                        && matchBytes[i].Value != actualPayload[i])
                    {
                        return false;
                    }
                }
            }

            return true;
        }

        public static MatchDiag Empty = new MatchDiag();

        [XmlElement("set-var")]
        public List<SetVar> SetVars { get; set; } = new List<SetVar>();
    }
}
