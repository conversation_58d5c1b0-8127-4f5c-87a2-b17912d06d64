import axios, { AxiosResponse } from 'axios'
import { USE_MOCK, mockApi } from '@/mock/mockApi'

export interface SequencePackage {
  name: string;
  sequences: any[];
}

export interface TestSuite {
  name: string;
  version: string;
  packages: SequencePackage[];
}

const BASE_URL = '/api/testsuite'

export const testSuiteApi = {
  getBuiltIn: (): Promise<AxiosResponse<TestSuite[]>> => {
    if (USE_MOCK) {
      return mockApi.testSuite.getBuiltIn();
    }
    return axios.get(`${BASE_URL}/builtin`);
  },

  getBuiltInXml: (suiteName: string, packageName: string): Promise<AxiosResponse<string>> => {
    if (USE_MOCK) {
      return mockApi.testSuite.getXml(suiteName, packageName);
    }
    return axios.get(`${BASE_URL}/builtin-xml`, { params: { suiteName, packageName } });
  },

  getXml: (suiteName: string, packageName: string): Promise<AxiosResponse<string>> => {
    if (USE_MOCK) {
      return mockApi.testSuite.getXml(suiteName, packageName);
    }
    return axios.get(`${BASE_URL}/xml`, { params: { suiteName, packageName } });
  }
}

export default testSuiteApi
