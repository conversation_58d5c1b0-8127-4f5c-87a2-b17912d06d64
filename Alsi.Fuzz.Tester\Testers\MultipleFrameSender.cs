using Alsi.App;
using Alsi.App.Devices.Core;
using Alsi.App.Devices.Core.TransportLayer.Frames;
using Alsi.Common.Utils.Timers;
using System;
using System.Linq;

namespace Alsi.Fuzz.Tester.Testers
{
    public class MultipleFrameSender
    {
        public MultipleFrameSender(
            CanFrame firstFrame,
            CanFrame[] consecutiveFrames,
            int diagResId,
            int diagTimeoutMs,
            bool waitFcInSendingCfs,
            Func<FlowControl> receiveFlowControlTimeout)
        {
            this.firstFrame = firstFrame;
            this.consecutiveFrames = consecutiveFrames;
            this.diagResId = diagResId;
            this.diagTimeoutMs = diagTimeoutMs;
            this.waitFcInSendingCfs = waitFcInSendingCfs;
            this.receiveFlowControlTimeout = receiveFlowControlTimeout;
        }

        private CanFrame firstFrame;
        private CanFrame[] consecutiveFrames;
        private int diagResId;
        private int diagTimeoutMs;
        private readonly bool waitFcInSendingCfs;
        private Func<FlowControl> receiveFlowControlTimeout;

        private FlowControl HandleFcMonitor(FrameMonitor monitor)
        {
            if (monitor.WaitForMatch())
            {
                // 接收到流控帧
                if (FlowControl.TryMatch(monitor.LastMatchedFrame.Data, false, out var flowControl))
                {
                    return flowControl;
                }

                throw new Exception("Received frame could not be parsed as Flow Control");
            }

            if (receiveFlowControlTimeout != null)
            {
                return receiveFlowControlTimeout.Invoke();
            }

            throw new Exception("No flow control received");
        }

        public CanFrame Execute()
        {
            // 监听 FC，发首帧
            var fcMonitor = FrameMonitor.CreateFcMatcher(diagResId, diagTimeoutMs);
            fcMonitor.StartListening();

            AppEnv.Logger.Debug($"MultipleFrameSender -> Send: {firstFrame}");
            DataBus.Send(firstFrame);

            var fc = HandleFcMonitor(fcMonitor);
            var blockSize = fc.BlockSize;
            var stMin = fc.STmin;

            // 一次发送的连续帧数量
            var cfCountOfRound = (!waitFcInSendingCfs || blockSize == 0) ? consecutiveFrames.Length : blockSize;

            FrameMonitor responseFrameMonitor = null;

            // 发连续帧，等流控帧
            var cfs = consecutiveFrames.ToArray();
            while (cfs.Length > 0)
            {
                var cfsOfRound = cfs.Take(cfCountOfRound).ToArray();
                cfs = cfs.Skip(cfCountOfRound).ToArray();

                for (var i = 0; i < cfsOfRound.Length; i++)
                {
                    var isLastCfOfRound = i == cfsOfRound.Length - 1;
                    var isLastCf = isLastCfOfRound && cfs.Length == 0;

                    if (isLastCfOfRound)
                    {
                        if (!isLastCf)
                        {
                            // 开始等流控
                            fcMonitor = FrameMonitor.CreateFcMatcher(diagResId, diagTimeoutMs);
                            fcMonitor.StartListening();
                        }
                        else
                        {
                            // 开始等响应首帧
                            responseFrameMonitor = FrameMonitor.CreateIdMatcher(diagResId, diagTimeoutMs);
                            responseFrameMonitor.StartListening();
                        }
                    }

                    var frame = cfsOfRound[i];
                    AppEnv.Logger.Debug($"MultipleFrameSender -> Send: {frame}");
                    DataBus.Send(frame);

                    if (!isLastCfOfRound && stMin > 0)
                    {
                        // 根据 STmin 值添加相应延时
                        if (stMin <= 127)
                        {
                            // 毫秒级
                            HighResolutionTimer.Sleep(stMin);
                        }
                        else if (stMin >= 0xF1 && stMin <= 0xF9)
                        {
                            // 微秒级转换
                            HighResolutionTimer.Sleep((stMin - 0xF0) / 10);
                        }
                    }

                    if (isLastCfOfRound)
                    {
                        if (!isLastCf)
                        {
                            fc = HandleFcMonitor(fcMonitor);

                            // 更新 BlockSize 和 STmin
                            blockSize = fc.BlockSize;
                            stMin = fc.STmin;
                            cfCountOfRound = blockSize == 0 ? consecutiveFrames.Length : blockSize;
                        }
                    }
                }
            }

            if (responseFrameMonitor != null && responseFrameMonitor.WaitForMatch())
            {
                return responseFrameMonitor.LastMatchedFrame;
            }

            return null;
        }
    }
}
