using FreeSql.DataAnnotations;
using System;

namespace Alsi.Fuzz.Core.Service.Results
{
    public class TestResult
    {
        [Column(IsIdentity = true, IsPrimary = true)]
        public Guid Id { get; set; }
        public TestType TestType { get; set; }
        public string TestPlanName { get; set; }
        public string ResultFolderName { get; set; }
        public int TotalCount { get; set; }
        public int SuccessCount { get; set; }
        public int FailureCount { get; set; }
        public DateTime? Begin { get; set; }
        public DateTime? End { get; set; }
        public DateTime CreationTime { get; set; }

        public double GetProgress()
        {
            if (TotalCount == 0)
            {
                return 0;
            }

            return (SuccessCount + FailureCount) * 100.0 / TotalCount;
        }
    }
}
