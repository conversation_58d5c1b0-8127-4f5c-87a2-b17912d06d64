<?xml version="1.0"?>
<doc>
    <assembly>
        <name>MahApps.Metro.IconPacks.Core</name>
    </assembly>
    <members>
        <member name="T:MahApps.Metro.IconPacks.MetaDataAttribute">
            <summary>
            Specifies meta data for a class.
            </summary>
        </member>
        <member name="T:MahApps.Metro.IconPacks.Converter.FlipToScaleXValueConverter">
            <summary>
            ValueConverter which converts the PackIconFlipOrientation enumeration value to ScaleX value of a ScaleTransformation.
            </summary>
        </member>
        <member name="T:MahApps.Metro.IconPacks.Converter.FlipToScaleYValueConverter">
            <summary>
            ValueConverter which converts the PackIconFlipOrientation enumeration value to ScaleY value of a ScaleTransformation.
            </summary>
        </member>
        <member name="T:MahApps.Metro.IconPacks.Converter.MarkupConverter">
            <summary>
            MarkupConverter is a MarkupExtension which can be used for IValueConverter.
            </summary>
        </member>
        <member name="M:MahApps.Metro.IconPacks.Converter.MarkupConverter.ProvideValue(System.IServiceProvider)">
            <inheritdoc />
        </member>
        <member name="M:MahApps.Metro.IconPacks.Converter.MarkupConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Converts a value.
            </summary>
            <param name="value">The value produced by the binding source.</param>
            <param name="targetType">The type of the binding target property.</param>
            <param name="parameter">The converter parameter to use.</param>
            <param name="culture">The culture to use in the converter.</param>
            <returns>A converted value. If the method returns null, the valid null value is used.</returns>
        </member>
        <member name="M:MahApps.Metro.IconPacks.Converter.MarkupConverter.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            Converts a value.
            </summary>
            <param name="value">The value that is produced by the binding target.</param>
            <param name="targetType">The type to convert to.</param>
            <param name="parameter">The converter parameter to use.</param>
            <param name="culture">The culture to use in the converter.</param>
            <returns>A converted value. If the method returns null, the valid null value is used.</returns>
        </member>
        <member name="M:MahApps.Metro.IconPacks.Converter.MarkupConverter.System#Windows#Data#IValueConverter#Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <inheritdoc />
        </member>
        <member name="M:MahApps.Metro.IconPacks.Converter.MarkupConverter.System#Windows#Data#IValueConverter#ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <inheritdoc />
        </member>
        <member name="P:MahApps.Metro.IconPacks.Converter.PackIconKindToImageConverterBase.Brush">
            <summary>
            Gets or sets the brush to draw the icon.
            </summary>
        </member>
        <member name="P:MahApps.Metro.IconPacks.Converter.PackIconKindToImageConverterBase.Flip">
            <summary>
            Gets or sets the flip orientation for the icon.
            </summary>
        </member>
        <member name="P:MahApps.Metro.IconPacks.Converter.PackIconKindToImageConverterBase.RotationAngle">
            <summary>
            Gets or sets the rotation (angle) for the icon.
            </summary>
        </member>
        <member name="M:MahApps.Metro.IconPacks.Converter.PackIconKindToImageConverterBase.GetPathData(System.Object)">
            <summary>
            Gets the path data for the given kind.
            </summary>
        </member>
        <member name="M:MahApps.Metro.IconPacks.Converter.PackIconKindToImageConverterBase.GetScaleTransform(System.Object)">
            <summary>
            Gets the ScaleTransform for the given kind.
            </summary>
            <param name="iconKind">The icon kind to draw.</param>
        </member>
        <member name="M:MahApps.Metro.IconPacks.Converter.PackIconKindToImageConverterBase.GetTransformGroup(System.Object)">
            <summary>
            Gets the <see cref="T:System.Windows.Media.TransformGroup" /> for the <see cref="T:System.Windows.Media.DrawingGroup" />.
            </summary>
            <param name="iconKind">The icon kind to draw.</param>
        </member>
        <member name="M:MahApps.Metro.IconPacks.Converter.PackIconKindToImageConverterBase.GetDrawingGroup(System.Object,System.Windows.Media.Brush,System.String)">
            <summary>
            Gets the <see cref="T:System.Windows.Media.DrawingGroup" /> object that will be used for the <see cref="T:System.Windows.Media.DrawingImage" />.
            </summary>
        </member>
        <member name="M:MahApps.Metro.IconPacks.Converter.PackIconKindToImageConverterBase.CreateImageSource(System.Object,System.Windows.Media.Brush)">
            <summary>
            Gets the ImageSource for the given kind.
            </summary>
        </member>
        <member name="M:MahApps.Metro.IconPacks.Converter.PackIconKindToImageConverterBase.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <inheritdoc />
        </member>
        <member name="M:MahApps.Metro.IconPacks.Converter.PackIconKindToImageConverterBase.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <inheritdoc />
        </member>
        <member name="T:MahApps.Metro.IconPacks.PackIconControlBase">
            <summary>
            Class PackIconControlBase which is the base class for any PackIcon control.
            </summary>
        </member>
        <member name="P:MahApps.Metro.IconPacks.PackIconControlBase.Data">
            <summary>
            Gets the path data for the current icon kind.
            </summary>
        </member>
        <member name="F:MahApps.Metro.IconPacks.PackIconControlBase.FlipProperty">
            <summary>
            Identifies the Flip dependency property.
            </summary>
        </member>
        <member name="P:MahApps.Metro.IconPacks.PackIconControlBase.Flip">
            <summary>
            Gets or sets the flip orientation.
            </summary>
        </member>
        <member name="F:MahApps.Metro.IconPacks.PackIconControlBase.RotationAngleProperty">
            <summary>
            Identifies the RotationAngle dependency property.
            </summary>
        </member>
        <member name="P:MahApps.Metro.IconPacks.PackIconControlBase.RotationAngle">
            <summary>
            Gets or sets the rotation (angle).
            </summary>
            <value>The rotation.</value>
        </member>
        <member name="F:MahApps.Metro.IconPacks.PackIconControlBase.SpinProperty">
            <summary>
            Identifies the Spin dependency property.
            </summary>
        </member>
        <member name="P:MahApps.Metro.IconPacks.PackIconControlBase.Spin">
            <summary>
            Gets or sets a value indicating whether the inner icon is spinning.
            </summary>
            <value><c>true</c> if spin; otherwise, <c>false</c>.</value>
        </member>
        <member name="F:MahApps.Metro.IconPacks.PackIconControlBase.SpinDurationProperty">
            <summary>
            Identifies the SpinDuration dependency property.
            </summary>
        </member>
        <member name="P:MahApps.Metro.IconPacks.PackIconControlBase.SpinDuration">
            <summary>
            Gets or sets the duration of the spinning animation (in seconds). This will also restart the spin animation.
            </summary>
            <value>The duration of the spin in seconds.</value>
        </member>
        <member name="F:MahApps.Metro.IconPacks.PackIconControlBase.SpinEasingFunctionProperty">
            <summary>
            Identifies the SpinEasingFunction dependency property.
            </summary>
        </member>
        <member name="P:MahApps.Metro.IconPacks.PackIconControlBase.SpinEasingFunction">
            <summary>
            Gets or sets the EasingFunction of the spinning animation. This will also restart the spin animation.
            </summary>
            <value>The spin easing function.</value>
        </member>
        <member name="F:MahApps.Metro.IconPacks.PackIconControlBase.SpinAutoReverseProperty">
            <summary>
            Identifies the SpinAutoReverse dependency property.
            </summary>
        </member>
        <member name="P:MahApps.Metro.IconPacks.PackIconControlBase.SpinAutoReverse">
            <summary>
            Gets or sets the AutoReverse of the spinning animation. This will also restart the spin animation.
            </summary>
            <value><c>true</c> if [spin automatic reverse]; otherwise, <c>false</c>.</value>
        </member>
        <member name="T:MahApps.Metro.IconPacks.PackIconFlipOrientation">
            <summary>
            Enum PackIconFlipOrientation for the Flip property of any PackIcon control.
            </summary>
        </member>
        <member name="F:MahApps.Metro.IconPacks.PackIconFlipOrientation.Normal">
            <summary>
            No flip
            </summary>
        </member>
        <member name="F:MahApps.Metro.IconPacks.PackIconFlipOrientation.Horizontal">
            <summary>
            Flip the icon horizontal
            </summary>
        </member>
        <member name="F:MahApps.Metro.IconPacks.PackIconFlipOrientation.Vertical">
            <summary>
            Flip the icon vertical
            </summary>
        </member>
        <member name="F:MahApps.Metro.IconPacks.PackIconFlipOrientation.Both">
            <summary>
            Flip the icon vertical and horizontal
            </summary>
        </member>
        <member name="P:MahApps.Metro.IconPacks.BasePackIconImageExtension.Brush">
            <summary>
            Gets or sets the brush to draw the icon.
            </summary>
        </member>
        <member name="P:MahApps.Metro.IconPacks.BasePackIconImageExtension.Flip">
            <summary>
            Gets or sets the flip orientation for the icon.
            </summary>
        </member>
        <member name="P:MahApps.Metro.IconPacks.BasePackIconImageExtension.RotationAngle">
            <summary>
            Gets or sets the rotation (angle) for the icon.
            </summary>
        </member>
        <member name="M:MahApps.Metro.IconPacks.BasePackIconImageExtension.GetPathData(System.Object)">
            <summary>
            Gets the path data for the given kind.
            </summary>
        </member>
        <member name="M:MahApps.Metro.IconPacks.BasePackIconImageExtension.GetScaleTransform(System.Object)">
            <summary>
            Gets the ScaleTransform for the given kind.
            </summary>
            <param name="iconKind">The icon kind to draw.</param>
        </member>
        <member name="M:MahApps.Metro.IconPacks.BasePackIconImageExtension.GetTransformGroup(System.Object)">
            <summary>
            Gets the <see cref="T:System.Windows.Media.TransformGroup" /> for the <see cref="T:System.Windows.Media.DrawingGroup" />.
            </summary>
            <param name="iconKind">The icon kind to draw.</param>
        </member>
        <member name="M:MahApps.Metro.IconPacks.BasePackIconImageExtension.GetDrawingGroup(System.Object,System.Windows.Media.Brush,System.String)">
            <summary>
            Gets the <see cref="T:System.Windows.Media.DrawingGroup" /> object that will be used for the <see cref="T:System.Windows.Media.DrawingImage" />.
            </summary>
        </member>
        <member name="M:MahApps.Metro.IconPacks.BasePackIconImageExtension.CreateImageSource(System.Object,System.Windows.Media.Brush)">
            <summary>
            Gets the ImageSource for the given kind.
            </summary>
        </member>
    </members>
</doc>
