using Alsi.App.Devices.Core;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
using vxlapi_NET;
using static vxlapi_NET.XLClass;
using static vxlapi_NET.XLDefine;

namespace Alsi.App.Devices.Vector
{
    public partial class EncapsulationVectorCan
    {
        [DllImport("kernel32.dll", SetLastError = true)]
        static extern int WaitForSingleObject(int handle, int timeOut);
        private static readonly string appName = "tab";
        public static bool filterErrorFrame = false;
        public static bool isFirstErrorFrame = true;
        public static List<XLcanRxEvent> ErrorCanfdFrameList = new List<XLcanRxEvent>();
        public delegate void SendCanDataToTrace(CanFrame dataFrame);
        public static event SendCanDataToTrace smttEvent;
        private static bool isReceiveCanfdData = false;
        private static int eventHandle = -1;

        public static void InitializeData()
        {
            OpenDriver();
        }

        public static void FinalizeData()
        {
            CloseDriver();
        }

        public static void TSAppStart(VectorInfo vectorInfo, ChannelConfig config, byte appChannel)
        {
            try
            {
                XL_Status status = VectorDeviceApi.VectorDriver.XL_SetApplConfig(appName, appChannel, (XL_HardwareType)vectorInfo.VectorDeviceType, (uint)vectorInfo.HWIndex, (uint)vectorInfo.HWChanel, XLDefine.XL_BusTypes.XL_BUS_TYPE_CAN);
                OperateVectorLog(status, "XL_SetApplConfig");
                ulong chMask = VectorDeviceApi.VectorDriver.XL_GetChannelMask((XL_HardwareType)vectorInfo.VectorDeviceType, vectorInfo.HWIndex, vectorInfo.HWChanel);
                OperateVectorLog(status, "XL_GetChannelMask");
                int chIdx = VectorDeviceApi.VectorDriver.XL_GetChannelIndex((XL_HardwareType)vectorInfo.VectorDeviceType, vectorInfo.HWIndex, vectorInfo.HWChanel);
                OperateVectorLog(status, "XL_GetChannelIndex");
                ulong accessMask = chMask;
                vectorInfo.AccessMask = accessMask;
                vectorInfo.PermissionMask = accessMask;

                OpenPort(vectorInfo);
                if (config.CommunicationType == CommunicationType.Can)
                {
                    SetCanConfig(vectorInfo, config);
                }
                else if (config.CommunicationType == CommunicationType.CanFd)
                {
                    SetCanFdConfig(vectorInfo, config);
                }

                status = VectorDeviceApi.VectorDriver.XL_SetNotification(vectorInfo.PortHandle, ref eventHandle, 1);
                ActivateChannel(vectorInfo);
                ErrorCanfdFrameList.Clear();
                isReceiveCanfdData = true;

                xlSyncTime = null;
                ulong syncTime = 0;
                if (VectorDeviceApi.VectorDriver.XL_GetSyncTime(vectorInfo.PortHandle, ref syncTime) == XL_Status.XL_SUCCESS)
                {
                    xlSyncTime = syncTime;
                }

                StartReceiveCanfdData(vectorInfo);
            }
            catch (Exception ex)
            {
                AppEnv.Logger.Error(ex, ex.Message);
            }
        }

        private static ulong? xlSyncTime = null;

        public static void SetSelfAck(VectorInfo vectorInfo, bool enableTesterSelfAck)
        {
            var outModel = enableTesterSelfAck ? XL_OutputMode.XL_OUTPUT_MODE_NORMAL : XL_OutputMode.XL_OUTPUT_MODE_SILENT;
            var status = VectorDeviceApi.VectorDriver.XL_CanSetChannelOutput(vectorInfo.PortHandle, vectorInfo.AccessMask, outModel);
        }

        public static void TSAppStop(VectorInfo vectorInfo)
        {
            isReceiveCanfdData = false;
            var status = VectorDeviceApi.VectorDriver.XL_DeactivateChannel(vectorInfo.PortHandle, vectorInfo.AccessMask);
            //CloseDriver();
        }

        public static DeviceChannel[] GetDeviceChannels()
        {
            var deviceChannels = new List<DeviceChannel>();
            try
            {
                var driverConfig = new XLClass.xl_driver_config();
                XL_Status status = VectorDeviceApi.VectorDriver.XL_GetDriverConfig(ref driverConfig);
                OperateVectorLog(status, "XL_GetDriverConfig");
                if (status == XL_Status.XL_SUCCESS && driverConfig.channel != null)
                {
                    var xlCanChanels = driverConfig.channel.
                                       Where(t => t.busParams.busType == XL_BusTypes.XL_BUS_TYPE_CAN
                                            || t.channelBusCapabilities == XL_BusCapabilities.XL_BUS_ACTIVE_CAP_CAN
                                            || t.channelBusCapabilities == XL_BusCapabilities.XL_BUS_COMPATIBLE_CAN);

                    foreach (xl_channel_config chanelData in xlCanChanels)
                    {
                        if (string.IsNullOrWhiteSpace(chanelData.name))
                        {
                            continue;
                        }
                        var deviceChannel = new DeviceChannel();
                        deviceChannel.Manufacturer = Manufacturer.Vector;
                        deviceChannel.DeviceFactory = Enum.GetName(typeof(Manufacturer), 1);
                        deviceChannel.CommunicationType = CommunicationType.CanFd;
                        deviceChannel.DeviceSerial = chanelData.serialNumber.ToString();
                        deviceChannel.DeviceType = chanelData.name.Replace("\0", "");
                        deviceChannel.VectorInfo = new VectorInfo();

                        var vector = deviceChannel.VectorInfo;
                        vector.HWIndex = chanelData.hwIndex;
                        vector.ChanelMask = chanelData.channelMask;
                        vector.HWChanel = chanelData.hwChannel;
                        vector.ChanelIndex = chanelData.channelIndex;
                        vector.VectorDeviceType = (int)chanelData.hwType;
                        deviceChannels.Add(deviceChannel);
                    }

                    var xlLinChanels = driverConfig.channel.
                                        Where(t => t.busParams.busType == XL_BusTypes.XL_BUS_TYPE_LIN
                                              || t.channelBusCapabilities == XL_BusCapabilities.XL_BUS_ACTIVE_CAP_LIN
                                              || t.channelBusCapabilities == XL_BusCapabilities.XL_BUS_COMPATIBLE_LIN);
                    foreach (xl_channel_config chanelData in xlLinChanels)
                    {
                        if (string.IsNullOrWhiteSpace(chanelData.name))
                        {
                            continue;
                        }
                        var deviceChannel = new DeviceChannel();
                        deviceChannel.Manufacturer = Manufacturer.Vector;
                        deviceChannel.DeviceFactory = Enum.GetName(typeof(Manufacturer), 1);
                        deviceChannel.CommunicationType = CommunicationType.Lin;
                        deviceChannel.DeviceSerial = chanelData.serialNumber.ToString();
                        deviceChannel.DeviceType = chanelData.name.Replace("\0", "");
                        deviceChannel.VectorInfo = new VectorInfo();

                        var vector = deviceChannel.VectorInfo;
                        vector.HWIndex = chanelData.hwIndex;
                        vector.ChanelMask = chanelData.channelMask;
                        vector.HWChanel = chanelData.hwChannel;
                        vector.ChanelIndex = chanelData.channelIndex;
                        vector.VectorDeviceType = (int)chanelData.hwType;
                        deviceChannels.Add(deviceChannel);
                    }

                    var xlEthChannels = driverConfig.channel.
                                           Where(t => t.busParams.busType == XL_BusTypes.XL_BUS_TYPE_ETHERNET
                                                 || t.channelBusCapabilities == XL_BusCapabilities.XL_BUS_COMPATIBLE_ETHERNET
                                                 || t.channelBusCapabilities == XL_BusCapabilities.XL_BUS_ACTIVE_CAP_ETHERNET);
                    foreach (xl_channel_config chanelData in xlEthChannels)
                    {
                        if (string.IsNullOrWhiteSpace(chanelData.name))
                        {
                            continue;
                        }
                        var deviceChannel = new DeviceChannel();
                        deviceChannel.Manufacturer = Manufacturer.Vector;
                        deviceChannel.DeviceFactory = Enum.GetName(typeof(Manufacturer), 1);
                        deviceChannel.CommunicationType = CommunicationType.Eth;
                        deviceChannel.DeviceSerial = chanelData.serialNumber.ToString();
                        deviceChannel.DeviceType = chanelData.name.Replace("\0", "");
                        deviceChannel.VectorInfo = new VectorInfo();

                        var vector = deviceChannel.VectorInfo;
                        vector.HWIndex = chanelData.hwIndex;
                        vector.ChanelMask = chanelData.channelMask;
                        vector.HWChanel = chanelData.hwChannel;
                        vector.ChanelIndex = chanelData.channelIndex;
                        vector.VectorDeviceType = (int)chanelData.hwType;
                        deviceChannels.Add(deviceChannel);
                    }

                    //deviceChannels.AddRange(EncapsulationVectorNetEth.GetDevice());
                }
            }
            catch (Exception ex)
            {
                AppEnv.Logger.Error(ex, ex.Message);
            }
            return deviceChannels.ToArray();
        }

        public static void SetCanConfig(VectorInfo vectorInfo, ChannelConfig config)
        {
            VectorDeviceApi.VectorDriver.XL_CanSetChannelBitrate(
                vectorInfo.PortHandle,
                vectorInfo.AccessMask,
                (uint)config.CanBitrate);
        }

        public static void SetCanFdConfig(VectorInfo vectorInfo, ChannelConfig config)
        {
            var canFdConf = new XLcanFdConf();
            // arbitration bitrate
            canFdConf.arbitrationBitRate = (uint)config.CanFdArbitrationBitRate;
            canFdConf.tseg1Abr = 119;
            canFdConf.tseg2Abr = 40;
            canFdConf.sjwAbr = 40;

            // data bitrate
            canFdConf.dataBitRate = (uint)config.CanFdDataBitRate;
            canFdConf.tseg1Dbr = 29;
            canFdConf.tseg2Dbr = 10;
            canFdConf.sjwDbr = 10;

            canFdConf.options = 0;

            var status = VectorDeviceApi.VectorDriver.XL_CanFdSetConfiguration(vectorInfo.PortHandle, vectorInfo.AccessMask, canFdConf);
        }

        public static void SendCanFrameData(VectorInfo vectorInfo, int canId, int dlc, byte[] data, bool rtr, bool isExt)
        {
            if (vectorInfo == null)
            {
                return;
            }
            var canTx = new XLcanTxEvent();
            canTx.tag = XL_CANFD_TX_EventTags.XL_CAN_EV_TAG_TX_MSG;
            canTx.tagData.canId = (uint)canId;
            canTx.tagData.dlc = (XL_CANFD_DLC)dlc;
            canTx.tagData.msgFlags = XL_CANFD_TX_MessageFlags.XL_CAN_TXMSG_FLAG_NONE;
            if (rtr)
            {
                canTx.tagData.msgFlags |= XL_CANFD_TX_MessageFlags.XL_CAN_TXMSG_FLAG_RTR;
            }
            if (isExt)
            {
                canTx.tagData.canId |= (uint)XL_MessageFlagsExtended.XL_CAN_EXT_MSG_ID;
            }

            canTx.channelIndex = vectorInfo.ChanelIndex;
            var dataLength = GetRxDataLength(canTx.tagData.dlc);
            if (data.Length < dataLength)
            {
                var newData = new byte[dataLength];
                data.CopyTo(newData, 0);
                canTx.tagData.data = newData;
            }
            else
            {
                canTx.tagData.data = data;
            }

            uint messageCounterSent = 0;
            var status = VectorDeviceApi.VectorDriver.XL_CanTransmitEx(vectorInfo.PortHandle, vectorInfo.AccessMask, ref messageCounterSent, canTx);
        }

        public static void SendCanFDFrameData(VectorInfo vectorInfo, int canId, int dlc, byte[] data, bool rtr, bool isExt)
        {
            if (vectorInfo == null)
            {
                return;
            }

            var canfd = new XLcanTxEvent();
            canfd.tag = XL_CANFD_TX_EventTags.XL_CAN_EV_TAG_TX_MSG;
            canfd.tagData.canId = (uint)canId;
            canfd.tagData.dlc = (XL_CANFD_DLC)dlc;
            canfd.tagData.msgFlags = XL_CANFD_TX_MessageFlags.XL_CAN_TXMSG_FLAG_BRS | XL_CANFD_TX_MessageFlags.XL_CAN_TXMSG_FLAG_EDL;
            if (rtr)
            {
                canfd.tagData.msgFlags |= XL_CANFD_TX_MessageFlags.XL_CAN_TXMSG_FLAG_RTR;
            }
            if (isExt)
            {
                canfd.tagData.canId |= (uint)XL_MessageFlagsExtended.XL_CAN_EXT_MSG_ID;
            }
            canfd.channelIndex = vectorInfo.ChanelIndex;
            var dataLength = GetRxDataLength(canfd.tagData.dlc);
            if (data.Length < dataLength)
            {
                var newData = new byte[dataLength];
                data.CopyTo(newData, 0);
                canfd.tagData.data = newData;
            }
            else
            {
                canfd.tagData.data = data;
            }

            uint messageCounterSent = 0;
            var txStatus = VectorDeviceApi.VectorDriver.XL_CanTransmitEx(vectorInfo.PortHandle, vectorInfo.AccessMask, ref messageCounterSent, canfd);
        }

        public static void StartReceiveCanfdData(VectorInfo vectorInfo)
        {
            Task.Run(() =>
            {
                var receivedEvent = new XLcanRxEvent();
                var waitResult = new XLDefine.WaitResults();
                while (isReceiveCanfdData)
                {
                    waitResult = (XLDefine.WaitResults)WaitForSingleObject(eventHandle, 1);
                    if (waitResult != XLDefine.WaitResults.WAIT_TIMEOUT)
                    {
                        var xlStatus = XLDefine.XL_Status.XL_SUCCESS;
                        while (xlStatus != XLDefine.XL_Status.XL_ERR_QUEUE_IS_EMPTY)
                        {
                            xlStatus = VectorDeviceApi.VectorDriver.XL_CanReceive(vectorInfo.PortHandle, ref receivedEvent);
                            if (xlStatus == XLDefine.XL_Status.XL_SUCCESS)
                            {
                                ReceiveCanfdFrame(receivedEvent);
                            }
                        }
                    }
                }
            });
        }

        public static void OpenDriver()
        {
            XL_Status status = VectorDeviceApi.VectorDriver.XL_OpenDriver();
            OperateVectorLog(status, "XL_OpenDriver");
        }

        public static void CloseDriver()
        {
            XL_Status status = VectorDeviceApi.VectorDriver.XL_CloseDriver();
            OperateVectorLog(status, "XL_CloseDriver");
        }

        public static void OpenPort(VectorInfo vectorInfo)
        {
            int PortHandle = vectorInfo.PortHandle;
            ulong permissionMask = vectorInfo.PermissionMask;

            var canfdStatus = VectorDeviceApi.VectorDriver.XL_OpenPort(ref PortHandle, appName, vectorInfo.AccessMask, ref permissionMask, 16000,
                               XL_InterfaceVersion.XL_INTERFACE_VERSION_V4, XL_BusTypes.XL_BUS_TYPE_CAN); //Canfd 需要设置XL_INTERFACE_VERSION_V4 Can需要设置成XL_INTERFACE_VERSION
            vectorInfo.PermissionMask = permissionMask;
            vectorInfo.PortHandle = PortHandle;
        }

        public static void ActivateChannel(VectorInfo vectorInfo)
        {
            var status = VectorDeviceApi.VectorDriver.XL_ActivateChannel(vectorInfo.PortHandle, vectorInfo.AccessMask,
                           XL_BusTypes.XL_BUS_TYPE_CAN, XLDefine.XL_AC_Flags.XL_ACTIVATE_NONE);
        }

        private static void FilterCanfdErrorFrame(XLcanRxEvent aData)
        {
            var dataFrame = ConvertDataFrame(aData);
            smttEvent?.Invoke(dataFrame);
        }

        private static CanFrame ConvertDataFrame(XLcanRxEvent vectorCanfdFrame)
        {
            var canDataFrame = new CanFrame();
            if (vectorCanfdFrame.tag == XL_CANFD_RX_EventTags.XL_CAN_EV_TAG_RX_ERROR)
            {
                canDataFrame.Id = -1;
            }
            else if (vectorCanfdFrame.tag == XL_CANFD_RX_EventTags.XL_CAN_EV_TAG_TX_OK)
            {
                canDataFrame.Properties = 1;

                var frameId = vectorCanfdFrame.tagData.canTxOkMsg.canId;
                if ((frameId & (uint)XL_MessageFlagsExtended.XL_CAN_EXT_MSG_ID) != 0)
                {
                    canDataFrame.IsExt = true;
                    frameId = frameId & 0x1FFFFFFF;
                }

                canDataFrame.Id = (int)frameId;

                var flags = vectorCanfdFrame.tagData.canTxOkMsg.msgFlags;
                canDataFrame.Rtr = flags.HasFlag(XL_CANFD_RX_MessageFlags.XL_CAN_RXMSG_FLAG_RTR);
                canDataFrame.IsCanFd = flags.HasFlag(XL_CANFD_RX_MessageFlags.XL_CAN_RXMSG_FLAG_EDL);
                canDataFrame.IsBrs = flags.HasFlag(XL_CANFD_RX_MessageFlags.XL_CAN_RXMSG_FLAG_BRS);
                canDataFrame.IsEsi = flags.HasFlag(XL_CANFD_RX_MessageFlags.XL_CAN_RXMSG_FLAG_ESI);

                var dataLength = GetRxDataLength(vectorCanfdFrame.tagData.canTxOkMsg.dlc);
                canDataFrame.Dlc = (byte)vectorCanfdFrame.tagData.canTxOkMsg.dlc;
                canDataFrame.Data = vectorCanfdFrame.tagData.canTxOkMsg.data.Length >= dataLength ?
                vectorCanfdFrame.tagData.canTxOkMsg.data.Take(dataLength).ToArray() : vectorCanfdFrame.tagData.canTxOkMsg.data;
            }
            else if (vectorCanfdFrame.tag == XL_CANFD_RX_EventTags.XL_CAN_EV_TAG_RX_OK)
            {
                canDataFrame.Properties = 0;

                var frameId = vectorCanfdFrame.tagData.canRxOkMsg.canId;
                if ((frameId & (uint)XL_MessageFlagsExtended.XL_CAN_EXT_MSG_ID) != 0)
                {
                    canDataFrame.IsExt = true;
                    frameId = frameId & 0x1FFFFFFF;
                }

                canDataFrame.Id = (int)frameId;
                var flags = vectorCanfdFrame.tagData.canRxOkMsg.msgFlags;
                canDataFrame.Rtr = flags.HasFlag(XL_CANFD_RX_MessageFlags.XL_CAN_RXMSG_FLAG_RTR);
                canDataFrame.IsCanFd = flags.HasFlag(XL_CANFD_RX_MessageFlags.XL_CAN_RXMSG_FLAG_EDL);
                canDataFrame.IsBrs = flags.HasFlag(XL_CANFD_RX_MessageFlags.XL_CAN_RXMSG_FLAG_BRS);
                canDataFrame.IsEsi = flags.HasFlag(XL_CANFD_RX_MessageFlags.XL_CAN_RXMSG_FLAG_ESI);

                var dataLength = GetRxDataLength(vectorCanfdFrame.tagData.canRxOkMsg.dlc);
                canDataFrame.Dlc = (byte)vectorCanfdFrame.tagData.canRxOkMsg.dlc;
                canDataFrame.Data = vectorCanfdFrame.tagData.canRxOkMsg.data.Length >= dataLength ?
                vectorCanfdFrame.tagData.canRxOkMsg.data.Take(dataLength).ToArray() : vectorCanfdFrame.tagData.canRxOkMsg.data;
            }

            if (xlSyncTime.HasValue)
            {
                canDataFrame.TimeUS = (ulong)Math.Max(((long)vectorCanfdFrame.timeStamp - (long)xlSyncTime.Value) / 1000, 0);
            }
            else
            {
                canDataFrame.TimeUS = vectorCanfdFrame.timeStamp;
            }

            canDataFrame.Channel = vectorCanfdFrame.channelIndex;
            return canDataFrame;
        }

        private static void ReceiveCanfdFrame(XLcanRxEvent rxMsg)
        {
            if (rxMsg.tagData.canRxOkMsg.totalBitCnt == 0
                && rxMsg.tagData.canTxOkMsg.totalBitCnt == 0
                && rxMsg.size == 0
                && rxMsg.tag != XL_CANFD_RX_EventTags.XL_CAN_EV_TAG_RX_ERROR)
            {
                return;
            }

            var dlc = GetRxDataLength(rxMsg.tagData.canRxOkMsg.dlc);
            if (!filterErrorFrame)
            {
                FilterCanfdErrorFrame(rxMsg);
                return;
            }
            if (rxMsg.tag == XL_CANFD_RX_EventTags.XL_CAN_EV_TAG_RX_ERROR)
            {
                if (ErrorCanfdFrameList.Count == 0)
                {
                    ErrorCanfdFrameList.Add(rxMsg);
                    FilterCanfdErrorFrame(ErrorCanfdFrameList[0]);
                }
                else if (ErrorCanfdFrameList.Count == 1)
                {
                    ErrorCanfdFrameList.Add(rxMsg);
                }
                else
                {
                    ErrorCanfdFrameList[1] = rxMsg;
                }
            }
            else
            {
                if (ErrorCanfdFrameList.Count == 2)
                {
                    FilterCanfdErrorFrame(ErrorCanfdFrameList[1]);
                }
                ErrorCanfdFrameList.Clear();
                FilterCanfdErrorFrame(rxMsg);
            }
        }

        private static int GetRxDataLength(XL_CANFD_DLC xlDlc)
        {
            int length = 0;
            switch (xlDlc)
            {
                case XL_CANFD_DLC.DLC_CAN_CANFD_0_BYTES:
                    length = 0;
                    break;
                case XL_CANFD_DLC.DLC_CAN_CANFD_1_BYTES:
                    length = 1;
                    break;
                case XL_CANFD_DLC.DLC_CAN_CANFD_2_BYTES:
                    length = 2;
                    break;
                case XL_CANFD_DLC.DLC_CAN_CANFD_3_BYTES:
                    length = 3;
                    break;
                case XL_CANFD_DLC.DLC_CAN_CANFD_4_BYTES:
                    length = 4;
                    break;
                case XL_CANFD_DLC.DLC_CAN_CANFD_5_BYTES:
                    length = 5;
                    break;
                case XL_CANFD_DLC.DLC_CAN_CANFD_6_BYTES:
                    length = 6;
                    break;
                case XL_CANFD_DLC.DLC_CAN_CANFD_7_BYTES:
                    length = 7;
                    break;
                case XL_CANFD_DLC.DLC_CAN_CANFD_8_BYTES:
                    length = 8;
                    break;
                case XL_CANFD_DLC.DLC_CANFD_12_BYTES:
                    length = 12;
                    break;
                case XL_CANFD_DLC.DLC_CANFD_16_BYTES:
                    length = 16;
                    break;
                case XL_CANFD_DLC.DLC_CANFD_20_BYTES:
                    length = 20;
                    break;
                case XL_CANFD_DLC.DLC_CANFD_24_BYTES:
                    length = 24;
                    break;
                case XL_CANFD_DLC.DLC_CANFD_32_BYTES:
                    length = 32;
                    break;
                case XL_CANFD_DLC.DLC_CANFD_48_BYTES:
                    length = 48;
                    break;
                case XL_CANFD_DLC.DLC_CANFD_64_BYTES:
                    length = 64;
                    break;
                default:
                    break;
            }
            return length;
        }

        private static void OperateVectorLog(XL_Status status, string msg)
        {
            if (status == XL_Status.XL_SUCCESS)
            {
                msg += "Sucess";
            }
            else
            {
                msg += "Failed";
            }
        }
    }
}
