using Alsi.App.Devices.Core;

namespace Alsi.Fuzz.Core.Models.TestPlans
{
    public class HardwareConfig
    {
        public CommunicationType CommunicationType { get; set; } = CommunicationType.Can;
        public CanConfig CanConfig { get; set; } = new CanConfig();
        public CanFdConfig CanFdConfig { get; set; } = new CanFdConfig();

        public string GetSelectedDeviceChannelName()
        {
            if (CommunicationType == CommunicationType.Can)
            {
                return CanConfig.DeviceChannelName;
            }
            else if (CommunicationType == CommunicationType.CanFd)
            {
                return CanFdConfig.DeviceChannelName;
            }

            return string.Empty;
        }

        public bool IsConfigured => !string.IsNullOrWhiteSpace(GetSelectedDeviceChannelName());
    }

    public class CanConfig
    {
        public string DeviceChannelName { get; set; } = string.Empty;
        public int DataBitrate { get; set; } = 500_000;
    }

    public class CanFdConfig
    {
        public string DeviceChannelName { get; set; } = string.Empty;
        public int ArbitrationBitrate { get; set; } = 500_000;
        public int DataBitrate { get; set; } = 2000_000;
    }
}
