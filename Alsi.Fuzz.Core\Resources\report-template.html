<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fuzz Report</title>
    <style>
        :root {
            /* 主题色 - 与element-variables.css一致 */
            --color-primary: #27408B;
            --color-primary-light: #3e55a1;
            --color-primary-lighter: #eef0ff;

            --color-success: #10864D;
            --color-warning: #E67E22;
            --color-danger: #C0392B;
            --color-info: #3498DB;

            --text-color-primary: #303133;
            --text-color-regular: #606266;
            --text-color-secondary: #909399;

            --border-color: #DCDFE6;
            --border-color-light: #E4E7ED;
            --border-color-lighter: #EBEEF5;

            --bg-color: #FFFFFF;
            --bg-color-page: #F5F7FA;
            --bg-color-light: #F5F7FA;
        }

        html,
        body {
            font-family: "Microsoft YaHei", "微软雅黑", Arial, sans-serif;
            margin: 0;
            color: var(--text-color-primary);
            background-color: var(--bg-color-page);
            height: 100%;
            overflow: hidden;
            /* 防止页面级滚动条 */
        }

        .container {
            margin: 0 auto;
            background-color: var(--bg-color);
            box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
            border-radius: 4px;
            padding: 20px;
            height: calc(100% - 40px);
            /* 减去padding */
            display: flex;
            flex-direction: column;
            overflow: hidden;
            /* 防止容器级滚动条 */
        }

        .header {
            background: var(--bg-color-light);
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
            border: 1px solid var(--border-color-lighter);
        }

        .summary {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-bottom: 20px;
        }

        .summary-card {
            background: var(--bg-color);
            border: 1px solid var(--border-color-light);
            border-radius: 4px;
            padding: 15px;
            flex: 1;
            min-width: 200px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
        }

        .stat-value {
            font-size: 24px;
            font-weight: bold;
        }

        .success {
            color: var(--color-success);
        }

        .failure {
            color: var(--color-danger);
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            border: 1px solid var(--border-color-lighter);
            font-size: 12px;
        }

        th,
        td {
            vertical-align: top;
            padding: 2px 4px;
            text-align: left;
            border-bottom: 1px solid var(--border-color-lighter);
            line-height: 1.5;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
            max-width: 35vw;
        }

        th {
            background-color: var(--bg-color-light);
            color: var(--text-color-regular);
            font-weight: 500;
        }

        tr:nth-child(even) {
            background-color: var(--bg-color-light);
        }

        tr:hover {
            background-color: var(--border-color-lighter);
        }

        .controls {
            margin-bottom: 15px;
        }

        .filters {
            display: flex;
            align-items: center;
            gap: 10px;
            flex-wrap: wrap;
        }

        .pagination {
            display: flex;
            gap: 5px;
            margin-top: 10px;
            align-items: center;
            flex-wrap: wrap;
        }

        .pagination button {
            padding: 6px 12px;
            cursor: pointer;
            border: 1px solid var(--border-color);
            background-color: var(--bg-color);
            color: var(--text-color-regular);
            border-radius: 4px;
            transition: all 0.3s;
        }

        .pagination button:hover {
            color: var(--color-primary);
            border-color: var(--color-primary-light);
        }

        .pagination button.active {
            background-color: var(--color-primary);
            color: white;
            border-color: var(--color-primary);
        }

        .pagination button:disabled {
            cursor: not-allowed;
            opacity: 0.5;
        }

        .filters select,
        .filters input {
            padding: 8px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            color: var(--text-color-regular);
            min-width: 20px;
        }

        .filters select:focus,
        .filters input:focus {
            border-color: var(--color-primary);
            outline: none;
        }

        .filters button {
            background-color: var(--color-primary);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .filters button:hover {
            background-color: var(--color-primary-light);
        }

        .hidden {
            display: none;
        }

        h3 {
            margin-top: 0;
            margin-bottom: 5px;
            color: var(--text-color-primary);
        }

        h1,
        h2 {
            margin-top: 0;
            margin-bottom: 10px;
            color: var(--text-color-primary);
        }

        /* 增加表格内状态标签样式 */
        .status-tag {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 12px;
            min-width: 40px;
            /* 设置最小宽度，使Passed和Failed宽度一致 */
            text-align: center;
            /* 居中显示 */
        }

        .status-passed {
            background-color: rgba(16, 134, 77, 0.1);
            color: var(--color-success);
        }

        .status-failed {
            background-color: rgba(192, 57, 43, 0.1);
            color: var(--color-danger);
        }

        .status-pending {
            background-color: rgba(230, 126, 34, 0.1);
            color: var(--color-warning);
        }

        /* 树形导航相关样式 */
        .main-content {
            display: flex;
            margin-top: 10px;
            border: 1px solid var(--border-color-lighter);
            border-radius: 4px;
            overflow: hidden;
            transition: all 0.3s ease;
            flex: 1;
            /* 填充剩余空间 */
            min-height: 0;
            /* 允许flex项收缩 */
        }

        .tree-nav {
            width: 25vw;
            /* 增加最小宽度 */
            border-right: 1px solid var(--border-color-lighter);
            background-color: var(--bg-color-light);
            overflow: auto;
            /* 允许滚动 */
            height: 100%;
            /* 填充父容器高度 */
            padding: 10px 0;
            transition: width 0.3s ease, opacity 0.3s ease;
        }

        .tree-nav.hidden {
            width: 0;
            padding: 0;
            opacity: 0;
            overflow: hidden;
        }

        .case-content {
            flex: 1;
            overflow: hidden;
            transition: all 0.3s ease;
            height: 100%;
            /* 填充父容器高度 */
            display: flex;
            flex-direction: column;
        }

        .tree-node {
            padding: 8px 15px;
            cursor: pointer;
            user-select: none;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .tree-node:hover {
            background-color: var(--border-color-lighter);
        }

        .tree-node.active {
            background-color: var(--color-primary-lighter);
            color: var(--color-primary);
        }

        .tree-node-left {
            display: flex;
            align-items: center;
            flex: 1;
            min-width: 0;
            /* 允许内容收缩 */
        }

        .tree-toggle {
            margin-right: 5px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 16px;
            height: 16px;
            flex-shrink: 0;
            /* 防止收缩 */
        }

        .tree-icon {
            display: inline-block;
            width: 0;
            height: 0;
            border-style: solid;
        }

        .tree-icon.collapsed {
            border-width: 5px 0 5px 8px;
            border-color: transparent transparent transparent var(--text-color-regular);
        }

        .tree-icon.expanded {
            border-width: 8px 5px 0 5px;
            border-color: var(--text-color-regular) transparent transparent transparent;
        }

        .tree-label {
            font-size: 14px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .tree-stats {
            display: flex;
            gap: 8px;
            font-size: 12px;
            align-items: center;
            flex-shrink: 0;
            /* 防止收缩 */
        }

        .stat-icon {
            margin-right: 2px;
            font-size: 10px;
        }

        .stat-total {
            color: var(--color-primary);
        }

        .stat-passed {
            color: var(--color-success);
        }

        .stat-failed {
            color: var(--color-danger);
        }

        .stat-pending {
            color: var(--color-warning);
        }

        .tree-children {
            margin-left: 20px;
        }

        .group-count,
        .success-count,
        .failure-count {
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 12px;
        }

        .group-count {
            background-color: rgba(64, 158, 255, 0.1);
            color: var(--color-primary);
        }

        .success-count {
            background-color: rgba(16, 134, 77, 0.1);
            color: var(--color-success);
        }

        .failure-count {
            background-color: rgba(192, 57, 43, 0.1);
            color: var(--color-danger);
        }

        /* 虚拟滚动相关样式 */
        .virtual-scroll-container {
            flex: 1;
            /* 填充剩余空间 */
            overflow: auto;
            position: relative;
            min-height: 0;
            /* 允许flex项收缩 */
        }

        .virtual-scroll-wrapper {
            position: relative;
        }

        .virtual-scroll-content {
            position: absolute;
            width: 100%;
            left: 0;
        }

        .virtual-scroll-item {
            display: flex;
            border-bottom: 1px solid var(--border-color-lighter);
            background-color: var(--bg-color);
        }

        .virtual-scroll-item:hover {
            background-color: var(--border-color-lighter);
        }

        .virtual-scroll-header {
            display: flex;
            background-color: var(--bg-color-light);
            position: sticky;
            top: 0;
            z-index: 1;
            border-bottom: 1px solid var(--border-color-lighter);
        }

        .header-cell,
        .item-cell {
            padding: 8px 12px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            font-size: 14px;
        }

        .header-cell {
            font-weight: 500;
            color: var(--text-color-regular);
        }

        .header-cell:nth-child(1),
        .item-cell:nth-child(1) {
            width: 5%;
        }

        .header-cell:nth-child(2),
        .item-cell:nth-child(2) {
            width: 15%;
        }

        .header-cell:nth-child(3),
        .item-cell:nth-child(3) {
            width: 15%;
        }

        .header-cell:nth-child(4),
        .item-cell:nth-child(4) {
            width: 35%;
        }

        .header-cell:nth-child(5),
        .item-cell:nth-child(5) {
            width: 10%;
        }

        .header-cell:nth-child(6),
        .item-cell:nth-child(6) {
            width: 20%;
        }

        /* 仪表板样式 */
        .dashboard {
            display: flex;
            flex-wrap: wrap;
            margin-bottom: 20px;
            border: 1px solid var(--border-color-lighter);
            border-radius: 4px;
            overflow: hidden;
        }

        .dashboard-panel {
            flex: 1;
            min-width: 300px;
            padding: 15px 20px;
            background-color: var(--bg-color);
            border-right: 1px solid var(--border-color-lighter);
        }

        .dashboard-panel:last-child {
            border-right: none;
        }

        .dashboard-panel h3 {
            margin: 0 0 15px 0;
            font-size: 16px;
            color: var(--text-color-primary);
            font-weight: 500;
        }

        .info-list {
            margin-bottom: 15px;
        }

        .info-item {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .info-label {
            color: var(--text-color-secondary);
            margin-right: 8px;
            min-width: 80px;
        }

        .info-value {
            color: var(--text-color-primary);
            font-weight: 500;
        }

        /* 统计卡片样式 */
        .stats-cards {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin: 15px 0;
        }

        .stat-card {
            flex: 1;
            min-width: 100px;
            background-color: var(--bg-color);
            border-radius: 6px;
            padding: 15px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
            border: 1px solid var(--border-color-lighter);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .stat-card-value {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .stat-card-label {
            font-size: 12px;
            color: var(--text-color-secondary);
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .passed-card .stat-card-value {
            color: var(--color-success);
        }

        .failed-card .stat-card-value {
            color: var(--color-danger);
        }

        .total-card .stat-card-value {
            color: var(--color-primary);
        }

        .rate-card .stat-card-value {
            color: var(--color-success);
        }

        .plan-card .stat-card-value {
            color: var(--color-primary);
            font-size: 18px;
            /* 稍小一点，因为可能是长文本 */
            word-break: break-word;
            /* 允许长文本换行 */
        }

        .execution-card .stat-card-value {
            color: var(--color-info);
            font-size: 16px;
            /* 稍小一点，因为可能是长文本 */
            margin: 6px 0;
        }

        .duration-card .stat-card-value {
            color: var(--color-warning);
        }

        .stat-item {
            display: flex;
            align-items: center;
            margin-right: 10px;
        }

        .stat-label {
            margin-right: 5px;
            color: var(--text-color-secondary);
            font-size: 14px;
        }

        /* 控制栏样式 */
        .controls {
            margin-bottom: 0;
        }

        .filters {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: var(--bg-color);
            border: 1px solid var(--border-color-lighter);
            border-radius: 4px;
            padding: 10px 15px;
        }

        .filter-left {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .filter-right {
            display: flex;
            align-items: center;
        }

        .checkbox-label {
            display: flex;
            align-items: center;
            cursor: pointer;
            user-select: none;
            font-size: 14px;
            color: var(--text-color-regular);
        }

        .checkbox-label input[type="checkbox"] {
            margin-right: 5px;
        }

        /* 筛选复选框最小宽度和颜色 */
        .filter-checkbox {
            min-width: 30px;
            /* 设置最小宽度，使All/Passed/Failed宽度一致 */
        }

        .filter-all {
            color: var(--color-primary);
            /* 蓝色 */
        }

        .filter-passed {
            color: var(--color-success);
            /* 绿色 */
        }

        .filter-failed {
            color: var(--color-danger);
            /* 红色 */
        }

        .filter-pending {
            color: var(--color-warning);
            /* 橙色 */
        }

        .vertical-divider {
            height: 20px;
            width: 1px;
            background-color: var(--border-color);
            margin: 0 5px;
        }

        .search-container {
            display: flex;
            align-items: center;
        }

        .search-container input {
            padding: 5px 10px;
            border: 1px solid var(--border-color);
            border-radius: 4px 0 0 4px;
            width: 250px;
        }

        .search-container button {
            padding: 5px 15px;
            background-color: var(--color-primary);
            color: white;
            border: none;
            border-radius: 0 4px 4px 0;
            cursor: pointer;
            height: 100%;
        }

        .search-container button:hover {
            background-color: var(--color-primary-light);
        }

        /* 空消息样式 */
        .empty-message {
            padding: 20px;
            text-align: center;
            color: var(--text-color-secondary);
            font-size: 16px;
            background-color: var(--bg-color-light);
            border-radius: 4px;
            /* 移除 margin */
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .dashboard {
                flex-direction: column;
            }

            .dashboard-panel {
                border-right: none;
                border-bottom: 1px solid var(--border-color-lighter);
            }

            .dashboard-panel:last-child {
                border-bottom: none;
            }

            .stats-cards {
                flex-direction: column;
            }

            .stat-card {
                width: 100%;
            }

            .tree-stats {
                gap: 5px;
            }

            .filters {
                flex-direction: column;
                align-items: flex-start;
            }

            .filter-left,
            .filter-right {
                width: 100%;
                margin-bottom: 10px;
            }

            .search-container {
                width: 100%;
            }

            .search-container input {
                width: calc(100% - 80px);
            }
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="dashboard">
            <div class="dashboard-panel">
                <h3>Information</h3>
                <div class="stats-cards">
                    <div class="stat-card plan-card">
                        <div class="stat-card-value" id="test-plan-name"></div>
                        <div class="stat-card-label">TEST PLAN</div>
                    </div>
                    <div class="stat-card execution-card">
                        <div class="stat-card-value" id="begin-time"></div>
                        <div class="stat-card-label">START TIME</div>
                    </div>
                    <div class="stat-card duration-card">
                        <div class="stat-card-value" id="duration"></div>
                        <div class="stat-card-label">DURATION</div>
                    </div>
                </div>
            </div>
            <div class="dashboard-panel">
                <h3>Statistics</h3>
                <div class="stats-cards">
                    <div class="stat-card passed-card">
                        <div class="stat-card-value" id="success-count"></div>
                        <div class="stat-card-label">PASSED</div>
                    </div>
                    <div class="stat-card failed-card">
                        <div class="stat-card-value" id="failure-count"></div>
                        <div class="stat-card-label">FAILED</div>
                    </div>
                    <div class="stat-card total-card">
                        <div class="stat-card-value" id="total-count"></div>
                        <div class="stat-card-label">TOTAL</div>
                    </div>
                    <div class="stat-card rate-card">
                        <div class="stat-card-value" id="success-rate"></div>
                        <div class="stat-card-label">SUCCESS RATE</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="controls">
            <div class="filters">
                <div class="filter-left">
                    <label class="checkbox-label">
                        <input type="checkbox" id="show-tree-checkbox" checked onchange="toggleTreePanel()">
                        <span>Group Panel</span>
                    </label>
                    <div class="vertical-divider"></div>
                    <label class="checkbox-label">
                        <input type="checkbox" id="filter-all" checked onchange="toggleFilter('all')">
                        <span class="filter-checkbox filter-all">All</span>
                    </label>
                    <label class="checkbox-label">
                        <input type="checkbox" id="filter-passed" checked onchange="toggleFilter('passed')">
                        <span class="filter-checkbox filter-passed">Passed</span>
                    </label>
                    <label class="checkbox-label">
                        <input type="checkbox" id="filter-failed" checked onchange="toggleFilter('failed')">
                        <span class="filter-checkbox filter-failed">Failed</span>
                    </label>
                    <label class="checkbox-label">
                        <input type="checkbox" id="filter-pending" checked onchange="toggleFilter('pending')">
                        <span class="filter-checkbox filter-pending">Pending</span>
                    </label>
                </div>
                <div class="filter-right">
                    <div class="search-container">
                        <input type="text" id="search-input" placeholder="Search Case ID, name or parameter..."
                            onkeypress="handleSearchKeyPress(event)">
                        <button onclick="applyFilters()">Filter</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="main-content">
            <div class="tree-nav" id="tree-nav">
                <!-- Tree navigation will be rendered here -->
            </div>
            <div class="case-content" id="case-content">
                <!-- Case content will be rendered here -->
            </div>
        </div>
    </div>

    <!-- Mock data for direct HTML viewing -->
    <script>
        // This is mock data that will be replaced by server during report generation
        // Test result data - will be replaced by server with actual data
        var testResult = {
            "Id": "9f7b59a3-6d21-473a-8501-e4c4a6feedb9",
            "TestType": "Case",
            "TestPlanName": "Demo Test Plan",
            "ResultFolderName": "Test Run 2023-10-30",
            "TotalCount": 120,
            "SuccessCount": 95,
            "FailureCount": 25,
            "Begin": "2023-10-30T09:00:00",
            "End": "2023-10-30T10:15:30",
            "CreationTime": "2023-10-30T08:59:00",
            "SuccessRate": 79.17
        };

        // Case results data - will be replaced by server with actual data
        // Generate 500,000 test cases for performance testing
        var caseResults = [];

        // Function to generate remaining test cases asynchronously
        function generateRemainingTestCases(startIndex) {
            const CHUNK_SIZE = 10000; // Generate 10,000 items at a time
            const endIndex = Math.min(startIndex + CHUNK_SIZE, 500000);

            for (let i = startIndex + 1; i <= endIndex; i++) {
                // Select random sequence name
                const sequenceName = sequenceNames[Math.floor(Math.random() * sequenceNames.length)];

                // Select random prefix
                const prefix = prefixes[Math.floor(Math.random() * prefixes.length)];

                // Generate random hex data
                const hexData = Array(16).fill(0).map(() =>
                    Math.floor(Math.random() * 255).toString(16).padStart(2, '0')).join('');

                // Generate random ID
                const id = Math.floor(Math.random() * 4095) + 1;

                // Determine state (70% success, 30% failure)
                const state = Math.random() < 0.7 ? 3 : 4;

                // Create test case
                caseResults.push({
                    "Id": i,
                    "TestResultId": "9f7b59a3-6d21-473a-8501-e4c4a6feedb9",
                    "SequenceId": `seq-${i.toString().padStart(6, '0')}`,
                    "Name": `${prefix}-TestCase${i}`,
                    "SequenceName": sequenceName,
                    "Parameter": `id=0x${id.toString(16).padStart(3, '0')};dlc=8;data=hex:${hexData}`,
                    "State": state,
                    "Begin": `2023-10-30T${(9 + Math.floor(i / 100000)).toString().padStart(2, '0')}:${Math.floor((i % 100000) / 1666).toString().padStart(2, '0')}:${Math.floor((i % 1666) / 27).toString().padStart(2, '0')}`,
                    "End": `2023-10-30T${(9 + Math.floor(i / 100000)).toString().padStart(2, '0')}:${Math.floor((i % 100000) / 1666).toString().padStart(2, '0')}:${(Math.floor((i % 1666) / 27) + 3).toString().padStart(2, '0')}`,
                    "Detail": state === 3 ? "Test executed successfully" : "Error: Unexpected response from ECU"
                });
            }

            console.log(`Generated ${endIndex} test cases`);

            // If not done, schedule the next chunk
            if (endIndex < 500000) {
                setTimeout(() => {
                    generateRemainingTestCases(endIndex);
                }, 0);
            } else {
                console.log("Finished generating all 500,000 test cases");
                // Update test result counts
                testResult.TotalCount = caseResults.length;
                testResult.SuccessCount = caseResults.filter(item => item.State === 3).length;
                testResult.FailureCount = caseResults.filter(item => item.State === 4).length;
                testResult.SuccessRate = (testResult.SuccessCount / testResult.TotalCount * 100).toFixed(2);

                // Render the UI once data is ready
                renderSummary();
                applyFilters();
            }
        }

        // TEST-RESULT-JSON-TEMPLATE
        // TEST-CASE-JSON-TEMPLATE

        // Define sequence names for variety
        const sequenceNames = [
            "DiagnosticSessionControl",
            "ECUReset",
            "SecurityAccess",
            "CommunicationControl",
            "ReadDataByIdentifier",
            "WriteDataByIdentifier",
            "ClearDiagnosticInformation",
            "ReadDTCInformation",
            "InputOutputControlByIdentifier",
            "RoutineControl",
            "RequestDownload",
            "RequestUpload",
            "TransferData",
            "RequestTransferExit",
            "AccessTimingParameter",
            "SecuredDataTransmission",
            "ControlDTCSetting",
            "ResponseOnEvent",
            "LinkControl",
            "can-frames"
        ];

        // Define test case name prefixes
        const prefixes = ["G100", "G200", "G300", "G400", "G500"];

        // Generate 500,000 test cases
        if (!caseResults.length) {
            for (let i = 1; i <= 500000; i++) {
                // Select random sequence name
                const sequenceName = sequenceNames[Math.floor(Math.random() * sequenceNames.length)];

                // Select random prefix
                const prefix = prefixes[Math.floor(Math.random() * prefixes.length)];

                // Generate random hex data
                const hexData = Array(16).fill(0).map(() =>
                    Math.floor(Math.random() * 255).toString(16).padStart(2, '0')).join('');

                // Generate random ID
                const id = Math.floor(Math.random() * 4095) + 1;

                // Determine state (70% success, 30% failure)
                const state = Math.random() < 0.7 ? 3 : 4;

                // Create test case
                caseResults.push({
                    "Id": i,
                    "TestResultId": "9f7b59a3-6d21-473a-8501-e4c4a6feedb9",
                    "SequenceId": `seq-${i.toString().padStart(6, '0')}`,
                    "Name": `${prefix}-TestCase${i}`,
                    "SequenceName": sequenceName,
                    "Parameter": `id=0x${id.toString(16).padStart(3, '0')};dlc=8;data=hex:${hexData}`,
                    "State": state,
                    "Begin": `2023-10-30T${(9 + Math.floor(i / 100000)).toString().padStart(2, '0')}:${Math.floor((i % 100000) / 1666).toString().padStart(2, '0')}:${Math.floor((i % 1666) / 27).toString().padStart(2, '0')}`,
                    "End": `2023-10-30T${(9 + Math.floor(i / 100000)).toString().padStart(2, '0')}:${Math.floor((i % 100000) / 1666).toString().padStart(2, '0')}:${(Math.floor((i % 1666) / 27) + 3).toString().padStart(2, '0')}`,
                    "Detail": state === 3 ? "Test executed successfully" : "Error: Unexpected response from ECU"
                });

                // To avoid browser freezing, generate data in chunks
                if (i === 1000) {
                    console.log("Generated first 1,000 test cases");
                    // Continue generating the rest asynchronously
                    setTimeout(() => {
                        generateRemainingTestCases(1000);
                    }, 0);

                    // Break the loop after the first 1000 items
                    break;
                }
            }
        }
    </script>

    <!-- Main script for rendering the report -->
    <script>
        // Constants
        const ITEM_HEIGHT = 40; // Estimated height of each row in pixels
        const BUFFER_SIZE = 10; // Number of extra items to render above and below viewport

        // State variables
        let filteredData = [...caseResults];
        let groupedData = {}; // Will hold grouped data
        let treeData = {}; // Will hold tree structure
        let selectedNodeId = 'all'; // Currently selected tree node

        // Initialize page when DOM content is loaded
        document.addEventListener('DOMContentLoaded', function () {
            renderSummary();
            applyFilters();
        });

        // Render summary information
        function renderSummary() {
            document.getElementById('test-plan-name').textContent = testResult.TestPlanName || 'Unnamed Test Plan';

            // Format execution time
            const beginTime = formatDateTime(testResult.Begin) || 'Not started';
            const endTime = formatDateTime(testResult.End) || 'Not finished';
            document.getElementById('begin-time').textContent = beginTime;

            // Calculate duration in simplified format (e.g., 1h 15m 30s)
            let duration = 'Not completed';
            if (testResult.Begin && testResult.End) {
                const beginDate = new Date(testResult.Begin);
                const endDate = new Date(testResult.End);
                const durationMs = endDate.getTime() - beginDate.getTime();

                const hours = Math.floor(durationMs / 3600000);
                const minutes = Math.floor((durationMs % 3600000) / 60000);
                const seconds = Math.floor((durationMs % 60000) / 1000);

                duration = '';
                if (hours > 0) {
                    duration += `${hours}h `;
                }
                if (minutes > 0 || hours > 0) {
                    duration += `${minutes}m `;
                }
                if (seconds > 0 || minutes > 0 || hours > 0) {
                    duration += `${seconds}s`;
                }
                if (duration === '') {
                    duration = '< 1s';
                }
            }
            document.getElementById('duration').textContent = duration;

            // Set counts and success rate
            document.getElementById('total-count').textContent = testResult.TotalCount;
            document.getElementById('success-count').textContent = testResult.SuccessCount;
            document.getElementById('failure-count').textContent = testResult.FailureCount;
            document.getElementById('success-rate').textContent = `${testResult.SuccessRate}%`;

            // Update progress bar to show success rate
            const progressFill = document.getElementById('progress-fill');
            if (progressFill) {
                progressFill.style.width = `${testResult.SuccessRate}%`;
                // Add tooltip to show exact percentage
                progressFill.title = `Success Rate: ${testResult.SuccessRate}%`;
            }
        }

        // Handle search input key press
        function handleSearchKeyPress(event) {
            if (event.key === 'Enter') {
                applyFilters();
            }
        }

        // Toggle tree panel visibility
        function toggleTreePanel() {
            const treeNav = document.getElementById('tree-nav');
            const showTreeCheckbox = document.getElementById('show-tree-checkbox');

            if (showTreeCheckbox.checked) {
                // Show tree panel
                treeNav.classList.remove('hidden');
            } else {
                // Hide tree panel
                treeNav.classList.add('hidden');
            }
        }

        // Toggle state filter
        function toggleFilter(filterType) {
            const allCheckbox = document.getElementById('filter-all');
            const passedCheckbox = document.getElementById('filter-passed');
            const failedCheckbox = document.getElementById('filter-failed');
            const pendingCheckbox = document.getElementById('filter-pending');

            // Handle "All" checkbox logic
            if (filterType === 'all' && allCheckbox.checked) {
                passedCheckbox.checked = true;
                failedCheckbox.checked = true;
                pendingCheckbox.checked = true;
            } else if (filterType === 'all' && !allCheckbox.checked) {
                passedCheckbox.checked = false;
                failedCheckbox.checked = false;
                pendingCheckbox.checked = false;
            }

            // Update "All" checkbox based on other checkboxes
            if (filterType !== 'all') {
                allCheckbox.checked = passedCheckbox.checked && failedCheckbox.checked && pendingCheckbox.checked;
            }

            // Apply filters immediately
            applyFilters();
        }

        // Apply filters to the test cases
        function applyFilters() {
            // Get filter state from checkboxes
            const passedChecked = document.getElementById('filter-passed').checked;
            const failedChecked = document.getElementById('filter-failed').checked;
            const pendingChecked = document.getElementById('filter-pending').checked;
            const searchText = document.getElementById('search-input').value.toLowerCase();

            filteredData = caseResults.filter(item => {
                // Check if item state matches selected filters
                const matchesState =
                    (item.State === 3 && passedChecked) || // Passed
                    (item.State === 4 && failedChecked) || // Failed
                    (item.State === 1 && pendingChecked);  // Pending

                // Check if item matches search text
                const matchesSearch =
                    (item.Id && item.Id.toString().includes(searchText)) ||
                    (item.Name && item.Name.toLowerCase().includes(searchText)) ||
                    (item.SequenceName && item.SequenceName.toLowerCase().includes(searchText)) ||
                    (item.Parameter && item.Parameter.toLowerCase().includes(searchText));

                return matchesState && (searchText === '' || matchesSearch);
            });

            // Group data by name prefix
            groupedData = groupDataByNamePrefix(filteredData);

            // Build tree structure
            buildTreeData();

            // Render tree and content
            renderTree();
            renderCaseContent();
        }

        // Group data by name prefix (e.g., G200)
        function groupDataByNamePrefix(data) {
            const groups = {};

            data.forEach(item => {
                // Extract prefix from name (e.g., "G200-TestCase123" -> "G200")
                const nameParts = (item.Name || '').split('-');
                const prefix = nameParts.length > 0 ? nameParts[0] : 'Other';

                if (!groups[prefix]) {
                    groups[prefix] = {
                        name: prefix,
                        expanded: false, // Default collapsed
                        items: [],
                        count: 0,
                        successCount: 0,
                        failureCount: 0,
                        pendingCount: 0
                    };
                }

                groups[prefix].items.push(item);
                groups[prefix].count++;

                if (item.State === 3) { // Success
                    groups[prefix].successCount++;
                } else if (item.State === 4) { // Failure
                    groups[prefix].failureCount++;
                } else if (item.State === 1) { // Pending
                    groups[prefix].pendingCount++;
                }
            });

            // Sort items within each group by case ID
            Object.values(groups).forEach(group => {
                group.items.sort((a, b) => a.Id - b.Id);
            });

            return groups;
        }

        // Build tree data structure
        function buildTreeData() {
            // Reset tree data
            treeData = {
                all: {
                    id: 'all',
                    label: 'All Cases',
                    expanded: true,
                    parent: null,
                    children: [],
                    items: filteredData,
                    count: filteredData.length,
                    successCount: filteredData.filter(item => item.State === 3).length,
                    failureCount: filteredData.filter(item => item.State === 4).length,
                    pendingCount: filteredData.filter(item => item.State === 1).length
                }
            };

            // Add group nodes
            const groupKeys = Object.keys(groupedData).sort();
            groupKeys.forEach(groupKey => {
                const group = groupedData[groupKey];
                const nodeId = `group-${groupKey}`;

                treeData[nodeId] = {
                    id: nodeId,
                    label: group.name,
                    expanded: false,
                    parent: 'all',
                    children: [],
                    items: group.items,
                    count: group.count,
                    successCount: group.successCount,
                    failureCount: group.failureCount
                };

                treeData.all.children.push(nodeId);
            });

            // If no node is selected or the selected node doesn't exist anymore, select 'all'
            if (!selectedNodeId || !treeData[selectedNodeId]) {
                selectedNodeId = 'all';
            }
        }

        // Render tree navigation
        function renderTree() {
            const treeNav = document.getElementById('tree-nav');
            treeNav.innerHTML = '';

            // Render root node
            const rootNode = treeData.all;
            const rootElement = createTreeNodeElement(rootNode);
            treeNav.appendChild(rootElement);

            // Render children if expanded
            if (rootNode.expanded) {
                const childrenContainer = document.createElement('div');
                childrenContainer.className = 'tree-children';

                rootNode.children.forEach(childId => {
                    const childNode = treeData[childId];
                    const childElement = createTreeNodeElement(childNode);
                    childrenContainer.appendChild(childElement);
                });

                treeNav.appendChild(childrenContainer);
            }
        }

        // Create tree node element
        function createTreeNodeElement(node) {
            const nodeElement = document.createElement('div');
            nodeElement.className = `tree-node ${node.id === selectedNodeId ? 'active' : ''}`;
            nodeElement.dataset.nodeId = node.id;

            // Create toggle icon if has children
            let toggleHtml = '';
            if (node.children && node.children.length > 0) {
                toggleHtml = `
                    <div class="tree-toggle">
                        <i class="tree-icon ${node.expanded ? 'expanded' : 'collapsed'}"></i>
                    </div>
                `;
            } else {
                toggleHtml = '<div class="tree-toggle" style="width: 16px;"></div>';
            }

            // Create stats HTML with icons
            const statsHtml = `
                <div class="tree-stats">
                    <span class="stat-total">
                        <span class="stat-icon">Σ</span>${node.count}
                    </span>
                    <span class="stat-passed">
                        <span class="stat-icon">✓</span>${node.successCount}
                    </span>
                    <span class="stat-failed">
                        <span class="stat-icon">✗</span>${node.failureCount}
                    </span>
                </div>
            `;

            // Set node HTML with left and right sections
            nodeElement.innerHTML = `
                <div class="tree-node-left">
                    ${toggleHtml}
                    <div class="tree-label" title="${node.label}">${node.label}</div>
                </div>
                ${statsHtml}
            `;

            // Add click event for node selection
            nodeElement.addEventListener('click', (e) => {
                // If click on toggle, toggle expansion
                if (e.target.closest('.tree-toggle')) {
                    toggleTreeNode(node.id);
                } else {
                    // Otherwise select node
                    selectTreeNode(node.id);
                }
            });

            return nodeElement;
        }

        // Toggle tree node expansion
        function toggleTreeNode(nodeId) {
            if (treeData[nodeId]) {
                treeData[nodeId].expanded = !treeData[nodeId].expanded;
                renderTree();
            }
        }

        // Select tree node
        function selectTreeNode(nodeId) {
            selectedNodeId = nodeId;
            renderTree();
            renderCaseContent();
        }

        // Render case content based on selected node
        function renderCaseContent() {
            const container = document.getElementById('case-content');
            container.innerHTML = '';

            if (!selectedNodeId || !treeData[selectedNodeId]) {
                container.innerHTML = '<div class="empty-message">No node selected</div>';
                return;
            }

            const node = treeData[selectedNodeId];

            if (node.items.length === 0) {
                container.innerHTML = '<div class="empty-message">No matching records found</div>';
                return;
            }

            // Create virtual scroll container
            const scrollContainer = document.createElement('div');
            scrollContainer.className = 'virtual-scroll-container';

            // Create header
            const header = document.createElement('div');
            header.className = 'virtual-scroll-header';
            header.innerHTML = `
                <div class="header-cell">ID</div>
                <div class="header-cell">Name</div>
                <div class="header-cell">Sequence</div>
                <div class="header-cell">Parameter</div>
                <div class="header-cell">Status</div>
                <div class="header-cell">Detail</div>
            `;

            // Create content wrapper for virtual scrolling
            const contentWrapper = document.createElement('div');
            contentWrapper.className = 'virtual-scroll-wrapper';
            contentWrapper.style.height = `${node.items.length * ITEM_HEIGHT}px`;

            // Create content container
            const contentContainer = document.createElement('div');
            contentContainer.className = 'virtual-scroll-content';

            // Add initial items (will be updated on scroll)
            renderVirtualItems(contentContainer, node.items, 0, Math.min(50, node.items.length));

            // Add scroll event for virtual scrolling
            scrollContainer.addEventListener('scroll', () => {
                const scrollTop = scrollContainer.scrollTop;
                const containerHeight = scrollContainer.clientHeight;

                // Calculate visible range
                const startIndex = Math.max(0, Math.floor(scrollTop / ITEM_HEIGHT) - BUFFER_SIZE);
                const endIndex = Math.min(
                    node.items.length,
                    Math.ceil((scrollTop + containerHeight) / ITEM_HEIGHT) + BUFFER_SIZE
                );

                // Update visible items
                renderVirtualItems(contentContainer, node.items, startIndex, endIndex);
            });

            // Assemble DOM
            contentWrapper.appendChild(contentContainer);
            scrollContainer.appendChild(header);
            scrollContainer.appendChild(contentWrapper);
            container.appendChild(scrollContainer);
        }

        // Render virtual items
        function renderVirtualItems(container, items, startIndex, endIndex) {
            // Clear existing content
            container.innerHTML = '';

            // Position container
            container.style.transform = `translateY(${startIndex * ITEM_HEIGHT}px)`;

            // Render visible items
            for (let i = startIndex; i < endIndex; i++) {
                const item = items[i];
                const row = document.createElement('div');
                row.className = 'virtual-scroll-item';
                row.style.height = `${ITEM_HEIGHT}px`;

                row.innerHTML = `
                    <div class="item-cell">${item.Id}</div>
                    <div class="item-cell" title="${item.Name || ''}">${item.Name || ''}</div>
                    <div class="item-cell" title="${item.SequenceName || ''}">${item.SequenceName || ''}</div>
                    <div class="item-cell" title="${item.Parameter || ''}">${item.Parameter || ''}</div>
                    <div class="item-cell"><span class="status-tag ${item.State === 3 ? 'status-passed' : (item.State === 4 ? 'status-failed' : 'status-pending')}">${item.State === 3 ? 'Passed' : (item.State === 4 ? 'Failed' : 'Pending')}</span></div>
                    <div class="item-cell" title="${item.Detail || ''}">${item.Detail || ''}</div>
                `;

                container.appendChild(row);
            }
        }

        // Toggle tree panel visibility
        function toggleTreePanel() {
            const treeNav = document.getElementById('tree-nav');
            const toggleBtn = document.getElementById('toggle-tree-btn');
            const isHidden = treeNav.classList.contains('hidden');

            if (isHidden) {
                // Show tree panel
                treeNav.classList.remove('hidden');
                toggleBtn.textContent = 'Hide Tree';
            } else {
                // Hide tree panel
                treeNav.classList.add('hidden');
                toggleBtn.textContent = 'Show Tree';
            }
        }

        // Format date time for display
        function formatDateTime(dateStr) {
            if (!dateStr) return '';
            const date = new Date(dateStr);
            return date.toLocaleString();
        }
    </script>
</body>

</html>