using Alsi.Fuzz.Core.Models.TestPlans;
using Alsi.Fuzz.Core.Service.Results;
using System.Linq;

namespace Alsi.Fuzz.Core.Contracts.Tester
{
    public class ExecutionRequest
    {
        public TestResult TestResult { get; set; }
        public HardwareConfig HardwareConfig { get; set; }
        public CaseConfig CaseConfig { get; set; }

        /// <summary>
        /// 这里需要用 XML 序列化，JSON 难以支撑多态
        /// </summary>
        public string SequencePackageXml { get; set; }
    }

    public class PagedQuery
    {
        public int PageNumber { get; set; }
        public int PageSize { get; set; }
    }

    public class PagedResult<T>
    {
        public PagedResult()
        {
        }

        public PagedResult(T[] items, int total, int pageNumber, int pageSize)
        {
            Items = items;
            Total = total;
            PageSize = pageSize;
            PageNumber = pageNumber;
        }

        public T[] Items { get; set; }
        public int Total { get; set; }
        public int PageSize { get; set; }
        public int PageNumber { get; set; }

        public static PagedResult<T> Build(T[] filteredItems, int pageNumber, int pageSize)
        {
            // 计算过滤后的数据总量
            int totalFilteredCount = filteredItems.Length;

            // 计算应该返回的记录  
            var items = filteredItems
                .Skip(pageSize * (pageNumber - 1))
                .Take(pageSize)
                .ToArray();

            // 如果当前页没有数据且总数据量大于0，返回第一页的数据  
            if (items.Length == 0 && totalFilteredCount > 0)
            {
                // 重置为第一页  
                pageNumber = 1;

                // 重新计算第一页的数据  
                items = filteredItems
                    .Skip(pageSize * (pageNumber - 1))
                    .Take(pageSize)
                    .ToArray();
            }

            return new PagedResult<T>(items, totalFilteredCount, pageSize, pageNumber);
        }
    }

    public class TesterSnapshotResponse
    {
        public ExecutionState ProcessState { get; set; }
        public string CurrentOperation { get; set; }
        public TestResult TestResult { get; set; }
        public PagedResult<CaseResult> PagedCaseResult { get; set; }
    }

    public class TesterSnapshot
    {
        public ExecutionState ProcessState { get; set; }
        public string CurrentOperation { get; set; }
        public TestResult TestResult { get; set; }
        public CaseResult[] CaseResults { get; set; }

        public void UpdateCaseStatistics()
        {
            TestResult.TotalCount = CaseResults.Length;
            TestResult.SuccessCount = CaseResults.Count(x => x.State == ExecutionState.Success);
            TestResult.FailureCount = CaseResults.Count(x => x.State == ExecutionState.Failure);
        }

        public bool IsCompleted()
        {
            return ProcessState == ExecutionState.Success || ProcessState == ExecutionState.Failure;
        }
    }
}
