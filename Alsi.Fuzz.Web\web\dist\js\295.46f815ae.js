"use strict";(self["webpackChunkfuzz_web"]=self["webpackChunkfuzz_web"]||[]).push([[295],{2971:function(e,t,s){s.d(t,{A:function(){return r}});var a=s(6768),l=s(4232),n=s(1021),u=(0,a.pM)({__name:"CaseStateTag",props:{state:{}},setup(e){const t=e,s=(0,a.EW)((()=>{switch(t.state){case n.si.Success:return"success";case n.si.Running:return"warning";case n.si.Failure:return"danger";case n.si.Pending:default:return"info"}})),u=e=>{switch(e){case n.si.Running:return"Running";case n.si.Pending:return"Not Run";case n.si.Success:return"Passed";case n.si.Failure:return"Failed";default:return"Unknown"}},i=(0,a.EW)((()=>u(t.state)));return(e,t)=>{const n=(0,a.g2)("el-tag");return(0,a.uX)(),(0,a.Wv)(n,{type:s.value,size:"small",style:{"min-width":"60px"}},{default:(0,a.k6)((()=>[(0,a.eW)((0,l.v_)(i.value),1)])),_:1},8,["type"])}}});const i=u;var r=i},4295:function(e,t,s){s.r(t),s.d(t,{default:function(){return h}});var a=s(6768),l=s(4232),n=s(144),u=s(1219),i=s(883),r=s(1420),c=s(2971),o=s(6741),d=s(7823);const v={class:"interoperation-container"},p={class:"toolbar"},k={class:"action-buttons"},m={class:"cases-list"},f={class:"case-header"},g={class:"case-sequence"},b={key:0,class:"case-detail"},R={class:"detail-content"},L={class:"case-actions"},_={class:"case-status"};var y=(0,a.pM)({__name:"Interoperation",setup(e){(0,a.pM)({name:"InteroperationView"});const t=(0,n.KR)(!0),s=(0,n.KR)(!1),y=(0,n.KR)(!1),C=(0,n.KR)(!1),F=(0,n.KR)({processState:r.si.Pending,currentOperation:"",testResult:{id:"",resultFolderName:"",testType:"",creationTime:"",totalCount:0,successCount:0,failureCount:0},caseResults:[]}),h=(0,n.KR)(!1),w=(0,n.KR)(null),I=(0,n.KR)(null);let S=null;const E=(0,a.EW)((()=>F.value.processState===r.si.Running)),W=(0,a.EW)((()=>F.value.caseResults||[])),X=e=>{I.value=e.testResultId,w.value=e.id,h.value=!0},K=()=>{h.value=!1,w.value=null},A=async()=>{s.value=!0;try{await r.cm.startTest(),C.value=!0,u.nk.success("Interoperation test started"),await P(),T()}catch(e){console.error("Failed to start interoperation test:",e),u.nk.error("Failed to start interoperation test")}finally{s.value=!1}},N=async()=>{y.value=!0;try{await r.cm.stopTest(),u.nk.success("Interoperation test stopped"),await P()}catch(e){console.error("Failed to stop interoperation test:",e),u.nk.error("Failed to stop interoperation test")}finally{y.value=!1}},P=async()=>{try{const e=await r.cm.getStatus();F.value=e.data,(0,r.xh)(F.value)&&S&&z(),t.value=!1}catch(e){console.error("Failed to fetch interoperation test status:",e)}},T=()=>{z(),S=window.setInterval(P,300)},z=()=>{S&&(clearInterval(S),S=null)};return(0,a.sV)((()=>{P().then((()=>{E.value&&(C.value=!0,T())}))})),(0,a.hi)((()=>{z()})),(e,t)=>{const u=(0,a.g2)("el-button"),r=(0,a.g2)("el-scrollbar");return(0,a.uX)(),(0,a.CE)("div",v,[(0,a.Lk)("div",p,[t[3]||(t[3]=(0,a.Lk)("h3",null,"Interoperation Test",-1)),(0,a.Lk)("div",k,[(0,a.bF)(u,{type:"success",size:"small",loading:s.value,onClick:A,disabled:E.value},{default:(0,a.k6)((()=>t[1]||(t[1]=[(0,a.eW)(" Start ")]))),_:1},8,["loading","disabled"]),(0,a.bF)(u,{type:"danger",size:"small",loading:y.value,onClick:N,disabled:!E.value},{default:(0,a.k6)((()=>t[2]||(t[2]=[(0,a.eW)(" Stop ")]))),_:1},8,["loading","disabled"])])]),(0,a.bF)(o.A,{"run-status":F.value,visible:C.value},null,8,["run-status","visible"]),W.value.length?((0,a.uX)(),(0,a.Wv)(r,{key:0,height:"100%"},{default:(0,a.k6)((()=>[(0,a.Lk)("div",m,[((0,a.uX)(!0),(0,a.CE)(a.FK,null,(0,a.pI)(W.value,((e,s)=>((0,a.uX)(),(0,a.CE)("div",{key:s,class:"case-item"},[(0,a.Lk)("div",f,[(0,a.Lk)("div",g,[(0,a.Lk)("div",null,(0,l.v_)(e.sequenceName),1),e.detail?((0,a.uX)(),(0,a.CE)("div",b,[(0,a.Lk)("div",R,(0,l.v_)(e.detail),1)])):(0,a.Q3)("",!0)]),(0,a.Lk)("div",L,[(0,a.Lk)("div",_,[(0,a.bF)(c.A,{state:e.state},null,8,["state"])]),(0,a.bF)(u,{type:"primary",size:"small",plain:"",onClick:t=>X(e)},{default:(0,a.k6)((()=>t[4]||(t[4]=[(0,a.eW)(" Open ")]))),_:2},1032,["onClick"])])])])))),128))])])),_:1})):((0,a.uX)(),(0,a.Wv)((0,n.R1)(i.x0),{key:1,description:"No items","image-size":150})),(0,a.bF)(d.A,{visible:h.value,"onUpdate:visible":t[0]||(t[0]=e=>h.value=e),testResultId:I.value,caseResultId:w.value,onClose:K},null,8,["visible","testResultId","caseResultId"])])}}}),C=s(1241);const F=(0,C.A)(y,[["__scopeId","data-v-12adb0ff"]]);var h=F},6741:function(e,t,s){s.d(t,{A:function(){return y}});var a=s(6768),l=s(144),n=s(4232),u=s(7477),i=s(1021),r=(0,a.pM)({__name:"TestStateTag",props:{state:{}},setup(e){const t=e,s=(0,a.EW)((()=>{switch(t.state){case i.si.Success:return"success";case i.si.Running:return"warning";case i.si.Failure:return"danger";case i.si.Pending:default:return"info"}})),l=e=>{switch(e){case i.si.Running:return"Running";case i.si.Pending:return"Not Run";case i.si.Success:return"Completed";case i.si.Failure:return"Faulted";case i.si.Paused:return"Paused";default:return"Unknown"}},u=(0,a.EW)((()=>l(t.state)));return(e,t)=>{const l=(0,a.g2)("el-tag");return(0,a.uX)(),(0,a.Wv)(l,{type:s.value,size:"small"},{default:(0,a.k6)((()=>[(0,a.eW)((0,n.v_)(u.value),1)])),_:1},8,["type"])}}});const c=r;var o=c;const d={key:0,class:"test-monitor"},v={class:"status-area"},p={class:"compact-status"},k={class:"status-header-inline"},m={key:0,class:"stats-row"},f={class:"stat-item success"},g={class:"stat-item failure"},b={class:"stat-item total"};var R=(0,a.pM)({__name:"TestMonitor",props:{runStatus:{},visible:{type:Boolean}},setup(e){const t=e,s=(0,a.EW)((()=>t.runStatus.testResult?.totalCount||0)),i=(0,a.EW)((()=>(t.runStatus.testResult?.successCount||0)+(t.runStatus.testResult?.failureCount||0)));return(e,t)=>{const r=(0,a.g2)("el-icon"),c=(0,a.g2)("el-progress");return e.visible?((0,a.uX)(),(0,a.CE)("div",d,[(0,a.Lk)("div",v,[(0,a.Lk)("div",p,[(0,a.Lk)("div",k,[s.value>0?((0,a.uX)(),(0,a.CE)("div",m,[(0,a.Lk)("div",f,[(0,a.bF)(r,null,{default:(0,a.k6)((()=>[(0,a.bF)((0,l.R1)(u.CircleCheckFilled))])),_:1}),(0,a.Lk)("span",null,(0,n.v_)(e.runStatus.testResult?.successCount||0),1)]),(0,a.Lk)("div",g,[(0,a.bF)(r,null,{default:(0,a.k6)((()=>[(0,a.bF)((0,l.R1)(u.CircleCloseFilled))])),_:1}),(0,a.Lk)("span",null,(0,n.v_)(e.runStatus.testResult?.failureCount||0),1)]),(0,a.Lk)("div",b,[(0,a.bF)(r,null,{default:(0,a.k6)((()=>[(0,a.bF)((0,l.R1)(u.InfoFilled))])),_:1}),(0,a.Lk)("span",null,(0,n.v_)(s.value||0),1)])])):(0,a.Q3)("",!0),(0,a.bF)(o,{state:e.runStatus.processState},null,8,["state"])]),(0,a.bF)(c,{percentage:s.value>0?Math.round(i.value/s.value*100):0,"stroke-width":8},null,8,["percentage"])])])])):(0,a.Q3)("",!0)}}}),L=s(1241);const _=(0,L.A)(R,[["__scopeId","data-v-ebdce83c"]]);var y=_},7823:function(e,t,s){s.d(t,{A:function(){return Q}});var a=s(6768),l=s(4232),n=s(144),u=s(1219),i=s(1021);const r=e=>{switch(e){case i.si.Success:return"success";case i.si.Running:return"primary";case i.si.Failure:return"danger";case i.si.Pending:default:return"info"}};var c=s(2971);const o={key:0,class:"loading"},d={key:1,class:"case-detail-content"},v={class:"basic-info"},p={class:"info-grid"},k={class:"info-item"},m={class:"value"},f={class:"info-item"},g={class:"value"},b={class:"info-item"},R={class:"value"},L={class:"info-item"},_={class:"value status-combined"},y={key:0,class:"info-item full-width"},C=["title"],F={key:1,class:"info-item full-width"},h=["title"],w={class:"steps-section"},I={key:0,class:"no-steps"},S={class:"step-content"},E={class:"step-row"},W={class:"step-left"},X={class:"step-timestamp"},K=["title"],A=["title"],N={class:"step-right"},P={class:"dialog-footer"};var T=(0,a.pM)({__name:"CaseDetailDialog",props:{visible:{type:Boolean},testResultId:{},caseResultId:{}},emits:["update:visible","close"],setup(e,{emit:t}){const s=e,T=t,z=(0,n.KR)(s.visible),M=(0,n.KR)(null),Q=(0,n.KR)([]),q=(0,n.KR)(!1);(0,a.wB)((()=>s.visible),(e=>{z.value=e,e&&s.testResultId&&s.caseResultId&&V()})),(0,a.wB)((()=>z.value),(e=>{T("update:visible",e),e||T("close")}));const V=async()=>{if(s.testResultId&&s.caseResultId){q.value=!0;try{const[e,t]=await Promise.all([i.GQ.getCaseResult(s.testResultId,s.caseResultId),i.GQ.getCaseSteps(s.testResultId,s.caseResultId)]);M.value=e.data,Q.value=t.data}catch(e){console.error("Failed to load case data:",e),u.nk.error("Failed to load case details")}finally{q.value=!1}}else u.nk.warning("Missing required parameters")},x=()=>{z.value=!1,M.value=null,Q.value=[]},B=e=>{if(!e)return"N/A";try{const t=new Date(e);return t.toLocaleString()}catch(t){return e}},D=e=>{if(!e&&0!==e)return"N/A";const t=e/1e6;return`${t.toFixed(6)}`};return(0,a.sV)((()=>{z.value&&s.testResultId&&s.caseResultId&&V()})),(e,t)=>{const s=(0,a.g2)("el-skeleton"),u=(0,a.g2)("el-empty"),i=(0,a.g2)("el-timeline-item"),T=(0,a.g2)("el-timeline"),V=(0,a.g2)("el-button"),U=(0,a.g2)("el-dialog");return(0,a.uX)(),(0,a.Wv)(U,{modelValue:z.value,"onUpdate:modelValue":t[0]||(t[0]=e=>z.value=e),title:`${M.value?.name||M.value?.sequenceName||""}`,width:"60%","destroy-on-close":""},{footer:(0,a.k6)((()=>[(0,a.Lk)("span",P,[(0,a.bF)(V,{onClick:x},{default:(0,a.k6)((()=>t[9]||(t[9]=[(0,a.eW)("Close")]))),_:1})])])),default:(0,a.k6)((()=>[q.value?((0,a.uX)(),(0,a.CE)("div",o,[(0,a.bF)(s,{rows:10,animated:""})])):((0,a.uX)(),(0,a.CE)("div",d,[(0,a.Lk)("div",v,[t[7]||(t[7]=(0,a.Lk)("h4",null,"Information",-1)),(0,a.Lk)("div",p,[(0,a.Lk)("div",k,[t[1]||(t[1]=(0,a.Lk)("div",{class:"label"},"Case Name:",-1)),(0,a.Lk)("div",m,(0,l.v_)(M.value?.name),1)]),(0,a.Lk)("div",f,[t[2]||(t[2]=(0,a.Lk)("div",{class:"label"},"Sequence Name:",-1)),(0,a.Lk)("div",g,(0,l.v_)(M.value?.sequenceName),1)]),(0,a.Lk)("div",b,[t[3]||(t[3]=(0,a.Lk)("div",{class:"label"},"Start Time:",-1)),(0,a.Lk)("div",R,(0,l.v_)(B(M.value?.begin)),1)]),(0,a.Lk)("div",L,[t[4]||(t[4]=(0,a.Lk)("div",{class:"label"},"End Time / Status:",-1)),(0,a.Lk)("div",_,[(0,a.eW)((0,l.v_)(B(M.value?.end))+" ",1),(0,a.bF)(c.A,{state:M.value?.state||"",class:"status-tag"},null,8,["state"])])]),M.value?.parameter?((0,a.uX)(),(0,a.CE)("div",y,[t[5]||(t[5]=(0,a.Lk)("div",{class:"label"},"Parameter:",-1)),(0,a.Lk)("div",{class:"value",title:M.value?.parameter},(0,l.v_)(M.value?.parameter),9,C)])):(0,a.Q3)("",!0),M.value?.detail?((0,a.uX)(),(0,a.CE)("div",F,[t[6]||(t[6]=(0,a.Lk)("div",{class:"label"},"Detail:",-1)),(0,a.Lk)("div",{class:"value",title:M.value?.detail},(0,l.v_)(M.value.detail),9,h)])):(0,a.Q3)("",!0)])]),(0,a.Lk)("div",w,[t[8]||(t[8]=(0,a.Lk)("h4",null,"Steps",-1)),0===Q.value.length?((0,a.uX)(),(0,a.CE)("div",I,[(0,a.bF)(u,{description:"No steps available"})])):((0,a.uX)(),(0,a.Wv)(T,{key:1},{default:(0,a.k6)((()=>[((0,a.uX)(!0),(0,a.CE)(a.FK,null,(0,a.pI)(Q.value,(e=>((0,a.uX)(),(0,a.Wv)(i,{key:e.id,type:(0,n.R1)(r)(e.state),hollow:"Success"!==e.state},{default:(0,a.k6)((()=>[(0,a.Lk)("div",S,[(0,a.Lk)("div",E,[(0,a.Lk)("div",W,[(0,a.Lk)("span",X,(0,l.v_)(D(e.timestamp)),1),(0,a.Lk)("span",{class:"step-name",title:e.name},(0,l.v_)(e.name),9,K),e.detail?((0,a.uX)(),(0,a.CE)("span",{key:0,title:e.detail,class:"step-detail-inline"},(0,l.v_)(e.detail),9,A)):(0,a.Q3)("",!0)]),(0,a.Lk)("div",N,[(0,a.bF)(c.A,{state:e.state},null,8,["state"])])])])])),_:2},1032,["type","hollow"])))),128))])),_:1}))])]))])),_:1},8,["modelValue","title"])}}}),z=s(1241);const M=(0,z.A)(T,[["__scopeId","data-v-a931aa76"]]);var Q=M}}]);
//# sourceMappingURL=295.46f815ae.js.map