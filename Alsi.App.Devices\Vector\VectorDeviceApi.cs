﻿using vxlapi_NET;
using static vxlapi_NET.XLDefine;

namespace Alsi.App.Devices.Vector
{
    public class VectorDeviceApi
    {
        public static XLDriver VectorDriver { get; set; } = new XLDriver();


        public static void InitializeData()
        {
            OpenDriver();
        }

        public static void FinalizeData()
        {
            CloseDriver();
        }

        public static void OpenDriver()
        {
            XL_Status status = VectorDriver.XL_OpenDriver();
            OperateVectorLog(status, "XL_OpenDriver");
        }

        public static void CloseDriver()
        {
            XL_Status status = VectorDriver.XL_CloseDriver();
            OperateVectorLog(status, "XL_CloseDriver");
        }

        private static void OperateVectorLog(XL_Status status, string msg)
        {
            if (status == XL_Status.XL_SUCCESS)
            {
                msg += "Sucess";
            }
            else
            {
                msg += "Failed";
            }
        }
    }
}
