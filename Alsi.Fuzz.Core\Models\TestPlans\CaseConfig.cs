using Alsi.App.Devices.Core;
using System;

namespace Alsi.Fuzz.Core.Models.TestPlans
{
    public class CaseConfig
    {
        public WhiteListFrame[] WhiteListFrames { get; set; } = Array.Empty<WhiteListFrame>();

        // 新增：保存选中的目标节点名称
        public string SelectedNodeName { get; set; }

        public bool EnableNmWakeup { get; set; } = true;
        public int NmWakeupId { get; set; } = 0x53F;
        public CommunicationType NmWakeupCommunicationType { get; set; } = CommunicationType.Can;
        public bool NmWakeupIsExt { get; set; }
        public int NmWakeupDlc { get; set; } = 8;
        public int[] NmWakeupData { get; set; } = { 0x3F, 0x50, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF };
        public int NmWakeupCycleMs { get; set; } = 100;
        public int NmWakeupDelayMs { get; set; } = 2000;

        public int DiagReqId { get; set; } = 0x731;
        public int DiagResId { get; set; } = 0x631;
        public bool DiagReqIsExt { get; set; }
        public int DiagTimeoutMs { get; set; } = 500;
        public bool IsDutMtuLessThan4096 { get; set; }
        public bool EnableDiagFallbackRequest { get; set; }
        public int[] DiagFallbackRequestPayload { get; set; } = { 0x10, 0x01 };
    }
}
