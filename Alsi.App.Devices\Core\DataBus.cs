using Alsi.App.Devices.Core.Channels;
using System;

namespace Alsi.App.Devices.Core
{
    public static class DataBus
    {
        public static bool IsRunning { get; private set; }
        private static ICanChannel CanChannel { get; set; }

        private static DataBusTimer dataBusTimer = new DataBusTimer();
        public static ulong Timestamp => dataBusTimer.Timestamp;
        public static void SyncTimeBaseDeviation(ulong timestamp, int channel)
            => dataBusTimer.SyncTimeBaseDeviation(timestamp, channel);
        public static int? HardwareTimeBaseChannel => dataBusTimer.HardwareTimeBaseChannel;

        public static void Start(DeviceChannel deviceChannel, ChannelConfig config)
        {
            if (IsRunning)
            {
                return;
            }

            IsRunning = true;

            if (deviceChannel.Manufacturer == Manufacturer.Tosun)
            {
                CanChannel = new TsLibCanChannel(deviceChannel, config);
            }
            else if (deviceChannel.Manufacturer == Manufacturer.Vector)
            {
                CanChannel = new VectorCanChannel(deviceChannel, config);
            }

            CanChannel.Initialize(filterErrorFrame: true);
            CanChannel.Start();
            dataBusTimer.Start();
        }

        public static void Stop()
        {
            if (CanChannel != null)
            {
                CanChannel.Stop();
                CanChannel.Release();
                CanChannel = null;
            }

            dataBusTimer.Stop();
            IsRunning = false;
        }

        public static void Send(CanFrame frame)
        {
            if (!IsRunning)
            {
                return;
            }

            if (CanChannel == null)
            {
                throw new Exception("The can channel is null");
            }

            if (frame.Id > 0x7FF && !frame.IsExt)
            {
                throw new Exception($"The data frame should be marked as EXT frame when the ID is {frame.Id:X}");
            }

            CanChannel.Send(frame);
        }

        public static event EventHandler<CanFrame> OnSent;
        public static event EventHandler<CanFrame> OnReceived;

        internal static void InvokeOnTransmitted(CanFrame frame)
        {
            try
            {
                if (frame.IsTx)
                {
                    OnSent?.Invoke(null, frame);
                }
                else
                {
                    OnReceived?.Invoke(null, frame);
                }
            }
            catch (Exception e)
            {
                AppEnv.Logger.Error(e, "Dispatch CAN frame failed.");
            }
        }
    }
}
