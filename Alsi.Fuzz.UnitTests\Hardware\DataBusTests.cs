using Alsi.App.Devices;
using Alsi.App.Devices.Core;
using Alsi.Common.Utils;
using Shouldly;
using Xunit.Abstractions;

namespace Alsi.Fuzz.UnitTests.Hardware
{
    [Collection(SerialUnitTestCollection.Name)]
    public class DataBusTests : UnitTestBase
    {
        private readonly ITestOutputHelper _testOutputHelper;

        public DataBusTests(ITestOutputHelper testOutputHelper)
            : base()
        {
            _testOutputHelper = testOutputHelper;
        }

        [Fact]
        public void DataBusSendFrame_ByVirtualChannel_ValidateSentEvent()
        {
            // Arrange & Act
            var deviceChannels = GetDeviceChannels();
            var virtualChannel = deviceChannels.FirstOrDefault(x => x.Name.Contains("Virtual Channel"));
            virtualChannel.ShouldNotBeNull();

            // Assert
            var allFrames = CollectFrames(virtualChannel);
            ValidateTxFrame(allFrames, _defaultTxFrame);
        }

        [SkippableTheory]
        [InlineData(CommunicationType.CanFd, 500_000, 2000_000)]
        public void DataBusSendFrame_ByTosunDevice_ValidateSentEvent(
            CommunicationType communicationType, int arbitrationBitRate, int canFdDataBitRate)
        {
            // Arrange & Act
            var deviceChannels = GetDeviceChannels();
            var tosunDeviceChannel = deviceChannels.FirstOrDefault(
                x => x.Manufacturer == Manufacturer.Tosun && x.CommunicationType == communicationType);
            Skip.If(tosunDeviceChannel == null, "Can't find TOSUN device");

            // Assert
            var config = new ChannelConfig();
            config.CommunicationType = communicationType;
            config.CanFdArbitrationBitRate = arbitrationBitRate;
            config.CanFdDataBitRate = canFdDataBitRate;

            var allFrames = CollectFrames(tosunDeviceChannel, durationMs: 1000, config);
            ValidateTxFrame(allFrames, _defaultTxFrame);

            var rxFrames = allFrames.Where(x => x.IsRx && !x.IsErrorFrame).ToArray();
            rxFrames.Length.ShouldBeGreaterThan(0);

            _testOutputHelper.WriteLine($"Received {rxFrames.Length} x frames");
            foreach (var frame in rxFrames.Take(10))
            {
                _testOutputHelper.WriteLine($"{frame.Id.ToHex()} Dlc={frame.Dlc} Data={frame.Data.ToHex()}");
            }
        }

        [SkippableTheory]
        [InlineData(CommunicationType.CanFd, 500_000, 500_000)]
        [InlineData(CommunicationType.CanFd, 500_000, 1000_000)]
        [InlineData(CommunicationType.CanFd, 300_000, 2000_000)]
        [InlineData(CommunicationType.CanFd, 500_000, 4000_000)]
        public void DataBusSendFrame_ByTosunDeviceWithInvalidBitrate_ValidateSentEvent(
            CommunicationType communicationType, int arbitrationBitRate, int canFdDataBitRate)
        {
            // Arrange & Act
            var deviceChannels = GetDeviceChannels();
            var tosunDeviceChannel = deviceChannels.FirstOrDefault(x => x.Manufacturer == Manufacturer.Tosun
                && x.CommunicationType == communicationType);
            Skip.If(tosunDeviceChannel == null, "Can't find TOSUN device");

            // Assert
            var config = new ChannelConfig();
            config.CommunicationType = communicationType;
            config.CanFdArbitrationBitRate = arbitrationBitRate;
            config.CanFdDataBitRate = canFdDataBitRate;
            var frames = CollectFrames(tosunDeviceChannel, durationMs: 1000, config);
            var rxFrames = frames.Where(x => x.IsRx && !x.IsErrorFrame).ToArray();
            rxFrames.Length.ShouldBe(0);

            _testOutputHelper.WriteLine($"Received {rxFrames.Length} x frames");
            foreach (var frame in rxFrames.Take(10))
            {
                _testOutputHelper.WriteLine($"{frame.Id.ToHex()} Dlc={frame.Dlc} Data={frame.Data.ToHex()}");
            }
        }

        private static DeviceChannel[]? deviceChannels = null;
        private static DeviceChannel[] GetDeviceChannels()
        {
            if (deviceChannels == null)
            {
                deviceChannels = DeviceEnv.GetDeviceChannels();
            }

            return deviceChannels;
        }

        private static CanFrame[] CollectFrames(
            DeviceChannel deviceChannel,
            int durationMs = 1000,
            ChannelConfig? config = null)
        {
            try
            {
                config ??= new ChannelConfig();

                DataBus.Start(deviceChannel, config);

                List<CanFrame> allFrames = new List<CanFrame>();
                DataBus.OnSent += (_, frame) =>
                {
                    allFrames.Add(frame);
                };

                DataBus.OnReceived += (_, frame) =>
                {
                    allFrames.Add(frame);
                };

                DataBus.Send(_defaultTxFrame);

                // Assert
                Thread.Sleep(durationMs);

                return allFrames.ToArray();
            }
            finally
            {
                DataBus.Stop();
            }
        }

        private void ValidateTxFrame(CanFrame[] allFrames, CanFrame txFrame)
        {
            var txFrames = allFrames.Where(x => x.IsTx).ToArray();
            txFrames.Length.ShouldBe(1);
            txFrames[0].Id.ShouldBe(txFrame.Id);
            txFrames[0].Data.ToHex().ShouldBe(txFrame.Data.ToHex());
            txFrames[0].Dlc.ShouldBe(txFrame.Dlc);
        }

        private static CanFrame _defaultTxFrame = new CanFrame
        {
            Id = 0x53F,
            IsExt = false,
            IsTx = true,
            Data = new byte[] { 0x3F, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF },
            Dlc = 8
        };
    }
}
