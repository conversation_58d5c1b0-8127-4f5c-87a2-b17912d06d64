using Alsi.Fuzz.Core.Service;
using Alsi.Fuzz.Core.Storage;
using Shouldly;

namespace Alsi.Fuzz.UnitTests.TestPlans
{
    public class TestPlanTests : UnitTestBase
    {
        private readonly TestPlanService _service;
        private readonly ITestPlanHistoryService _historyService;
        private readonly string _testDir;

        private const string TempFolderName = "AlsiFuzzTests";

        public TestPlanTests()
        {
            _historyService = new TestPlanHistoryService(AppDbContext);
            _service = new TestPlanService(new TestPlanStorage(), _historyService);
            _testDir = Path.Combine(Path.GetTempPath(), TempFolderName, Guid.NewGuid().ToString());
            Directory.CreateDirectory(_testDir);
        }

        [Fact]
        public async Task CreateTestPlan_ShouldCreateValidPlan()
        {
            // Arrange
            var desc = "Test Description";

            // Act
            var plan = await _service.CreateAsync(desc);

            // Assert
            plan.ShouldNotBeNull();
            plan.Manifest.Description.ShouldBe(desc);
            plan.Config.ShouldNotBeNull();
        }

        [Fact]
        public async Task SaveAndLoadTestPlan_ShouldWork()
        {
            // Arrange
            var plan = await _service.CreateAsync("Description");
            var path = Path.Combine(_testDir, "testplan.fzp");

            // Act
            await _service.SaveAsync(plan, path);
            var loadedPlan = await _service.LoadAsync(path);

            // Assert
            loadedPlan.ShouldNotBeNull();
            loadedPlan.Manifest.Description.ShouldBe(plan.Manifest.Description);
        }

        [Fact]
        public async Task LoadTestPlan_WithInvalidPath_ShouldThrow()
        {
            // Arrange
            var invalidPath = Path.Combine(_testDir, "nonexistent.fzp");

            // Act & Assert
            await Should.ThrowAsync<FileNotFoundException>(() =>
                _service.LoadAsync(invalidPath));
        }

        [Fact]
        public async Task LoadTestPlan_ShouldRecordHistory()
        {
            // Arrange
            var plan = await _service.CreateAsync("Description");
            var path = Path.Combine(_testDir, "testplan.fzp");
            await _service.SaveAsync(plan, path);

            // Act
            var loadedPlan = await _service.LoadAsync(path);
            var history = await _historyService.GetRecentHistoryAsync(1);

            // Assert
            history.Length.ShouldBe(1);
            history[0].FilePath.ShouldBe(path);
            history[0].LastModified.ShouldBe(plan.Manifest.Modified);
        }

        [Fact]
        public async Task GetRecentHistory_ShouldReturnOrderedResults()
        {
            // Arrange
            await _historyService.ClearHistoryAsync();
            var plans = new[]
            {
                await _service.CreateAsync("Description 1"),
                await _service.CreateAsync("Description 2"),
                await _service.CreateAsync("Description 3")
            };

            for (int i = 0; i < plans.Length; i++)
            {
                var path = Path.Combine(_testDir, $"Plan {i + 1}.fzp");
                await _service.SaveAsync(plans[i], path);
                await _service.LoadAsync(path);
                await Task.Delay(100); // 确保时间戳不同
            }

            // Act
            var history = await _historyService.GetRecentHistoryAsync();

            // Assert
            history.Length.ShouldBeGreaterThanOrEqualTo(3);
            Path.GetFileNameWithoutExtension(history[0].FilePath).ShouldBe("Plan 3");
            Path.GetFileNameWithoutExtension(history[1].FilePath).ShouldBe("Plan 2");
            Path.GetFileNameWithoutExtension(history[2].FilePath).ShouldBe("Plan 1");
        }

        [Fact]
        public async Task ClearHistory_ShouldRemoveAllRecords()
        {
            // Arrange
            var plan = await _service.CreateAsync("Description");
            var path = Path.Combine(_testDir, "testplan.fzp");
            await _service.SaveAsync(plan, path);
            await _service.LoadAsync(path);
            var history = await _historyService.GetRecentHistoryAsync();

            // Act
            await _historyService.ClearHistoryAsync();
            history = await _historyService.GetRecentHistoryAsync();

            // Assert
            history.Length.ShouldBe(0);
        }

        public override void Dispose()
        {
            base.Dispose();

            if (Directory.Exists(_testDir))
            {
                if (_testDir.Contains(TempFolderName))
                {
                    Directory.Delete(_testDir, true);
                }
            }
        }
    }
}
