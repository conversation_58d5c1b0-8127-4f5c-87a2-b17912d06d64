using FreeSql.DataAnnotations;
using System;

namespace Alsi.Fuzz.Core.Models.History
{
    public class TestPlanHistory
    {
        [Column(IsIdentity = true, IsPrimary = true)]
        public Guid Id { get; set; }

        public string FilePath { get; set; }

        public DateTime LastAccessTime { get; set; }

        public DateTime LastModified { get; set; }

        public bool IsDeleted { get; set; }
    }
}
