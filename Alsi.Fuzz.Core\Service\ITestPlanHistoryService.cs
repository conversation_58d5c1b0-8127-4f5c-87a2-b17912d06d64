using Alsi.Fuzz.Core.Models.History;
using Alsi.Fuzz.Core.Models.TestPlans;
using System.Threading.Tasks;

namespace Alsi.Fuzz.Core.Service
{
    public interface ITestPlanHistoryService
    {
        Task RecordAccessAsync(TestPlan plan, string filePath);
        Task<TestPlanHistory[]> GetRecentHistoryAsync(int count = 10);
        Task ClearHistoryAsync();
        Task DeleteRecordAsync(string filePath);
    }
}
