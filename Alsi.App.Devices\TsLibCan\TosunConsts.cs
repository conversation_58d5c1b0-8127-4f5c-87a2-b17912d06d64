﻿using System.Collections.Generic;

namespace Alsi.App.Devices.TsLibCan
{
    public static class TosunConsts
    {
        public static Dictionary<string, int> CanChannels = new Dictionary<string, int>()
        {
            { "TOSUN HS CAN Mini", 1 },
            { "TOSUN HS CANFD Mini", 1 },
            { "TOSUN HS TC1001", 1 },
            { "TOSUN HS TC1011", 1 },
            { "TOSUN HS TC1012", 1 },
            { "TOSUN HS TC1012/P", 1 },
            { "TOSUN HS TC1013", 2 },
            { "TOSUN HS TC1014", 4 },
            { "TOSUN HS TC1016", 4 },
            { "TOSUN HS TC1016/P", 4 },
            { "TOSUN HS TC1017", 8 },
            { "TOSUN HS TC1018", 12 },
            { "TOSUN HS TC1026", 1 },
            { "TOSUN HS TC1026/P", 1 },
            { "TOSUN HS TC1034", 2 },
            { "TOSUN HS TC1038", 12 },
            { "TOSUN HS TP1013", 2 },
            { "TOSUN HS TP1018", 12 },
            { "TOSUN HS MP1013", 2 },
            { "TOSUN HS TC1114B", 4 },
            { "TOSUN HS TC1054", 2 },
            { "TOSUN HS TC1058", 20 },
            { "TOSUN HS Tlog1002", 2 },
            { "TOSUN HS Tlog1004", 4 },
            { "TOSUN HS Tlog1038", 12 },
            { "TOSUN HS GW2110", 10 },
            { "TOSUN HS GW2112", 2 }
        };

        public static Dictionary<string, int> LinChannels = new Dictionary<string, int>()
        {
            { "TOSUN HS LIN Mini", 1 },            
            { "TOSUN HS TL1001", 1 },
            { "TOSUN HS TC1012/P", 1 },
            { "TOSUN HS TC1016/P", 1 },
            { "TOSUN HS TC1026/P", 6 },
            { "TOSUN HS TC1038", 12 },
            { "TOSUN HS TC1058", 10 },
            { "TOSUN HS Tlog1002", 2 },
            { "TOSUN HS Tlog1004", 2 },
            { "TOSUN HS Tlog1038", 12 }
        };
    }
}
