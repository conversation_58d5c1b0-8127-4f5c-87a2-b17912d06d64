using Alsi.Fuzz.Core.Service.CaseFactory.Core;
using System.Collections.Generic;

namespace Alsi.Fuzz.Core.Service.CaseFactory.Factories.Iso15765
{
    public class G37_CaseFactory : CaseFactoryBase
    {
        public override CaseMutation[] Generate(MutationOptions options)
        {
            var list = new List<CaseMutation>();

            var ff_dl = 0x40;
            var dlc = 8;
            // TestCondition-1: Repeat (G371)

            // G371 - Repeat FirstFrame
            foreach (var repeat in new[] { 5, 50, 100, 2000 })
            {
                var caseMutation = CaseMutation.Create($"G371-Repeat_FF_{repeat}")
                    .MutateDlc(dlc)
                    .MutateTpFfDl12b(ff_dl)
                    .Mutate(MutationFieldType.TP_Repeat_Index, "0")
                    .Mutate(MutationFieldType.TP_Repeat_Times, repeat.ToString());
                list.Add(caseMutation);
            }

            // G371 - Repeat ConsecutiveFrame SN=1
            foreach (var repeat in new[] { 5, 50, 100, 2000 })
            {
                var caseMutation = CaseMutation.Create($"G371-Repeat_CF1_{repeat}")
                    .MutateDlc(dlc)
                    .MutateTpFfDl12b(ff_dl)
                    .Mutate(MutationFieldType.TP_Repeat_Index, "1")
                    .Mutate(MutationFieldType.TP_Repeat_Times, repeat.ToString());
                list.Add(caseMutation);
            }

            // G371 - Repeat ConsecutiveFrame SN=2
            foreach (var repeat in new[] { 5, 50, 100, 2000 })
            {
                var caseMutation = CaseMutation.Create($"G371-Repeat_CF2_{repeat}")
                    .MutateDlc(dlc)
                    .MutateTpFfDl12b(ff_dl)
                    .Mutate(MutationFieldType.TP_Repeat_Index, "2")
                    .Mutate(MutationFieldType.TP_Repeat_Times, repeat.ToString());
                list.Add(caseMutation);
            }

            // G371 - Repeat ConsecutiveFrame SN=3
            foreach (var repeat in new[] { 5, 50, 100, 2000 })
            {
                var caseMutation = CaseMutation.Create($"G371-Repeat_CF3_{repeat}")
                    .MutateDlc(dlc)
                    .MutateTpFfDl12b(ff_dl)
                    .Mutate(MutationFieldType.TP_Repeat_Index, "3")
                    .Mutate(MutationFieldType.TP_Repeat_Times, repeat.ToString());
                list.Add(caseMutation);
            }

            // TestCondition-2: Skip (G372)

            // G372 - Skip FirstFrame
            {
                var caseMutation = CaseMutation.Create($"G372-Skip_FF")
                    .MutateDlc(dlc)
                    .MutateTpFfDl12b(ff_dl)
                    .Mutate(MutationFieldType.TP_Skip_Index, "0");
                list.Add(caseMutation);
            }

            // G372 - Skip ConsecutiveFrame SN=1
            {
                var caseMutation = CaseMutation.Create($"G372-Skip_CF1")
                    .MutateDlc(dlc)
                    .MutateTpFfDl12b(ff_dl)
                    .Mutate(MutationFieldType.TP_Skip_Index, "1");
                list.Add(caseMutation);
            }

            // G372 - Skip ConsecutiveFrame SN=2
            {
                var caseMutation = CaseMutation.Create($"G372-Skip_CF2")
                    .MutateDlc(dlc)
                    .MutateTpFfDl12b(ff_dl)
                    .Mutate(MutationFieldType.TP_Skip_Index, "2");
                list.Add(caseMutation);
            }

            // G372 - Skip ConsecutiveFrame SN=3
            {
                var caseMutation = CaseMutation.Create($"G372-Skip_CF3")
                    .MutateDlc(dlc)
                    .MutateTpFfDl12b(ff_dl)
                    .Mutate(MutationFieldType.TP_Skip_Index, "3");
                list.Add(caseMutation);
            }

            // TestCondition-3: Swap (G373)

            // G373 - Swap FirstFrame and ConsecutiveFrame SN=1
            {
                var caseMutation = CaseMutation.Create($"G373-Swap_FF_CF1")
                    .MutateDlc(dlc)
                    .MutateTpFfDl12b(ff_dl)
                    .Mutate(MutationFieldType.TP_Swap_A, "0")
                    .Mutate(MutationFieldType.TP_Swap_B, "1");
                list.Add(caseMutation);
            }

            // G373 - Swap FirstFrame and ConsecutiveFrame SN=2
            {
                var caseMutation = CaseMutation.Create($"G373-Swap_FF_CF2")
                    .MutateDlc(dlc)
                    .MutateTpFfDl12b(ff_dl)
                    .Mutate(MutationFieldType.TP_Swap_A, "0")
                    .Mutate(MutationFieldType.TP_Swap_B, "2");
                list.Add(caseMutation);
            }

            // G373 - Swap FirstFrame and ConsecutiveFrame SN=3
            {
                var caseMutation = CaseMutation.Create($"G373-Swap_FF_CF3")
                    .MutateDlc(dlc)
                    .MutateTpFfDl12b(ff_dl)
                    .Mutate(MutationFieldType.TP_Swap_A, "0")
                    .Mutate(MutationFieldType.TP_Swap_B, "3");
                list.Add(caseMutation);
            }

            // G373 - Swap ConsecutiveFrame SN=1 and SN=2
            {
                var caseMutation = CaseMutation.Create($"G373-Swap_CF1_CF2")
                    .MutateDlc(dlc)
                    .MutateTpFfDl12b(ff_dl)
                    .Mutate(MutationFieldType.TP_Swap_A, "1")
                    .Mutate(MutationFieldType.TP_Swap_B, "2");
                list.Add(caseMutation);
            }

            // G373 - Swap ConsecutiveFrame SN=1 and SN=3
            {
                var caseMutation = CaseMutation.Create($"G373-Swap_CF1_CF3")
                    .MutateDlc(dlc)
                    .MutateTpFfDl12b(ff_dl)
                    .Mutate(MutationFieldType.TP_Swap_A, "1")
                    .Mutate(MutationFieldType.TP_Swap_B, "3");
                list.Add(caseMutation);
            }

            // G373 - Swap ConsecutiveFrame SN=2 and SN=3
            {
                var caseMutation = CaseMutation.Create($"G373-Swap_CF2_CF3")
                    .MutateDlc(dlc)
                    .MutateTpFfDl12b(ff_dl)
                    .Mutate(MutationFieldType.TP_Swap_A, "2")
                    .Mutate(MutationFieldType.TP_Swap_B, "3");
                list.Add(caseMutation);
            }

            // TestCondition-4: Reverse (G374)

            // G374 - Reverse from FirstFrame to ConsecutiveFrame SN=3
            {
                var caseMutation = CaseMutation.Create($"G374-Reverse_FF_CF3")
                    .MutateDlc(dlc)
                    .MutateTpFfDl12b(ff_dl)
                    .Mutate(MutationFieldType.TP_Reverse_From, "0")
                    .Mutate(MutationFieldType.TP_Reverse_To, "3");
                list.Add(caseMutation);
            }

            // G374 - Reverse from ConsecutiveFrame SN=1 to SN=3
            {
                var caseMutation = CaseMutation.Create($"G374-Reverse_CF1_CF3")
                    .MutateDlc(dlc)
                    .MutateTpFfDl12b(ff_dl)
                    .Mutate(MutationFieldType.TP_Reverse_From, "1")
                    .Mutate(MutationFieldType.TP_Reverse_To, "3");
                list.Add(caseMutation);
            }

            // G374 - Reverse from ConsecutiveFrame SN=2 to SN=3
            {
                var caseMutation = CaseMutation.Create($"G374-Reverse_CF2_CF3")
                    .MutateDlc(dlc)
                    .MutateTpFfDl12b(ff_dl)
                    .Mutate(MutationFieldType.TP_Reverse_From, "2")
                    .Mutate(MutationFieldType.TP_Reverse_To, "3");
                list.Add(caseMutation);
            }

            return list.ToArray();
        }
    }
}
