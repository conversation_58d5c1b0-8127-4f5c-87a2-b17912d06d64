import axios from 'axios';
import { ElMessage, ElMessageBox } from 'element-plus';

// 格式化错误信息，显示所有层级的异常
const formatErrorMessage = (error: any): string => {
  if (!error.response || !error.response.data) {
    return error.message || 'Unknown error';
  }

  const errorData = error.response.data;
  const errorMessages = [];

  // 添加主异常信息
  if (errorData.exceptionMessage) {
    errorMessages.push(errorData.exceptionMessage);
  }

  // 递归添加所有内部异常信息
  let currentException = errorData.innerException;
  while (currentException) {
    if (currentException.exceptionMessage) {
      errorMessages.push(currentException.exceptionMessage);
    }
    currentException = currentException.innerException;
  }

  // 如果没有找到任何异常信息，返回通用错误消息
  if (errorMessages.length === 0) {
    return errorData.message || 'An error occurred';
  }

  // 返回所有异常信息，每个一行
  return errorMessages.join('<br>');
};

// 显示详细错误信息
const showDetailedError = (error: any): void => {
  if (!error.response || !error.response.data) {
    ElMessage.error(error.message || 'Unknown error');
    return;
  }

  // 获取格式化的错误信息
  const errorMessage = formatErrorMessage(error);

  // 使用对话框显示详细错误信息
  ElMessageBox.alert(
    errorMessage,
    'Error',
    {
      confirmButtonText: 'OK',
      dangerouslyUseHTMLString: true,
      closeOnClickModal: true,  // 允许点击空白区域关闭
      closeOnPressEscape: true, // 允许按ESC键关闭
      showClose: true           // 显示右上角关闭按钮
    }
  );
};

// 检查是否为用户取消操作
const isUserCanceled = (error: any): boolean => {
  // 检查错误响应数据
  if (error.response && error.response.data) {
    // 检查直接等于字符串的情况
    if (error.response.data === 'UserCanceled') {
      return true;
    }

    // 检查错误消息字段
    if (error.response.data.message === 'UserCanceled') {
      return true;
    }

    // 检查错误代码字段
    if (error.response.data.errorCode === 'UserCanceled') {
      return true;
    }
  }

  return false;
};

// 设置响应拦截器
export const setupErrorHandler = (): void => {
  axios.interceptors.response.use(
    response => response,
    error => {
      // 检查是否为用户取消操作
      if (isUserCanceled(error)) {
        // 用户取消操作，显示信息提示而不是错误
        ElMessage.info("Operation cancelled by user");

        // 继续抛出错误，以便调用者可以进行额外处理
        return Promise.reject(error);
      }

      // 处理其他错误
      showDetailedError(error);

      // 继续抛出错误，以便调用者可以进行额外处理
      return Promise.reject(error);
    }
  );
};

export default setupErrorHandler;
