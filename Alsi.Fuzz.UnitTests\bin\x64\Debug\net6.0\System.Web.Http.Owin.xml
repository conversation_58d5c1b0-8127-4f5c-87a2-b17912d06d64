﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Web.Http.Owin</name>
  </assembly>
  <members>
    <member name="T:System.Net.Http.OwinHttpRequestMessageExtensions">
      <summary>Provides extension methods for the <see cref="T:System.Net.Http.HttpRequestMessage" /> class.</summary>
    </member>
    <member name="M:System.Net.Http.OwinHttpRequestMessageExtensions.GetOwinContext(System.Net.Http.HttpRequestMessage)">
      <summary>Gets the OWIN context for the specified request.</summary>
      <returns>The OWIN environment for the specified context, if available; otherwise <see cref="null" />.</returns>
      <param name="request">The HTTP request message.</param>
    </member>
    <member name="M:System.Net.Http.OwinHttpRequestMessageExtensions.GetOwinEnvironment(System.Net.Http.HttpRequestMessage)">
      <summary>Gets the OWIN environment for the specified request.</summary>
      <returns>The OWIN environment for the specified request, if available; otherwise <see cref="null" />.</returns>
      <param name="request">The HTTP request message.</param>
    </member>
    <member name="M:System.Net.Http.OwinHttpRequestMessageExtensions.SetOwinContext(System.Net.Http.HttpRequestMessage,Microsoft.Owin.IOwinContext)">
      <summary>Sets the OWIN context for the specified request.</summary>
      <param name="request">The HTTP request message.</param>
      <param name="context">The OWIN context to set.</param>
    </member>
    <member name="M:System.Net.Http.OwinHttpRequestMessageExtensions.SetOwinEnvironment(System.Net.Http.HttpRequestMessage,System.Collections.Generic.IDictionary{System.String,System.Object})">
      <summary>Sets the OWIN environment for the specified request.</summary>
      <param name="request">The HTTP request message.</param>
      <param name="environment">The OWIN environment to set.</param>
    </member>
    <member name="T:System.Web.Http.HostAuthenticationAttribute">
      <summary>Represents an authentication attribute that authenticates via OWIN middleware.</summary>
    </member>
    <member name="M:System.Web.Http.HostAuthenticationAttribute.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.HostAuthenticationAttribute" /> class.</summary>
      <param name="authenticationType">The authentication type of the OWIN middleware to use.</param>
    </member>
    <member name="P:System.Web.Http.HostAuthenticationAttribute.AllowMultiple"></member>
    <member name="M:System.Web.Http.HostAuthenticationAttribute.AuthenticateAsync(System.Web.Http.Filters.HttpAuthenticationContext,System.Threading.CancellationToken)"></member>
    <member name="P:System.Web.Http.HostAuthenticationAttribute.AuthenticationType">
      <summary>Gets the authentication type of the OWIN middleware to use.</summary>
    </member>
    <member name="M:System.Web.Http.HostAuthenticationAttribute.ChallengeAsync(System.Web.Http.Filters.HttpAuthenticationChallengeContext,System.Threading.CancellationToken)"></member>
    <member name="T:System.Web.Http.HostAuthenticationFilter">
      <summary>Represents an authentication filter that authenticates via OWIN middleware.</summary>
    </member>
    <member name="M:System.Web.Http.HostAuthenticationFilter.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.HostAuthenticationFilter" /> class.</summary>
      <param name="authenticationType">The authentication type of the OWIN middleware to use.</param>
    </member>
    <member name="P:System.Web.Http.HostAuthenticationFilter.AllowMultiple">
      <summary>Gets a value indicating whether the filter allows multiple authentication.</summary>
      <returns>true if the filter allows multiple authentication; otherwise, false.</returns>
    </member>
    <member name="M:System.Web.Http.HostAuthenticationFilter.AuthenticateAsync(System.Web.Http.Filters.HttpAuthenticationContext,System.Threading.CancellationToken)">
      <summary>Asynchronously authenticates the request.</summary>
      <returns>The task that completes the authentication.</returns>
      <param name="context">The authentication context.</param>
      <param name="cancellationToken">The cancellation token.</param>
    </member>
    <member name="P:System.Web.Http.HostAuthenticationFilter.AuthenticationType">
      <summary>Gets the authentication type of the OWIN middleware to use.</summary>
      <returns>The authentication type of the OWIN middleware to use.</returns>
    </member>
    <member name="M:System.Web.Http.HostAuthenticationFilter.ChallengeAsync(System.Web.Http.Filters.HttpAuthenticationChallengeContext,System.Threading.CancellationToken)">
      <summary>Asynchronously challenges an authentication.</summary>
      <returns>The task that completes the challenge.</returns>
      <param name="context">The context.</param>
      <param name="cancellationToken">The cancellation token.</param>
    </member>
    <member name="T:System.Web.Http.OwinHttpConfigurationExtensions">
      <summary>Provides extension methods for the <see cref="T:System.Web.Http.HttpConfiguration" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.OwinHttpConfigurationExtensions.SuppressDefaultHostAuthentication(System.Web.Http.HttpConfiguration)">
      <summary>Enables suppression of the host's default authentication.</summary>
      <param name="configuration">The server configuration.</param>
    </member>
    <member name="T:System.Web.Http.Owin.HttpMessageHandlerAdapter">
      <summary>Represents an OWIN component that submits requests to an <see cref="T:System.Net.Http.HttpMessageHandler" /> when invoked.</summary>
    </member>
    <member name="M:System.Web.Http.Owin.HttpMessageHandlerAdapter.#ctor(Microsoft.Owin.OwinMiddleware,System.Net.Http.HttpMessageHandler,System.Web.Http.Hosting.IHostBufferPolicySelector)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Owin.HttpMessageHandlerAdapter" /> class.</summary>
      <param name="next">The next component in the pipeline.</param>
      <param name="messageHandler">The <see cref="T:System.Net.Http.HttpMessageHandler" /> to submit requests to.</param>
      <param name="bufferPolicySelector">The <see cref="T:System.Web.Http.Hosting.IHostBufferPolicySelector" /> that determines whether or not to buffer requests and responses.</param>
    </member>
    <member name="M:System.Web.Http.Owin.HttpMessageHandlerAdapter.#ctor(Microsoft.Owin.OwinMiddleware,System.Web.Http.Owin.HttpMessageHandlerOptions)">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Owin.HttpMessageHandlerAdapter" /> class.</summary>
      <param name="next">The next component in the pipeline.</param>
      <param name="options">The options to configure this adapter.</param>
    </member>
    <member name="P:System.Web.Http.Owin.HttpMessageHandlerAdapter.AppDisposing">
      <summary>Gets the cancellation token that triggers cleanup of this component.</summary>
      <returns>The cancellation token.</returns>
    </member>
    <member name="P:System.Web.Http.Owin.HttpMessageHandlerAdapter.BufferPolicySelector">
      <summary>Gets the <see cref="T:System.Web.Http.Hosting.IHostBufferPolicySelector" /> that determines whether or not to buffer requests and responses.</summary>
      <returns>The <see cref="T:System.Web.Http.Hosting.IHostBufferPolicySelector" /> that determines whether or not to buffer requests and responses.</returns>
    </member>
    <member name="M:System.Web.Http.Owin.HttpMessageHandlerAdapter.Dispose">
      <summary>Releases all resources used by the current instance of the <see cref="T:System.Web.Http.Owin.HttpMessageHandlerAdapter" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.Owin.HttpMessageHandlerAdapter.Dispose(System.Boolean)">
      <summary>Releases unmanaged and optionally managed resources.</summary>
      <param name="disposing">true to release both managed and unmanaged resources; false to release only unmanaged resources.</param>
    </member>
    <member name="P:System.Web.Http.Owin.HttpMessageHandlerAdapter.ExceptionHandler">
      <summary>Gets the <see cref="T:System.Web.Http.ExceptionHandling.IExceptionHandler" /> to use to process unhandled exceptions.</summary>
      <returns>The <see cref="T:System.Web.Http.ExceptionHandling.IExceptionHandler" /> to use to process unhandled exceptions.</returns>
    </member>
    <member name="P:System.Web.Http.Owin.HttpMessageHandlerAdapter.ExceptionLogger">
      <summary>Gets the <see cref="T:System.Web.Http.ExceptionHandling.IExceptionLogger" /> to use to log unhandled exceptions.</summary>
      <returns>The <see cref="T:System.Web.Http.ExceptionHandling.IExceptionLogger" /> to use to log unhandled exceptions.</returns>
    </member>
    <member name="M:System.Web.Http.Owin.HttpMessageHandlerAdapter.Invoke(Microsoft.Owin.IOwinContext)">
      <returns>Returns <see cref="T:System.Threading.Tasks.Task" />.</returns>
    </member>
    <member name="P:System.Web.Http.Owin.HttpMessageHandlerAdapter.MessageHandler">
      <summary>Gets the <see cref="T:System.Net.Http.HttpMessageHandler" /> to submit requests to.</summary>
      <returns>The <see cref="T:System.Net.Http.HttpMessageHandler" /> to submit requests to.</returns>
    </member>
    <member name="T:System.Web.Http.Owin.HttpMessageHandlerOptions">
      <summary>Represents the options for configuring an <see cref="T:System.Web.Http.Owin.HttpMessageHandlerAdapter" />.</summary>
    </member>
    <member name="M:System.Web.Http.Owin.HttpMessageHandlerOptions.#ctor">
      <summary>Initializes a new instance of the<see cref="T:System.Web.Http.Owin.HttpMessageHandlerOptions" /> class.</summary>
    </member>
    <member name="P:System.Web.Http.Owin.HttpMessageHandlerOptions.AppDisposing">
      <summary>Gets or sets the <see cref="T:System.Threading.CancellationToken" /> that triggers cleanup of the <see cref="T:System.Web.Http.Owin.HttpMessageHandlerAdapter" />.</summary>
      <returns>The <see cref="T:System.Threading.CancellationToken" /> that triggers cleanup of the <see cref="T:System.Web.Http.Owin.HttpMessageHandlerAdapter" />.</returns>
    </member>
    <member name="P:System.Web.Http.Owin.HttpMessageHandlerOptions.BufferPolicySelector">
      <summary>Gets or sets the <see cref="T:System.Web.Http.Hosting.IHostBufferPolicySelector" /> that determines whether or not to buffer requests and responses.</summary>
      <returns>The <see cref="T:System.Web.Http.Hosting.IHostBufferPolicySelector" /> that determines whether or not to buffer requests and responses.</returns>
    </member>
    <member name="P:System.Web.Http.Owin.HttpMessageHandlerOptions.ExceptionHandler">
      <summary>Gets or sets the <see cref="T:System.Web.Http.ExceptionHandling.IExceptionHandler" /> to use to process unhandled exceptions.</summary>
      <returns>The <see cref="T:System.Web.Http.ExceptionHandling.IExceptionHandler" /> to use to process unhandled exceptions.</returns>
    </member>
    <member name="P:System.Web.Http.Owin.HttpMessageHandlerOptions.ExceptionLogger">
      <summary>Gets or sets the <see cref="T:System.Web.Http.ExceptionHandling.IExceptionLogger" /> to use to log unhandled exceptions.</summary>
      <returns>The <see cref="T:System.Web.Http.ExceptionHandling.IExceptionLogger" /> to use to log unhandled exceptions.</returns>
    </member>
    <member name="P:System.Web.Http.Owin.HttpMessageHandlerOptions.MessageHandler">
      <summary>Gets or sets the <see cref="T:System.Net.Http.HttpMessageHandler" /> to submit requests to.</summary>
      <returns>The <see cref="T:System.Net.Http.HttpMessageHandler" /> to submit requests to.</returns>
    </member>
    <member name="T:System.Web.Http.Owin.OwinBufferPolicySelector">
      <summary>Provides the default implementation of <see cref="T:System.Web.Http.Hosting.IHostBufferPolicySelector" /> used by the OWIN Web API adapter.</summary>
    </member>
    <member name="M:System.Web.Http.Owin.OwinBufferPolicySelector.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Owin.OwinBufferPolicySelector" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.Owin.OwinBufferPolicySelector.UseBufferedInputStream(System.Object)">
      <summary>Determines whether the host should buffer the HttpRequestMessage entity body.</summary>
      <returns>true if buffering should be used; otherwise a streamed request should be used.</returns>
      <param name="hostContext">The host context.</param>
    </member>
    <member name="M:System.Web.Http.Owin.OwinBufferPolicySelector.UseBufferedOutputStream(System.Net.Http.HttpResponseMessage)">
      <summary>Determines whether the host should buffer the HttpResponseMessage entity body.</summary>
      <returns>true if buffering should be used; otherwise a streamed response should be used.</returns>
      <param name="response">The response.</param>
    </member>
    <member name="T:System.Web.Http.Owin.OwinExceptionCatchBlocks">
      <summary>Provides the catch blocks used within this assembly.</summary>
    </member>
    <member name="P:System.Web.Http.Owin.OwinExceptionCatchBlocks.HttpMessageHandlerAdapterBufferContent">
      <summary>Gets the catch block in System.Web.Http.Owin.HttpMessageHandlerAdapter.BufferContent.</summary>
      <returns>The catch block in System.Web.Http.Owin.HttpMessageHandlerAdapter.BufferContent.</returns>
    </member>
    <member name="P:System.Web.Http.Owin.OwinExceptionCatchBlocks.HttpMessageHandlerAdapterBufferError">
      <summary>Gets the catch block in System.Web.Http.Owin.HttpMessageHandlerAdapter.BufferError.</summary>
      <returns>The catch block in System.Web.Http.Owin.HttpMessageHandlerAdapter.BufferError.</returns>
    </member>
    <member name="P:System.Web.Http.Owin.OwinExceptionCatchBlocks.HttpMessageHandlerAdapterComputeContentLength">
      <summary>Gets the catch block in System.Web.Http.Owin.HttpMessageHandlerAdapter.ComputeContentLength.</summary>
      <returns>The catch block in System.Web.Http.Owin.HttpMessageHandlerAdapter.ComputeContentLength.</returns>
    </member>
    <member name="P:System.Web.Http.Owin.OwinExceptionCatchBlocks.HttpMessageHandlerAdapterStreamContent">
      <summary>Gets the catch block in System.Web.Http.Owin.HttpMessageHandlerAdapter.StreamContent.</summary>
      <returns>The catch block in System.Web.Http.Owin.HttpMessageHandlerAdapter.StreamContent.</returns>
    </member>
    <member name="T:System.Web.Http.Owin.PassiveAuthenticationMessageHandler">
      <summary>Represents a message handler that treats all OWIN authentication middleware as passive.</summary>
    </member>
    <member name="M:System.Web.Http.Owin.PassiveAuthenticationMessageHandler.#ctor">
      <summary>Initializes a new instance of the <see cref="T:System.Web.Http.Owin.PassiveAuthenticationMessageHandler" /> class.</summary>
    </member>
    <member name="M:System.Web.Http.Owin.PassiveAuthenticationMessageHandler.SendAsync(System.Net.Http.HttpRequestMessage,System.Threading.CancellationToken)">
      <summary>Asynchronously sends a message request.</summary>
      <returns>The task that completes the asynchronous operation.</returns>
      <param name="request">The message request.</param>
      <param name="cancellationToken">The cancellation token.</param>
    </member>
  </members>
</doc>