using Alsi.Fuzz.Core.Service.CaseFactory.Core;
using System.Collections.Generic;

namespace Alsi.Fuzz.Core.Service.CaseFactory.Factories.Iso15765
{
    public class G42_CaseFactory : CaseFactoryBase
    {
        public override CaseMutation[] Generate(MutationOptions options)
        {
            var list = new List<CaseMutation>();

            // 定义要测试的 SN 值
            int[] targetSNs = new[] { 1, 30, 59, 88, 117, 146, 175, 204, 233, 262, 291, 320, 349, 378, 407, 436, 465, 494, 523, 552, 581 };

            // 定义重复次数
            int[] repeatTimes = new[] { 5, 50, 100, 2000 };

            // 固定的数据包大小
            int ffDl = 4095;

            // TestCondition-1: Repeat 场景
            GenerateRepeatScenarios(list, targetSNs, repeatTimes, ffDl);

            // TestCondition-2: Skip 场景
            GenerateSkipScenarios(list, targetSNs, ffDl);

            // TestCondition-3: Swap 场景
            GenerateSwapScenarios(list, targetSNs, ffDl);

            // TestCondition-4: Reverse 场景
            GenerateReverseScenarios(list, targetSNs, ffDl);

            return list.ToArray();
        }

        private const byte repeatByte = 0x50;

        // TestCondition-1: Repeat 场景
        private void GenerateRepeatScenarios(List<CaseMutation> list, int[] targetSNs, int[] repeatTimes, int ffDl)
        {
            // 1.1 重复 FirstFrame
            foreach (var repeatTime in repeatTimes)
            {
                var caseMutation = CaseMutation.Create($"G421-Repeat_FF_{repeatTime}")
                    .MutateDlc(8)
                    .Mutate(MutationFieldType.TP_BigData_Repeat_Index, "0") // 0 表示 FirstFrame
                    .Mutate(MutationFieldType.TP_BigData_Repeat_Times, repeatTime.ToString())
                    .MutateTpParameters(repeatByte, ffDl);
                list.Add(caseMutation);
            }

            // 1.2 重复特定 SN 的 ConsecutiveFrame
            foreach (var targetSN in targetSNs)
            {
                foreach (var repeatTime in repeatTimes)
                {
                    var caseMutation = CaseMutation.Create($"G421-Repeat_CF{targetSN}_{repeatTime}")
                        .MutateDlc(8)
                        .Mutate(MutationFieldType.TP_BigData_Repeat_Index, targetSN.ToString())
                        .Mutate(MutationFieldType.TP_BigData_Repeat_Times, repeatTime.ToString())
                        .MutateTpParameters(repeatByte, ffDl);
                    list.Add(caseMutation);
                }
            }
        }

        // TestCondition-2: Skip 场景
        private void GenerateSkipScenarios(List<CaseMutation> list, int[] targetSNs, int ffDl)
        {
            foreach (var targetSN in targetSNs)
            {
                var caseMutation = CaseMutation.Create($"G422-Skip_CF{targetSN}")
                    .MutateDlc(8)
                    .Mutate(MutationFieldType.TP_BigData_Skip_Index, targetSN.ToString())
                    .MutateTpParameters(repeatByte, ffDl);
                list.Add(caseMutation);
            }
        }

        // TestCondition-3: Swap 场景
        private void GenerateSwapScenarios(List<CaseMutation> list, int[] targetSNs, int ffDl)
        {
            // 3.1 交换 SN=1 和 SN=585（最后一帧）
            {
                var caseMutation = CaseMutation.Create($"G423-Swap_CF1_CF585")
                    .MutateDlc(8)
                    .Mutate(MutationFieldType.TP_BigData_Swap_A, "1")
                    .Mutate(MutationFieldType.TP_BigData_Swap_B, "585")
                    .MutateTpParameters(repeatByte, ffDl);
                list.Add(caseMutation);
            }

            // 3.2 交换特定 SN 和 (585-SN)
            foreach (var targetSN in targetSNs)
            {
                if (targetSN == 1) continue; // 已在 3.1 中处理

                int swapTo = 585 - targetSN;
                var caseMutation = CaseMutation.Create($"G423-Swap_CF{targetSN}_CF{swapTo}")
                    .MutateDlc(8)
                    .Mutate(MutationFieldType.TP_BigData_Swap_A, targetSN.ToString())
                    .Mutate(MutationFieldType.TP_BigData_Swap_B, swapTo.ToString())
                    .MutateTpParameters(repeatByte, ffDl);
                list.Add(caseMutation);
            }

            // 3.3 交换特定 SN 和 SN=1
            foreach (var targetSN in targetSNs)
            {
                if (targetSN == 1) continue; // 避免自交换

                var caseMutation = CaseMutation.Create($"G423-Swap_CF{targetSN}_CF1")
                    .MutateDlc(8)
                    .Mutate(MutationFieldType.TP_BigData_Swap_A, targetSN.ToString())
                    .Mutate(MutationFieldType.TP_BigData_Swap_B, "1")
                    .MutateTpParameters(repeatByte, ffDl);
                list.Add(caseMutation);
            }
        }

        // TestCondition-4: Reverse 场景
        private void GenerateReverseScenarios(List<CaseMutation> list, int[] targetSNs, int ffDl)
        {
            // 4.1 从 FirstFrame 到 SN=585 的反转
            {
                var caseMutation = CaseMutation.Create($"G424-Reverse_FF_CF585")
                    .MutateDlc(8)
                    .Mutate(MutationFieldType.TP_BigData_Reverse_From, "0") // 0 表示 FirstFrame
                    .Mutate(MutationFieldType.TP_BigData_Reverse_To, "585")
                    .MutateTpParameters(repeatByte, ffDl);
                list.Add(caseMutation);
            }

            // 4.2 从 SN=1 到 SN=585 的反转
            {
                var caseMutation = CaseMutation.Create($"G424-Reverse_CF1_CF585")
                    .MutateDlc(8)
                    .Mutate(MutationFieldType.TP_BigData_Reverse_From, "1")
                    .Mutate(MutationFieldType.TP_BigData_Reverse_To, "585")
                    .MutateTpParameters(repeatByte, ffDl);
                list.Add(caseMutation);
            }

            // 4.3 从特定 SN 到 SN=585 的反转
            foreach (var targetSN in targetSNs)
            {
                if (targetSN == 1) continue; // 已在 4.2 中处理

                var caseMutation = CaseMutation.Create($"G424-Reverse_CF{targetSN}_CF585")
                    .MutateDlc(8)
                    .Mutate(MutationFieldType.TP_BigData_Reverse_From, targetSN.ToString())
                    .Mutate(MutationFieldType.TP_BigData_Reverse_To, "585")
                    .MutateTpParameters(repeatByte, ffDl);
                list.Add(caseMutation);
            }
        }
    }
}
