using Alsi.Fuzz.Core.Models.TestSuites.Steps;
using Alsi.Fuzz.Core.Models.TestSuites.Steps.Diagnostic;
using Alsi.Fuzz.Core.Models.TestSuites.Steps.Isotp;
using System.Collections.Generic;
using System.Xml.Serialization;

namespace Alsi.Fuzz.Core.Models.TestSuites
{
    public class Sequence
    {
        [XmlAttribute("name")]
        public string Name { get; set; }

        [XmlElement("wait", typeof(WaitStep))]
        [XmlElement("print", typeof(PrintStep))]
        [XmlElement("send", typeof(SendStep))]
        [XmlElement("recv", typeof(ReceiveStep))]
        [XmlElement("send-diag", typeof(SendDiagStep))]
        [XmlElement("recv-diag", typeof(RecvDiagStep))]
        [XmlElement("calc-key", typeof(CalcKeyStep))]
        [XmlElement("send-isotp", typeof(SendIsotpStep))]
        [XmlElement("recv-isotp", typeof(RecvIsotpStep))]
        public List<StepBase> Steps { get; set; } = new List<StepBase>();
    }
}
