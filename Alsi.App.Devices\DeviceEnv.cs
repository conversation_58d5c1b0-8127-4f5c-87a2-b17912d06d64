﻿using Alsi.App.Devices.Core;
using Alsi.App.Devices.TsLibCan;
using Alsi.App.Devices.Vector;
using System.Collections.Generic;
using System.Linq;

namespace Alsi.App.Devices
{
    public static class DeviceEnv
    {
        public static DeviceChannel[] GetDeviceChannels()
        {
            var list = new List<DeviceChannel>();
            list.AddRange(EncapsulationLibTsCan.GetDeviceChannels());
            list.AddRange(EncapsulationVectorCan.GetDeviceChannels());
            return list.OrderBy(x => x.Name).ToArray();
        }

        public static bool TryGetDeviceChannel(string name, out DeviceChannel deviceChannel)
        {
            deviceChannel = GetDeviceChannels().FirstOrDefault(x => x.Name == name);
            return deviceChannel != null;
        }
    }
}
