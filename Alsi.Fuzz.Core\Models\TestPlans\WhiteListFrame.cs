using System;

namespace Alsi.Fuzz.Core.Models.TestPlans
{
    public class WhiteListFrame
    {
        public WhiteListFrame(uint id, string name, byte dlc, string transmitter, string[] receivers)
        {
            if (id > 0x80000000)
            {
                Id = (int)(id - 0x80000000);
                IsExt = true;
            }
            else
            {
                Id = (int)id;
                IsExt = false;
            }

            Name = name;
            Dlc = dlc;
            Transmitter = transmitter;
            Receivers = receivers;
        }

        public int Id { get; set; }
        public string Name { get; set; }
        public byte Dlc { get; set; }
        public bool IsExt { get; set; }
        public string Transmitter { get; set; }
        public string[] Receivers { get; set; } = Array.Empty<string>();
    }
}
