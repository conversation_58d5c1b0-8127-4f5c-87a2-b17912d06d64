<template>
  <el-tooltip 
    v-if="begin" 
    :content="tooltipContent" 
    placement="top" 
    effect="dark"
    raw-content
  >
    <span class="time-display">{{ formattedTime }}</span>
  </el-tooltip>
</template>

<script setup lang="ts">
import { computed, defineProps } from 'vue';
import { ElTooltip } from 'element-plus';

const props = defineProps<{
  begin: string | null;
  end?: string | null;
  showDuration?: boolean;
}>();

const formatDate = (date: Date): string => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');
  const milliseconds = String(date.getMilliseconds()).padStart(3, '0');
  
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}.${milliseconds}`;
};

const formatTimeOnly = (date: Date): string => {
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');
  return `${hours}:${minutes}:${seconds}`;
};

const tooltipContent = computed((): string => {
  if (!props.begin) return '';
  
  const beginDate = new Date(props.begin);
  const beginFull = formatDate(beginDate);
  
  if (!props.end) return `Start: ${beginFull}`;
  
  const endDate = new Date(props.end);
  const endFull = formatDate(endDate);
  const durationMs = endDate.getTime() - beginDate.getTime();
  
  let durationText = '';
  if (durationMs < 1000) {
    durationText = `${durationMs} ms`;
  } else {
    const seconds = Math.floor(durationMs / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) {
      durationText = `${hours}h ${minutes % 60}m ${seconds % 60}s`;
    } else if (minutes > 0) {
      durationText = `${minutes}m ${seconds % 60}s`;
    } else {
      durationText = `${seconds}s`;
    }
  }
  
  return `Duration: ${durationText}<br>Start: ${beginFull}<br>End: ${endFull}`;
});

const formattedTime = computed((): string => {
  if (!props.begin) return '-';
  
  const beginDate = new Date(props.begin);
  const beginStr = formatTimeOnly(beginDate);
  
  if (!props.end || !props.showDuration) return beginStr;
  
  const endDate = new Date(props.end);
  const durationMs = endDate.getTime() - beginDate.getTime();
  
  if (durationMs < 1000) {
    return `Started at ${beginStr}, elapsed ${durationMs}ms`;
  } else {
    const seconds = Math.round(durationMs / 1000);
    return `Started at ${beginStr}, elapsed ${seconds}s`;
  }
});
</script>

<style scoped>
.time-display {
  font-size: 11px;
  color: var(--el-text-color-secondary);
}
</style>
