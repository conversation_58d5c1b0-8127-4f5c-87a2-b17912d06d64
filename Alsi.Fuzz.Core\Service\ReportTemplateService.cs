using Alsi.Common.Utils;
using Alsi.Fuzz.Core.Service.Results;
using System;
using System.Collections.Generic;
using System.IO;
using System.Reflection;

namespace Alsi.Fuzz.Core.Service
{
    public static class ReportTemplateService
    {
        private static string GetReportTemplate()
        {
            var assembly = Assembly.GetExecutingAssembly();
            var resourceName = "Alsi.Fuzz.Core.Resources.report-template.html";

            using (var stream = assembly.GetManifestResourceStream(resourceName))
            {
                if (stream == null)
                {
                    throw new FileNotFoundException($"未找到嵌入资源: {resourceName}");
                }

                using (var reader = new StreamReader(stream))
                {
                    return reader.ReadToEnd();
                }
            }
        }

        public static string GenerateHtmlReport(TestResult testResult, IEnumerable<CaseResult> caseResults)
        {
            if (testResult == null)
            {
                throw new ArgumentNullException(nameof(testResult));
            }

            // 读取HTML模板
            string template = GetReportTemplate();

            // 计算成功率
            double successRate = 0;
            if (testResult.TotalCount > 0)
            {
                successRate = (double)testResult.SuccessCount / testResult.TotalCount * 100;
            }

            // 将测试结果和成功率一起序列化
            var testResultWithRate = new
            {
                testResult.Id,
                testResult.TestType,
                testResult.TestPlanName,
                testResult.ResultFolderName,
                testResult.TotalCount,
                testResult.SuccessCount,
                testResult.FailureCount,
                testResult.Begin,
                testResult.End,
                testResult.CreationTime,
                SuccessRate = Math.Round(successRate, 2)
            };

            // 序列化测试结果和测试用例为JSON
            var testResultJson = JsonUtils.Serialize(testResultWithRate);
            var caseResultsJson = JsonUtils.Serialize(caseResults ?? new List<CaseResult>());

            // 替换模板中的JavaScript变量
            template = template.Replace("// TEST-RESULT-JSON-TEMPLATE", $"testResult = {testResultJson};")
                               .Replace("// TEST-CASE-JSON-TEMPLATE", $"caseResults = {caseResultsJson};");

            return template;
        }
    }
}
