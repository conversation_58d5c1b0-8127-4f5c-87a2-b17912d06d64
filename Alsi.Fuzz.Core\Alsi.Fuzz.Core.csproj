﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{9C5C66B4-B1FA-4F41-9DAF-3779C5940C53}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Alsi.Fuzz.Core</RootNamespace>
    <AssemblyName>Alsi.Fuzz.Core</AssemblyName>
    <TargetFrameworkVersion>v4.6.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\x64\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <LangVersion>7.3</LangVersion>
    <ErrorReport>prompt</ErrorReport>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <OutputPath>bin\x64\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <LangVersion>7.3</LangVersion>
    <ErrorReport>prompt</ErrorReport>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="FreeSql, Version=*********, Culture=neutral, PublicKeyToken=a33928e5d4a4b39c, processorArchitecture=MSIL">
      <HintPath>..\packages\FreeSql.3.5.106\lib\net451\FreeSql.dll</HintPath>
    </Reference>
    <Reference Include="ICSharpCode.SharpZipLib, Version=********, Culture=neutral, PublicKeyToken=1b03e6acf1164f73, processorArchitecture=MSIL">
      <HintPath>..\packages\SharpZipLib.1.4.2\lib\netstandard2.0\ICSharpCode.SharpZipLib.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=********, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Buffers, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Buffers.4.5.1\lib\net461\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.Memory, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.4.5.5\lib\net461\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Numerics.Vectors.4.5.0\lib\net46\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=6.0.2.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.6.1.1\lib\net462\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions, Version=4.2.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Threading.Tasks.Extensions.4.5.4\lib\net461\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Contracts\Tester\ExecutionRequest.cs" />
    <Compile Include="FuzzConsts.cs" />
    <Compile Include="Models\History\TestPlanHistory.cs" />
    <Compile Include="Models\Tester\EnvVars.cs" />
    <Compile Include="Models\Tester\TesterProcess.cs" />
    <Compile Include="Models\TestPlans\CaseConfig.cs" />
    <Compile Include="Models\TestPlans\SecurityConfig.cs" />
    <Compile Include="Models\TestPlans\SequenceConfig.cs" />
    <Compile Include="Models\TestPlans\SequenceConfigDto.cs" />
    <Compile Include="Models\TestPlans\TestPlanConfig.cs" />
    <Compile Include="Models\TestPlans\WhiteListFrame.cs" />
    <Compile Include="Models\TestSuites\EnvVar.cs" />
    <Compile Include="Models\TestSuites\SetVar.cs" />
    <Compile Include="Models\TestSuites\Steps\CalcKeyStep.cs" />
    <Compile Include="Models\TestSuites\Steps\Diagnostic\MatchDiag.cs" />
    <Compile Include="Models\TestSuites\Steps\Diagnostic\RecvDiagStep.cs" />
    <Compile Include="Models\TestSuites\Steps\Isotp\RecvIsotpStep.cs" />
    <Compile Include="Models\TestSuites\Steps\Diagnostic\SendDiagStep.cs" />
    <Compile Include="Models\TestSuites\Steps\Isotp\SendIsotpStep.cs" />
    <Compile Include="Models\TestSuites\Steps\MatchFrame.cs" />
    <Compile Include="Models\TestSuites\Steps\Frame.cs" />
    <Compile Include="Models\TestSuites\Steps\PrintStep.cs" />
    <Compile Include="Models\TestSuites\Steps\ReceiveStep.cs" />
    <Compile Include="Models\TestSuites\Steps\SendStep.cs" />
    <Compile Include="Models\TestSuites\Sequence.cs" />
    <Compile Include="Models\TestSuites\SequencePackage.cs" />
    <Compile Include="Models\TestSuites\Steps\StepBase.cs" />
    <Compile Include="Models\TestSuites\Steps\WaitStep.cs" />
    <Compile Include="Models\TestSuites\Store.cs" />
    <Compile Include="Models\TestSuites\TestSuite.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Service\BuiltInTestSuiteService.cs" />
    <Compile Include="Service\CaseFactory\Core\CaseMutation.cs" />
    <Compile Include="Service\CaseFactory\Core\IsoType.cs" />
    <Compile Include="Service\CaseFactory\Core\MutationFactoryDiscoverer.cs" />
    <Compile Include="Service\CaseFactory\CaseFactoryUtils.cs" />
    <Compile Include="Service\CaseFactory\Core\MutationField.cs" />
    <Compile Include="Service\CaseFactory\Core\MutationFieldType.cs" />
    <Compile Include="Service\CaseFactory\Factories\Iso11898\G22_CaseFactory.cs" />
    <Compile Include="Service\CaseFactory\Factories\Iso11898\G21_CaseFactory.cs" />
    <Compile Include="Service\CaseFactory\Factories\Iso11898\G12_CaseFactory.cs" />
    <Compile Include="Service\CaseFactory\Factories\Iso11898\G11_CaseFactory.cs" />
    <Compile Include="Service\CaseFactory\Core\MutationOptions.cs" />
    <Compile Include="Service\CaseFactory\Core\ICaseFactory.cs" />
    <Compile Include="Service\CaseFactory\Factories\Iso14229\CaseFactoryBase.cs" />
    <Compile Include="Service\CaseFactory\Factories\Iso14229\Consts\Subfunction.cs" />
    <Compile Include="Service\CaseFactory\Factories\Iso14229\Consts\UdsService.cs" />
    <Compile Include="Service\CaseFactory\Factories\Iso14229\Consts\IsoUdsConsts.cs" />
    <Compile Include="Service\CaseFactory\Factories\Iso14229\G3111_G3112_G3113_CaseFactory.cs" />
    <Compile Include="Service\CaseFactory\Factories\Iso14229\G3211_CaseFactory.cs" />
    <Compile Include="Service\CaseFactory\Factories\Iso14229\G3311_G3312_G3313_G3314_CaseFactory.cs" />
    <Compile Include="Service\CaseFactory\Factories\Iso14229\G3141_G3151_CaseFactory.cs" />
    <Compile Include="Service\CaseFactory\Factories\Iso14229\G3131_G3132_CaseFactory.cs" />
    <Compile Include="Service\CaseFactory\Factories\Iso14229\G3125_CaseFactory.cs" />
    <Compile Include="Service\CaseFactory\Factories\Iso14229\G3124_CaseFactory.cs" />
    <Compile Include="Service\CaseFactory\Factories\Iso14229\G3121_G3122_G3123_CaseFactory.cs" />
    <Compile Include="Service\CaseFactory\Factories\Iso14229\G3114_CaseFactory.cs" />
    <Compile Include="Service\CaseFactory\Factories\Iso14229\G3115_G3116_G3117_CaseFactory.cs" />
    <Compile Include="Service\CaseFactory\Factories\Iso14229\G42_CaseFactory.cs" />
    <Compile Include="Service\CaseFactory\Factories\Iso14229\G41_CaseFactory.cs" />
    <Compile Include="Service\CaseFactory\Factories\Iso14229\G341_G342_CaseFactory.cs" />
    <Compile Include="Service\CaseFactory\Factories\Iso14229\G3321_G3322_G3323_CaseFactory.cs" />
    <Compile Include="Service\CaseFactory\Factories\Iso14229\G431_G432_CaseFactory.cs" />
    <Compile Include="Service\CaseFactory\Factories\Iso15765\CaseFactoryBase.cs" />
    <Compile Include="Service\CaseFactory\Factories\Iso15765\G31_G32_CaseFactory.cs" />
    <Compile Include="Service\CaseFactory\Factories\Iso15765\G33_G34_CaseFactory.cs" />
    <Compile Include="Service\CaseFactory\Factories\Iso15765\G42_CaseFactory.cs" />
    <Compile Include="Service\CaseFactory\Factories\Iso15765\G41_CaseFactory.cs" />
    <Compile Include="Service\CaseFactory\Factories\Iso15765\G37_CaseFactory.cs" />
    <Compile Include="Service\CaseFactory\Factories\Iso15765\G36_CaseFactory.cs" />
    <Compile Include="Service\CaseFactory\Factories\Iso15765\G35_CaseFactory.cs" />
    <Compile Include="Service\CaseService.cs" />
    <Compile Include="Service\DeviceScanner.cs" />
    <Compile Include="Service\HardwareService.cs" />
    <Compile Include="Service\FileLocker.cs" />
    <Compile Include="Service\IHardwareService.cs" />
    <Compile Include="Service\ReportTemplateService.cs" />
    <Compile Include="Service\Results\CaseResult.cs" />
    <Compile Include="Service\Results\CaseStep.cs" />
    <Compile Include="Service\Results\ITestResultReaderService.cs" />
    <Compile Include="Service\Results\ResultContext.cs" />
    <Compile Include="Service\Results\Sequence.cs" />
    <Compile Include="Service\Results\TestResult.cs" />
    <Compile Include="Service\Results\TestResultReaderService.cs" />
    <Compile Include="Service\Results\TestResultWriterService.cs" />
    <Compile Include="Service\Results\ExecutionState.cs" />
    <Compile Include="Service\Results\TestType.cs" />
    <Compile Include="Service\StatusPollingService.cs" />
    <Compile Include="Service\Tester\ITesterManager.cs" />
    <Compile Include="Service\ITestPlanHistoryService.cs" />
    <Compile Include="Service\ITestPlanService.cs" />
    <Compile Include="Service\Tester\TesterConsts.cs" />
    <Compile Include="Service\Tester\TesterManager.cs" />
    <Compile Include="Service\Tester\ApiResponse.cs" />
    <Compile Include="Service\Tester\ITesterApiClient.cs" />
    <Compile Include="Service\Tester\TesterApiClient.cs" />
    <Compile Include="Service\TestPlanHistoryService.cs" />
    <Compile Include="Service\TestPlanManager.cs" />
    <Compile Include="Service\TestPlanService.cs" />
    <Compile Include="Models\TestPlans\HardwareConfig.cs" />
    <Compile Include="Models\TestPlans\TestPlanManifest.cs" />
    <Compile Include="Models\TestPlans\TestPlan.cs" />
    <Compile Include="Service\Results\ITestResultWriterService.cs" />
    <Compile Include="Storage\TestPlanStorage.cs" />
    <Compile Include="Storage\DataLogStorage.cs" />
    <Compile Include="Utils\LhsSampler.cs" />
    <Compile Include="Utils\SecurityAccessUtils.cs" />
    <Compile Include="Utils\SequencePackageUtils.cs" />
    <Compile Include="Utils\SequenceUtils.cs" />
    <Compile Include="Utils\UniformSampler.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="packages.config" />
    <EmbeddedResource Include="Resources\sequences-11898.xml" />
    <EmbeddedResource Include="Resources\sequences-14229.xml" />
    <EmbeddedResource Include="Resources\report-template.html" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\Alsi.Common\Alsi.App.Database\Alsi.App.Database.csproj">
      <Project>{11c6fa99-c7d1-43b0-bc29-3bbd3d4e0eb9}</Project>
      <Name>Alsi.App.Database</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Alsi.Common\Alsi.App\Alsi.App.csproj">
      <Project>{2BF46D86-9704-494A-8998-A478B601DF80}</Project>
      <Name>Alsi.App</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Alsi.Common\Alsi.Common.Utils\Alsi.Common.Utils.csproj">
      <Project>{f6626691-d7f8-483c-9423-ed1c6e9214f7}</Project>
      <Name>Alsi.Common.Utils</Name>
    </ProjectReference>
    <ProjectReference Include="..\Alsi.App.Devices\Alsi.App.Devices.csproj">
      <Project>{B5BD0CC8-03EB-4319-8B67-0FA697B43553}</Project>
      <Name>Alsi.App.Devices</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Resources\sequences-15765.xml" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>