using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text.RegularExpressions;

namespace Alsi.Fuzz.Core.Utils
{
    public static class SequenceUtils
    {
        public static uint ParseUint(string value)
        {
            value = value.Trim();
            if (value.StartsWith("hex:", StringComparison.OrdinalIgnoreCase))
            {
                value = value.Substring(4);
                return uint.Parse(value, NumberStyles.HexNumber);
            }
            else if (value.StartsWith("0x", StringComparison.OrdinalIgnoreCase))
            {
                value = value.Substring(2);
                return uint.Parse(value, NumberStyles.HexNumber);
            }
            else
            {
                return uint.Parse(value);
            }
        }

        public static int ParseInt(string value)
        {
            value = value.Trim();
            if (value.StartsWith("hex:", StringComparison.OrdinalIgnoreCase))
            {
                value = value.Substring(4);
                return int.Parse(value, NumberStyles.HexNumber);
            }
            else if (value.StartsWith("0x", StringComparison.OrdinalIgnoreCase))
            {
                value = value.Substring(2);
                return int.Parse(value, NumberStyles.HexNumber);
            }
            else
            {
                return int.Parse(value);
            }
        }

        public static byte ParseByte(string value)
        {
            value = value.Trim();
            if (value.StartsWith("hex:", StringComparison.OrdinalIgnoreCase))
            {
                value = value.Substring(4);
                return byte.Parse(value, NumberStyles.HexNumber);
            }
            else if (value.StartsWith("0x", StringComparison.OrdinalIgnoreCase))
            {
                value = value.Substring(2);
                return byte.Parse(value, NumberStyles.HexNumber);
            }
            else
            {
                return byte.Parse(value);
            }
        }

        public static byte[] ParseBytes(string value)
        {
            var matchBytes = ParseMatchBytes(value);
            if (matchBytes.Any(x => !x.HasValue))
            {
                throw new Exception($"Parse bytes failed: {value}");
            }
            
            return matchBytes.Select(x => x.Value).ToArray();
        }

        public static byte?[] ParseMatchBytes(string value)
        {
            value = value.Trim();
            string originalValue = value; // 保存原始字符串用于确定标签位置

            // 获取所有标签及其位置
            var repeatMatch = Regex.Match(originalValue, @"repeat:\s*(\d+)", RegexOptions.IgnoreCase);
            var takeMatch = Regex.Match(originalValue, @"take:\s*(\d+)", RegexOptions.IgnoreCase);
            
            // 存储操作及其在字符串中的位置
            var operations = new List<Tuple<string, int, int>>();
            
            // 记录并移除标签
            if (repeatMatch.Success)
            {
                int repeatCount = int.Parse(repeatMatch.Groups[1].Value);
                if (repeatCount < 1)
                    throw new FormatException($"Invalid repeat count: {repeatCount}");
                    
                operations.Add(new Tuple<string, int, int>("repeat", repeatCount, repeatMatch.Index));
                
                // 从处理字符串中移除repeat部分
                value = Regex.Replace(value, @"repeat:\s*\d+\s*", "", RegexOptions.IgnoreCase).Trim();
            }
            
            if (takeMatch.Success)
            {
                int takeCount = int.Parse(takeMatch.Groups[1].Value);
                if (takeCount < 1)
                    throw new FormatException($"Invalid take count: {takeCount}");
                    
                operations.Add(new Tuple<string, int, int>("take", takeCount, takeMatch.Index));
                
                // 从处理字符串中移除take部分
                value = Regex.Replace(value, @"take:\s*\d+\s*", "", RegexOptions.IgnoreCase).Trim();
            }
            
            // 按照标签在原字符串中的位置排序操作
            operations = operations.OrderBy(o => o.Item3).ToList();
            
            // 解析基本字节数组
            byte?[] bytes = ParseBasicBytes(value);
            
            // 按顺序应用操作
            foreach (var operation in operations)
            {
                string opType = operation.Item1;
                int opValue = operation.Item2;
                
                if (opType == "repeat" && opValue > 1)
                {
                    var originalBytes = bytes;
                    bytes = new byte?[originalBytes.Length * opValue];
                    
                    for (int i = 0; i < opValue; i++)
                    {
                        for (int j = 0; j < originalBytes.Length; j++)
                        {
                            bytes[i * originalBytes.Length + j] = originalBytes[j];
                        }
                    }
                }
                else if (opType == "take" && opValue < bytes.Length)
                {
                    bytes = bytes.Take(opValue).ToArray();
                }
            }
            
            return bytes;
        }

        private static byte?[] ParseBasicBytes(string value)
        {
            value = value.Trim();
            var bytes = new List<byte?>();

            // 处理hex:前缀格式
            if (value.StartsWith("hex:", StringComparison.OrdinalIgnoreCase))
            {
                var hexValue = value.Substring(4).Trim();

                // 检查是否包含分隔符
                if (hexValue.Contains(" ") || hexValue.Contains(","))
                {
                    // 按空格或逗号分隔的十六进制值
                    var items = hexValue.Split(new[] { ' ', ',' }, StringSplitOptions.RemoveEmptyEntries);
                    foreach (var item in items)
                    {
                        bytes.Add(InternalParseMatchByte(item));
                    }

                    return bytes.ToArray();
                }
                else
                {
                    // 连续的十六进制字符串，每两个字符一个字节
                    // 如果长度为奇数，前面补0
                    if (hexValue.Length % 2 != 0)
                    {
                        hexValue = "0" + hexValue;
                    }

                    for (int i = 0; i < hexValue.Length; i += 2)
                    {
                        var hexByte = hexValue.Substring(i, 2);
                        bytes.Add(InternalParseMatchByte(hexByte));
                    }

                    return bytes.ToArray();
                }
            }

            // 原有逻辑处理
            var originalItems = value.Split(new[] { ' ', ',' }, StringSplitOptions.RemoveEmptyEntries);

            foreach (var item in originalItems)
            {
                if (item.StartsWith("0x", StringComparison.OrdinalIgnoreCase))
                {
                    // 处理十六进制格式
                    string hexValue = item.Substring(2);

                    // 如果长度为奇数，前面补0
                    if (hexValue.Length % 2 != 0)
                    {
                        hexValue = "0" + hexValue;
                    }

                    // 将十六进制字符串按每两个字符解析为一个字节
                    for (int i = 0; i < hexValue.Length; i += 2)
                    {
                        string hexByte = hexValue.Substring(i, 2);
                        bytes.Add(InternalParseMatchByte(hexByte));
                    }
                }
                else
                {
                    // 处理十进制格式
                    bytes.Add(InternalParseMatchByte(item));
                }
            }

            return bytes.ToArray();
        }

        private static byte? InternalParseMatchByte(string value, bool isHex = true)
        {
            if (value == "**" || value == "*")
            {
                return null;
            }

            if (isHex)
            {
                if (byte.TryParse(value, NumberStyles.HexNumber, CultureInfo.InvariantCulture, out byte result))
                {
                    return result;
                }
            }
            else
            {
                if (byte.TryParse(value, out byte result))
                {
                    return result;
                }
            }

            throw new FormatException($"Can't parse {(isHex ? "HEX" : "DEC")} value as byte: {value}");
        }
    }
}
