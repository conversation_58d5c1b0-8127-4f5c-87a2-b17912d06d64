using Alsi.App.Devices.Core;
using System.Collections.Concurrent;
using System.Linq;

namespace Alsi.Fuzz.Tester.Testers
{
    public class DataCache
    {
        private ConcurrentQueue<CanFrame> Queue { get; set; }
            = new ConcurrentQueue<CanFrame>();

        public void Add(CanFrame dataFrame)
        {
            Queue.Enqueue(dataFrame);
        }

        public void Clear()
        {
            while (Queue.Count > 0)
            {
                Queue.TryDequeue(out _);
            }
        }

        public CanFrame[] GetAll()
        {
            return Queue.OrderBy(x => x.TimeUS).ToArray();
        }
    }
}
