using Alsi.App.Devices.Core;
using Alsi.Fuzz.Core.Models.TestPlans;
using System;

namespace Alsi.Fuzz.Web.Dto
{
    public class CaseConfigDto
    {
        public WhiteListFrame[] WhiteListFrames { get; set; } = Array.Empty<WhiteListFrame>();

        // 新增：保存选中的目标节点名称
        public string SelectedNodeName { get; set; }

        public bool EnableNmWakeup { get; set; }
        public int NmWakeupId { get; set; }
        public CommunicationType NmWakeupCommunicationType { get; set; }
        public bool NmWakeupIsExt { get; set; }
        public int NmWakeupDlc { get; set; }
        public int[] NmWakeupData { get; set; }
        public int NmWakeupCycleMs { get; set; }
        public int NmWakeupDelayMs { get; set; }

        public int DiagReqId { get; set; }
        public int DiagResId { get; set; }
        public bool DiagReqIsExt { get; set; }
        public int DiagTimeoutMs { get; set; }
        public CommunicationType DiagCommunicationType { get; set; }
        public bool IsDutMtuLessThan4096 { get; set; }
        public bool EnableDiagFallbackRequest { get; set; }
        public int[] DiagFallbackRequestPayload { get; set; }

        // 安全配置相关
        public string SecurityDllPath { get; set; }  // 新选择的DLL路径
        public bool RemoveSecurityDll { get; set; }  // 是否移除现有DLL
        public SecurityConfigInfoDto SecurityInfo { get; set; } // 只读信息
    }

    public class SecurityConfigInfoDto
    {
        public bool HasDll { get; set; }
        public string DllFileName { get; set; }
        public int DllSize { get; set; }
    }
}
