using Alsi.App.Devices.Core;
using Alsi.Common.Utils.Autosar;
using Alsi.Fuzz.Core.Service.CaseFactory.Core;
using System;
using System.Collections.Generic;

namespace Alsi.Fuzz.Core.Service.CaseFactory.Factories.Iso11898
{
    public class G22_CaseFactory : ICaseFactory
    {
        public IsoType IsoType => IsoType.Iso11898;

        public CaseMutation[] Generate(MutationOptions options)
        {
            var list = new List<CaseMutation>();
            if (options.CommunicationType != CommunicationType.CanFd)
            {
                return list.ToArray();
            }

            var random = options.Random;

            // G221
            for (var i = 0; i <= 7; i++)
            {
                foreach (var item in new byte[] { 0, 1, 0xFE, 0xFF })
                {
                    var id = i * 100 + item;
                    var bytes = new byte[0];
                    var mutation = CaseMutation.Create($"G221-ID_{id:X}")
                        .MutateId(id)
                        .MutateDlc(0)
                        .MutateRtr(0)
                        .MutateExt(false)
                        .MutateData(bytes);
                    list.Add(mutation);
                }
            }

            // G222
            var idG222 = random.Next(0, 0x800);
            var mutationG222 = CaseMutation.Create($"G222-ID_{idG222:X}")
                .MutateId(idG222)
                .MutateDlc(0)
                .MutateRtr(1)
                .MutateExt(false)
                .MutateData(Array.Empty<byte>());
            list.Add(mutationG222);

            // G223
            for (var i = 0; i <= 7; i++)
            {
                foreach (var item in new byte[] { 0, 1, 0xFE, 0xFF })
                {
                    var id = i * 100 + item;
                    var dlc = random.Next(1, 16);
                    var bytes = new byte[DlcUtils.GetDataLength(dlc)];
                    random.NextBytes(bytes);
                    var mutation = CaseMutation.Create($"G223-ID_{id:X}")
                        .MutateId(id)
                        .MutateDlc(dlc)
                        .MutateRtr(0)
                        .MutateExt(false)
                        .MutateData(bytes);
                    list.Add(mutation);
                }
            }

            // G224
            var idG224 = random.Next(0, 0x800);
            var mutationG224 = CaseMutation.Create($"G224-ID_{idG224:X}")
                .MutateId(idG224)
                .MutateDlc(random.Next(1, 16))
                .MutateRtr(1)
                .MutateExt(false)
                .MutateData(Array.Empty<byte>());
            list.Add(mutationG224);

            // G225
            foreach (var id in new int[] { 0x0, 0x1, 0xFF, 0x1FF, 0x1FFF, 0x1FFFF, 0x1FFFFF, 0x1FFFFF, 0x1FFFFFF, 0xFFFFFFF, 0x10000000, 0x10000001, 0x1FFFFFFE, 0x1FFFFFFF })
            {
                var bytes = new byte[0];
                var mutation = CaseMutation.Create($"G225-ID_{id:X}")
                    .MutateId(id)
                    .MutateDlc(0)
                    .MutateRtr(0)
                    .MutateExt(true)
                    .MutateData(bytes);
                list.Add(mutation);
            }

            // G226
            var idG226 = random.Next(0, 0x20000000);
            var mutationG226 = CaseMutation.Create($"G226-ID_{idG226:X}")
                .MutateId(idG226)
                .MutateDlc(random.Next(1, 16))
                .MutateRtr(1)
                .MutateExt(true)
                .MutateData(Array.Empty<byte>());
            list.Add(mutationG226);

            // G227
            foreach (var id in new int[] { 0x0, 0x1, 0xFF, 0x1FF, 0x1FFF, 0x1FFFF, 0x1FFFFF, 0x1FFFFF, 0x1FFFFFF, 0xFFFFFFF, 0x10000000, 0x10000001, 0x1FFFFFFE, 0x1FFFFFFF })
            {
                var dlc = random.Next(1, 16);
                var bytes = new byte[DlcUtils.GetDataLength(dlc)];
                random.NextBytes(bytes);
                var mutation = CaseMutation.Create($"G227-ID_{id:X}")
                    .MutateId(id)
                    .MutateDlc(dlc)
                    .MutateRtr(0)
                    .MutateExt(true)
                    .MutateData(bytes);
                list.Add(mutation);
            }

            // G228
            var idG228 = random.Next(0, 0x20000000);
            var mutationG228 = CaseMutation.Create($"G228-ID_{idG228:X}")
                .MutateId(idG228)
                .MutateDlc(random.Next(1, 16))
                .MutateRtr(1)
                .MutateExt(true)
                .MutateData(Array.Empty<byte>());
            list.Add(mutationG228);

            return list.ToArray();
        }
    }
}
