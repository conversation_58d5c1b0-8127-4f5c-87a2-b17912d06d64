using Alsi.App;
using Alsi.App.Controllers;
using Alsi.App.Devices;
using Alsi.Fuzz.Core.Contracts.Tester;
using Alsi.Fuzz.Core.Service.Results;
using Alsi.Fuzz.Core.Utils;
using Alsi.Fuzz.Tester.Testers;
using System.Web.Http;

namespace Alsi.Fuzz.Tester.Controllers
{
    public class TesterController : ControllerBase
    {
        [HttpGet]
        public IHttpActionResult Get()
        {
            return Ok(new
            {
                Program.IsRunning
            });
        }

        [HttpPost]
        [Route("api/tester/stop")]
        public IHttpActionResult Stop()
        {
            TesterEnv.Snapshot.ProcessState = ExecutionState.Failure;
            return Ok();
        }

        [HttpPost]
        [Route("api/tester/exit")]
        public IHttpActionResult Exit()
        {
            Program.IsRunning = false;
            TesterEnv.Snapshot.ProcessState = ExecutionState.Failure;
            return Ok();
        }

        [HttpPost]
        [Route("api/tester/pause")]
        public IHttpActionResult Pause()
        {
            if (TesterEnv.Snapshot.ProcessState == ExecutionState.Running)
            {
                Program.IsPaused = true;
                TesterEnv.Snapshot.ProcessState = ExecutionState.Paused;
            }
            return Ok();
        }

        [HttpPost]
        [Route("api/tester/resume")]
        public IHttpActionResult Resume()
        {
            Program.IsPaused = false;
            TesterEnv.Snapshot.ProcessState = ExecutionState.Running;
            return Ok();
        }

        [HttpPost]
        [Route("api/tester/snapshot")]
        public IHttpActionResult GetSnapshot([FromBody] PagedQuery request)
        {
            var snapshot = TesterEnv.Snapshot;
            return Ok(new TesterSnapshotResponse
            {
                ProcessState = snapshot.ProcessState,
                CurrentOperation = snapshot.CurrentOperation,
                TestResult = snapshot.TestResult,
                PagedCaseResult = PagedResult<CaseResult>.Build(snapshot.CaseResults, request.PageNumber, request.PageSize)
            });
        }

        [HttpPost]
        [Route("api/tester/execute")]
        public IHttpActionResult Execute([FromBody] ExecutionRequest request)
        {
            var package = SequencePackageUtils.LoadFromString(request.SequencePackageXml);

            // 验证请求
            if (request == null || package == null)
            {
                return BadRequest("The sequence package is empty");
            }

            var hardwareConfig = request.HardwareConfig;
            if (hardwareConfig == null || !hardwareConfig.IsConfigured)
            {
                return BadRequest("The hardware is not configured");
            }

            var deviceChannelName = hardwareConfig.GetSelectedDeviceChannelName();

            AppEnv.Logger.Info($"Device Channel Name: {deviceChannelName}");

            if (!DeviceEnv.TryGetDeviceChannel(deviceChannelName, out var deviceChannel))
            {
                return BadRequest($"Can't find the device channel: {deviceChannelName}");
            }

            var testResult = request.TestResult;

            // 初始化测试器快照
            TesterEnv.BeginTester(testResult);

            // 异步执行
            new PackageTester().Start(request, deviceChannel, package);

            return Ok(new { Success = true, Message = "测试已启动" });
        }
    }
}
