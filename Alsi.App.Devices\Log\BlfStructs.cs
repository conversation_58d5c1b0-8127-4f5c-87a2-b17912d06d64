using System.Runtime.InteropServices;

namespace Alsi.Common.Log
{
    [StructLayout(LayoutKind.Sequential)]
    public struct VBLCANMessage
    {
        public VBLObjectHeader mHeader;                /* object header */
        public ushort mChannel;                 /* application channel */
        public byte mFlags;  // 0:Rx  1:Tx                     /* CAN dir & rtr */
        public byte mDLC;                        /* CAN dlc */
        public uint mID;                          /* CAN ID */
        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 8)]
        public byte[] mData;                    /* CAN data */
    }

    [StructLayout(LayoutKind.Sequential)]
    public struct VBLCANFDMessage64
    {
        public VBLObjectHeader mHeader;                     /* object header */
        public byte mChannel;                    /* application channel */
        public byte mDLC;                        /* CAN dlc */
        public byte mValidDataBytes;             /* Valid payload length of mData */
        public byte mTxCount;                    /* TXRequiredCount (4 bits), TxReqCount (4 Bits) */
        public uint mID;                         /* CAN ID */
        public uint mFrameLength;                /* message length in ns - without 3 inter frame space bits */
        /* and by Rx-message also without 1 End-Of-Frame bit */
        public uint mFlags;                      /* flags */
        public uint mBtrCfgArb;                  /* bit rate used in arbitration phase */
        public uint mBtrCfgData;                 /* bit rate used in data phase */
        public uint mTimeOffsetBrsNs;            /* time offset of brs field */
        public uint mTimeOffsetCrcDelNs;         /* time offset of crc delimiter field */
        public ushort mBitCount;                   /* complete message length in bits */
        public byte mDir;                         // 0:Rx  1:Tx   
        public byte mExtDataOffset;
        public uint mCRC;                        /* CRC for CAN */
        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 64)]
        public byte[] mData;                   /* CAN FD data */
        VBLCANFDExtFrameData mExtFrameData;
    }

    [StructLayout(LayoutKind.Sequential)]
    public struct VBLCANFDMessage
    {
        public VBLObjectHeader mHeader;                     /* object header */
        public ushort mChannel;                    /* application channel */
        public byte mFlags;                      /* CAN dir & rtr */
        public byte mDLC;                        /* CAN dlc */
        public uint mID;                         /* CAN ID */
        public uint mFrameLength;                /* message length in ns - without 3 inter frame space bits and by Rx-message also without 1 End-Of-Frame bit */
        public byte mArbBitCount;                /* bit count of arbitration phase */
        public byte mCANFDFlags;                 /* CAN FD flags */
        public byte mValidDataBytes;             /* Valid payload length of mData */
        public byte mReserved1;                  /* reserved */
        public uint mReserved2;                  /* reserved */
        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 64)]
        public byte[] mData;                   /* CAN FD data */
    }

    [StructLayout(LayoutKind.Sequential)]
    public struct VBLLINMessage
    {
        public VBLObjectHeader mHeader;                     /* object header */
        public ushort mChannel;                    /* application channel */
        public byte mID;                         /* LIN ID */
        public byte mDLC;                        /* LIN DLC */
        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 8)]
        public byte[] mData;                    /* t.b.d. */
        public byte mFSMId;                      /* t.b.d. */
        public byte mFSMState;                   /* t.b.d. */
        public byte mHeaderTime;                 /* t.b.d. */
        public byte mFullTime;                   /* t.b.d. */
        public ushort mCRC;                        /* t.b.d. */
        public byte mDir;                        /* t.b.d. */
        public byte mReserved;                   /* t.b.d. */
    }

    [StructLayout(LayoutKind.Sequential)]
    public struct VBLCANErrorFrame
    {
        public VBLObjectHeader mHeader;                     /* object header */
        public ushort mChannel;                    /* application channel */
        public ushort mLength;                     /* CAN error frame length */
    }

    [StructLayout(LayoutKind.Sequential)]
    public struct VBLCANFDErrorFrame64
    {
        public VBLObjectHeader mHeader;                     /* object header */
        public byte mChannel;                    /* application channel */
        public byte mDLC;                        /* CAN dlc */
        public byte mValidDataBytes;             /* Valid payload length of mData */
        public byte mECC;
        public ushort mFlags;
        public ushort mErrorCodeExt;
        public ushort mExtFlags;                   /* FD specific flags */
        public byte mExtDataOffset;
        public byte reserved1;
        public uint mID;                         /* CAN ID */
        public uint mFrameLength;                /* message length in ns - without 3 inter frame space bits */
        /* and by Rx-message also without 1 End-Of-Frame bit */
        public uint mBtrCfgArb;                  /* bit rate used in arbitration phase */
        public uint mBtrCfgData;                 /* bit rate used in data phase */
        public uint mTimeOffsetBrsNs;            /* time offset of brs field */
        public uint mTimeOffsetCrcDelNs;         /* time offset of crc delimiter field */
        public uint mCRC;
        public ushort mErrorPosition;              /* error position as bit offset */
        public ushort mReserved2;
        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 64)]
        public byte[] mData;                   /* CAN FD data */
        public VBLCANFDExtFrameData mExtFrameData;
    }

    [StructLayout(LayoutKind.Sequential)]
    public struct VBLLINChecksumInfo
    {
        public VBLObjectHeader mHeader;                     /* object header */
        public ushort mChannel;                    /* application channel */
        public byte mID;                         /* LIN ID */
        public byte mChecksumModel;              /* LIN checksum model */
    }

    [StructLayout(LayoutKind.Sequential)]
    public struct VBLCANFDExtFrameData
    {
        public uint mBTRExtArb;
        public uint mBTRExtData;
    }

    [StructLayout(LayoutKind.Sequential)]
    public struct VBLObjectHeader
    {
        public VBLObjectHeaderBase mBase;                  /* base header object */
        public uint mObjectFlags;           /* object flags */
        public ushort mClientIndex;          /* client index of send node */
        public ushort mObjectVersion;         /* object specific version */
        public ulong mObjectTimeStamp;       /* object timestamp */
    }


    [StructLayout(LayoutKind.Sequential)]
    public struct VBLObjectHeaderBase
    {
        public uint mSignature;                        /* signature (BL_OBJ_SIGNATURE) */
        public ushort mHeaderSize;                       /* sizeof object header */
        public ushort mHeaderVersion;                    /* header version (1) */
        public uint mObjectSize;                     /* object size */
        public uint mObjectType;                     /* object type (BL_OBJ_TYPE_XXX) */
    }
}
