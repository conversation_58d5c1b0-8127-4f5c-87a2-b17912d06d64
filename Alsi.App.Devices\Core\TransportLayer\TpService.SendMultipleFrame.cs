using Alsi.App.Devices.Core.TransportLayer.Frames;
using Alsi.Common.Utils.Autosar;
using Alsi.Common.Utils.Timers;
using System;
using System.Linq;

namespace Alsi.App.Devices.Core.TransportLayer
{
    public partial class TpService
    {
        /// <summary>
        /// 发送多帧
        /// </summary>
        public bool SendMultipleFrame(Request request)
        {
            if (MultipleFrame.TryBuild(request.Payload, request.PayloadLength, request.IsCanfd, out var multipleFrame))
            {
                waitFlowControlEvent.Reset();

                // [发送方] 多帧 - 发送首帧
                if (request.IsCanfd)
                {
                    var dlc = (byte)DlcUtils.GetDlc(multipleFrame.FirstFrame.Data.Length < 8 ? 8 : multipleFrame.FirstFrame.Data.Length);
                    HandleRequest(request.RequestId, dlc, multipleFrame.FirstFrame.Data,
                        request.IsCanfd, request.RequestIsExt);
                }
                else
                {
                    HandleRequest(request.RequestId, 8, multipleFrame.FirstFrame.Data,
                        request.IsCanfd, request.RequestIsExt);
                }

                Context.Recorder.Add(multipleFrame.FirstFrame);

                if (multipleFrame != null)
                {
                    var consecutiveFrames = multipleFrame.ConsecutiveFrames.ToList();
                    while (consecutiveFrames.Count > 0)
                    {
                        var flowControl = WaitFlowControl();
                        var sendConsecutiveFrames = Array.Empty<ConsecutiveFrame>();
                        if (flowControl.BlockSize == 0)
                        {
                            sendConsecutiveFrames = consecutiveFrames.ToArray();
                            consecutiveFrames.Clear();
                        }
                        else
                        {
                            sendConsecutiveFrames = consecutiveFrames.Take(flowControl.BlockSize).ToArray();
                            consecutiveFrames = consecutiveFrames.Skip(flowControl.BlockSize).ToList();
                        }

                        SendConsecutiveFrames(request, sendConsecutiveFrames);
                    }
                }

                return true;
            }
            return false;
        }

        private FlowControl WaitFlowControl()
        {
            // [发送方] 等待流控帧，等待时间 N_Bs
            bool isWaiting = true;
            FlowControl flowControl = null;
            while (isWaiting)
            {
                isWaiting = false;

                var waitFlowControlTimeout = DiagParams.N_Bs;
                var isReceived = waitFlowControlEvent.WaitOne(waitFlowControlTimeout);
                if (!isReceived)
                {
                    // 未收到流控帧
                    throw new NoFlowControlException($"No flow control frame was received in {waitFlowControlTimeout}ms.");
                }

                waitFlowControlEvent.Reset();

                flowControl = Context.Recorder.GetDiagFrames<FlowControl>(false).LastOrDefault();
                if (flowControl == null)
                {
                    throw new Exception($"Failed to get the flow control frame.");
                }

                if (flowControl.FlowState == FlowState.Wait)
                {
                    // 收到0x31，继续等待
                    isWaiting = true;
                }
            }

            return flowControl;
        }

        private void SendConsecutiveFrames(Request request, ConsecutiveFrame[] consecutiveFrames)
        {
            // [接收方] 流控制帧 -> 发送连续帧（CF）
            var flowControl = Context.Recorder.GetDiagFrames<FlowControl>(false).LastOrDefault();
            if (flowControl == null)
            {
                throw new Exception($"The flow control frame is null.");
            }

            foreach (var cf in consecutiveFrames)
            {
                if (flowControl.STmin > 0)
                {
                    HighResolutionTimer.Sleep(flowControl.STmin);
                }
                if (request.IsCanfd)
                {
                    var dlc = (byte)DlcUtils.GetDlc(cf.Data.Length < 8 ? 8 : cf.Data.Length);
                    HandleRequest(Context.Request.RequestId, dlc, cf.Data,
                        request.IsCanfd, request.RequestIsExt);
                }
                else
                {
                    HandleRequest(Context.Request.RequestId, 8, cf.Data,
                        request.IsCanfd, request.RequestIsExt);
                }

                Context.Recorder.Add(cf);
            }
        }
    }
}
