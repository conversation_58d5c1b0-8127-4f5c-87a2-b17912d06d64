﻿using System.Collections.Generic;
using System.Xml.Serialization;

namespace Alsi.Fuzz.Core.Models.TestSuites
{
    [XmlRoot("testsuite")]
    public class TestSuite
    {
        [XmlAttribute("name")]
        public string Name { get; set; }

        [XmlAttribute("version")]
        public string Version { get; set; }

        [XmlElement("package")]
        public List<SequencePackage> Packages { get; set; } = new List<SequencePackage>();
    }
}
