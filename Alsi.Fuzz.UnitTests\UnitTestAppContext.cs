using Alsi.App;
using Alsi.App.Database;
using Alsi.App.Database.Midwares;
using Alsi.App.Devices;

namespace Alsi.Fuzz.UnitTests
{
    public class UnitTestAppContext : IDisposable
    {
        public WebHostApp WebHostApp { get; private set; }
        public DbContext AppDbContext { get; private set; }

        private readonly string _dbFileName;
        private bool _disposed;
        private static object _locker = new object();

        public UnitTestAppContext()
        {
            // 防止用例并发执行时，静态的APP数据库上下文并发冲突
            lock (_locker)
            {
                _dbFileName = $"fuzz-test-{Guid.NewGuid():N}.sqlite";
                WebHostApp = WebHostApp
                    .Create(appName: "FuzzUnitTest", appFolderName: "Alsi.Atts", productFolderName: "FuzzUnitTest")
                    .UseFreeSql(_dbFileName)
                    .UseDevice()
                    .Build();

                var sqliteFilePath = GetDatabasePath();
                if (!File.Exists(sqliteFilePath))
                {
                    throw new Exception($"Initialize database failed, can't find file: {sqliteFilePath}");
                }

                AppDbContext = DbEnv.AppDbContext;
            }
        }

        private string GetDatabasePath()
        {
            return Path.Combine(WebHostApp.DataFolder, "db", _dbFileName);
        }

        public void Dispose()
        {
            if (_disposed)
            {
                return;
            }

            WebHostApp?.Release();

            var dbPath = GetDatabasePath();
            if (File.Exists(dbPath))
            {
                try
                {
                    if (dbPath.Contains(_dbFileName))
                    {
                        File.Delete(dbPath);
                    }
                }
                catch (IOException)
                {
                    // 可以考虑记录日志
                }
            }
            _disposed = true;
        }
    }
}
