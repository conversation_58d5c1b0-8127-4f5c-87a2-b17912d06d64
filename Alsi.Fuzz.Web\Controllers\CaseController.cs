using Alsi.App;
using Alsi.App.Desktop.Utils;
using Alsi.App.Devices.Core;
using Alsi.Common.Utils;
using Alsi.Fuzz.Core;
using Alsi.Fuzz.Core.Contracts.Tester;
using Alsi.Fuzz.Core.Models.Tester;
using Alsi.Fuzz.Core.Models.TestSuites;
using Alsi.Fuzz.Core.Models.TestSuites.Steps.Diagnostic;
using Alsi.Fuzz.Core.Models.TestSuites.Steps.Isotp;
using Alsi.Fuzz.Core.Service;
using Alsi.Fuzz.Core.Service.CaseFactory;
using Alsi.Fuzz.Core.Service.CaseFactory.Core;
using Alsi.Fuzz.Core.Service.CaseFactory.Factories.Iso14229.Consts;
using Alsi.Fuzz.Core.Service.Results;
using Alsi.Fuzz.Core.Service.Tester;
using Alsi.Fuzz.Core.Utils;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using System.Web.Http;

namespace Alsi.Fuzz.Web.Controllers
{
    public class CaseController : WebControllerBase
    {
        private static StatusPollingService StatusPollingService { get; set; } = new StatusPollingService();

        [HttpGet]
        [ActionName("latest-interoperation-case-results")]
        public IHttpActionResult GetLatestInteroperationCaseResults()
        {
            return Ok(new CaseService().GetLatestCaseResults(TestType.Interoperation));
        }

        public class GenerateCasesRequest
        {
            public CoverageType coverage { get; set; }
            public string[] SequenceNames { get; set; }
        }

        [HttpDelete]
        [ActionName("test-result")]
        public IHttpActionResult DeleteTestResults(Guid testResultId)
        {
            var resultReader = new TestResultReaderService();
            resultReader.DeleteTestResult(testResultId);
            return Ok();
        }

        [HttpGet]
        [ActionName("test-results")]
        public IHttpActionResult GetTestResults()
        {
            var resultReader = new TestResultReaderService();
            var testResults = resultReader
                .GetTestResults()
                .Where(x => x.TestType == TestType.Case)
                .OrderByDescending(x => x.CreationTime)
                .ToArray();
            return Ok(testResults);
        }

        [HttpGet]
        [ActionName("case-steps")]
        public IHttpActionResult GetCaseSteps(Guid testResultId, int caseResultId)
        {
            var caseResult = InternalGetCaseResult(testResultId, caseResultId);
            var resultReader = new TestResultReaderService();
            var caseSteps = resultReader.GetCaseSteps(caseResult);
            return Ok(caseSteps);
        }

        [HttpGet]
        [ActionName("case-result")]
        public IHttpActionResult GetCaseResult(Guid testResultId, int caseResultId)
        {
            var caseResult = InternalGetCaseResult(testResultId, caseResultId);
            return Ok(caseResult);
        }

        private CaseResult InternalGetCaseResult(Guid testResultId, int caseResultId)
        {
            var resultReader = new TestResultReaderService();
            var testResult = resultReader
                .GetTestResults()
                .Where(x => x.Id == testResultId)
                .OrderByDescending(x => x.CreationTime)
                .FirstOrDefault();
            if (testResult == null)
            {
                throw new Exception($"Can't find test result by ID: {testResultId}");
            }

            var caseResult = resultReader
                .GetCaseResults(testResult)
                .FirstOrDefault(x => x.Id == caseResultId);
            if (caseResult == null)
            {
                throw new Exception($"Can't find case result by ID: testResultId={testResultId} caseResultId={caseResultId}");
            }
            return caseResult;
        }

        [HttpGet]
        [ActionName("cases")]
        public IHttpActionResult GetCases(Guid? testResultId = null)
        {
            ITestResultReaderService resultReader = new TestResultReaderService();
            var testPlanName = System.IO.Path.GetFileNameWithoutExtension(TestPlanManager.Instance.GetCurrentPlanPath());
            TestResult testResult = null;
            if (testResultId.HasValue)
            {
                testResult = resultReader
                    .GetTestResults()
                    .Where(x => x.TestType == TestType.Case && x.Id == testResultId)
                    .OrderByDescending(x => x.CreationTime)
                    .FirstOrDefault();
            }
            else
            {
                testResult = resultReader
                    .GetTestResults()
                    .Where(x => x.TestType == TestType.Case)
                    .OrderByDescending(x => x.CreationTime)
                    .FirstOrDefault();
            }

            if (testResult == null)
            {
                return Ok(Array.Empty<CaseResult>());
            }

            ITestResultWriterService resultWriter = new TestResultWriterService();
            try
            {
                resultWriter.Begin(testResult);
                var caseResults = resultWriter.GetCaseResults();
                return Ok(caseResults);
            }
            finally
            {
                resultWriter.End();
            }
        }

        [HttpPost]
        [ActionName("save-cases")]
        public IHttpActionResult SaveCases([FromBody] GenerateCasesRequest request)
        {
            var caseResults = InternalGenerateCases(request);

            ITestResultReaderService resultReader = new TestResultReaderService();
            var testPlanName = Path.GetFileNameWithoutExtension(TestPlanManager.Instance.GetCurrentPlanPath());
            var testResult = resultReader.CreateTestResult(testPlanName, TestType.Case);

            ITestResultWriterService resultWriter = new TestResultWriterService();
            resultWriter.Begin(testResult);
            foreach (var caseResult in caseResults)
            {
                caseResult.TestResultId = testResult.Id;
            }

            resultWriter.BatchAddCaseResult(caseResults);
            resultWriter.End();

            return Ok(caseResults);
        }

        [HttpPost]
        [ActionName("generate-cases")]
        public IHttpActionResult GenerateCases([FromBody] GenerateCasesRequest request)
        {
            var cases = InternalGenerateCases(request);
            return Ok(cases);
        }

        private CaseResult[] InternalGenerateCases(GenerateCasesRequest request)
        {
            var testPlan = TestPlanManager.Instance.GetCurrentPlan();
            var caseService = new CaseService();
            var caseResults = caseService.GetLatestCaseResults(TestType.Interoperation);

            var caseConfig = testPlan.Config.CaseConfig;

            var hardwareConfig = testPlan.Config.HardwareConfig;
            var isCanFd = hardwareConfig.CommunicationType == CommunicationType.CanFd;
            var isCan = hardwareConfig.CommunicationType == CommunicationType.Can;

            var sequenceConfig = testPlan.Config.SequenceConfigList.FirstOrDefault(x => x.IsSelected);
            var sequencePackageName = sequenceConfig?.SequencePackageName;
            var isoType = IsoType.Unknown;

            var xmlServices = new List<XmlService>();
            if (sequencePackageName == FuzzConsts.PackageName14229)
            {
                isoType = IsoType.Iso14229;

                var package = SequencePackageUtils.LoadFromString(sequenceConfig.SequencePackageXml);
                var envVars = package.SetVars.Select(x => new EnvVar(x.Name, x.Value)).ToList();

                foreach (var sequence in package.Sequences)
                {
                    var isSupported = false;
                    var caseResult = caseResults.FirstOrDefault(x => x.SequenceName == sequence.Name);
                    if (caseResult?.State == ExecutionState.Success)
                    {
                        isSupported = true;
                    }

                    var hasMultipleFrameRequest = false;
                    if (!string.IsNullOrWhiteSpace(caseResult?.ResultProps))
                    {
                        try
                        {
                            var resultProps = JsonUtils.Deserialize<ResultProps>(caseResult.ResultProps);
                            hasMultipleFrameRequest = resultProps.HasMultipleFrameRequest;
                        }
                        catch (Exception e)
                        {
                            AppEnv.Logger.Error(e, $"Failed to deserialize: {caseResult.ResultProps}");
                        }
                    }


                    var xmlService = new XmlService
                    {
                        IsSupported = isSupported,
                        HasMultipleFrameRequest = hasMultipleFrameRequest,
                        SequenceName = sequence.Name,
                    };

                    xmlServices.Add(xmlService);
                    var match = Regex.Match(sequence.Name, @".*\((0x[\d|a-f|A-F]+)\).*");
                    if (match.Success)
                    {
                        var sid = SequenceUtils.ParseByte(match.Groups[1].Value);
                        xmlService.Id = sid;
                    }

                    var isoService = IsoUdsConsts.Services.FirstOrDefault(x => x.Id == xmlService.Id);
                    var hasSubfunction = isoService != null && isoService.Subfunctions.Any();

                    foreach (var step in sequence.Steps)
                    {
                        if (step is SendDiagStep sendDiagStep)
                        {
                            var payload = sendDiagStep.HexPayload?.Trim();
                            if (!string.IsNullOrWhiteSpace(payload))
                            {
                                envVars.Eval(ref payload);
                                var payloadBytes = SequenceUtils.ParseBytes(payload);
                                if (payloadBytes[0] == xmlService.Id && payloadBytes.Length > 1)
                                {
                                    if (hasSubfunction)
                                    {
                                        var subfunctionId = payloadBytes[1];
                                        xmlService.SubfunctionId = subfunctionId;
                                        xmlService.Parameter2k = payloadBytes.Skip(2).ToArray();
                                    }
                                    else
                                    {
                                        xmlService.SubfunctionId = null;
                                        xmlService.Parameter2k = payloadBytes.Skip(1).ToArray();
                                    }
                                }
                            }
                        }
                    }
                }
            }

            if (sequencePackageName == FuzzConsts.PackageName11898)
            {
                isoType = IsoType.Iso11898;
            }

            var tpParameters = Array.Empty<byte>();
            var tpParametersBigDataPayload = Array.Empty<byte>();
            var tpParametersBigDataRepeat = 1;

            if (sequencePackageName == FuzzConsts.PackageName15765)
            {
                isoType = IsoType.Iso15765;

                var package = SequencePackageUtils.LoadFromString(sequenceConfig.SequencePackageXml);
                var envVars = package.SetVars.Select(x => new EnvVar(x.Name, x.Value)).ToList();

                foreach (var sequence in package.Sequences)
                {
                    var isBigData = sequence.Name.Contains("big-data");

                    foreach (var step in sequence.Steps)
                    {
                        if (step is SendIsotpStep sendIsotpStep)
                        {
                            var payload = sendIsotpStep.HexPayload?.Trim();
                            if (!string.IsNullOrWhiteSpace(payload))
                            {
                                envVars.Eval(ref payload);
                                var payloadBytes = SequenceUtils.ParseBytes(payload);
                                var repeat = 1;
                                if (sendIsotpStep.RepeatPayload.HasValue && sendIsotpStep.RepeatPayload.Value > 0)
                                {
                                    repeat = sendIsotpStep.RepeatPayload.Value;
                                }

                                if (isBigData)
                                {
                                    tpParametersBigDataPayload = payloadBytes;
                                    tpParametersBigDataRepeat = repeat;
                                }
                                else
                                {
                                    var repeatPayloadBytes = new List<byte>();
                                    for (var i = 0; i < repeat; i++)
                                    {
                                        repeatPayloadBytes.AddRange(payloadBytes);
                                    }

                                    tpParameters = repeatPayloadBytes.ToArray();
                                }
                            }
                        }
                    }
                }
            }
            if (isoType == IsoType.Unknown)
            {
                throw new Exception($"Can't parse ISO type from sequence package name : {sequencePackageName}");
            }

            var whiteListFrames = caseConfig.WhiteListFrames
                .Where(x => x.Transmitter == caseConfig.SelectedNodeName || x.Receivers.Contains(caseConfig.SelectedNodeName))
                .ToArray();

            var options = new MutationOptions
            {
                IsoType = isoType,
                Coverage = request.coverage,

                InteroperationResults = caseResults,
                SelectedSequenceNames = request.SequenceNames,

                CommunicationType = hardwareConfig.CommunicationType,

                WhiteListFrames = whiteListFrames,
                SelectedNodeName = caseConfig.SelectedNodeName,

                XmlServices = xmlServices.Where(x => request.SequenceNames.Contains(x.SequenceName)).OrderBy(x => x.Id).ToArray(),

                TpParameters = tpParameters,
                TpParametersBigDataPayload = tpParametersBigDataPayload,
                TpParametersBigDataRepeat = tpParametersBigDataRepeat,

                IsDutMtuLessThan4096 = caseConfig.IsDutMtuLessThan4096
            };

            var cases = CaseFactoryUtils.Generate(options);
            return cases;
        }

        [HttpPost]
        [ActionName("start")]
        public async Task<IHttpActionResult> Start()
        {
            if (StatusPollingService.IsTesterRunning)
            {
                return Ok();
            }

            StatusPollingService.TesterSnapshot = new TesterSnapshot();

            var success = await TesterManager.Instance.StartAsync();
            if (!success)
            {
                throw new Exception("Failed to start tester");
            }

            var apiClient = TesterManager.Instance.GetApiClient();
            if (apiClient == null)
            {
                throw new Exception("The API client is null");
            }

            // 等待 tester 启动
            await TesterManager.Instance.WaitTesterAsync(expectIsRunning: true);

            var testResult = new CaseService().GetLatestTestResult(TestType.Case);
            await TesterManager.Instance.GetApiClient().StartAsync(testResult);

            await StatusPollingService.UpdateStatusAsync(true);
            StatusPollingService.StartStatusPolling();

            return Ok();
        }

        [HttpPost]
        [ActionName("stop")]
        public async Task<IHttpActionResult> Stop()
        {
            await TesterManager.Instance.GetApiClient().StopAsync();
            await StatusPollingService.UpdateStatusAsync(false);
            StatusPollingService.StopStatusPolling();
            return Ok();
        }

        [HttpPost]
        [ActionName("pause")]
        public async Task<IHttpActionResult> Pause()
        {
            await TesterManager.Instance.GetApiClient().PauseAsync();
            return Ok();
        }

        [HttpPost]
        [ActionName("resume")]
        public async Task<IHttpActionResult> Resume()
        {
            await TesterManager.Instance.GetApiClient().ResumeAsync();
            return Ok();
        }

        [HttpPost]
        [ActionName("status")]
        public IHttpActionResult GetStatus([FromBody] PagedQuery request)
        {
            // 如果没有提供分页参数，使用默认值
            request = request ?? new PagedQuery { PageNumber = 1, PageSize = 1000 };
            
            // 从 StatusPollingService 获取数据
            var snapshot = StatusPollingService.TesterSnapshot;
            
            // 构造分页响应
            var response = new TesterSnapshotResponse
            {
                ProcessState = snapshot.ProcessState,
                CurrentOperation = snapshot.CurrentOperation,
                TestResult = snapshot.TestResult,
                PagedCaseResult = PagedResult<CaseResult>.Build(snapshot.CaseResults, request.PageNumber, request.PageSize)
            };
            
            return Ok(response);
        }

        // 保留原有的GET方法用于兼容性
        [HttpGet]
        [ActionName("status")]
        public IHttpActionResult GetStatus()
        {
            return Ok(StatusPollingService.TesterSnapshot);
        }

        [HttpPost]
        [ActionName("generate-report")]
        public IHttpActionResult GenerateHtmlReport(Guid testResultId)
        {
            var resultReader = new TestResultReaderService();
            var testResult = resultReader
                .GetTestResults()
                .FirstOrDefault(x => x.Id == testResultId);

            if (testResult == null)
            {
                return NotFound();
            }

            // 获取相关的测试用例结果
            var caseResults = resultReader.GetCaseResults(testResult);

            // 使用ReportTemplateService生成HTML报告
            var htmlContent = ReportTemplateService.GenerateHtmlReport(testResult, caseResults);

            if (!UiUtils.SelectFolder(out var folder))
            {
                throw new Exception("Please select a folder");
            }

            var fileName = $"{testResult.ResultFolderName}.html";
            var existedNames = Directory.GetFileSystemEntries(folder).Select(x => Path.GetFileName(x)).ToArray();
            var newFileName = PathUtils.GetAlternativeFileName(fileName, existedNames);
            var filePath = Path.Combine(folder, newFileName);
            File.WriteAllText(filePath, htmlContent);
            UiUtils.RevealInExplorer(filePath);

            return Ok();
        }
    }
}
