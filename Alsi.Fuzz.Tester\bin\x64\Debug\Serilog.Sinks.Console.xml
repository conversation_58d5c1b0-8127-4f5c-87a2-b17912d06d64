<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Serilog.Sinks.Console</name>
    </assembly>
    <members>
        <member name="T:Serilog.ConsoleLoggerConfigurationExtensions">
            <summary>
            Adds the WriteTo.Console() extension method to <see cref="T:Serilog.LoggerConfiguration"/>.
            </summary>
        </member>
        <member name="M:Serilog.ConsoleLoggerConfigurationExtensions.Console(Serilog.Configuration.LoggerSinkConfiguration,Serilog.Events.LogEventLevel,System.String,System.IFormatProvider,Serilog.Core.LoggingLevelSwitch,System.Nullable{Serilog.Events.LogEventLevel},Serilog.Sinks.SystemConsole.Themes.ConsoleTheme,System.Boolean,System.Object)">
            <summary>
            Writes log events to <see cref="T:System.Console"/>.
            </summary>
            <param name="sinkConfiguration">Logger sink configuration.</param>
            <param name="restrictedToMinimumLevel">The minimum level for
            events passed through the sink. Ignored when <paramref name="levelSwitch"/> is specified.</param>
            <param name="outputTemplate">A message template describing the format used to write to the sink.
            The default is <code>"[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj}{NewLine}{Exception}"</code>.</param>
            <param name="syncRoot">An object that will be used to `lock` (sync) access to the console output. If you specify this, you
            will have the ability to lock on this object, and guarantee that the console sink will not be about to output anything while
            the lock is held.</param>
            <param name="formatProvider">Supplies culture-specific formatting information, or null.</param>
            <param name="levelSwitch">A switch allowing the pass-through minimum level
            to be changed at runtime.</param>
            <param name="standardErrorFromLevel">Specifies the level at which events will be written to standard error.</param>
            <param name="theme">The theme to apply to the styled output. If not specified,
            uses <see cref="P:Serilog.Sinks.SystemConsole.Themes.SystemConsoleTheme.Literate"/>.</param>
            <param name="applyThemeToRedirectedOutput">Applies the selected or default theme even when output redirection is detected.</param>
            <returns>Configuration object allowing method chaining.</returns>
            <exception cref="T:System.ArgumentNullException">When <paramref name="sinkConfiguration"/> is <code>null</code></exception>
            <exception cref="T:System.ArgumentNullException">When <paramref name="outputTemplate"/> is <code>null</code></exception>
        </member>
        <member name="M:Serilog.ConsoleLoggerConfigurationExtensions.Console(Serilog.Configuration.LoggerSinkConfiguration,Serilog.Formatting.ITextFormatter,Serilog.Events.LogEventLevel,Serilog.Core.LoggingLevelSwitch,System.Nullable{Serilog.Events.LogEventLevel},System.Object)">
            <summary>
            Writes log events to <see cref="T:System.Console"/>.
            </summary>
            <param name="sinkConfiguration">Logger sink configuration.</param>
            <param name="formatter">Controls the rendering of log events into text, for example to log JSON. To
            control plain text formatting, use the overload that accepts an output template.</param>
            <param name="syncRoot">An object that will be used to `lock` (sync) access to the console output. If you specify this, you
            will have the ability to lock on this object, and guarantee that the console sink will not be about to output anything while
            the lock is held.</param>
            <param name="restrictedToMinimumLevel">The minimum level for
            events passed through the sink. Ignored when <paramref name="levelSwitch"/> is specified.</param>
            <param name="levelSwitch">A switch allowing the pass-through minimum level
            to be changed at runtime.</param>
            <param name="standardErrorFromLevel">Specifies the level at which events will be written to standard error.</param>
            <returns>Configuration object allowing method chaining.</returns>
            <exception cref="T:System.ArgumentNullException">When <paramref name="sinkConfiguration"/> is <code>null</code></exception>
            <exception cref="T:System.ArgumentNullException">When <paramref name="formatter"/> is <code>null</code></exception>
        </member>
        <member name="T:Serilog.Sinks.SystemConsole.Output.LevelOutputFormat">
            <summary>
            Implements the {Level} element.
            can now have a fixed width applied to it, as well as casing rules.
            Width is set through formats like "u3" (uppercase three chars),
            "w1" (one lowercase char), or "t4" (title case four chars).
            </summary>
        </member>
        <member name="M:Serilog.Sinks.SystemConsole.Rendering.Casing.Format(System.String,System.String)">
            <summary>
            Apply upper or lower casing to <paramref name="value"/> when <paramref name="format"/> is provided.
            Returns <paramref name="value"/> when no or invalid format provided.
            </summary>
            <param name="value">Provided string for formatting.</param>
            <param name="format">Format string.</param>
            <returns>The provided <paramref name="value"/> with formatting applied.</returns>
        </member>
        <member name="M:Serilog.Sinks.SystemConsole.Rendering.Padding.Apply(System.IO.TextWriter,System.String,System.Nullable{Serilog.Parsing.Alignment})">
            <summary>
            Writes the provided value to the output, applying direction-based padding when <paramref name="alignment"/> is provided.
            </summary>
            <param name="output">Output object to write result.</param>
            <param name="value">Provided value.</param>
            <param name="alignment">The alignment settings to apply when rendering <paramref name="value"/>.</param>
        </member>
        <member name="T:Serilog.Sinks.SystemConsole.Themes.AnsiConsoleTheme">
            <summary>
            A console theme using the ANSI terminal escape sequences. Recommended
            for Linux and Windows 10+.
            </summary>
        </member>
        <member name="P:Serilog.Sinks.SystemConsole.Themes.AnsiConsoleTheme.Code">
            <summary>
            A 256-color theme along the lines of Visual Studio Code.
            </summary>
        </member>
        <member name="P:Serilog.Sinks.SystemConsole.Themes.AnsiConsoleTheme.Grayscale">
            <summary>
            A theme using only gray, black and white.
            </summary>
        </member>
        <member name="P:Serilog.Sinks.SystemConsole.Themes.AnsiConsoleTheme.Literate">
            <summary>
            A theme in the style of the original <i>Serilog.Sinks.Literate</i>.
            </summary>
        </member>
        <member name="P:Serilog.Sinks.SystemConsole.Themes.AnsiConsoleTheme.Sixteen">
            <summary>
            A theme in the style of the original <i>Serilog.Sinks.Literate</i> using only standard 16 terminal colors that will work on light backgrounds.
            </summary>
        </member>
        <member name="M:Serilog.Sinks.SystemConsole.Themes.AnsiConsoleTheme.#ctor(System.Collections.Generic.IReadOnlyDictionary{Serilog.Sinks.SystemConsole.Themes.ConsoleThemeStyle,System.String})">
            <summary>
            Construct a theme given a set of styles.
            </summary>
            <param name="styles">Styles to apply within the theme.</param>
            <exception cref="T:System.ArgumentNullException">When <paramref name="styles"/> is <code>null</code></exception>
        </member>
        <member name="P:Serilog.Sinks.SystemConsole.Themes.AnsiConsoleTheme.CanBuffer">
            <inheritdoc/>
        </member>
        <member name="P:Serilog.Sinks.SystemConsole.Themes.AnsiConsoleTheme.ResetCharCount">
            <inheritdoc/>
        </member>
        <member name="M:Serilog.Sinks.SystemConsole.Themes.AnsiConsoleTheme.Set(System.IO.TextWriter,Serilog.Sinks.SystemConsole.Themes.ConsoleThemeStyle)">
            <inheritdoc/>
        </member>
        <member name="M:Serilog.Sinks.SystemConsole.Themes.AnsiConsoleTheme.Reset(System.IO.TextWriter)">
            <inheritdoc/>
        </member>
        <member name="T:Serilog.Sinks.SystemConsole.Themes.ConsoleTheme">
            <summary>
            The base class for styled terminal output.
            </summary>
        </member>
        <member name="P:Serilog.Sinks.SystemConsole.Themes.ConsoleTheme.None">
            <summary>
            No styling applied.
            </summary>
        </member>
        <member name="P:Serilog.Sinks.SystemConsole.Themes.ConsoleTheme.CanBuffer">
            <summary>
            True if styling applied by the theme is written into the output, and can thus be
            buffered and measured.
            </summary>
        </member>
        <member name="M:Serilog.Sinks.SystemConsole.Themes.ConsoleTheme.Set(System.IO.TextWriter,Serilog.Sinks.SystemConsole.Themes.ConsoleThemeStyle)">
            <summary>
            Begin a span of text in the specified <paramref name="style"/>.
            </summary>
            <param name="output">Output destination.</param>
            <param name="style">Style to apply.</param>
            <returns> The number of characters written to <paramref name="output"/>. </returns>
        </member>
        <member name="M:Serilog.Sinks.SystemConsole.Themes.ConsoleTheme.Reset(System.IO.TextWriter)">
            <summary>
            Reset the output to un-styled colors.
            </summary>
            <param name="output">Output destination.</param>
        </member>
        <member name="P:Serilog.Sinks.SystemConsole.Themes.ConsoleTheme.ResetCharCount">
            <summary>
            The number of characters written by the <see cref="M:Serilog.Sinks.SystemConsole.Themes.ConsoleTheme.Reset(System.IO.TextWriter)"/> method.
            </summary>
        </member>
        <member name="T:Serilog.Sinks.SystemConsole.Themes.ConsoleThemeStyle">
            <summary>
            Elements styled by a console theme.
            </summary>
        </member>
        <member name="F:Serilog.Sinks.SystemConsole.Themes.ConsoleThemeStyle.Text">
            <summary>
            Prominent text, generally content within an event's message.
            </summary>
        </member>
        <member name="F:Serilog.Sinks.SystemConsole.Themes.ConsoleThemeStyle.SecondaryText">
            <summary>
            Boilerplate text, for example items specified in an output template.
            </summary>
        </member>
        <member name="F:Serilog.Sinks.SystemConsole.Themes.ConsoleThemeStyle.TertiaryText">
            <summary>
            De-emphasized text, for example literal text in output templates and
            punctuation used when writing structured data.
            </summary>
        </member>
        <member name="F:Serilog.Sinks.SystemConsole.Themes.ConsoleThemeStyle.Invalid">
            <summary>
            Output demonstrating some kind of configuration issue, e.g. an invalid
            message template token.
            </summary>
        </member>
        <member name="F:Serilog.Sinks.SystemConsole.Themes.ConsoleThemeStyle.Null">
            <summary>
            The built-in <see langword="null"/> value.
            </summary>
        </member>
        <member name="F:Serilog.Sinks.SystemConsole.Themes.ConsoleThemeStyle.Name">
            <summary>
            Property and type names.
            </summary>
        </member>
        <member name="F:Serilog.Sinks.SystemConsole.Themes.ConsoleThemeStyle.String">
            <summary>
            Strings.
            </summary>
        </member>
        <member name="F:Serilog.Sinks.SystemConsole.Themes.ConsoleThemeStyle.Number">
            <summary>
            Numbers.
            </summary>
        </member>
        <member name="F:Serilog.Sinks.SystemConsole.Themes.ConsoleThemeStyle.Boolean">
            <summary>
            <see cref="T:System.Boolean"/> values.
            </summary>
        </member>
        <member name="F:Serilog.Sinks.SystemConsole.Themes.ConsoleThemeStyle.Scalar">
            <summary>
            All other scalar values, e.g. <see cref="T:System.Guid"/> instances.
            </summary>
        </member>
        <member name="F:Serilog.Sinks.SystemConsole.Themes.ConsoleThemeStyle.Object">
            <summary>
            Unrecognized literal values, e.g. <see cref="T:System.Guid"/> instances.
            </summary>
        </member>
        <member name="F:Serilog.Sinks.SystemConsole.Themes.ConsoleThemeStyle.LevelVerbose">
            <summary>
            Level indicator.
            </summary>
        </member>
        <member name="F:Serilog.Sinks.SystemConsole.Themes.ConsoleThemeStyle.LevelDebug">
            <summary>
            Level indicator.
            </summary>
        </member>
        <member name="F:Serilog.Sinks.SystemConsole.Themes.ConsoleThemeStyle.LevelInformation">
            <summary>
            Level indicator.
            </summary>
        </member>
        <member name="F:Serilog.Sinks.SystemConsole.Themes.ConsoleThemeStyle.LevelWarning">
            <summary>
            Level indicator.
            </summary>
        </member>
        <member name="F:Serilog.Sinks.SystemConsole.Themes.ConsoleThemeStyle.LevelError">
            <summary>
            Level indicator.
            </summary>
        </member>
        <member name="F:Serilog.Sinks.SystemConsole.Themes.ConsoleThemeStyle.LevelFatal">
            <summary>
            Level indicator.
            </summary>
        </member>
        <member name="T:Serilog.Sinks.SystemConsole.Themes.SystemConsoleTheme">
            <summary>
            A console theme using the styling facilities of the <see cref="T:System.Console"/> class. Recommended
            for Windows versions prior to Windows 10.
            </summary>
        </member>
        <member name="P:Serilog.Sinks.SystemConsole.Themes.SystemConsoleTheme.Grayscale">
            <summary>
            A theme using only gray, black and white.
            </summary>
        </member>
        <member name="P:Serilog.Sinks.SystemConsole.Themes.SystemConsoleTheme.Literate">
            <summary>
            A theme in the style of the original <i>Serilog.Sinks.Literate</i>.
            </summary>
        </member>
        <member name="P:Serilog.Sinks.SystemConsole.Themes.SystemConsoleTheme.Colored">
            <summary>
            A theme based on the original Serilog "colored console" sink.
            </summary>
        </member>
        <member name="M:Serilog.Sinks.SystemConsole.Themes.SystemConsoleTheme.#ctor(System.Collections.Generic.IReadOnlyDictionary{Serilog.Sinks.SystemConsole.Themes.ConsoleThemeStyle,Serilog.Sinks.SystemConsole.Themes.SystemConsoleThemeStyle})">
            <summary>
            Construct a theme given a set of styles.
            </summary>
            <param name="styles">Styles to apply within the theme.</param>
            <exception cref="T:System.ArgumentNullException">When <paramref name="styles"/> is <code>null</code></exception>
        </member>
        <member name="P:Serilog.Sinks.SystemConsole.Themes.SystemConsoleTheme.Styles">
            <inheritdoc/>
        </member>
        <member name="P:Serilog.Sinks.SystemConsole.Themes.SystemConsoleTheme.CanBuffer">
            <inheritdoc/>
        </member>
        <member name="P:Serilog.Sinks.SystemConsole.Themes.SystemConsoleTheme.ResetCharCount">
            <inheritdoc/>
        </member>
        <member name="M:Serilog.Sinks.SystemConsole.Themes.SystemConsoleTheme.Set(System.IO.TextWriter,Serilog.Sinks.SystemConsole.Themes.ConsoleThemeStyle)">
            <inheritdoc/>
        </member>
        <member name="M:Serilog.Sinks.SystemConsole.Themes.SystemConsoleTheme.Reset(System.IO.TextWriter)">
            <inheritdoc/>
        </member>
        <member name="T:Serilog.Sinks.SystemConsole.Themes.SystemConsoleThemeStyle">
            <summary>
            Styling applied using the <see cref="T:System.ConsoleColor"/> enumeration.
            </summary>
        </member>
        <member name="F:Serilog.Sinks.SystemConsole.Themes.SystemConsoleThemeStyle.Foreground">
            <summary>
            The foreground color to apply.
            </summary>
        </member>
        <member name="F:Serilog.Sinks.SystemConsole.Themes.SystemConsoleThemeStyle.Background">
            <summary>
            The background color to apply.
            </summary>
        </member>
    </members>
</doc>
