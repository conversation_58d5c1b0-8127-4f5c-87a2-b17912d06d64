using System;
using System.Collections.Generic;

namespace Alsi.Fuzz.Core.Service.CaseFactory.Factories.Iso14229.Consts
{
    public class UdsService
    {
        public UdsService(byte id, string name)
        {
            Id = id;
            Name = name;
        }

        public byte Id { get; set; }
        public string Name { get; set; }

        public List<Subfunction> Subfunctions { get; set; } = new List<Subfunction>();

        public UdsService AddSubfuntions(byte[] subfunctions, byte[] lengthArray)
        {
            if (subfunctions.Length != lengthArray.Length)
            {
                throw new Exception($"The subfunctions's count is not equal to the length array's count");
            }

            for (var i = 0; i < subfunctions.Length && i < lengthArray.Length; i++)
            {
                var subfunction = new Subfunction(subfunctions[i], lengthArray[i]);
                Subfunctions.Add(subfunction);
            }
            return this;
        }
    }
}
