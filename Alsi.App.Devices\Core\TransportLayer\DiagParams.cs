namespace Alsi.App.Devices.Core.TransportLayer
{
    public class DiagParams
    {
        public int P2ExtTime { get; set; } = 5000;

        /// <summary>
        /// 接收方发送流控帧之后，发送方发送的连续帧之间的时间最小间隔
        /// 如果值为0，表示对于发送方发送CF的最小时间没有要求
        /// </summary>
        public byte StMin { get; set; } = 0;

        /// <summary>
        /// 接收方发送流控帧之后，发送方被允许连续发送的最大帧数目
        /// 特殊情况下，如果该值为0，则表示发送连续帧没有限制
        /// 如果值为8，表示发送方最多能连续发送8帧CF就会继续受到接收方的流控帧
        /// </summary>
        public byte Bs { get; set; } = 0;

        public int N_Bs { get; set; } = 1000;
    }
}
