{"version": 3, "file": "js/393.ab0d17d2.js", "mappings": "kRAGA,MAAMA,EAAa,CAAEC,MAAO,0BACtBC,EAAa,CAAED,MAAO,gBACtBE,EAAa,CACjBC,IAAK,EACLH,MAAO,qBAEHI,EAAa,CAAEJ,MAAO,2BACtBK,EAAa,CACjBF,IAAK,EACLH,MAAO,qBAEHM,EAAa,CACjBH,IAAK,EACLH,MAAO,mBAEHO,EAAa,CACjBJ,IAAK,EACLH,MAAO,iBAEHQ,EAAa,CAAC,WACdC,EAAa,CAAET,MAAO,eACtBU,EAAc,CAAEV,MAAO,6BACvBW,EAAc,CAAEX,MAAO,qBACvBY,EAAc,CAAEZ,MAAO,mBACvBa,EAAc,CAAEb,MAAO,iBACvBc,EAAc,CAAEd,MAAO,iBACvBe,EAAc,CAAEf,MAAO,iBACvBgB,EAAc,CAAEhB,MAAO,iBACvBiB,EAAc,CAAEjB,MAAO,gBACvBkB,EAAc,CAAElB,MAAO,eACvBmB,EAAc,CAAEnB,MAAO,kBACvBoB,EAAc,CAAEpB,MAAO,kBACvBqB,EAAc,CAAElB,IAAK,GACrBmB,EAAc,CAClBnB,IAAK,EACLH,MAAO,mBAEHuB,EAAc,CAAEvB,MAAO,eACvBwB,EAAc,CAAExB,MAAO,uBACvByB,EAAc,CAClBtB,IAAK,EACLH,MAAO,mBAaT,OAA4B0B,EAAAA,EAAAA,IAAiB,CAC3CC,OAAQ,cACRC,KAAAA,CAAMC,GCiFR,MAAMC,GAAcC,EAAAA,EAAAA,IAAkB,IAChCC,GAAcD,EAAAA,EAAAA,IAAkB,IAChCE,GAAiBF,EAAAA,EAAAA,IAAmB,MACpCG,GAAiBH,EAAAA,EAAAA,IAAmB,MACpCI,GAAiBJ,EAAAA,EAAAA,KAAI,GAGrBK,GAAqBL,EAAAA,EAAAA,KAAI,GACzBM,GAAeN,EAAAA,EAAAA,KAAI,GACnBO,GAAoBP,EAAAA,EAAAA,KAAI,GAGxBQ,GAAsBR,EAAAA,EAAAA,KAAI,GAG1BS,GAAmBC,EAAAA,EAAAA,KAAS,KAChC,IAAKR,EAAeS,MAAO,MAAO,GAClC,MAAMC,EAAeb,EAAYY,MAAME,MAAKC,GAAQA,EAAKC,KAAOb,EAAeS,QAC/E,OAAOC,EAAeA,EAAaI,iBAAmB,EAAE,IAIpDC,EAAmBC,UACvBb,EAAmBM,OAAQ,EAC3B,IACE,MAAMQ,QAAiBC,EAAAA,GAAOC,iBAC9BtB,EAAYY,MAAQQ,EAASG,I,CAC7B,MAAOC,GACPC,QAAQD,MAAM,cAAeA,GAC7BE,EAAAA,GAAUF,MAAM,+B,CAChB,QACAlB,EAAmBM,OAAQ,C,GAKzBe,EAAkBC,IACtBC,EAAiBD,EAAIZ,GAAG,EAIpBa,EAAmBV,UACvBhB,EAAeS,MAAQkB,QACjBC,EAAWD,GACjBzB,EAAeO,OAAQ,CAAI,EAIvBmB,EAAaZ,UACjBZ,EAAaK,OAAQ,EACrBV,EAAYU,MAAQ,GAEpB,IACE,MAAMQ,QAAiBC,EAAAA,GAAOW,SAASF,GACvC5B,EAAYU,MAAQQ,EAASG,I,CAC7B,MAAOC,GACPC,QAAQD,MAAM,cAAeA,GAC7BE,EAAAA,GAAUF,MAAM,+B,CAChB,QACAjB,EAAaK,OAAQ,C,GAKnBqB,EAAiBC,IACrBC,EAAAA,EAAaC,QACX,gDAAgDF,EAAOjB,qBACvD,UACA,CACEoB,kBAAmB,SACnBC,iBAAkB,SAClBC,KAAM,YAGPC,MAAK,KACJC,EAAiBP,EAAOlB,GAAG,IAE5B0B,OAAM,QAEL,EAIAD,EAAmBtB,UACvB,UACQE,EAAAA,GAAOoB,iBAAiBX,GAC9BJ,EAAAA,GAAUiB,QAAQ,0CAGZzB,IAGFf,EAAeS,QAAUkB,IAC3B3B,EAAeS,MAAQ,KACvBV,EAAYU,MAAQ,GACpBP,EAAeO,OAAQ,E,CAEzB,MAAOY,GACPC,QAAQD,MAAM,YAAaA,GAC3BE,EAAAA,GAAUF,MAAM,+B,GAKdoB,EAAqBzB,UACzB,GAAKW,EAAL,CAKAtB,EAAkBI,OAAQ,EAC1B,UACQS,EAAAA,GAAOuB,mBAAmBd,GAChCJ,EAAAA,GAAUiB,QAAQ,iC,CAClB,MAAOnB,GACPC,QAAQD,MAAM,0BAA2BA,GACzCE,EAAAA,GAAUF,MAAM,iC,CAChB,QACAhB,EAAkBI,OAAQ,C,OAZ1Bc,EAAAA,GAAUmB,QAAQ,gD,EAiBhBC,EAAkBC,IACtB3C,EAAeQ,MAAQmC,EAAW/B,GAClCP,EAAoBG,OAAQ,CAAI,EAI5BoC,GAAoBA,KACxBvC,EAAoBG,OAAQ,EAC5BR,EAAeQ,MAAQ,IAAI,EAIvBqC,GAAkBC,IACtB,IAAKA,EAAY,OAAO,KACxB,IACE,MAAMC,EAAO,IAAIC,KAAKF,GACtB,OAAOC,EAAKE,gB,CACZ,MAAOC,GACP,OAAOJ,C,GAKLK,GAAqBL,IACzB,IAAKA,EAAY,OAAO,KACxB,IACE,MAAMC,EAAO,IAAIC,KAAKF,GAEhBM,EAAQ,IAAIJ,KACZK,EAAUN,EAAKO,YAAcF,EAAME,WACzBP,EAAKQ,aAAeH,EAAMG,YAC1BR,EAAKS,gBAAkBJ,EAAMI,cAE7C,OAAIH,EAEKN,EAAKU,mBAAmB,GAAI,CAAEC,KAAM,UAAWC,OAAQ,UAAWC,OAAQ,YAG1Eb,EAAKc,mBAAmB,GAAI,CAAEC,MAAO,UAAWC,IAAK,YACrD,IACAhB,EAAKU,mBAAmB,GAAI,CAAEC,KAAM,UAAWC,OAAQ,W,CAEhE,MAAOT,GACP,OAAOJ,C,GAQLkB,GAAoBA,CAACC,EAA2BC,KACpD,IAAKD,IAAcC,EAAS,MAAO,IAEnC,IACE,MAAMC,EAAQ,IAAInB,KAAKiB,GACjBG,EAAM,IAAIpB,KAAKkB,GACfG,EAAaD,EAAIE,UAAYH,EAAMG,UAEzC,GAAID,EAAa,EAAG,MAAO,IAE3B,GAAIA,EAAa,IACf,MAAO,GAAGA,MACL,GAAIA,EAAa,IAAO,CAC7B,MAAME,EAAUC,KAAKC,MAAMJ,EAAa,KACxC,MAAO,GAAGE,I,CACL,GAAIF,EAAa,KAAS,CAC/B,MAAMK,EAAUF,KAAKC,MAAMJ,EAAa,KAClCE,EAAUC,KAAKC,MAAOJ,EAAa,IAAS,KAClD,MAAO,GAAGK,MAAYH,I,CACjB,CACL,MAAMI,EAAQH,KAAKC,MAAMJ,EAAa,MAChCK,EAAUF,KAAKC,MAAOJ,EAAa,KAAW,KACpD,MAAO,GAAGM,MAAUD,I,EAEtB,MAAOxB,GAEP,OADA7B,QAAQD,MAAM,8BAA+B8B,GACtC,G,GDtEX,OC2EA0B,EAAAA,EAAAA,KAAU,KACR9D,GAAkB,ID5Eb,CAAC+D,EAAUC,KAChB,MAAMC,GAAyBC,EAAAA,EAAAA,IAAkB,eAC3CC,GAAsBD,EAAAA,EAAAA,IAAkB,YACxCE,GAAwBF,EAAAA,EAAAA,IAAkB,cAC1CG,GAAqBH,EAAAA,EAAAA,IAAkB,WACvCI,GAAuBJ,EAAAA,EAAAA,IAAkB,aACzCK,GAAwBL,EAAAA,EAAAA,IAAkB,cAEhD,OAAQM,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAoB,MAAO1H,EAAY,EAC3D2H,EAAAA,EAAAA,IAAoB,MAAOzH,EAAY,CACnCkC,EAAeO,QA0HZ8E,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAoB,MAAOnG,EAAa,EACrDoG,EAAAA,EAAAA,IAAoB,MAAOnG,EAAa,EACtCoG,EAAAA,EAAAA,IAAaL,EAAsB,CACjCjD,KAAM,UACNuD,KAAM,QACNC,QAASb,EAAO,KAAOA,EAAO,GAAMc,GAAiB3F,EAAeO,OAAQ,GAC5E1C,MAAO,eACN,CACD+H,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBL,EAAAA,EAAAA,IAAaN,EAAoB,KAAM,CACrCU,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBL,EAAAA,EAAAA,KAAaM,EAAAA,EAAAA,IAAOC,EAAAA,UAEtBC,EAAG,IAELnB,EAAO,KAAOA,EAAO,IAAKoB,EAAAA,EAAAA,IAAiB,cAE7CD,EAAG,KAELT,EAAAA,EAAAA,IAAoB,KAAM,MAAMW,EAAAA,EAAAA,IAAiB7F,EAAiBE,OAAQ,MAE5EgF,EAAAA,EAAAA,IAAoB,MAAOlG,EAAa,EACtCmG,EAAAA,EAAAA,IAAaW,EAAAA,EAAU,CACrBC,MAAOvG,EAAYU,MACnB8F,aAAc5D,GACb,KAAM,EAAG,CAAC,UACZvC,EAAaK,QACT8E,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAoB,MAAOhG,EAAa,EACrDkG,EAAAA,EAAAA,IAAaJ,EAAuB,CAAEkB,SAAS,QAEjDC,EAAAA,EAAAA,IAAoB,IAAI,UAvJ/BlB,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAoB,MAAOvH,EAAY,CACpD8G,EAAO,KAAOA,EAAO,IAAKU,EAAAA,EAAAA,IAAoB,MAAO,CAAE1H,MAAO,eAAiB,EAC7E0H,EAAAA,EAAAA,IAAoB,KAAM,KAAM,kBAC9B,KACJA,EAAAA,EAAAA,IAAoB,MAAOtH,EAAY,CACpCgC,EAAmBM,QACf8E,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAoB,MAAOpH,EAAY,EACpDsH,EAAAA,EAAAA,IAAaV,EAAwB,CACnC0B,KAAM,EACNC,SAAU,QAGgB,IAA7B9G,EAAYY,MAAMmG,SAChBrB,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAoB,MAAOnH,EAAY,EACpDqH,EAAAA,EAAAA,IAAaR,EAAqB,CAAE2B,YAAa,+BAElDtB,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAoB,QAASlH,EAAY,CACtDyG,EAAO,KAAOA,EAAO,IAAKU,EAAAA,EAAAA,IAAoB,QAAS,KAAM,EAC3DA,EAAAA,EAAAA,IAAoB,KAAM,KAAM,EAC9BA,EAAAA,EAAAA,IAAoB,KAAM,CAAE1H,MAAO,eAAiB,SACpD0H,EAAAA,EAAAA,IAAoB,KAAM,CAAE1H,MAAO,qBAAuB,eAC1D0H,EAAAA,EAAAA,IAAoB,KAAM,CAAE1H,MAAO,mBAAqB,aACxD0H,EAAAA,EAAAA,IAAoB,KAAM,CAAE1H,MAAO,iBAAmB,WACtD0H,EAAAA,EAAAA,IAAoB,KAAM,CAAE1H,MAAO,iBAAmB,WACtD0H,EAAAA,EAAAA,IAAoB,KAAM,CAAE1H,MAAO,gBAAkB,UACrD0H,EAAAA,EAAAA,IAAoB,KAAM,CAAE1H,MAAO,kBAAoB,eAEvD,KACJ0H,EAAAA,EAAAA,IAAoB,QAAS,KAAM,GAChCF,EAAAA,EAAAA,KAAW,IAAOC,EAAAA,EAAAA,IAAoBsB,EAAAA,GAAW,MAAMC,EAAAA,EAAAA,IAAYlH,EAAYY,OAAO,CAACgB,EAAKuF,MACnFzB,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAoB,KAAM,CAC9CtH,IAAKuD,EAAIZ,GACT9C,OAAOkJ,EAAAA,EAAAA,IAAgB,CAAE,aAAcD,EAAQ,IAAM,IACrDpB,QAAUC,GAAiBrE,EAAeC,IACzC,EACDgE,EAAAA,EAAAA,IAAoB,KAAMjH,EAAY,EACpCkH,EAAAA,EAAAA,IAAaP,EAAuB,CAClC+B,QAASzF,EAAIX,iBACbqG,UAAW,MACX,aAAc,KACb,CACDrB,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBN,EAAAA,EAAAA,IAAoB,OAAQhH,GAAa2H,EAAAA,EAAAA,IAAiB3E,EAAIX,kBAAmB,MAEnFoF,EAAG,GACF,KAAM,CAAC,eAEZT,EAAAA,EAAAA,IAAoB,KAAM/G,EAAa,EACrCgH,EAAAA,EAAAA,IAAaP,EAAuB,CAClC+B,QAASpE,GAAerB,EAAI2F,cAC5BD,UAAW,MACX,aAAc,KACb,CACDrB,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBN,EAAAA,EAAAA,IAAoB,OAAQ,MAAMW,EAAAA,EAAAA,IAAiBhD,GAAkB3B,EAAI2F,eAAgB,MAE3FlB,EAAG,GACF,KAAM,CAAC,eAEZT,EAAAA,EAAAA,IAAoB,KAAM9G,EAAa,EACrC8G,EAAAA,EAAAA,IAAoB,OAAQ,MAAMW,EAAAA,EAAAA,IAAiBnC,GAAkBxC,EAAI2F,aAAc3F,EAAI4C,MAAO,MAEpGoB,EAAAA,EAAAA,IAAoB,KAAM7G,EAAa,EACrC6G,EAAAA,EAAAA,IAAoB,OAAQ5G,GAAauH,EAAAA,EAAAA,IAAiB3E,EAAI4F,cAAe,MAE/E5B,EAAAA,EAAAA,IAAoB,KAAM3G,EAAa,EACrC2G,EAAAA,EAAAA,IAAoB,OAAQ1G,GAAaqH,EAAAA,EAAAA,IAAiB3E,EAAI6F,cAAe,MAE/E7B,EAAAA,EAAAA,IAAoB,KAAMzG,EAAa,EACrCyG,EAAAA,EAAAA,IAAoB,OAAQxG,GAAamH,EAAAA,EAAAA,IAAiB3E,EAAI8F,YAAa,MAE7E9B,EAAAA,EAAAA,IAAoB,KAAMvG,EAAa,EACrCuG,EAAAA,EAAAA,IAAoB,MAAOtG,EAAa,EACtCuG,EAAAA,EAAAA,IAAaL,EAAsB,CACjCjD,KAAM,UACNuD,KAAM,QACNC,SAAS4B,EAAAA,EAAAA,KAAgB3B,GAAiBpD,EAAmBhB,EAAIZ,KAAM,CAAC,SACxE4G,MAAO,mBACN,CACD3B,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBL,EAAAA,EAAAA,IAAaN,EAAoB,KAAM,CACrCU,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBL,EAAAA,EAAAA,KAAaM,EAAAA,EAAAA,IAAO0B,EAAAA,cAEtBxB,EAAG,OAGPA,EAAG,GACF,KAAM,CAAC,aACVR,EAAAA,EAAAA,IAAaL,EAAsB,CACjCjD,KAAM,SACNuD,KAAM,QACNC,SAAS4B,EAAAA,EAAAA,KAAgB3B,GAAiB/D,EAAcL,IAAO,CAAC,SAChEgG,MAAO,iBACN,CACD3B,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBL,EAAAA,EAAAA,IAAaN,EAAoB,KAAM,CACrCU,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBL,EAAAA,EAAAA,KAAaM,EAAAA,EAAAA,IAAO2B,EAAAA,YAEtBzB,EAAG,OAGPA,EAAG,GACF,KAAM,CAAC,iBAGb,GAAI3H,MACL,MAC0B,IAA7BsB,EAAYY,MAAMmG,SACdrB,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAoB,KAAMpG,EAAa2F,EAAO,KAAOA,EAAO,GAAK,EAC9EU,EAAAA,EAAAA,IAAoB,KAAM,CACxBmC,QAAS,IACT7J,MAAO,aACN,WAAY,QAEjB0I,EAAAA,EAAAA,IAAoB,IAAI,gBAuChDf,EAAAA,EAAAA,IAAamC,EAAAA,EAAkB,CAC7BrB,QAASlG,EAAoBG,MAC7B,mBAAoBsE,EAAO,KAAOA,EAAO,GAAMc,GAAkBvF,EAAqBG,MAAQoF,GAC9FlE,aAAc3B,EAAeS,MAC7BqH,aAAc7H,EAAeQ,MAC7BsH,QAASlF,IACR,KAAM,EAAG,CAAC,UAAW,eAAgB,kBACxC,CAEJ,I,UEvbA,MAAMmF,GAA2B,OAAgB,EAAQ,CAAC,CAAC,YAAY,qBAEvE,O", "sources": ["webpack://fuzz-web/./src/views/testplan/TestResults.vue?4a50", "webpack://fuzz-web/./src/views/testplan/TestResults.vue", "webpack://fuzz-web/./src/views/testplan/TestResults.vue?899d"], "sourcesContent": ["import { defineComponent as _defineComponent } from 'vue'\nimport { createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, renderList as _renderList, Fragment as _Fragment, toDisplayString as _toDisplayString, withCtx as _withCtx, unref as _unref, withModifiers as _withModifiers, normalizeClass as _normalizeClass, createTextVNode as _createTextVNode } from \"vue\"\n\nconst _hoisted_1 = { class: \"test-results-container\" }\nconst _hoisted_2 = { class: \"content-area\" }\nconst _hoisted_3 = {\n  key: 0,\n  class: \"test-results-page\"\n}\nconst _hoisted_4 = { class: \"results-table-container\" }\nconst _hoisted_5 = {\n  key: 0,\n  class: \"loading-container\"\n}\nconst _hoisted_6 = {\n  key: 1,\n  class: \"empty-container\"\n}\nconst _hoisted_7 = {\n  key: 2,\n  class: \"results-table\"\n}\nconst _hoisted_8 = [\"onClick\"]\nconst _hoisted_9 = { class: \"column-name\" }\nconst _hoisted_10 = { class: \"result-name text-ellipsis\" }\nconst _hoisted_11 = { class: \"column-start-time\" }\nconst _hoisted_12 = { class: \"column-duration\" }\nconst _hoisted_13 = { class: \"column-passed\" }\nconst _hoisted_14 = { class: \"success-count\" }\nconst _hoisted_15 = { class: \"column-failed\" }\nconst _hoisted_16 = { class: \"failure-count\" }\nconst _hoisted_17 = { class: \"column-total\" }\nconst _hoisted_18 = { class: \"total-count\" }\nconst _hoisted_19 = { class: \"column-actions\" }\nconst _hoisted_20 = { class: \"action-buttons\" }\nconst _hoisted_21 = { key: 0 }\nconst _hoisted_22 = {\n  key: 1,\n  class: \"test-cases-page\"\n}\nconst _hoisted_23 = { class: \"page-header\" }\nconst _hoisted_24 = { class: \"case-list-container\" }\nconst _hoisted_25 = {\n  key: 0,\n  class: \"loading-overlay\"\n}\n\nimport { ref, onMounted, computed } from 'vue';\r\nimport { ElMessage, ElMessageBox } from 'element-plus';\r\nimport { appApi, TestResult } from '@/api/appApi';\r\nimport { CaseResult } from '@/api/interoperationApi';\r\nimport CaseList from '@/components/test/CaseList.vue';\r\nimport CaseDetailDialog from '@/components/test/CaseDetailDialog.vue';\r\nimport { Delete, Download, Back } from '@element-plus/icons-vue';\r\n\r\n// 状态变量\r\n\nexport default /*@__PURE__*/_defineComponent({\n  __name: 'TestResults',\n  setup(__props) {\n\r\nconst testResults = ref<TestResult[]>([]);\r\nconst caseResults = ref<CaseResult[]>([]);\r\nconst selectedTestId = ref<string | null>(null);\r\nconst selectedCaseId = ref<number | null>(null);\r\nconst showCaseDetail = ref(false);\r\n\r\n// 加载状态\r\nconst loadingTestResults = ref(true);\r\nconst loadingCases = ref(false);\r\nconst downloadingReport = ref(false);\r\n\r\n// 用例详情对话框相关\r\nconst detailDialogVisible = ref(false);\r\n\r\n// 计算属性：获取选中的测试名称\r\nconst selectedTestName = computed(() => {\r\n  if (!selectedTestId.value) return '';\r\n  const selectedTest = testResults.value.find(test => test.id === selectedTestId.value);\r\n  return selectedTest ? selectedTest.resultFolderName : '';\r\n});\r\n\r\n// 获取测试结果列表\r\nconst fetchTestResults = async () => {\r\n  loadingTestResults.value = true;\r\n  try {\r\n    const response = await appApi.getTestResults();\r\n    testResults.value = response.data;\r\n  } catch (error) {\r\n    console.error('获取测试结果列表失败:', error);\r\n    ElMessage.error('Failed to fetch test results');\r\n  } finally {\r\n    loadingTestResults.value = false;\r\n  }\r\n};\r\n\r\n// 处理表格行点击\r\nconst handleRowClick = (row: TestResult) => {\r\n  selectTestResult(row.id);\r\n};\r\n\r\n// 选择测试结果\r\nconst selectTestResult = async (testResultId: string) => {\r\n  selectedTestId.value = testResultId;\r\n  await fetchCases(testResultId);\r\n  showCaseDetail.value = true;\r\n};\r\n\r\n// 获取测试用例列表\r\nconst fetchCases = async (testResultId: string) => {\r\n  loadingCases.value = true;\r\n  caseResults.value = []; // 清空之前的用例\r\n\r\n  try {\r\n    const response = await appApi.getCases(testResultId);\r\n    caseResults.value = response.data;\r\n  } catch (error) {\r\n    console.error('获取测试用例列表失败:', error);\r\n    ElMessage.error('Failed to fetch case results');\r\n  } finally {\r\n    loadingCases.value = false;\r\n  }\r\n};\r\n\r\n// 确认删除\r\nconst confirmDelete = (result: TestResult) => {\r\n  ElMessageBox.confirm(\r\n    `Are you sure you want to delete test result \"${result.resultFolderName}\"?`,\r\n    'Warning',\r\n    {\r\n      confirmButtonText: 'Delete',\r\n      cancelButtonText: 'Cancel',\r\n      type: 'warning',\r\n    }\r\n  )\r\n    .then(() => {\r\n      deleteTestResult(result.id);\r\n    })\r\n    .catch(() => {\r\n      // 用户取消\r\n    });\r\n};\r\n\r\n// 删除测试结果\r\nconst deleteTestResult = async (testResultId: string) => {\r\n  try {\r\n    await appApi.deleteTestResult(testResultId);\r\n    ElMessage.success('Test result deleted successfully');\r\n\r\n    // 刷新测试结果列表\r\n    await fetchTestResults();\r\n\r\n    // 如果删除的是当前选中的测试结果，返回到结果列表\r\n    if (selectedTestId.value === testResultId) {\r\n      selectedTestId.value = null;\r\n      caseResults.value = [];\r\n      showCaseDetail.value = false;\r\n    }\r\n  } catch (error) {\r\n    console.error('删除测试结果失败:', error);\r\n    ElMessage.error('Failed to delete test result');\r\n  }\r\n};\r\n\r\n// 下载HTML报告\r\nconst downloadHtmlReport = async (testResultId: string) => {\r\n  if (!testResultId) {\r\n    ElMessage.warning('No test result selected for report generation');\r\n    return;\r\n  }\r\n\r\n  downloadingReport.value = true;\r\n  try {\r\n    await appApi.downloadHtmlReport(testResultId);\r\n    ElMessage.success('Report downloaded successfully');\r\n  } catch (error) {\r\n    console.error('Download report failed:', error);\r\n    ElMessage.error('Failed to download test report');\r\n  } finally {\r\n    downloadingReport.value = false;\r\n  }\r\n};\r\n\r\n// 查看用例详情\r\nconst viewCaseDetail = (caseResult: CaseResult) => {\r\n  selectedCaseId.value = caseResult.id;\r\n  detailDialogVisible.value = true;\r\n};\r\n\r\n// 关闭详情对话框\r\nconst closeDetailDialog = () => {\r\n  detailDialogVisible.value = false;\r\n  selectedCaseId.value = null;\r\n};\r\n\r\n// 格式化日期 - 完整格式（用于tooltip）\r\nconst formatDateFull = (dateString?: string | null) => {\r\n  if (!dateString) return null;\r\n  try {\r\n    const date = new Date(dateString);\r\n    return date.toLocaleString();\r\n  } catch (e) {\r\n    return dateString;\r\n  }\r\n};\r\n\r\n// 格式化日期 - 紧凑格式（用于表格显示）\r\nconst formatDateCompact = (dateString?: string | null) => {\r\n  if (!dateString) return null;\r\n  try {\r\n    const date = new Date(dateString);\r\n    // 只显示时间部分，或者只显示日期和时间的小时和分钟\r\n    const today = new Date();\r\n    const isToday = date.getDate() === today.getDate() &&\r\n                    date.getMonth() === today.getMonth() &&\r\n                    date.getFullYear() === today.getFullYear();\r\n\r\n    if (isToday) {\r\n      // 如果是今天，只显示时间\r\n      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', second: '2-digit' });\r\n    } else {\r\n      // 否则显示日期和时间，但格式更紧凑\r\n      return date.toLocaleDateString([], { month: '2-digit', day: '2-digit' }) +\r\n             ' ' +\r\n             date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });\r\n    }\r\n  } catch (e) {\r\n    return dateString;\r\n  }\r\n};\r\n\r\n// 保留原来的formatDate函数以兼容其他地方的调用\r\nconst formatDate = formatDateFull;\r\n\r\n// 计算持续时间\r\nconst calculateDuration = (startTime?: string | null, endTime?: string | null) => {\r\n  if (!startTime || !endTime) return '-';\r\n\r\n  try {\r\n    const start = new Date(startTime);\r\n    const end = new Date(endTime);\r\n    const durationMs = end.getTime() - start.getTime();\r\n\r\n    if (durationMs < 0) return '-'; // 防止负值\r\n\r\n    if (durationMs < 1000) {\r\n      return `${durationMs}ms`;\r\n    } else if (durationMs < 60000) {\r\n      const seconds = Math.floor(durationMs / 1000);\r\n      return `${seconds}s`;\r\n    } else if (durationMs < 3600000) {\r\n      const minutes = Math.floor(durationMs / 60000);\r\n      const seconds = Math.floor((durationMs % 60000) / 1000);\r\n      return `${minutes}m ${seconds}s`;\r\n    } else {\r\n      const hours = Math.floor(durationMs / 3600000);\r\n      const minutes = Math.floor((durationMs % 3600000) / 60000);\r\n      return `${hours}h ${minutes}m`;\r\n    }\r\n  } catch (e) {\r\n    console.error('Error calculating duration:', e);\r\n    return '-';\r\n  }\r\n};\r\n\r\n// 组件挂载时获取测试结果列表\r\nonMounted(() => {\r\n  fetchTestResults();\r\n});\r\n\nreturn (_ctx: any,_cache: any) => {\n  const _component_el_skeleton = _resolveComponent(\"el-skeleton\")!\n  const _component_el_empty = _resolveComponent(\"el-empty\")!\n  const _component_el_tooltip = _resolveComponent(\"el-tooltip\")!\n  const _component_el_icon = _resolveComponent(\"el-icon\")!\n  const _component_el_button = _resolveComponent(\"el-button\")!\n  const _component_el_loading = _resolveComponent(\"el-loading\")!\n\n  return (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [\n    _createElementVNode(\"div\", _hoisted_2, [\n      (!showCaseDetail.value)\n        ? (_openBlock(), _createElementBlock(\"div\", _hoisted_3, [\n            _cache[4] || (_cache[4] = _createElementVNode(\"div\", { class: \"page-header\" }, [\n              _createElementVNode(\"h3\", null, \"Test Results\")\n            ], -1)),\n            _createElementVNode(\"div\", _hoisted_4, [\n              (loadingTestResults.value)\n                ? (_openBlock(), _createElementBlock(\"div\", _hoisted_5, [\n                    _createVNode(_component_el_skeleton, {\n                      rows: 5,\n                      animated: \"\"\n                    })\n                  ]))\n                : (testResults.value.length === 0)\n                  ? (_openBlock(), _createElementBlock(\"div\", _hoisted_6, [\n                      _createVNode(_component_el_empty, { description: \"No test results found\" })\n                    ]))\n                  : (_openBlock(), _createElementBlock(\"table\", _hoisted_7, [\n                      _cache[3] || (_cache[3] = _createElementVNode(\"thead\", null, [\n                        _createElementVNode(\"tr\", null, [\n                          _createElementVNode(\"th\", { class: \"column-name\" }, \"Name\"),\n                          _createElementVNode(\"th\", { class: \"column-start-time\" }, \"Start Time\"),\n                          _createElementVNode(\"th\", { class: \"column-duration\" }, \"Duration\"),\n                          _createElementVNode(\"th\", { class: \"column-passed\" }, \"Passed\"),\n                          _createElementVNode(\"th\", { class: \"column-failed\" }, \"Failed\"),\n                          _createElementVNode(\"th\", { class: \"column-total\" }, \"Total\"),\n                          _createElementVNode(\"th\", { class: \"column-actions\" }, \"Actions\")\n                        ])\n                      ], -1)),\n                      _createElementVNode(\"tbody\", null, [\n                        (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(testResults.value, (row, index) => {\n                          return (_openBlock(), _createElementBlock(\"tr\", {\n                            key: row.id,\n                            class: _normalizeClass({ 'row-stripe': index % 2 === 1 }),\n                            onClick: ($event: any) => (handleRowClick(row))\n                          }, [\n                            _createElementVNode(\"td\", _hoisted_9, [\n                              _createVNode(_component_el_tooltip, {\n                                content: row.resultFolderName,\n                                placement: \"top\",\n                                \"show-after\": 500\n                              }, {\n                                default: _withCtx(() => [\n                                  _createElementVNode(\"span\", _hoisted_10, _toDisplayString(row.resultFolderName), 1)\n                                ]),\n                                _: 2\n                              }, 1032, [\"content\"])\n                            ]),\n                            _createElementVNode(\"td\", _hoisted_11, [\n                              _createVNode(_component_el_tooltip, {\n                                content: formatDateFull(row.creationTime),\n                                placement: \"top\",\n                                \"show-after\": 500\n                              }, {\n                                default: _withCtx(() => [\n                                  _createElementVNode(\"span\", null, _toDisplayString(formatDateCompact(row.creationTime)), 1)\n                                ]),\n                                _: 2\n                              }, 1032, [\"content\"])\n                            ]),\n                            _createElementVNode(\"td\", _hoisted_12, [\n                              _createElementVNode(\"span\", null, _toDisplayString(calculateDuration(row.creationTime, row.end)), 1)\n                            ]),\n                            _createElementVNode(\"td\", _hoisted_13, [\n                              _createElementVNode(\"span\", _hoisted_14, _toDisplayString(row.successCount), 1)\n                            ]),\n                            _createElementVNode(\"td\", _hoisted_15, [\n                              _createElementVNode(\"span\", _hoisted_16, _toDisplayString(row.failureCount), 1)\n                            ]),\n                            _createElementVNode(\"td\", _hoisted_17, [\n                              _createElementVNode(\"span\", _hoisted_18, _toDisplayString(row.totalCount), 1)\n                            ]),\n                            _createElementVNode(\"td\", _hoisted_19, [\n                              _createElementVNode(\"div\", _hoisted_20, [\n                                _createVNode(_component_el_button, {\n                                  type: \"primary\",\n                                  size: \"small\",\n                                  onClick: _withModifiers(($event: any) => (downloadHtmlReport(row.id)), [\"stop\"]),\n                                  title: 'Download Report'\n                                }, {\n                                  default: _withCtx(() => [\n                                    _createVNode(_component_el_icon, null, {\n                                      default: _withCtx(() => [\n                                        _createVNode(_unref(Download))\n                                      ]),\n                                      _: 1\n                                    })\n                                  ]),\n                                  _: 2\n                                }, 1032, [\"onClick\"]),\n                                _createVNode(_component_el_button, {\n                                  type: \"danger\",\n                                  size: \"small\",\n                                  onClick: _withModifiers(($event: any) => (confirmDelete(row)), [\"stop\"]),\n                                  title: 'Delete Result'\n                                }, {\n                                  default: _withCtx(() => [\n                                    _createVNode(_component_el_icon, null, {\n                                      default: _withCtx(() => [\n                                        _createVNode(_unref(Delete))\n                                      ]),\n                                      _: 1\n                                    })\n                                  ]),\n                                  _: 2\n                                }, 1032, [\"onClick\"])\n                              ])\n                            ])\n                          ], 10, _hoisted_8))\n                        }), 128)),\n                        (testResults.value.length === 0)\n                          ? (_openBlock(), _createElementBlock(\"tr\", _hoisted_21, _cache[2] || (_cache[2] = [\n                              _createElementVNode(\"td\", {\n                                colspan: \"7\",\n                                class: \"empty-row\"\n                              }, \"No data\", -1)\n                            ])))\n                          : _createCommentVNode(\"\", true)\n                      ])\n                    ]))\n            ])\n          ]))\n        : (_openBlock(), _createElementBlock(\"div\", _hoisted_22, [\n            _createElementVNode(\"div\", _hoisted_23, [\n              _createVNode(_component_el_button, {\n                type: \"primary\",\n                size: \"small\",\n                onClick: _cache[0] || (_cache[0] = ($event: any) => (showCaseDetail.value = false)),\n                class: \"back-button\"\n              }, {\n                default: _withCtx(() => [\n                  _createVNode(_component_el_icon, null, {\n                    default: _withCtx(() => [\n                      _createVNode(_unref(Back))\n                    ]),\n                    _: 1\n                  }),\n                  _cache[5] || (_cache[5] = _createTextVNode(\" Back \"))\n                ]),\n                _: 1\n              }),\n              _createElementVNode(\"h3\", null, _toDisplayString(selectedTestName.value), 1)\n            ]),\n            _createElementVNode(\"div\", _hoisted_24, [\n              _createVNode(CaseList, {\n                cases: caseResults.value,\n                onViewDetail: viewCaseDetail\n              }, null, 8, [\"cases\"]),\n              (loadingCases.value)\n                ? (_openBlock(), _createElementBlock(\"div\", _hoisted_25, [\n                    _createVNode(_component_el_loading, { visible: true })\n                  ]))\n                : _createCommentVNode(\"\", true)\n            ])\n          ]))\n    ]),\n    _createVNode(CaseDetailDialog, {\n      visible: detailDialogVisible.value,\n      \"onUpdate:visible\": _cache[1] || (_cache[1] = ($event: any) => ((detailDialogVisible).value = $event)),\n      testResultId: selectedTestId.value,\n      caseResultId: selectedCaseId.value,\n      onClose: closeDetailDialog\n    }, null, 8, [\"visible\", \"testResultId\", \"caseResultId\"])\n  ]))\n}\n}\n\n})", "<template>\r\n  <div class=\"test-results-container\">\r\n    <!-- 主内容区域 - 单页布局 -->\r\n    <div class=\"content-area\">\r\n      <!-- 测试结果列表页面 -->\r\n      <div v-if=\"!showCaseDetail\" class=\"test-results-page\">\r\n        <div class=\"page-header\">\r\n          <h3>Test Results</h3>\r\n        </div>\r\n\r\n        <!-- 测试结果表格 -->\r\n        <div class=\"results-table-container\">\r\n          <div v-if=\"loadingTestResults\" class=\"loading-container\">\r\n            <el-skeleton :rows=\"5\" animated />\r\n          </div>\r\n\r\n          <div v-else-if=\"testResults.length === 0\" class=\"empty-container\">\r\n            <el-empty description=\"No test results found\" />\r\n          </div>\r\n\r\n          <table v-else class=\"results-table\">\r\n            <thead>\r\n              <tr>\r\n                <th class=\"column-name\">Name</th>\r\n                <th class=\"column-start-time\">Start Time</th>\r\n                <th class=\"column-duration\">Duration</th>\r\n                <th class=\"column-passed\">Passed</th>\r\n                <th class=\"column-failed\">Failed</th>\r\n                <th class=\"column-total\">Total</th>\r\n                <th class=\"column-actions\">Actions</th>\r\n              </tr>\r\n            </thead>\r\n            <tbody>\r\n              <tr\r\n                v-for=\"(row, index) in testResults\"\r\n                :key=\"row.id\"\r\n                :class=\"{ 'row-stripe': index % 2 === 1 }\"\r\n                @click=\"handleRowClick(row)\"\r\n              >\r\n                <td class=\"column-name\">\r\n                  <el-tooltip :content=\"row.resultFolderName\" placement=\"top\" :show-after=\"500\">\r\n                    <span class=\"result-name text-ellipsis\">{{ row.resultFolderName }}</span>\r\n                  </el-tooltip>\r\n                </td>\r\n                <td class=\"column-start-time\">\r\n                  <el-tooltip :content=\"formatDateFull(row.creationTime)\" placement=\"top\" :show-after=\"500\">\r\n                    <span>{{ formatDateCompact(row.creationTime) }}</span>\r\n                  </el-tooltip>\r\n                </td>\r\n                <td class=\"column-duration\">\r\n                  <span>{{ calculateDuration(row.creationTime, row.end) }}</span>\r\n                </td>\r\n                <td class=\"column-passed\">\r\n                  <span class=\"success-count\">{{ row.successCount }}</span>\r\n                </td>\r\n                <td class=\"column-failed\">\r\n                  <span class=\"failure-count\">{{ row.failureCount }}</span>\r\n                </td>\r\n                <td class=\"column-total\">\r\n                  <span class=\"total-count\">{{ row.totalCount }}</span>\r\n                </td>\r\n                <td class=\"column-actions\">\r\n                  <div class=\"action-buttons\">\r\n                    <el-button\r\n                      type=\"primary\"\r\n                      size=\"small\"\r\n                      @click.stop=\"downloadHtmlReport(row.id)\"\r\n                      :title=\"'Download Report'\"\r\n                    >\r\n                      <el-icon><Download /></el-icon>\r\n                    </el-button>\r\n\r\n                    <el-button\r\n                      type=\"danger\"\r\n                      size=\"small\"\r\n                      @click.stop=\"confirmDelete(row)\"\r\n                      :title=\"'Delete Result'\"\r\n                    >\r\n                      <el-icon><Delete /></el-icon>\r\n                    </el-button>\r\n                  </div>\r\n                </td>\r\n              </tr>\r\n              <tr v-if=\"testResults.length === 0\">\r\n                <td colspan=\"7\" class=\"empty-row\">No data</td>\r\n              </tr>\r\n            </tbody>\r\n          </table>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 测试用例详情页面 -->\r\n      <div v-else class=\"test-cases-page\">\r\n        <div class=\"page-header\">\r\n          <el-button\r\n            type=\"primary\"\r\n            size=\"small\"\r\n            @click=\"showCaseDetail = false\"\r\n            class=\"back-button\"\r\n          >\r\n            <el-icon><Back /></el-icon>\r\n            Back\r\n          </el-button>\r\n          <h3>{{ selectedTestName }}</h3>\r\n        </div>\r\n\r\n        <div class=\"case-list-container\">\r\n          <CaseList\r\n            :cases=\"caseResults\"\r\n            @view-detail=\"viewCaseDetail\"\r\n          />\r\n\r\n          <!-- 加载状态 -->\r\n          <div v-if=\"loadingCases\" class=\"loading-overlay\">\r\n            <el-loading :visible=\"true\" />\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 添加用例详情对话框组件 -->\r\n    <CaseDetailDialog\r\n      v-model:visible=\"detailDialogVisible\"\r\n      :testResultId=\"selectedTestId\"\r\n      :caseResultId=\"selectedCaseId\"\r\n      @close=\"closeDetailDialog\"\r\n    />\r\n  </div>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { ref, onMounted, computed } from 'vue';\r\nimport { ElMessage, ElMessageBox } from 'element-plus';\r\nimport { appApi, TestResult } from '@/api/appApi';\r\nimport { CaseResult } from '@/api/interoperationApi';\r\nimport CaseList from '@/components/test/CaseList.vue';\r\nimport CaseDetailDialog from '@/components/test/CaseDetailDialog.vue';\r\nimport { Delete, Download, Back } from '@element-plus/icons-vue';\r\n\r\n// 状态变量\r\nconst testResults = ref<TestResult[]>([]);\r\nconst caseResults = ref<CaseResult[]>([]);\r\nconst selectedTestId = ref<string | null>(null);\r\nconst selectedCaseId = ref<number | null>(null);\r\nconst showCaseDetail = ref(false);\r\n\r\n// 加载状态\r\nconst loadingTestResults = ref(true);\r\nconst loadingCases = ref(false);\r\nconst downloadingReport = ref(false);\r\n\r\n// 用例详情对话框相关\r\nconst detailDialogVisible = ref(false);\r\n\r\n// 计算属性：获取选中的测试名称\r\nconst selectedTestName = computed(() => {\r\n  if (!selectedTestId.value) return '';\r\n  const selectedTest = testResults.value.find(test => test.id === selectedTestId.value);\r\n  return selectedTest ? selectedTest.resultFolderName : '';\r\n});\r\n\r\n// 获取测试结果列表\r\nconst fetchTestResults = async () => {\r\n  loadingTestResults.value = true;\r\n  try {\r\n    const response = await appApi.getTestResults();\r\n    testResults.value = response.data;\r\n  } catch (error) {\r\n    console.error('获取测试结果列表失败:', error);\r\n    ElMessage.error('Failed to fetch test results');\r\n  } finally {\r\n    loadingTestResults.value = false;\r\n  }\r\n};\r\n\r\n// 处理表格行点击\r\nconst handleRowClick = (row: TestResult) => {\r\n  selectTestResult(row.id);\r\n};\r\n\r\n// 选择测试结果\r\nconst selectTestResult = async (testResultId: string) => {\r\n  selectedTestId.value = testResultId;\r\n  await fetchCases(testResultId);\r\n  showCaseDetail.value = true;\r\n};\r\n\r\n// 获取测试用例列表\r\nconst fetchCases = async (testResultId: string) => {\r\n  loadingCases.value = true;\r\n  caseResults.value = []; // 清空之前的用例\r\n\r\n  try {\r\n    const response = await appApi.getCases(testResultId);\r\n    caseResults.value = response.data;\r\n  } catch (error) {\r\n    console.error('获取测试用例列表失败:', error);\r\n    ElMessage.error('Failed to fetch case results');\r\n  } finally {\r\n    loadingCases.value = false;\r\n  }\r\n};\r\n\r\n// 确认删除\r\nconst confirmDelete = (result: TestResult) => {\r\n  ElMessageBox.confirm(\r\n    `Are you sure you want to delete test result \"${result.resultFolderName}\"?`,\r\n    'Warning',\r\n    {\r\n      confirmButtonText: 'Delete',\r\n      cancelButtonText: 'Cancel',\r\n      type: 'warning',\r\n    }\r\n  )\r\n    .then(() => {\r\n      deleteTestResult(result.id);\r\n    })\r\n    .catch(() => {\r\n      // 用户取消\r\n    });\r\n};\r\n\r\n// 删除测试结果\r\nconst deleteTestResult = async (testResultId: string) => {\r\n  try {\r\n    await appApi.deleteTestResult(testResultId);\r\n    ElMessage.success('Test result deleted successfully');\r\n\r\n    // 刷新测试结果列表\r\n    await fetchTestResults();\r\n\r\n    // 如果删除的是当前选中的测试结果，返回到结果列表\r\n    if (selectedTestId.value === testResultId) {\r\n      selectedTestId.value = null;\r\n      caseResults.value = [];\r\n      showCaseDetail.value = false;\r\n    }\r\n  } catch (error) {\r\n    console.error('删除测试结果失败:', error);\r\n    ElMessage.error('Failed to delete test result');\r\n  }\r\n};\r\n\r\n// 下载HTML报告\r\nconst downloadHtmlReport = async (testResultId: string) => {\r\n  if (!testResultId) {\r\n    ElMessage.warning('No test result selected for report generation');\r\n    return;\r\n  }\r\n\r\n  downloadingReport.value = true;\r\n  try {\r\n    await appApi.downloadHtmlReport(testResultId);\r\n    ElMessage.success('Report downloaded successfully');\r\n  } catch (error) {\r\n    console.error('Download report failed:', error);\r\n    ElMessage.error('Failed to download test report');\r\n  } finally {\r\n    downloadingReport.value = false;\r\n  }\r\n};\r\n\r\n// 查看用例详情\r\nconst viewCaseDetail = (caseResult: CaseResult) => {\r\n  selectedCaseId.value = caseResult.id;\r\n  detailDialogVisible.value = true;\r\n};\r\n\r\n// 关闭详情对话框\r\nconst closeDetailDialog = () => {\r\n  detailDialogVisible.value = false;\r\n  selectedCaseId.value = null;\r\n};\r\n\r\n// 格式化日期 - 完整格式（用于tooltip）\r\nconst formatDateFull = (dateString?: string | null) => {\r\n  if (!dateString) return null;\r\n  try {\r\n    const date = new Date(dateString);\r\n    return date.toLocaleString();\r\n  } catch (e) {\r\n    return dateString;\r\n  }\r\n};\r\n\r\n// 格式化日期 - 紧凑格式（用于表格显示）\r\nconst formatDateCompact = (dateString?: string | null) => {\r\n  if (!dateString) return null;\r\n  try {\r\n    const date = new Date(dateString);\r\n    // 只显示时间部分，或者只显示日期和时间的小时和分钟\r\n    const today = new Date();\r\n    const isToday = date.getDate() === today.getDate() &&\r\n                    date.getMonth() === today.getMonth() &&\r\n                    date.getFullYear() === today.getFullYear();\r\n\r\n    if (isToday) {\r\n      // 如果是今天，只显示时间\r\n      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', second: '2-digit' });\r\n    } else {\r\n      // 否则显示日期和时间，但格式更紧凑\r\n      return date.toLocaleDateString([], { month: '2-digit', day: '2-digit' }) +\r\n             ' ' +\r\n             date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });\r\n    }\r\n  } catch (e) {\r\n    return dateString;\r\n  }\r\n};\r\n\r\n// 保留原来的formatDate函数以兼容其他地方的调用\r\nconst formatDate = formatDateFull;\r\n\r\n// 计算持续时间\r\nconst calculateDuration = (startTime?: string | null, endTime?: string | null) => {\r\n  if (!startTime || !endTime) return '-';\r\n\r\n  try {\r\n    const start = new Date(startTime);\r\n    const end = new Date(endTime);\r\n    const durationMs = end.getTime() - start.getTime();\r\n\r\n    if (durationMs < 0) return '-'; // 防止负值\r\n\r\n    if (durationMs < 1000) {\r\n      return `${durationMs}ms`;\r\n    } else if (durationMs < 60000) {\r\n      const seconds = Math.floor(durationMs / 1000);\r\n      return `${seconds}s`;\r\n    } else if (durationMs < 3600000) {\r\n      const minutes = Math.floor(durationMs / 60000);\r\n      const seconds = Math.floor((durationMs % 60000) / 1000);\r\n      return `${minutes}m ${seconds}s`;\r\n    } else {\r\n      const hours = Math.floor(durationMs / 3600000);\r\n      const minutes = Math.floor((durationMs % 3600000) / 60000);\r\n      return `${hours}h ${minutes}m`;\r\n    }\r\n  } catch (e) {\r\n    console.error('Error calculating duration:', e);\r\n    return '-';\r\n  }\r\n};\r\n\r\n// 组件挂载时获取测试结果列表\r\nonMounted(() => {\r\n  fetchTestResults();\r\n});\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.test-results-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  flex: 1;\r\n  padding: 20px;\r\n}\r\n\r\n/* 内容区域样式 */\r\n.content-area {\r\n  flex: 1;\r\n  min-height: 0;\r\n  display: flex;\r\n  flex-direction: column;\r\n  width: 100%;\r\n  min-width: 0; /* 允许容器缩小到小于内容宽度 */\r\n\r\n  .test-results-page, .test-cases-page {\r\n    flex: 1;\r\n    display: flex;\r\n    flex-direction: column;\r\n    overflow: hidden;\r\n  }\r\n\r\n  .page-header {\r\n    display: flex;\r\n    align-items: center;\r\n    margin-bottom: 16px;\r\n\r\n    h2, h3 {\r\n      margin: 0;\r\n      font-size: 16px;\r\n      font-weight: 500;\r\n      color: #303133;\r\n    }\r\n\r\n    .back-button {\r\n      margin-right: 16px;\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .el-icon {\r\n        margin-right: 4px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .results-table-container {\r\n    flex: 1;\r\n    overflow: auto;\r\n    border: 1px solid #ebeef5;\r\n    border-radius: 4px;\r\n    box-shadow: none;\r\n    width: 100%;\r\n    min-width: 0; /* 允许容器缩小到小于内容宽度 */\r\n\r\n    .loading-container, .empty-container {\r\n      padding: 40px;\r\n      display: flex;\r\n      justify-content: center;\r\n      align-items: center;\r\n    }\r\n\r\n    .result-name {\r\n      font-weight: 500;\r\n      color: var(--el-color-primary);\r\n      font-size: 12px;\r\n    }\r\n\r\n    .text-ellipsis {\r\n      max-width: 100%;\r\n      overflow: hidden;\r\n      text-overflow: ellipsis;\r\n      white-space: nowrap;\r\n      display: inline-block;\r\n      width: 100%; /* 确保占满整个单元格 */\r\n    }\r\n\r\n    .success-count {\r\n      color: var(--el-color-success);\r\n      font-weight: 500;\r\n      font-size: 12px;\r\n    }\r\n\r\n    .failure-count {\r\n      color: var(--el-color-danger);\r\n      font-weight: 500;\r\n      font-size: 12px;\r\n    }\r\n\r\n    .total-count {\r\n      color: var(--el-color-primary);\r\n      font-weight: 500;\r\n      font-size: 12px;\r\n    }\r\n\r\n    .action-buttons {\r\n      display: flex;\r\n      justify-content: center;\r\n      gap: 4px;\r\n\r\n      .el-button {\r\n        padding: 5px 8px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .case-list-container {\r\n    flex: 1;\r\n    position: relative;\r\n    border: 1px solid #ebeef5;\r\n    border-radius: 4px;\r\n    overflow: hidden;\r\n\r\n    .loading-overlay {\r\n      position: absolute;\r\n      top: 0;\r\n      left: 0;\r\n      right: 0;\r\n      bottom: 0;\r\n      background-color: rgba(255, 255, 255, 0.7);\r\n      display: flex;\r\n      justify-content: center;\r\n      align-items: center;\r\n      z-index: 100;\r\n    }\r\n  }\r\n}\r\n\r\n/* 普通表格样式 */\r\n.results-table {\r\n  width: 100%;\r\n  border-collapse: collapse;\r\n  font-size: 12px;\r\n  table-layout: auto;\r\n\r\n  /* 表头样式 */\r\n  thead {\r\n    tr {\r\n      background-color: #f5f7fa;\r\n\r\n      th {\r\n        color: #606266;\r\n        font-size: 12px;\r\n        height: 40px;\r\n        padding: 4px 6px;\r\n        font-weight: bold;\r\n        text-align: left;\r\n        border-bottom: 1px solid #ebeef5;\r\n      }\r\n    }\r\n  }\r\n\r\n  /* 表格内容样式 */\r\n  tbody {\r\n    tr {\r\n      cursor: pointer;\r\n      height: 36px;\r\n      border-bottom: 1px solid #ebeef5;\r\n\r\n      &:hover {\r\n        background-color: #f5f7fa;\r\n      }\r\n\r\n      &.row-stripe {\r\n        background-color: #fafafa;\r\n\r\n        &:hover {\r\n          background-color: #f5f7fa;\r\n        }\r\n      }\r\n\r\n      td {\r\n        padding: 0 6px;\r\n        text-align: left;\r\n      }\r\n    }\r\n  }\r\n\r\n  /* 列对齐方式 */\r\n  .column-name {\r\n    text-align: left;\r\n    min-width: 80px;\r\n  }\r\n\r\n  .column-start-time {\r\n    text-align: left;\r\n    min-width: 80px;\r\n  }\r\n\r\n  .column-duration {\r\n    text-align: center;\r\n    min-width: 70px;\r\n  }\r\n\r\n  .column-passed, .column-failed, .column-total {\r\n    text-align: center;\r\n    min-width: 50px;\r\n  }\r\n\r\n  .column-actions {\r\n    text-align: center;\r\n    min-width: 60px;\r\n  }\r\n\r\n  /* 空数据行样式 */\r\n  .empty-row {\r\n    text-align: center;\r\n    padding: 20px;\r\n    color: #909399;\r\n  }\r\n\r\n  /* 优化表格在窄屏幕上的显示 */\r\n  @media (max-width: 768px) {\r\n    font-size: 11px;\r\n\r\n    thead th, tbody td {\r\n      padding: 0 4px;\r\n    }\r\n  }\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .content-area {\r\n    padding: 8px;\r\n\r\n    .page-header {\r\n      flex-direction: column;\r\n      align-items: flex-start;\r\n      gap: 8px;\r\n\r\n      .back-button {\r\n        margin-right: 0;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n", "import script from \"./TestResults.vue?vue&type=script&setup=true&lang=ts\"\nexport * from \"./TestResults.vue?vue&type=script&setup=true&lang=ts\"\n\nimport \"./TestResults.vue?vue&type=style&index=0&id=53a374b6&scoped=true&lang=scss\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-53a374b6\"]])\n\nexport default __exports__"], "names": ["_hoisted_1", "class", "_hoisted_2", "_hoisted_3", "key", "_hoisted_4", "_hoisted_5", "_hoisted_6", "_hoisted_7", "_hoisted_8", "_hoisted_9", "_hoisted_10", "_hoisted_11", "_hoisted_12", "_hoisted_13", "_hoisted_14", "_hoisted_15", "_hoisted_16", "_hoisted_17", "_hoisted_18", "_hoisted_19", "_hoisted_20", "_hoisted_21", "_hoisted_22", "_hoisted_23", "_hoisted_24", "_hoisted_25", "_defineComponent", "__name", "setup", "__props", "testResults", "ref", "caseResults", "selectedTestId", "selectedCaseId", "showCaseDetail", "loadingTestResults", "loadingCases", "downloadingReport", "detailDialogVisible", "selectedTestName", "computed", "value", "selectedTest", "find", "test", "id", "resultFolderName", "fetchTestResults", "async", "response", "appApi", "getTestResults", "data", "error", "console", "ElMessage", "handleRowClick", "row", "selectTestResult", "testResultId", "fetchCases", "getCases", "confirmDelete", "result", "ElMessageBox", "confirm", "confirmButtonText", "cancelButtonText", "type", "then", "deleteTestResult", "catch", "success", "downloadHtmlReport", "warning", "viewCaseDetail", "caseResult", "closeDetailDialog", "formatDateFull", "dateString", "date", "Date", "toLocaleString", "e", "formatDateCompact", "today", "isToday", "getDate", "getMonth", "getFullYear", "toLocaleTimeString", "hour", "minute", "second", "toLocaleDateString", "month", "day", "calculateDuration", "startTime", "endTime", "start", "end", "durationMs", "getTime", "seconds", "Math", "floor", "minutes", "hours", "onMounted", "_ctx", "_cache", "_component_el_skeleton", "_resolveComponent", "_component_el_empty", "_component_el_tooltip", "_component_el_icon", "_component_el_button", "_component_el_loading", "_openBlock", "_createElementBlock", "_createElementVNode", "_createVNode", "size", "onClick", "$event", "default", "_withCtx", "_unref", "Back", "_", "_createTextVNode", "_toDisplayString", "CaseList", "cases", "onViewDetail", "visible", "_createCommentVNode", "rows", "animated", "length", "description", "_Fragment", "_renderList", "index", "_normalizeClass", "content", "placement", "creationTime", "successCount", "failureCount", "totalCount", "_withModifiers", "title", "Download", "Delete", "colspan", "CaseDetailDialog", "caseResultId", "onClose", "__exports__"], "sourceRoot": ""}