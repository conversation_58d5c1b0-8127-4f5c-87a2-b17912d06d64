using Alsi.App.Devices.TsLibCan;
using System;

namespace Alsi.App.Devices.Core.Channels
{
    public class TsLibCanChannel : ICanChannel
    {
        public TsLibCanChannel(DeviceChannel deviceChannel, ChannelConfig config)
        {
            _deviceChannel = deviceChannel;
            _config = config;
        }

        private IntPtr deviceHandle;
        private ulong? timestampDeviation = null;
        private readonly DeviceChannel _deviceChannel;
        private readonly ChannelConfig _config;

        public void Initialize(bool filterErrorFrame)
        {
            TsLibApi.Initialize();

            EncapsulationLibTsCan.filterErrorFrame = filterErrorFrame;
        }

        public void Release()
        {
            TsLibApi.Release();
        }

        public void Start()
        {
            EncapsulationLibTsCan.smttEvent += OnFrameTransmitted;

            var tosunChannel = _deviceChannel.TsLibCanChannelInfo.TosunChanel;
            EncapsulationLibTsCan.Start(ref deviceHandle, _deviceChannel.DeviceSerial, tosunChannel, _config);
        }

        public void Stop()
        {
            timestampDeviation = null;

            EncapsulationLibTsCan.smttEvent -= OnFrameTransmitted;
            if (deviceHandle != IntPtr.Zero)
            {
                EncapsulationLibTsCan.Stop(deviceHandle, _deviceChannel.DeviceSerial);
                deviceHandle = IntPtr.Zero;
            }
        }

        public void Send(CanFrame frame)
        {
            if (deviceHandle == IntPtr.Zero)
            {
                throw new Exception("Can't send data, the device info data is null");
            }

            var tosunChannel = _deviceChannel.TsLibCanChannelInfo.TosunChanel;
            if (frame.IsCanFd)
            {
                EncapsulationLibTsCan.SendCanFDFrameData(deviceHandle, tosunChannel, frame.Id, frame.Dlc, frame.Data, frame.Rtr, frame.IsExt);
            }
            else
            {
                EncapsulationLibTsCan.SendCanFrameData(deviceHandle, tosunChannel, frame.Id, frame.Dlc, frame.Data, frame.Rtr, frame.IsExt);
            }
        }

        private void OnFrameTransmitted(IntPtr deviceHandle, CanFrame frame)
        {
            var tosunChannel = frame.Channel;
            if (this.deviceHandle != deviceHandle
                || _deviceChannel.TsLibCanChannelInfo.TosunChanel != tosunChannel)
            {
                return;
            }

            // 同星通道，转换为应用通道
            frame.Channel = _deviceChannel.Channel;

            if (!DataBus.HardwareTimeBaseChannel.HasValue || DataBus.HardwareTimeBaseChannel == frame.Channel)
            {
                // 如当前通道，是时间戳基准通道，使用硬件时间戳，同步当前总线时间戳
                DataBus.SyncTimeBaseDeviation(frame.TimeUS, frame.Channel);
            }
            else
            {
                // 如当前通道，不是时间戳基准通道，收到首帧时，用当前总线时间戳计算当前通道时间偏差
                if (!timestampDeviation.HasValue)
                {
                    timestampDeviation = DataBus.Timestamp - frame.TimeUS;
                    AppEnv.Logger.Debug($"Initialize timestamp deviation of channel [{frame.Channel} {_deviceChannel.CommunicationType} {_deviceChannel.Name}]: {timestampDeviation.Value}");
                }

                // 用当前通道的首帧时间偏差，更新当前通道所有数据的时间戳
                frame.TimeUS += timestampDeviation.Value;
            }


            frame.Channel = (byte)_deviceChannel.TsLibCanChannelInfo.TosunDisplayChanel;

            DataBus.InvokeOnTransmitted(frame);
        }
    }
}
