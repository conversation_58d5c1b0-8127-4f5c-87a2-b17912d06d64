using System.Collections.Generic;

namespace Alsi.Fuzz.Core.Models.TestPlans
{
    public class TestPlanConfig
    {
        public HardwareConfig HardwareConfig { get; set; } = new HardwareConfig();
        public List<SequenceConfig> SequenceConfigList { get; set; } = new List<SequenceConfig>();
        public CaseConfig CaseConfig { get; set; } = new CaseConfig();
        public SecurityConfig SecurityConfig { get; set; } = new SecurityConfig();
    }
}
