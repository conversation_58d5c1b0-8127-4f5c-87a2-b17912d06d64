using System;
using System.Collections.Generic;
using System.Linq;

namespace Alsi.Fuzz.Core.Utils
{
    /// <summary>
    /// 拉丁超立方体采样(Latin Hypercube Sampling)实现
    /// LHS是一种分层采样方法，确保采样点在每个输入变量的边缘分布上均匀分布
    /// </summary>
    public class LhsSampler
    {
        private static Random _random;

        public LhsSampler(int? seed = null)
        {
            _random = seed.HasValue ? new Random(seed.Value) : new Random();
        }

        /// <summary>
        /// 基于Bit的LHS采样
        /// 将每个位视为一个维度，每个维度有两个可能值(0或1)
        /// </summary>
        /// <param name="length">需要生成的字节数组长度</param>
        /// <param name="sampleCount">要生成的样本数量，默认为字节长度*8</param>
        /// <returns>生成的字节数组列表</returns>
        public List<byte[]> GenerateBitBasedSamples(int length, int? sampleCount = null)
        {
            int samples = sampleCount ?? length * 8;
            var result = new List<byte[]>(samples);
            int totalBits = length * 8;

            // 创建一个包含所有位索引的列表
            var bitIndices = Enumerable.Range(0, totalBits).ToList();

            // 为每个样本翻转不同的位
            var baseData = new byte[length]; // 默认都是0
            result.Add((byte[])baseData.Clone());

            // 随机打乱位索引顺序
            ShuffleList(bitIndices);

            // 为剩余样本生成数据，每次翻转一个不同的位
            for (int i = 0; i < Math.Min(samples - 1, totalBits); i++)
            {
                var data = (byte[])baseData.Clone();
                int bitIndex = bitIndices[i];
                int byteIndex = bitIndex / 8;
                int bitPosition = bitIndex % 8;

                // 翻转指定位
                data[byteIndex] = (byte)(data[byteIndex] | (1 << bitPosition));
                result.Add(data);
            }

            // 如果需要更多样本，随机生成剩余部分
            for (int i = totalBits + 1; i <= samples; i++)
            {
                var data = new byte[length];
                for (int j = 0; j < length; j++)
                {
                    data[j] = (byte)_random.Next(0, 256);
                }
                result.Add(data);
            }

            return result;
        }

        /// <summary>
        /// 基于Byte的LHS采样
        /// 将每个字节位置视为一个维度，每个维度划分为多个区间
        /// </summary>
        /// <param name="length">需要生成的字节数组长度</param>
        /// <param name="samplesPerDimension">每个维度的样本数量</param>
        /// <returns>生成的字节数组列表</returns>
        public List<byte[]> GenerateByteBasedSamples(int length, int samplesPerDimension = 256)
        {
            // 确保样本数量不超过256（字节可能的值范围）
            samplesPerDimension = Math.Min(samplesPerDimension, 256);
            var samples = new List<byte[]>(samplesPerDimension);

            // 为每个字节位置创建均匀划分的区间
            var intervalsByDimension = new List<int[]>(length);
            for (int dim = 0; dim < length; dim++)
            {
                // 为每个维度创建区间
                var intervals = GenerateIntervals(samplesPerDimension);
                // 随机打乱区间，确保维度间的随机性
                ShuffleArray(intervals);
                intervalsByDimension.Add(intervals);
            }

            // 生成样本
            for (int i = 0; i < samplesPerDimension; i++)
            {
                var sample = new byte[length];
                for (int dim = 0; dim < length; dim++)
                {
                    // 从每个维度的不同区间选择值
                    int intervalValue = intervalsByDimension[dim][i];
                    // 在区间内随机选择一个值
                    int intervalSize = 256 / samplesPerDimension;
                    int minValue = intervalValue * intervalSize;
                    int maxValue = (intervalValue + 1) * intervalSize;
                    sample[dim] = (byte)_random.Next(minValue, maxValue);
                }
                samples.Add(sample);
            }

            return samples;
        }

        /// <summary>
        /// 生成均匀分布的区间索引 
        /// </summary>
        private int[] GenerateIntervals(int count)
        {
            var intervals = new int[count];
            for (int i = 0; i < count; i++)
            {
                intervals[i] = i;
            }
            return intervals;
        }

        /// <summary>
        /// 随机打乱数组的顺序 (Fisher-Yates洗牌算法)
        /// </summary>
        public void ShuffleArray<T>(T[] array)
        {
            for (int i = array.Length - 1; i > 0; i--)
            {
                int j = _random.Next(i + 1);
                var temp = array[i];
                array[i] = array[j];
                array[j] = temp;
            }
        }

        /// <summary>
        /// 随机打乱列表的顺序
        /// </summary>
        public void ShuffleList<T>(List<T> list)
        {
            for (int i = list.Count - 1; i > 0; i--)
            {
                int j = _random.Next(i + 1);
                T temp = list[i];
                list[i] = list[j];
                list[j] = temp;
            }
        }
    }
}
