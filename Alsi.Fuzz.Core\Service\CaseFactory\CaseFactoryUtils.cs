using Alsi.Fuzz.Core.Service.CaseFactory.Core;
using Alsi.Fuzz.Core.Service.Results;
using Alsi.Fuzz.Core.Utils;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Alsi.Fuzz.Core.Service.CaseFactory
{
    public static class CaseFactoryUtils
    {
        private static ICaseFactory[] factories = null;

        private static void Initialize()
        {
            if (factories != null)
            {
                return;
            }

            factories = MutationFactoryDiscoverer.GetAllFactories();
        }

        public static CaseResult[] Generate(MutationOptions options)
        {
            Initialize();

            var caseId = 1;
            var list = new List<CaseResult>();

            var isoFactories = factories.Where(x => x.IsoType == options.IsoType).OrderBy(x => x.GetType().Name);
            if (options.IsoType == IsoType.Iso11898)
            {
                foreach (var caseResult in options.InteroperationResults)
                {
                    if (!options.SelectedSequenceNames.Contains(caseResult.SequenceName))
                    {
                        continue;
                    }

                    foreach (var factory in isoFactories)
                    {
                        var caseMutations = factory.Generate(options);
                        foreach (var caseMutation in caseMutations)
                        {
                            var parameter = caseMutation.Serialize();

                            list.Add(new CaseResult()
                            {
                                Id = caseId,
                                Parameter = parameter,
                                Name = caseMutation.Name,
                                SequenceName = caseResult.SequenceName,
                                State = ExecutionState.Pending
                            });
                            ++caseId;
                        }
                    }

                }
            }
            else if (options.IsoType == IsoType.Iso14229)
            {
                var allCaseMutations = new List<CaseMutation>();
                foreach (var factory in isoFactories)
                {
                    var caseMutations = factory.Generate(options);
                    allCaseMutations.AddRange(caseMutations);
                }

                foreach (var caseMutation in allCaseMutations.OrderBy(x => x.Name))
                {
                    var sequenceName = FuzzConsts.DefaultUdsSequenceName;
                    var parameter = caseMutation.Serialize();
                    var field = caseMutation.Fields.FirstOrDefault(x => x.FieldType == MutationFieldType.Payload);
                    if (field != null)
                    {
                        var bytes = SequenceUtils.ParseBytes(field?.Value);
                        if (bytes.Length > 0)
                        {
                            var sid = bytes[0];

                            var xmlService = options.XmlServices.FirstOrDefault(x => x.Id == sid);
                            if (!string.IsNullOrWhiteSpace(xmlService?.SequenceName))
                            {
                                sequenceName = xmlService?.SequenceName;
                            }
                        }
                    }

                    list.Add(new CaseResult()
                    {
                        Id = caseId,
                        Parameter = parameter,
                        Name = caseMutation.Name,
                        SequenceName = sequenceName,
                        State = ExecutionState.Pending
                    });
                    ++caseId;
                }
            }
            else if (options.IsoType == IsoType.Iso15765)
            {
                var allCaseMutations = new List<CaseMutation>();
                foreach (var factory in isoFactories)
                {
                    var caseMutations = factory.Generate(options);
                    allCaseMutations.AddRange(caseMutations);
                }

                #region just for debug
                var groupNames = new List<string>();
                #endregion

                foreach (var caseMutation in allCaseMutations)
                {
                    #region just for debug
                    var groupName = caseMutation.Name.Split('-').First();
                    if (groupNames.Contains(groupName))
                    {
                        //continue;
                    }

                    if (!groupName.StartsWith("G4112"))
                    {
                        //continue;
                    }
                    groupNames.Add(groupName);
                    #endregion

                    var parameter = caseMutation.Serialize();
                    var isBigData = caseMutation.Fields.Any(x => x.FieldType.ToString().StartsWith("TP_BigData_"));
                    var sequenceName = isBigData ? FuzzConsts.TpBigDataSequenceName : FuzzConsts.TpNormalDataSequenceName;

                    list.Add(new CaseResult()
                    {
                        Id = caseId,
                        Parameter = parameter,
                        Name = caseMutation.Name,
                        SequenceName = sequenceName,
                        State = ExecutionState.Pending
                    });
                    ++caseId;
                }
            }
            else
            {
                throw new Exception($"Invalid ISO type: {options.IsoType}");
            }

            return list.ToArray();
        }
    }
}
