using Alsi.Fuzz.Core.Utils;
using Shouldly;

namespace Alsi.Fuzz.UnitTests.Utils
{
    public class LhsSamplerTests
    {
        [Fact]
        public void GenerateBitBasedSamplesTest()
        {
            var dlc = 2;
            var frames = new LhsSampler(0).GenerateBitBasedSamples(dlc);
            frames.Count.ShouldBe(dlc * 8);
        }

        [Fact]
        public void GenerateByteBasedSamplesTest()
        {
            var dlc = 2;
            var frames = new LhsSampler(0).GenerateByteBasedSamples(dlc);
            frames.Count.ShouldBe(256);
        }
    }
}
