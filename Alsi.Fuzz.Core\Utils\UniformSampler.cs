using System;
using System.Collections.Generic;
using System.Linq;

namespace Alsi.Fuzz.Core.Utils
{
    public class UniformSampler
    {
        private Random _random;

        public UniformSampler(Random random)
        {
            _random = random;
        }

        public byte[] UniformSampleBytes(byte min, byte max, int sampleCount)
        {
            return UniformSample(min, max, sampleCount).Select(x => (byte)x).ToArray();
        }

        public int[] UniformSample(int min, int max, int sampleCount)
        {
            if (sampleCount < 1)
            {
                return Array.Empty<int>();
            }

            var length = max - min;
            var step = length / sampleCount;
            // 采样点覆盖的元素数量
            var logicalCount = step * (sampleCount - 1) + 1;
            // 可滑动的起始索引
            var beginIndex = min + _random.Next(0, length - logicalCount);

            var list = new List<int>();
            for (var i = beginIndex; i < max; i += step)
            {
                list.Add(i);
                if (list.Count >= sampleCount)
                {
                    break;
                }
            }

            return list.ToArray();
        }

        public T[] UniformSample<T>(T[] source, int sampleCount)
        {
            if (sampleCount < 1)
            {
                return Array.Empty<T>();
            }

            var step = source.Length / sampleCount;
            // 采样点覆盖的元素数量
            var logicalCount = step * (sampleCount - 1) + 1;
            // 可滑动的起始索引
            var beginIndex = _random.Next(0, source.Length - logicalCount);

            var list = new List<T>();
            for (var i = beginIndex; i < source.Length; i += step)
            {
                list.Add(source[i]);
                if (list.Count >= sampleCount)
                {
                    break;
                }
            }

            return list.ToArray();
        }
    }
}
