using Alsi.App.Devices.Core;
using System;

namespace Alsi.Fuzz.Core.Service.Results
{
    public interface ITestResultReaderService
    {
        TestResult[] GetTestResults();
        CanFrame[] GetFrames(TestResult testResult);
        CaseResult[] GetCaseResults(TestResult testResult);
        CaseStep[] GetCaseSteps(CaseResult caseResult);

        TestResult CreateTestResult(string testPlanName, TestType testType);
        int UpdateTestResult(TestResult testResult);
        void DeleteTestResult(Guid testResultId);
    }
}
