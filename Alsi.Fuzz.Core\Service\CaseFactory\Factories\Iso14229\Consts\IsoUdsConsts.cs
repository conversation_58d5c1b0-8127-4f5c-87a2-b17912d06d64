using System.Collections.Generic;

namespace Alsi.Fuzz.Core.Service.CaseFactory.Factories.Iso14229.Consts
{
    public static class IsoUdsConsts
    {
        private static UdsService[] _services = null;
        public static UdsService[] Services
        {
            get
            {
                if (_services == null)
                {
                    _services = Build();
                }
                return _services;
            }
        }

        private static UdsService[] Build()
        {
            var services = new List<UdsService>();

            var service10 = new UdsService(0x10, "DiagnosticSessionControl");
            service10.AddSubfuntions(
                new byte[] { 0x1, 0x2, 0x3, 0x4 },
                new byte[] { 2, 2, 2, 2 });
            services.Add(service10);

            var service11 = new UdsService(0x11, "ECUReset");
            service11.AddSubfuntions(
                new byte[] { 0x1, 0x2, 0x3, 0x4, 0x5 },
                new byte[] { 2, 2, 2, 2, 2 });
            services.Add(service11);

            var service27Request = new UdsService(0x27, "SecurityAccess-RequestSeed");
            service27Request.AddSubfuntions(
                new byte[] { 0x01, 0x03, 0x05, 0x07, 0x09, 0x0B, 0x0D, 0x0F, 0x11, 0x13, 0x15, 0x17, 0x19, 0x1B, 0x1D, 0x1F, 0x21, 0x23, 0x25, 0x27, 0x29, 0x2B, 0x2D, 0x2F, 0x31, 0x33, 0x35, 0x37, 0x39, 0x3B, 0x3D, 0x3F, 0x41 },
                new byte[] { 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2 });
            services.Add(service27Request);

            var service27Response = new UdsService(0x27, "SecurityAccess-SendKey");
            service27Response.AddSubfuntions(
                new byte[] { 0x02, 0x04, 0x06, 0x08, 0x0A, 0x0C, 0x0E, 0x10, 0x12, 0x14, 0x16, 0x18, 0x1A, 0x1C, 0x1E, 0x20, 0x22, 0x24, 0x26, 0x28, 0x2A, 0x2C, 0x2E, 0x30, 0x32, 0x34, 0x36, 0x38, 0x3A, 0x3C, 0x3E, 0x40, 0x42 },
                // 2 + UIconfigKeylength 预设为 5
                new byte[] { 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5, 5 });
            services.Add(service27Response);

            var service28 = new UdsService(0x28, "CommunicationControl");
            service28.AddSubfuntions(
                new byte[] { 0x0, 0x1, 0x2, 0x3, 0x4, 0x5 },
                new byte[] { 3, 3, 3, 3, 3, 3 });
            services.Add(service28);

            var service3E = new UdsService(0x3E, "TesterPresent");
            service3E.AddSubfuntions(
                new byte[] { 0x0 },
                new byte[] { 2 });
            services.Add(service3E);

            var service29 = new UdsService(0x29, "Authentication");
            service29.AddSubfuntions(
                new byte[] { 0x0, 0x1, 0x2, 0x3, 0x4, 0x5, 0x6, 0x7, 0x8 },
                // 2 + XML Configured
                new byte[] { 2, 5, 5, 5, 5, 5, 5, 5, 2});
            services.Add(service29);

            services.Add(new UdsService(0x84, "SecuredDataTransmission"));

            var service85 = new UdsService(0x85, "ControlDTCSetting");
            service85.AddSubfuntions(
                new byte[] { 0x1, 0x2 },
                new byte[] { 2, 2 });
            services.Add(service85);

            var service86 = new UdsService(0x86, "ResponseOnEvent");
            service86.AddSubfuntions(
                new byte[] { 0x0, 0x1 },
                // TODO: XML Configured
                new byte[] { 5, 5 });
            services.Add(service86);

            var service87 = new UdsService(0x87, "LinkControl");
            service87.AddSubfuntions(
                new byte[] { 0x1, 0x2, 0x3 },
                new byte[] { 3, 3, 2 });
            services.Add(service87);

            services.Add(new UdsService(0x22, "ReadDataByIdentifier"));
            services.Add(new UdsService(0x23, "ReadMemoryByAddress"));
            services.Add(new UdsService(0x24, "ReadScalingDataByIdentifier"));
            services.Add(new UdsService(0x2A, "ReadDataByPeriodicIdentifier"));
            services.Add(new UdsService(0x2C, "DynamicallyDefineDataIdentifier"));
            services.Add(new UdsService(0x2E, "WriteDataByIdentifier"));
            services.Add(new UdsService(0x3D, "WriteMemoryByAddress"));
            services.Add(new UdsService(0x14, "ClearDiagnosticInformation"));

            var service19 = new UdsService(0x19, "ReadDTCInformation");
            service19.AddSubfuntions(
                new byte[] { 0x1, 0x2, 0x3, 0x4, 0x5, 0x6, 0x7, 0x8, 0x9, 0xA, 0xB, 0xC, 0xD, 0xE, 0x14, 0x15, 0x16, 0x17, 0x18, 0x19, 0x1A, 0x42, 0x55, 0x56 },
                new byte[] { 3, 3, 6, 6, 3, 6, 4, 4, 5, 2, 2, 2, 2, 2, 2, 2, 3, 4, 7, 7, 3, 5, 3, 4 });
            services.Add(service19);

            services.Add(new UdsService(0x2F, "InputOutputControlByIdentifier"));
            services.Add(new UdsService(0x31, "RoutineControl"));
            services.Add(new UdsService(0x34, "RequestDownload"));
            services.Add(new UdsService(0x35, "RequestUpload"));
            services.Add(new UdsService(0x36, "TransferData"));
            services.Add(new UdsService(0x37, "RequestTransferExit"));
            services.Add(new UdsService(0x38, "RequestFileTransfer"));

            return services.ToArray();
        }
    }
}
