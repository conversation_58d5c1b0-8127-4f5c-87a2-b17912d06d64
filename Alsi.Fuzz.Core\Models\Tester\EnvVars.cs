using Alsi.Common.Utils;
using Alsi.Fuzz.Core.Models.TestSuites;
using Alsi.Fuzz.Core.Models.TestSuites.Steps.Diagnostic;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Remoting.Messaging;

namespace Alsi.Fuzz.Core.Models.Tester
{
    public static class EnvVarExtension
    {
        public static void Set(this List<EnvVar> envVars, string name, string value)
        {
            var envVar = envVars.FirstOrDefault(x => x.Name.Trim().Equals(name.Trim(), StringComparison.OrdinalIgnoreCase));
            if (envVar != null)
            {
                envVar.Value = value;
            }
            else
            {
                envVars.Add(new EnvVar(name, value));
            }
        }

        public static void Eval(this List<EnvVar> envVars, ref string value)
        {
            foreach (var varName in envVars.Select(x => x.Name).OrderByDescending(x => x.Length))
            {
                if (envVars.TryGet(varName, out var varValue))
                {
                    value = value.Replace(varName, varValue);
                }
            }
        }

        public static void SetByCommand(this List<EnvVar> envVars, IEnumerable<SetVar> setVars, byte[] data)
        {
            foreach (var setVar in setVars)
            {
                if (!string.IsNullOrWhiteSpace(setVar.Index)
                    && int.TryParse(setVar.Index, out var index)
                    && index >= 0)
                {
                    var varBytes = data.Skip(index);
                    if (!string.IsNullOrWhiteSpace(setVar.Length)
                        && int.TryParse(setVar.Length, out var length)
                        && length >= 0)
                    {
                        varBytes.Take(length);
                    }

                    envVars.Set(setVar.Name, varBytes.ToHex());
                }
                else
                {
                    envVars.Set(setVar.Name, setVar.Value);
                }
            }
        }

        public static bool TryGet(this List<EnvVar> envVars, string name, out string value)
        {
            value = string.Empty;
            if (string.IsNullOrWhiteSpace(name))
            {
                return false;
            }

            name = name.Trim();

            if (!name.StartsWith("$"))
            {
                return false;
            }

            var envVar = envVars.FirstOrDefault(x => x.Name.Equals(name, StringComparison.OrdinalIgnoreCase));
            if (envVar != null)
            {
                value = envVar.Value;
                return true;
            }
            return false;
        }
    }
}
