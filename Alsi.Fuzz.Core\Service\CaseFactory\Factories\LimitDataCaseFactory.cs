using Alsi.App.Devices.Core;
using Alsi.Common.Utils;
using Alsi.Fuzz.Core.Service.CaseFactory.Core;
using System.Collections.Generic;

namespace Alsi.Fuzz.Core.Service.CaseFactory.Factories
{
    public class HsLimitDataCaseFactory : LimitDataCaseFactory
    {
        public override CommunicationType CommunicationType => CommunicationType.Can;
    }

    public class FdLimitDataCaseFactory : LimitDataCaseFactory
    {
        public override CommunicationType CommunicationType => CommunicationType.CanFd;
    }

    public abstract class LimitDataCaseFactory : ICaseFactory
    {
        public int Group => 2;
        public abstract CommunicationType CommunicationType { get; }
        public CaseMutation[] Generate(MutationOptions options)
        {
            var dataList = GenerateLimitValues(options.LimitValues, options.Dlc);

            var list = new List<CaseMutation>();
            foreach (var id in options.WhiteListIds)
            {
                for (var i = 0; i < dataList.Count; i++)
                {
                    byte[] data = dataList[i];
                    var name = $"G{Group}-ID{id.ToHex()}-LIMIT{i + 1}/{dataList.Count}";
                    var caseMutation = CaseMutation.Create(name).MutateId(id).MutateData(data);
                    list.Add(caseMutation);
                }
            }

            return list.ToArray();
        }

        private List<byte[]> GenerateLimitValues(byte[] limitValues, int dlc)
        {
            var result = new List<byte[]>();
            foreach (var limitValue in limitValues)
            {
                var data = new byte[dlc];
                for (int i = 0; i < dlc; i++)
                {
                    data[i] = limitValue;
                }
                result.Add(data);
            }
            return result;
        }
    }
}
