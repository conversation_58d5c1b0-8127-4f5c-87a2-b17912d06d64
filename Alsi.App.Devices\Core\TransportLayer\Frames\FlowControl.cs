namespace Alsi.App.Devices.Core.TransportLayer.Frames
{
    public class FlowControl : IDiagFrame
    {
        public FlowControl(byte[] data, bool isTx)
        {
            Data = data;
            IsTx = isTx;
        }

        public bool IsTx { get; }
        public byte[] Data { get; }

        public byte BlockSize => Data[1];
        public byte STmin => Data[2];
        public FlowState FlowState => (FlowState)(Data[0] & 0xF);

        public static FlowControl Build(FlowState flowState, byte blockSize, byte stMin)
        {
            var data = new byte[8];
            data[0] = (byte)(0x30 | (byte)flowState);
            data[1] = blockSize;
            data[2] = stMin;
            return new FlowControl(data, true);
        }

        public static bool TryMatch(byte[] data, bool isTx, out FlowControl flowControl)
        {
            flowControl = null;
            if (data[0] >> 4 == 3)
            {
                flowControl = new FlowControl(data, isTx);
                return true;
            }
            return false;
        }
    }
}
