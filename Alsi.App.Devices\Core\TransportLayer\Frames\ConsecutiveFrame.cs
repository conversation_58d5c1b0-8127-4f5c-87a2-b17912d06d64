using System.Linq;

namespace Alsi.App.Devices.Core.TransportLayer.Frames
{
    public class ConsecutiveFrame : IDiagFrame
    {
        public ConsecutiveFrame(byte[] data, bool isTx)
        {
            Data = data;
            IsTx = isTx;
        }

        public bool IsTx { get; }
        public byte[] Data { get; }
        public byte[] Payload => Data.Skip(1).ToArray();
        public bool IsDiagnosticEnd { get; set; }

        public static bool TryMatch(byte[] data, bool isTx, out ConsecutiveFrame consecutiveFrame)
        {
            consecutiveFrame = null;
            if (data[0] >> 4 == 2)
            {
                consecutiveFrame = new ConsecutiveFrame(data, isTx);
                return true;
            }
            return false;
        }
    }
}
