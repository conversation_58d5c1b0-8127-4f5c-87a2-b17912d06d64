using System.Collections.Generic;
using System.Xml.Serialization;

namespace Alsi.Fuzz.Core.Models.TestSuites.Steps
{
    public class CalcKeyStep: StepBase
    {
        [XmlAttribute("seed")]
        public string Seed { get; set; } = string.Empty;

        [XmlAttribute("hex-level")]
        public string Level { get; set; } = string.Empty;

        [XmlElement("set-var")]
        public List<SetVar> SetVars { get; set; } = new List<SetVar>();
    }
}
