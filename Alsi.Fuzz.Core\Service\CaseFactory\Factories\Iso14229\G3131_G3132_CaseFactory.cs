using Alsi.Fuzz.Core.Service.CaseFactory.Core;
using Alsi.Fuzz.Core.Service.CaseFactory.Factories.Iso14229.Consts;
using System.Collections.Generic;
using System.Linq;

namespace Alsi.Fuzz.Core.Service.CaseFactory.Factories.Iso14229
{
    public class G3131_G3132_CaseFactory : CaseFactoryBase
    {
        public override CaseMutation[] Generate(MutationOptions options)
        {
            var xmlServices = options.XmlServices;

            var supportedXmlServicesWithSubfunction = xmlServices
                // 仅遍历支持的服务
                .Where(x => x.SubfunctionId.HasValue && x.IsSupported)
                // 仅遍历有 Subfunction 的服务
                .Where(x => IsoUdsConsts.Services.FirstOrDefault(service => x.Id == service.Id)?.Subfunctions.Any() == true)
                .ToArray();

            var supportedXmlServicesWithoutSubfunction = xmlServices
                // 仅遍历支持的服务
                .Where(x => !x.SubfunctionId.HasValue && x.IsSupported)
                // 仅遍历没有 Subfunction 的服务
                .Where(x => IsoUdsConsts.Services.FirstOrDefault(service => x.Id == service.Id)?.Subfunctions.Any() == false)
                .ToArray();

            var caseMutations = new List<CaseMutation>();

            foreach (var xmlServiceWithSubfunction in supportedXmlServicesWithSubfunction)
            {
                var sid = xmlServiceWithSubfunction.Id;
                foreach (var xmlServiceWithoutSubfunction in supportedXmlServicesWithoutSubfunction)
                {
                    var payload = new List<byte> { sid, };
                    payload.AddRange(xmlServiceWithoutSubfunction.Parameter2k);
                    var caseMutation = CaseMutation.Create($"G3131-Sid{sid:X2}-OtherSid{xmlServiceWithoutSubfunction.Id:X2}")
                        .MutatePayload(payload.ToArray())
                        .MutatePayloadLength(payload.Count);
                    caseMutations.Add(caseMutation);
                }
            }

            foreach (var xmlServiceWithoutSubfunction in supportedXmlServicesWithoutSubfunction)
            {
                var sid = xmlServiceWithoutSubfunction.Id;
                foreach (var xmlServiceWithSubfunction in supportedXmlServicesWithSubfunction)
                {
                    var payload = new List<byte> { sid, xmlServiceWithSubfunction.SubfunctionId.Value };
                    payload.AddRange(xmlServiceWithSubfunction.Parameter2k);
                    var caseMutation = CaseMutation.Create($"G3132-Sid{sid:X2}-OtherSid{xmlServiceWithSubfunction.Id:X2}")
                        .MutatePayload(payload.ToArray())
                        .MutatePayloadLength(payload.Count);
                    caseMutations.Add(caseMutation);
                }
            }

            return caseMutations.ToArray();
        }
    }
}
