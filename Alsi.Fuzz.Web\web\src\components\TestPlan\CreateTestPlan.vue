<template>
  <el-dialog v-model="dialogVisible" title="Create Test Plan" width="500px">
    <el-form :model="form" label-width="90px">
      <el-form-item label="Name" required>
        <el-input v-model="form.name" placeholder="Enter test plan name" />
      </el-form-item>
      <el-form-item label="Folder" required>
        <div class="folder-input">
          <el-input v-model="form.folder" placeholder="Select save folder" />
          <el-button @click="handleSelectFolder">Browse</el-button>
        </div>
      </el-form-item>
      <el-form-item label="Description">
        <el-input v-model="form.description" type="textarea" placeholder="Enter test plan description" />
      </el-form-item>
    </el-form>
    <template #footer>
      <span>
        <el-button @click="close">Cancel</el-button>
        <el-button type="primary" @click="handleCreateTestPlan">
          Create
        </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts">
import { defineComponent, ref, reactive } from 'vue';
import { ElMessage } from 'element-plus';
import { testPlanApi } from '@/api';
import { explorerApi } from '@/api/explorerApi';

export default defineComponent({
  name: 'CreateTestPlan',
  emits: ['created'],
  setup(props, { emit }) {
    const dialogVisible = ref(false);
    const form = reactive({
      name: '',
      description: '',
      folder: ''
    });

    const handleSelectFolder = async () => {
      try {
        const response = await explorerApi.selectFolder();
        if (response.data) {
          form.folder = response.data;
        }
      } catch (error: any) {
        if (error.response?.status === 400 && error.response.data === "UserCanceled") {
          // 用户取消选择，不需要提示
          return;
        }
        ElMessage.error("Failed to select folder");
      }
    };

    const handleCreateTestPlan = async () => {
      if (!form.name) {
        ElMessage.warning("Please enter test plan name");
        return;
      }

      if (!form.folder) {
        ElMessage.warning("Please select save folder");
        return;
      }

      const response = await testPlanApi.create({
        name: form.name,
        description: form.description,
        folder: form.folder
      });

      if (response.data) {
        close();
        emit('created', response.data);
      }
    };

    const close = () => {
      dialogVisible.value = false;
      form.name = '';
      form.description = '';
      form.folder = '';
    };

    const show = () => {
      dialogVisible.value = true;
    };

    return {
      dialogVisible,
      form,
      handleCreateTestPlan,
      handleSelectFolder,
      close,
      show
    };
  }
});
</script>

<style scoped>
.folder-input {
  display: flex;
  gap: 8px;
  width: 100%;
  /* 确保容器占满整个宽度 */
}

.folder-input .el-input {
  flex: 1;
  /* 让输入框占据除按钮外的所有空间 */
}

:deep(.el-form-item__content) {
  width: 100%;
  /* 确保表单项内容区域占满整个宽度 */
}
</style>
