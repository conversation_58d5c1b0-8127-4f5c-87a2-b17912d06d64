using Alsi.App.Devices.Core.TransportLayer.Frames;
using System;
using System.Threading;

namespace Alsi.App.Devices.Core
{
    /// <summary>
    /// 通用数据帧监听器，支持自定义匹配条件和操作
    /// </summary>
    public class FrameMonitor
    {
        private SemaphoreSlim _semaphore;
        private bool _isListening;
        private int _timeoutMs;
        private Func<CanFrame, bool> _frameMatchPredicate;
        private EventHandler<CanFrame> _frameReceivedHandler;
        private CanFrame _lastMatchedFrame;

        /// <summary>
        /// 最后匹配到的数据帧
        /// </summary>
        public CanFrame LastMatchedFrame => _lastMatchedFrame;

        /// <summary>
        /// 创建数据帧监听器
        /// </summary>
        /// <param name="frameMatchPredicate">数据帧匹配条件</param>
        /// <param name="timeoutMs">超时时间(毫秒)</param>
        public FrameMonitor(Func<CanFrame, bool> frameMatchPredicate, int timeoutMs)
        {
            _frameMatchPredicate = frameMatchPredicate ?? throw new ArgumentNullException(nameof(frameMatchPredicate));
            _timeoutMs = timeoutMs;
            _semaphore = new SemaphoreSlim(0, 1);
        }

        /// <summary>
        /// 开始监听数据帧
        /// </summary>
        public void StartListening()
        {
            if (_isListening)
                return;

            _lastMatchedFrame = null;

            // 创建事件处理器并保存引用
            _frameReceivedHandler = new EventHandler<CanFrame>(OnFrameReceived);
            DataBus.OnReceived += _frameReceivedHandler;
            _isListening = true;
        }

        /// <summary>
        /// 停止监听数据帧
        /// </summary>
        public void StopListening()
        {
            if (!_isListening || _frameReceivedHandler == null)
                return;

            // 使用保存的引用取消订阅
            DataBus.OnReceived -= _frameReceivedHandler;
            _frameReceivedHandler = null;
            _isListening = false;
        }

        /// <summary>
        /// 等待匹配的数据帧
        /// </summary>
        /// <returns>是否在超时前匹配到数据帧</returns>
        public bool WaitForMatch()
        {
            if (_lastMatchedFrame != null)
                return true;

            bool result = _semaphore.Wait(_timeoutMs);

            // 等待结束后自动停止监听
            StopListening();

            // 释放信号量资源
            _semaphore.Dispose();
            _semaphore = null;

            return result;
        }

        /// <summary>
        /// 执行操作并等待匹配的数据帧
        /// </summary>
        /// <param name="action">要执行的操作</param>
        /// <returns>是否在超时前匹配到数据帧</returns>
        public bool ExecuteAndWaitForMatch(Action action)
        {
            if (!_isListening)
                StartListening();

            // 执行操作
            action?.Invoke();

            // 等待匹配
            return WaitForMatch();
        }

        private void OnFrameReceived(object sender, CanFrame frame)
        {
            if (!_frameMatchPredicate(frame))
                return;

            _lastMatchedFrame = frame;
            _semaphore.Release();
        }

        public static FrameMonitor CreateFcMatcher(int id, int timeoutMs)
        {
            return new FrameMonitor(frame =>
            {
                if (frame.Id == id)
                {
                    if (FlowControl.TryMatch(frame.Data, false, out _))
                    {
                        return true;
                    }
                }
                return false;
            }, timeoutMs);
        }

        /// <summary>
        /// 创建基于ID匹配的监听器
        /// </summary>
        public static FrameMonitor CreateIdMatcher(int id, int timeoutMs)
        {
            return new FrameMonitor(frame => frame.Id == id, timeoutMs);
        }

        /// <summary>
        /// 创建基于多条件匹配的监听器
        /// </summary>
        public static FrameMonitor CreateCustomMatcher(Func<CanFrame, bool> matchPredicate, int timeoutMs)
        {
            return new FrameMonitor(matchPredicate, timeoutMs);
        }
    }
}
