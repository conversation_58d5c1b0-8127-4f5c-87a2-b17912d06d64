"use strict";(self["webpackChunkfuzz_web"]=self["webpackChunkfuzz_web"]||[]).push([[393],{9252:function(e,t,l){l.r(t),l.d(t,{default:function(){return G}});l(8111),l(116);var a=l(6768),s=l(4232),n=l(5130),o=l(144),u=l(1219),r=l(2933),c=l(1021),i=l(6256),d=l(7823),k=l(7477);const v={class:"test-results-container"},m={class:"content-area"},f={key:0,class:"test-results-page"},p={class:"results-table-container"},g={key:0,class:"loading-container"},h={key:1,class:"empty-container"},y={key:2,class:"results-table"},L=["onClick"],b={class:"column-name"},w={class:"result-name text-ellipsis"},F={class:"column-start-time"},C={class:"column-duration"},R={class:"column-passed"},_={class:"success-count"},D={class:"column-failed"},T={class:"failure-count"},E={class:"column-total"},K={class:"total-count"},X={class:"column-actions"},$={class:"action-buttons"},M={key:0},N={key:1,class:"test-cases-page"},z={class:"page-header"},I={class:"case-list-container"},Q={key:0,class:"loading-overlay"};var A=(0,a.pM)({__name:"TestResults",setup(e){const t=(0,o.KR)([]),l=(0,o.KR)([]),A=(0,o.KR)(null),S=(0,o.KR)(null),B=(0,o.KR)(!1),G=(0,o.KR)(!0),x=(0,o.KR)(!1),W=(0,o.KR)(!1),V=(0,o.KR)(!1),Y=(0,a.EW)((()=>{if(!A.value)return"";const e=t.value.find((e=>e.id===A.value));return e?e.resultFolderName:""})),H=async()=>{G.value=!0;try{const e=await c.GQ.getTestResults();t.value=e.data}catch(e){console.error("获取测试结果列表失败:",e),u.nk.error("Failed to fetch test results")}finally{G.value=!1}},P=e=>{U(e.id)},U=async e=>{A.value=e,await j(e),B.value=!0},j=async e=>{x.value=!0,l.value=[];try{const t=await c.GQ.getCases(e);l.value=t.data}catch(t){console.error("获取测试用例列表失败:",t),u.nk.error("Failed to fetch case results")}finally{x.value=!1}},q=e=>{r.s.confirm(`Are you sure you want to delete test result "${e.resultFolderName}"?`,"Warning",{confirmButtonText:"Delete",cancelButtonText:"Cancel",type:"warning"}).then((()=>{J(e.id)})).catch((()=>{}))},J=async e=>{try{await c.GQ.deleteTestResult(e),u.nk.success("Test result deleted successfully"),await H(),A.value===e&&(A.value=null,l.value=[],B.value=!1)}catch(t){console.error("删除测试结果失败:",t),u.nk.error("Failed to delete test result")}},O=async e=>{if(e){W.value=!0;try{await c.GQ.downloadHtmlReport(e),u.nk.success("Report downloaded successfully")}catch(t){console.error("Download report failed:",t),u.nk.error("Failed to download test report")}finally{W.value=!1}}else u.nk.warning("No test result selected for report generation")},Z=e=>{S.value=e.id,V.value=!0},ee=()=>{V.value=!1,S.value=null},te=e=>{if(!e)return null;try{const t=new Date(e);return t.toLocaleString()}catch(t){return e}},le=e=>{if(!e)return null;try{const t=new Date(e),l=new Date,a=t.getDate()===l.getDate()&&t.getMonth()===l.getMonth()&&t.getFullYear()===l.getFullYear();return a?t.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit",second:"2-digit"}):t.toLocaleDateString([],{month:"2-digit",day:"2-digit"})+" "+t.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})}catch(t){return e}},ae=(e,t)=>{if(!e||!t)return"-";try{const l=new Date(e),a=new Date(t),s=a.getTime()-l.getTime();if(s<0)return"-";if(s<1e3)return`${s}ms`;if(s<6e4){const e=Math.floor(s/1e3);return`${e}s`}if(s<36e5){const e=Math.floor(s/6e4),t=Math.floor(s%6e4/1e3);return`${e}m ${t}s`}{const e=Math.floor(s/36e5),t=Math.floor(s%36e5/6e4);return`${e}h ${t}m`}}catch(l){return console.error("Error calculating duration:",l),"-"}};return(0,a.sV)((()=>{H()})),(e,u)=>{const r=(0,a.g2)("el-skeleton"),c=(0,a.g2)("el-empty"),W=(0,a.g2)("el-tooltip"),H=(0,a.g2)("el-icon"),U=(0,a.g2)("el-button"),j=(0,a.g2)("el-loading");return(0,a.uX)(),(0,a.CE)("div",v,[(0,a.Lk)("div",m,[B.value?((0,a.uX)(),(0,a.CE)("div",N,[(0,a.Lk)("div",z,[(0,a.bF)(U,{type:"primary",size:"small",onClick:u[0]||(u[0]=e=>B.value=!1),class:"back-button"},{default:(0,a.k6)((()=>[(0,a.bF)(H,null,{default:(0,a.k6)((()=>[(0,a.bF)((0,o.R1)(k.Back))])),_:1}),u[5]||(u[5]=(0,a.eW)(" Back "))])),_:1}),(0,a.Lk)("h3",null,(0,s.v_)(Y.value),1)]),(0,a.Lk)("div",I,[(0,a.bF)(i.A,{cases:l.value,onViewDetail:Z},null,8,["cases"]),x.value?((0,a.uX)(),(0,a.CE)("div",Q,[(0,a.bF)(j,{visible:!0})])):(0,a.Q3)("",!0)])])):((0,a.uX)(),(0,a.CE)("div",f,[u[4]||(u[4]=(0,a.Lk)("div",{class:"page-header"},[(0,a.Lk)("h3",null,"Test Results")],-1)),(0,a.Lk)("div",p,[G.value?((0,a.uX)(),(0,a.CE)("div",g,[(0,a.bF)(r,{rows:5,animated:""})])):0===t.value.length?((0,a.uX)(),(0,a.CE)("div",h,[(0,a.bF)(c,{description:"No test results found"})])):((0,a.uX)(),(0,a.CE)("table",y,[u[3]||(u[3]=(0,a.Lk)("thead",null,[(0,a.Lk)("tr",null,[(0,a.Lk)("th",{class:"column-name"},"Name"),(0,a.Lk)("th",{class:"column-start-time"},"Start Time"),(0,a.Lk)("th",{class:"column-duration"},"Duration"),(0,a.Lk)("th",{class:"column-passed"},"Passed"),(0,a.Lk)("th",{class:"column-failed"},"Failed"),(0,a.Lk)("th",{class:"column-total"},"Total"),(0,a.Lk)("th",{class:"column-actions"},"Actions")])],-1)),(0,a.Lk)("tbody",null,[((0,a.uX)(!0),(0,a.CE)(a.FK,null,(0,a.pI)(t.value,((e,t)=>((0,a.uX)(),(0,a.CE)("tr",{key:e.id,class:(0,s.C4)({"row-stripe":t%2===1}),onClick:t=>P(e)},[(0,a.Lk)("td",b,[(0,a.bF)(W,{content:e.resultFolderName,placement:"top","show-after":500},{default:(0,a.k6)((()=>[(0,a.Lk)("span",w,(0,s.v_)(e.resultFolderName),1)])),_:2},1032,["content"])]),(0,a.Lk)("td",F,[(0,a.bF)(W,{content:te(e.creationTime),placement:"top","show-after":500},{default:(0,a.k6)((()=>[(0,a.Lk)("span",null,(0,s.v_)(le(e.creationTime)),1)])),_:2},1032,["content"])]),(0,a.Lk)("td",C,[(0,a.Lk)("span",null,(0,s.v_)(ae(e.creationTime,e.end)),1)]),(0,a.Lk)("td",R,[(0,a.Lk)("span",_,(0,s.v_)(e.successCount),1)]),(0,a.Lk)("td",D,[(0,a.Lk)("span",T,(0,s.v_)(e.failureCount),1)]),(0,a.Lk)("td",E,[(0,a.Lk)("span",K,(0,s.v_)(e.totalCount),1)]),(0,a.Lk)("td",X,[(0,a.Lk)("div",$,[(0,a.bF)(U,{type:"primary",size:"small",onClick:(0,n.D$)((t=>O(e.id)),["stop"]),title:"Download Report"},{default:(0,a.k6)((()=>[(0,a.bF)(H,null,{default:(0,a.k6)((()=>[(0,a.bF)((0,o.R1)(k.Download))])),_:1})])),_:2},1032,["onClick"]),(0,a.bF)(U,{type:"danger",size:"small",onClick:(0,n.D$)((t=>q(e)),["stop"]),title:"Delete Result"},{default:(0,a.k6)((()=>[(0,a.bF)(H,null,{default:(0,a.k6)((()=>[(0,a.bF)((0,o.R1)(k.Delete))])),_:1})])),_:2},1032,["onClick"])])])],10,L)))),128)),0===t.value.length?((0,a.uX)(),(0,a.CE)("tr",M,u[2]||(u[2]=[(0,a.Lk)("td",{colspan:"7",class:"empty-row"},"No data",-1)]))):(0,a.Q3)("",!0)])]))])]))]),(0,a.bF)(d.A,{visible:V.value,"onUpdate:visible":u[1]||(u[1]=e=>V.value=e),testResultId:A.value,caseResultId:S.value,onClose:ee},null,8,["visible","testResultId","caseResultId"])])}}}),S=l(1241);const B=(0,S.A)(A,[["__scopeId","data-v-53a374b6"]]);var G=B}}]);
//# sourceMappingURL=393.ab0d17d2.js.map