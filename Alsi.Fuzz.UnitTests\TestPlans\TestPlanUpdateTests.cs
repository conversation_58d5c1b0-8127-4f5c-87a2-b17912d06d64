using Alsi.App.Devices.Core;
using Alsi.Fuzz.Core.Models.TestPlans;
using Alsi.Fuzz.Core.Service;
using Alsi.Fuzz.Core.Storage;
using Shouldly;

namespace Alsi.Fuzz.UnitTests.TestPlans
{
    public class TestPlanUpdateTests : UnitTestBase
    {
        private readonly TestPlanService _service;
        private readonly ITestPlanHistoryService _historyService;
        private readonly string _testDir;

        private const string TempFolderName = "AlsiFuzzTests";

        public TestPlanUpdateTests()
            : base()
        {
            _historyService = new TestPlanHistoryService(AppDbContext);
            _service = new TestPlanService(new TestPlanStorage(), _historyService);
            _testDir = Path.Combine(Path.GetTempPath(), TempFolderName, Guid.NewGuid().ToString());
            Directory.CreateDirectory(_testDir);
        }

        [Fact]
        public async Task UpdateBasicInfo_ShouldUpdateDescription()
        {
            // Arrange
            var originalDescription = "原始描述";
            var updatedDescription = "更新后的描述";
            var plan = await _service.CreateAsync(originalDescription);
            var path = Path.Combine(_testDir, "update_description_test.fzp");
            await _service.SaveAsync(plan, path);

            // 确认原始描述
            plan.Manifest.Description.ShouldBe(originalDescription);
            var originalModified = plan.Manifest.Modified;

            // Act
            var updatedPlan = await _service.UpdateBasicInfoAsync(path, updatedDescription);
            var reloadedPlan = await _service.LoadAsync(path);

            // Assert
            updatedPlan.ShouldNotBeNull();
            updatedPlan.Manifest.Description.ShouldBe(updatedDescription);
            updatedPlan.Manifest.Modified.ShouldBeGreaterThan(originalModified);

            // 验证重新加载的计划是否正确更新
            reloadedPlan.ShouldNotBeNull();
            reloadedPlan.Manifest.Description.ShouldBe(updatedDescription);
            reloadedPlan.Manifest.Modified.ShouldBeGreaterThan(originalModified);
        }

        [Fact]
        public async Task UpdateHardwareConfig_ShouldUpdateCanConfig()
        {
            // Arrange
            var plan = await _service.CreateAsync("硬件配置测试");
            var path = Path.Combine(_testDir, "update_can_config_test.fzp");
            await _service.SaveAsync(plan, path);

            // 原始配置
            var originalModified = plan.Manifest.Modified;

            // 创建新的硬件配置
            var newHardwareConfig = new HardwareConfig
            {
                CommunicationType = CommunicationType.Can,
                CanConfig = new CanConfig
                {
                    DeviceChannelName = "VN1630 123456",
                    DataBitrate = 1000000
                }
            };

            // Act
            var updatedPlan = await _service.UpdateHardwareConfigAsync(path, newHardwareConfig);
            var reloadedPlan = await _service.LoadAsync(path);

            // Assert
            updatedPlan.ShouldNotBeNull();
            var hardwareConfig = updatedPlan.Config.HardwareConfig;
            hardwareConfig.ShouldNotBeNull();
            hardwareConfig.CommunicationType.ShouldBe(CommunicationType.Can);
            hardwareConfig.CanConfig.DeviceChannelName.ShouldBe("VN1630 123456");
            hardwareConfig.CanConfig.DataBitrate.ShouldBe(1000000);
            updatedPlan.Manifest.Modified.ShouldBeGreaterThan(originalModified);

            // 验证重新加载的计划是否正确更新
            var reloadHardwareConfig = reloadedPlan.Config.HardwareConfig;
            reloadedPlan.ShouldNotBeNull();
            reloadHardwareConfig.ShouldNotBeNull();
            reloadHardwareConfig.CommunicationType.ShouldBe(CommunicationType.Can);
            reloadHardwareConfig.CanConfig.DeviceChannelName.ShouldBe("VN1630 123456");
            reloadHardwareConfig.CanConfig.DataBitrate.ShouldBe(1000000);
        }

        [Fact]
        public async Task UpdateHardwareConfig_ShouldUpdateCanFdConfig()
        {
            // Arrange
            var plan = await _service.CreateAsync("硬件配置测试 - CANFD");
            var path = Path.Combine(_testDir, "update_canfd_config_test.fzp");
            await _service.SaveAsync(plan, path);

            // 原始配置
            var originalModified = plan.Manifest.Modified;

            // 创建新的硬件配置
            var newHardwareConfig = new HardwareConfig
            {
                CommunicationType = CommunicationType.CanFd,
                CanFdConfig = new CanFdConfig
                {
                    DeviceChannelName = "VN1640 789012",
                    ArbitrationBitrate = 500000,
                    DataBitrate = 2000000
                }
            };

            // Act
            var updatedPlan = await _service.UpdateHardwareConfigAsync(path, newHardwareConfig);
            var reloadedPlan = await _service.LoadAsync(path);

            // Assert
            updatedPlan.ShouldNotBeNull();
            var hardwareConfig = updatedPlan.Config.HardwareConfig;
            hardwareConfig.ShouldNotBeNull();
            hardwareConfig.CommunicationType.ShouldBe(CommunicationType.CanFd);
            hardwareConfig.CanFdConfig.DeviceChannelName.ShouldBe("VN1640 789012");
            hardwareConfig.CanFdConfig.ArbitrationBitrate.ShouldBe(500000);
            hardwareConfig.CanFdConfig.DataBitrate.ShouldBe(2000000);
            updatedPlan.Manifest.Modified.ShouldBeGreaterThan(originalModified);

            // 验证重新加载的计划是否正确更新
            reloadedPlan.ShouldNotBeNull();
            reloadedPlan.Config.HardwareConfig.ShouldNotBeNull();
            reloadedPlan.Config.HardwareConfig.CommunicationType.ShouldBe(CommunicationType.CanFd);
            reloadedPlan.Config.HardwareConfig.CanFdConfig.DeviceChannelName.ShouldBe("VN1640 789012");
            reloadedPlan.Config.HardwareConfig.CanFdConfig.ArbitrationBitrate.ShouldBe(500000);
            reloadedPlan.Config.HardwareConfig.CanFdConfig.DataBitrate.ShouldBe(2000000);
        }

        [Fact]
        public async Task UpdateHardwareConfig_ShouldSwitchBetweenCanAndCanFd()
        {
            // Arrange
            var plan = await _service.CreateAsync("硬件配置切换测试");
            var path = Path.Combine(_testDir, "switch_config_test.fzp");

            // 设置初始CAN配置
            plan.Config.HardwareConfig.CommunicationType = CommunicationType.Can;
            plan.Config.HardwareConfig.CanConfig.DeviceChannelName = "VN1630 123456";
            plan.Config.HardwareConfig.CanConfig.DataBitrate = 500000;
            await _service.SaveAsync(plan, path);

            // 创建CANFD配置
            var canFdConfig = new HardwareConfig
            {
                CommunicationType = CommunicationType.CanFd,
                CanFdConfig = new CanFdConfig
                {
                    DeviceChannelName = "VN1640 789012",
                    ArbitrationBitrate = 500000,
                    DataBitrate = 2000000
                }
            };

            // Act - 从CAN切换到CANFD
            var updatedToFd = await _service.UpdateHardwareConfigAsync(path, canFdConfig);

            // Assert - 验证切换到CANFD成功
            updatedToFd.Config.HardwareConfig.CommunicationType.ShouldBe(CommunicationType.CanFd);

            // 创建CAN配置
            var canConfig = new HardwareConfig
            {
                CommunicationType = CommunicationType.Can,
                CanConfig = new CanConfig
                {
                    DeviceChannelName = "TC1001 ABCDEF",
                    DataBitrate = 250000
                }
            };

            // Act - 从CANFD切换回CAN
            var updatedToCan = await _service.UpdateHardwareConfigAsync(path, canConfig);

            // Assert - 验证切换回CAN成功
            updatedToCan.Config.HardwareConfig.CommunicationType.ShouldBe(CommunicationType.Can);
            updatedToCan.Config.HardwareConfig.CanConfig.DeviceChannelName.ShouldBe("TC1001 ABCDEF");
            updatedToCan.Config.HardwareConfig.CanConfig.DataBitrate.ShouldBe(250000);
        }

        [Fact]
        public async Task UpdateHardwareConfig_WithInvalidPath_ShouldThrow()
        {
            // Arrange
            var invalidPath = Path.Combine(_testDir, "nonexistent.fzp");
            var config = new HardwareConfig
            {
                CommunicationType = CommunicationType.Can,
                CanConfig = new CanConfig
                {
                    DeviceChannelName = "VN1630 123456",
                    DataBitrate = 500000
                }
            };

            // Act & Assert
            await Should.ThrowAsync<FileNotFoundException>(() =>
                _service.UpdateHardwareConfigAsync(invalidPath, config));
        }

        [Fact]
        public async Task UpdateBasicInfo_WithInvalidPath_ShouldThrow()
        {
            // Arrange
            var invalidPath = Path.Combine(_testDir, "nonexistent.fzp");

            // Act & Assert
            await Should.ThrowAsync<FileNotFoundException>(() =>
                _service.UpdateBasicInfoAsync(invalidPath, "新描述"));
        }

        public override void Dispose()
        {
            base.Dispose();

            if (Directory.Exists(_testDir))
            {
                if (_testDir.Contains(TempFolderName))
                {
                    Directory.Delete(_testDir, true);
                }
            }
        }
    }
}
