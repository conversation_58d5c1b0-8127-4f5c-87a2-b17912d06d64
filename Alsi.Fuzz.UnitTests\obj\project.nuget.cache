{"version": 2, "dgSpecHash": "DAzBAFGAVWU=", "success": true, "projectFilePath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.UnitTests\\Alsi.Fuzz.UnitTests.csproj", "expectedPackageFiles": ["D:\\nuget_packages\\coverlet.collector\\3.2.0\\coverlet.collector.3.2.0.nupkg.sha512", "D:\\nuget_packages\\diffengine\\11.3.0\\diffengine.11.3.0.nupkg.sha512", "D:\\nuget_packages\\emptyfiles\\4.4.0\\emptyfiles.4.4.0.nupkg.sha512", "D:\\nuget_packages\\freesql\\3.5.106\\freesql.3.5.106.nupkg.sha512", "D:\\nuget_packages\\freesql.provider.sqlite\\3.5.106\\freesql.provider.sqlite.3.5.106.nupkg.sha512", "D:\\nuget_packages\\microsoft.codecoverage\\17.5.0\\microsoft.codecoverage.17.5.0.nupkg.sha512", "D:\\nuget_packages\\microsoft.csharp\\4.7.0\\microsoft.csharp.4.7.0.nupkg.sha512", "D:\\nuget_packages\\microsoft.net.test.sdk\\17.5.0\\microsoft.net.test.sdk.17.5.0.nupkg.sha512", "D:\\nuget_packages\\microsoft.testplatform.objectmodel\\17.5.0\\microsoft.testplatform.objectmodel.17.5.0.nupkg.sha512", "D:\\nuget_packages\\microsoft.testplatform.testhost\\17.5.0\\microsoft.testplatform.testhost.17.5.0.nupkg.sha512", "D:\\nuget_packages\\newtonsoft.json\\13.0.3\\newtonsoft.json.13.0.3.nupkg.sha512", "D:\\nuget_packages\\nuget.frameworks\\5.11.0\\nuget.frameworks.5.11.0.nupkg.sha512", "D:\\nuget_packages\\shouldly\\4.3.0\\shouldly.4.3.0.nupkg.sha512", "D:\\nuget_packages\\stub.system.data.sqlite.core.netstandard\\*********\\stub.system.data.sqlite.core.netstandard.*********.nupkg.sha512", "D:\\nuget_packages\\system.codedom\\6.0.0\\system.codedom.6.0.0.nupkg.sha512", "D:\\nuget_packages\\system.data.sqlite.core\\*********\\system.data.sqlite.core.*********.nupkg.sha512", "D:\\nuget_packages\\system.management\\6.0.1\\system.management.6.0.1.nupkg.sha512", "D:\\nuget_packages\\system.reflection.metadata\\1.6.0\\system.reflection.metadata.1.6.0.nupkg.sha512", "D:\\nuget_packages\\validation\\2.5.51\\validation.2.5.51.nupkg.sha512", "D:\\nuget_packages\\xunit\\2.9.3\\xunit.2.9.3.nupkg.sha512", "D:\\nuget_packages\\xunit.abstractions\\2.0.3\\xunit.abstractions.2.0.3.nupkg.sha512", "D:\\nuget_packages\\xunit.analyzers\\1.18.0\\xunit.analyzers.1.18.0.nupkg.sha512", "D:\\nuget_packages\\xunit.assert\\2.9.3\\xunit.assert.2.9.3.nupkg.sha512", "D:\\nuget_packages\\xunit.core\\2.9.3\\xunit.core.2.9.3.nupkg.sha512", "D:\\nuget_packages\\xunit.extensibility.core\\2.9.3\\xunit.extensibility.core.2.9.3.nupkg.sha512", "D:\\nuget_packages\\xunit.extensibility.execution\\2.9.3\\xunit.extensibility.execution.2.9.3.nupkg.sha512", "D:\\nuget_packages\\xunit.runner.visualstudio\\3.0.2\\xunit.runner.visualstudio.3.0.2.nupkg.sha512", "D:\\nuget_packages\\xunit.skippablefact\\1.5.23\\xunit.skippablefact.1.5.23.nupkg.sha512"], "logs": []}