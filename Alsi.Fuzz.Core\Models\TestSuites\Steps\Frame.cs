using Alsi.App.Devices.Core;
using Alsi.Common.Utils.Autosar;
using Alsi.Fuzz.Core.Models.Tester;
using Alsi.Fuzz.Core.Utils;
using System;
using System.Collections.Generic;
using System.Xml.Serialization;

namespace Alsi.Fuzz.Core.Models.TestSuites.Steps
{
    public class Frame
    {
        [XmlAttribute("id")]
        public string Id { get; set; }

        [XmlAttribute("dlc")]
        public string Dlc { get; set; }

        [XmlElement("hex-data")]
        public string Data { get; set; }

        [XmlAttribute("type")]
        public string Type { get; set; }

        [XmlAttribute("is-ext")]
        public bool IsExt { get; set; }

        public CanFrame ToCanFrame(List<EnvVar> envVars)
        {
            var typeValue = Type.Trim();
            envVars.Eval(ref typeValue);

            var isCanFd = false;
            if (typeValue.Equals("can", StringComparison.OrdinalIgnoreCase))
            {
                isCanFd = false;
            }
            else if (typeValue.Equals("canfd", StringComparison.OrdinalIgnoreCase))
            {
                isCanFd = true;
            }
            else
            {
                throw new Exception($"Unknown frame type: {typeValue}");
            }

            var dataValue = Data;
            envVars.Eval(ref dataValue);

            var data = SequenceUtils.ParseBytes(dataValue);

            byte dlc = 0;
            if (string.IsNullOrWhiteSpace(Dlc))
            {
                dlc = (byte)DlcUtils.GetDlc(data.Length);
            }
            else
            {
                var dlcValue = Dlc;
                envVars.Eval(ref dlcValue);
                dlc = SequenceUtils.ParseByte(Dlc);
            }

            var idValue = Id;
            envVars.Eval(ref idValue);
            var id = SequenceUtils.ParseInt(idValue);

            var isExt = IsExt;
            var frame = new CanFrame()
            {
                Id = id,
                Dlc = dlc,
                Data = data,
                IsCanFd = isCanFd,
                IsExt = isExt
            };

            return frame;
        }
    }
}
