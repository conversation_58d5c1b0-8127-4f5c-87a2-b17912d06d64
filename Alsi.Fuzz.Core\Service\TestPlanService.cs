using Alsi.Fuzz.Core.Models.TestPlans;
using Alsi.Fuzz.Core.Storage;
using Alsi.Fuzz.Core.Utils;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace Alsi.Fuzz.Core.Service
{
    public class TestPlanService : ITestPlanService
    {
        private readonly TestPlanStorage _storage;
        private readonly ITestPlanHistoryService _historyService;

        public TestPlanService(TestPlanStorage storage, ITestPlanHistoryService historyService)
        {
            _storage = storage ?? throw new ArgumentNullException(nameof(storage));
            _historyService = historyService ?? throw new ArgumentNullException(nameof(historyService));
        }

        public Task<TestPlan> CreateAsync(string description)
        {
            var now = DateTime.Now;
            var plan = new TestPlan
            {
                Manifest = new TestPlanManifest
                {
                    Description = description,
                    Created = now,
                    Modified = now
                },
                Config = new TestPlanConfig()
            };

            return Task.FromResult(plan);
        }

        public async Task<TestPlan> LoadAsync(string path)
        {
            var testPlan = _storage.Load(path);
            await _historyService.RecordAccessAsync(testPlan, path);
            return testPlan;
        }

        public Task SaveAsync(TestPlan plan, string path)
        {
            _storage.Save(path, plan);
            return Task.CompletedTask;
        }

        public Task<TestPlan> UpdateBasicInfoAsync(string path, string description)
        {
            var testPlan = _storage.Load(path);

            testPlan.Manifest.Description = description;
            testPlan.Manifest.Modified = DateTime.Now;

            _storage.Save(path, testPlan);

            return Task.FromResult(testPlan);
        }

        public Task<TestPlan> UpdateHardwareConfigAsync(string path, HardwareConfig hardwareConfig)
        {
            var testPlan = _storage.Load(path);

            testPlan.Config.HardwareConfig = hardwareConfig;
            testPlan.Manifest.Modified = DateTime.Now;

            _storage.Save(path, testPlan);

            return Task.FromResult(testPlan);
        }

        public Task<TestPlan> UpdateSecurityConfigAsync(string path, SecurityConfig securityConfig)
        {
            var testPlan = _storage.Load(path);

            testPlan.Config.SecurityConfig = securityConfig;
            testPlan.Manifest.Modified = DateTime.Now;

            _storage.Save(path, testPlan);

            return Task.FromResult(testPlan);
        }

        public Task<TestPlan> UpdateSequenceConfigAsync(string path, SequenceConfigDto request)
        {
            if (!string.IsNullOrWhiteSpace(request.SequencePackageXml))
            {
                // 检查 XML 是否能被解析为 Sequence
                SequencePackageUtils.LoadFromString(request.SequencePackageXml);
            }

            var testPlan = _storage.Load(path);

            var config = testPlan.Config.SequenceConfigList.FirstOrDefault(
                x => x.TestSuiteName == request.TestSuiteName
                && x.SequencePackageName == request.SequencePackageName);
            if (config != null)
            {
                config.SequencePackageXml = request.SequencePackageXml;
            }
            else
            {
                testPlan.Config.SequenceConfigList.Add(new SequenceConfig
                {
                    SequencePackageXml = request.SequencePackageXml,
                    SequencePackageName = request.SequencePackageName,
                    TestSuiteName = request.TestSuiteName
                });
            }

            foreach (var item in testPlan.Config.SequenceConfigList)
            {
                item.IsSelected = item.TestSuiteName == request.TestSuiteName
                && item.SequencePackageName == request.SequencePackageName;
            }

            testPlan.Manifest.Modified = DateTime.Now;

            _storage.Save(path, testPlan);

            return Task.FromResult(testPlan);
        }

        public Task<TestPlan> UpdateCaseConfigAsync(string path, CaseConfig caseConfig)
        {
            var testPlan = _storage.Load(path);

            testPlan.Config.CaseConfig = caseConfig;
            testPlan.Manifest.Modified = DateTime.Now;

            _storage.Save(path, testPlan);

            return Task.FromResult(testPlan);
        }
    }
}
