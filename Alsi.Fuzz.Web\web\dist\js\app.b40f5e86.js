(function(){"use strict";var e={687:function(e,t,n){n.d(t,{A:function(){return C}});var a=n(6768),s=n(144),o=n(8954),r=n(8814),i=n(5229),l=n(2103),c=n(4541),u=(0,a.pM)({__name:"XmlEditor",props:{value:{},language:{},readOnly:{type:Boolean}},emits:["update:value","change"],setup(e,{emit:t}){const n=e,u=t,d=(0,s.KR)(null);let p=null;const m=[(0,r.$K)(),(0,r.Wu)(),(0,r.N$)(),(0,i.b6)(),(0,r.VH)(),(0,r.A)(),o.$t.allowMultipleSelections.of(!0),(0,c.WD)(),(0,c.y9)(c.Zt,{fallback:!0}),(0,c.Lv)(),r.w4.of([...i.pw,...i.cL,...c.f7])],f=()=>{if(!d.value)return;const e=[...m,(0,l._n)(),r.Lz.updateListener.of((e=>{if(e.docChanged){const t=e.state.doc.toString();u("update:value",t),u("change",t)}})),r.Lz.contentAttributes.of({contenteditable:n.readOnly?"false":"true",class:n.readOnly?"readonly-but-selectable":""}),r.Lz.editable.of(!n.readOnly)],t=o.$t.create({doc:n.value,extensions:e});p=new r.Lz({state:t,parent:d.value})};return(0,a.sV)((()=>{f()})),(0,a.wB)((()=>n.value),(e=>{if(p&&e!==p.state.doc.toString()){const t=p?.scrollDOM.scrollTop||0;p.dispatch({changes:{from:0,to:p.state.doc.length,insert:e}}),setTimeout((()=>{p&&(p.scrollDOM.scrollTop=t)}),0)}})),(0,a.wB)((()=>n.readOnly),(e=>{p&&p.dispatch({effects:[r.Lz.editable.reconfigure(!e),r.Lz.contentAttributes.reconfigure({contenteditable:e?"false":"true",class:e?"readonly-but-selectable":""})]})})),(0,a.xo)((()=>{p&&(p.destroy(),p=null)})),(e,t)=>((0,a.uX)(),(0,a.CE)("div",{ref_key:"editorContainer",ref:d,class:"editor-container"},null,512))}}),d=n(1241);const p=(0,d.A)(u,[["__scopeId","data-v-48b1a46a"]]);var m=p;const f={class:"xml-viewer"},g={class:"editor-container"};var h=(0,a.pM)({__name:"XmlViewer",props:{title:{},content:{}},emits:["update:content"],setup(e,{emit:t}){const n=t,s=e=>{n("update:content",e)};return(e,t)=>((0,a.uX)(),(0,a.CE)("div",f,[(0,a.Lk)("div",g,[(0,a.bF)(m,{value:e.content,language:"xml","onUpdate:value":s},null,8,["value"])])]))}});const v=(0,d.A)(h,[["__scopeId","data-v-1d6f83a0"]]);var C=v},1021:function(e,t,n){n.d(t,{Fp:function(){return a},GQ:function(){return c},si:function(){return s},xh:function(){return i}});var a,s,o=n(4373),r=n(3144);n(8769);function i(e){return e.processState===s.Success||e.processState===s.Failure}(function(e){e[e["Normal"]=0]="Normal",e[e["High"]=1]="High"})(a||(a={})),function(e){e["Pending"]="Pending",e["Running"]="Running",e["Success"]="Success",e["Failure"]="Failure",e["Paused"]="Paused"}(s||(s={}));const l="/api/app",c={getAppInfo(){return r.Xo?r.Z0.app.getAppInfo():o.A.get(`${l}/appInfo`)},logError:e=>r.Xo?r.Z0.app.logError(e):o.A.post(`${l}/logError`,e),exit:()=>r.Xo?r.Z0.app.exit():o.A.post(`${l}/exit`),getLatestInteroperationCaseResults(){return r.Xo?r.Z0.app.getLatestInteroperationCaseResults():o.A.get("/api/case/latest-interoperation-case-results")},generateCases(e,t){const n={coverage:e,sequenceNames:t};return r.Xo?r.Z0.app.generateCases(e,t):o.A.post("/api/case/generate-cases",{coverage:n.coverage,SequenceNames:n.sequenceNames})},saveCases(e,t){const n={coverage:e,sequenceNames:t};return r.Xo?r.Z0.app.saveCases(e,t):o.A.post("/api/case/save-cases",{coverage:n.coverage,SequenceNames:n.sequenceNames})},getSavedCases(){return r.Xo?r.Z0.app.getSavedCases():o.A.get("/api/case/cases")},startTest(){return r.Xo?r.Z0.app.startTest():o.A.post("/api/case/start")},stopTest(){return r.Xo?r.Z0.app.stopTest():o.A.post("/api/case/stop")},pauseTest(){return r.Xo?r.Z0.app.pauseTest():o.A.post("/api/case/pause")},resumeTest(){return r.Xo?r.Z0.app.resumeTest():o.A.post("/api/case/resume")},getTestStatus(){return r.Xo?r.Z0.app.getTestStatus():o.A.get("/api/case/status")},getTestStatusPaged(e){return r.Xo?r.Z0.app.getTestStatusPaged(e):o.A.post("/api/case/status",e)},getTestResults(){return r.Xo?r.Z0.app.getTestResults():o.A.get("/api/case/test-results")},getCases(e){return r.Xo?r.Z0.app.getCases(e):o.A.get(`/api/case/cases?testResultId=${e}`)},getCaseSteps(e,t){return r.Xo?r.Z0.app.getCaseSteps(e,t):o.A.get(`/api/case/case-steps?testResultId=${e}&caseResultId=${t}`)},getCaseResult(e,t){return r.Xo?r.Z0.case.getCaseResult(e,t):o.A.get(`/api/case/case-result?testResultId=${e}&caseResultId=${t}`)},deleteTestResult(e){return r.Xo?r.Z0.app.deleteTestResult(e):o.A.delete(`/api/case/test-result?testResultId=${e}`)},downloadHtmlReport(e){if(r.Xo)return r.Z0.app.downloadHtmlReport(e);const t=`/api/case/generate-report?testResultId=${e}`;return o.A.post(t)}}},1420:function(e,t,n){n.d(t,{cm:function(){return l},si:function(){return a},xh:function(){return r}});var a,s=n(4373),o=n(3144);(function(e){e["Pending"]="Pending",e["Running"]="Running",e["Success"]="Success",e["Failure"]="Failure"})(a||(a={}));function r(e){return e.processState===a.Success||e.processState===a.Failure}const i="/api/interoperation",l={startTest:()=>o.Xo?o.Z0.interoperation.startTest():s.A.post(`${i}/start`),stopTest:()=>o.Xo?o.Z0.interoperation.stopTest():s.A.post(`${i}/stop`),getStatus:()=>o.Xo?o.Z0.interoperation.getStatus():s.A.get(`${i}/status`)}},1748:function(e,t,n){n.d(t,{p:function(){return r}});var a=n(4373),s=n(3144);const o="/api/testsuite",r={getBuiltIn:()=>s.Xo?s.Z0.testSuite.getBuiltIn():a.A.get(`${o}/builtin`),getBuiltInXml:(e,t)=>s.Xo?s.Z0.testSuite.getXml(e,t):a.A.get(`${o}/builtin-xml`,{params:{suiteName:e,packageName:t}}),getXml:(e,t)=>s.Xo?s.Z0.testSuite.getXml(e,t):a.A.get(`${o}/xml`,{params:{suiteName:e,packageName:t}})}},2616:function(e,t,n){n.d(t,{f:function(){return u}});var a=n(1114),s=(n(4114),n(144)),o=n(1219),r=n(3703),i=n(7479);const l=(0,s.Kh)({currentPlan:null,recentPlans:[],isLoading:!1});class c{constructor(){(0,a.A)(this,"router",null)}setRouter(e){this.router=e}getState(){return(0,s.tB)(l)}async openFromExplorer(){l.isLoading=!0;try{const e=await r.E.openInExplorer();if(e.data)return l.currentPlan=e.data,o.nk.success("Test plan opened successfully"),this.navigateToTestPlan(),await this.loadRecentPlans(),e.data}finally{l.isLoading=!1}return null}async openFromPath(e){l.isLoading=!0;try{const t=await r.E.open(e);if(t.data)return l.currentPlan=t.data,o.nk.success("Test plan opened successfully"),this.navigateToTestPlan(),await this.loadRecentPlans(),t.data}finally{l.isLoading=!1}return null}async createTestPlan(e){l.isLoading=!0;try{const t=await r.E.create(e);if(t.data)return l.currentPlan=t.data,o.nk.success("Test plan created successfully"),this.navigateToTestPlan(),await this.loadRecentPlans(),t.data}catch(t){o.nk.error("Failed to create test plan"),console.error(t)}finally{l.isLoading=!1}return null}async closeTestPlan(){try{await r.E.close(),l.currentPlan=null,o.nk.success("Test plan closed successfully"),this.router&&this.router.push("/")}catch(e){o.nk.error("Failed to close test plan"),console.error(e)}}async loadRecentPlans(){try{const e=await i.I.get();return l.recentPlans=e.data,e.data}catch(e){return console.error("Failed to load recent plans:",e),[]}}async clearHistory(){try{return await i.I.clear(),l.recentPlans=[],o.nk.success("History cleared"),!0}catch(e){return o.nk.error("Failed to clear history"),console.error(e),!1}}async updateBasicInfo(e){l.isLoading=!0;try{const t=await r.E.updateBasicInfo(e);if(t.data)return l.currentPlan=t.data,o.nk.success("Description updated successfully"),t.data}catch(t){o.nk.error("Failed to update description"),console.error("Error updating description:",t)}finally{l.isLoading=!1}return null}async getCurrentPlan(){if(l.currentPlan)return l.currentPlan;l.isLoading=!0;try{const e=await r.E.getCurrentPlan();return l.currentPlan=e.data,e.data}catch(e){return console.error("Error loading current plan:",e),null}finally{l.isLoading=!1}}navigateToTestPlan(){this.router&&this.router.push({name:"test-plan.basic-setting"})}}const u=new c},3144:function(e,t,n){n.d(t,{Xo:function(){return H},$C:function(){return j},Z0:function(){return G},lL:function(){return V},MI:function(){return K}});n(4114),n(8111),n(116);const a=()=>Math.random().toString(36).substring(2,15),s=()=>(new Date).toISOString(),o=()=>{const e=new Date,t=new Date(e.getTime()-24*Math.floor(30*Math.random())*60*60*1e3);return t.toISOString()},r=[{path:"D:\\TestPlans\\CAN总线测试计划.fzp",manifest:{name:"CAN总线测试计划",description:"针对汽车CAN总线通信的模糊测试",created:o(),modified:s()},config:{targetDevice:"ECM",protocol:"CAN",testCases:["TC001","TC002","TC003"]}},{path:"D:\\TestPlans\\UDS协议测试.fzp",manifest:{name:"UDS协议测试",description:"针对UDS协议的诊断服务测试",created:o(),modified:o()},config:{targetDevice:"BCM",protocol:"UDS",testCases:["UDS001","UDS002"]}},{path:"D:\\TestPlans\\CANFD高速协议测试.fzp",manifest:{name:"CANFD高速协议测试",description:"针对CANFD高速数据传输协议的稳定性测试",created:o(),modified:o()},config:{targetDevice:"Gateway",protocol:"CANFD",testCases:["FD001","FD002","FD003","FD004"]}}],i=[{id:a(),filePath:"D:\\TestPlans\\can_test.fzp",planName:"CAN总线测试计划",lastAccessTime:o(),lastModified:o(),isDeleted:!1},{id:a(),filePath:"D:\\TestPlans\\uds_protocol.fzp",planName:"UDS协议测试",lastAccessTime:o(),lastModified:o(),isDeleted:!1},{id:a(),filePath:"D:\\TestPlans\\canfd_test.fzp",planName:"CANFD高速协议测试",lastAccessTime:s(),lastModified:o(),isDeleted:!1}],l=[];let c=null;const u={openInExplorer:()=>{const e=Math.floor(Math.random()*r.length),t=r[e];return c=t,d(t.path,t.name),K(t)},open:e=>{const t=i.find((t=>t.filePath===e));let n;if(t){const a=r.find((e=>e.manifest.name===t.planName));n=a||r[0],d(e,a?.manifest.name||"")}else n=r[0],d(e,n.name);return c=n,K(n)},create:e=>{const t=(new Date).toISOString(),n=`D:\\TestPlans\\${e.name.replace(/\s+/g,"_").toLowerCase()}.fzp`,a={path:n,manifest:{name:e.name,description:e.description,created:t,modified:t},config:{targetDevice:"Default",protocol:"CAN",testCases:[]}};return r.push(a),d(n,e.name),c=a,K(a)},close:()=>(c=null,console.log("模拟关闭测试计划"),K(void 0)),getCurrentPlan:()=>c?K(c):V(404,"No test plan is currently open"),updateBasicInfo:e=>{if(!c)return V(404,"No test plan is currently open");c.manifest.description=e,c.manifest.modified=(new Date).toISOString();const t=i.find((e=>e.filePath===c?.path));return t&&(t.lastModified=(new Date).toISOString()),K(c)},checkFileExists:e=>{const t=Math.random()>.1;return K({exists:t})}};function d(e,t){const n=(new Date).toISOString(),a=i.find((t=>t.filePath===e));a?(a.lastAccessTime=n,a.lastModified=n,a.isDeleted=!1):i.push({id:j(),filePath:e,planName:t,lastAccessTime:n,lastModified:n,isDeleted:!1}),i.sort(((e,t)=>e.isDeleted&&!t.isDeleted?1:!e.isDeleted&&t.isDeleted?-1:new Date(t.lastAccessTime).getTime()-new Date(e.lastAccessTime).getTime()))}n(2489),n(7588);const p={get:()=>{const e=i.filter((e=>!e.isDeleted)).sort(((e,t)=>new Date(t.lastAccessTime).getTime()-new Date(e.lastAccessTime).getTime()));return K(e)},clear:()=>(i.forEach((e=>{e.isDeleted=!0})),K(void 0)),deleteRecord:e=>{const t=i.find((t=>t.filePath===e&&!t.isDeleted));return t&&(t.isDeleted=!0),K(void 0)}};n(1701);var m=n(1021);function f(){const e=[],t=["DiagnosticSessionControl","ECUReset","SecurityAccess","CommunicationControl","ReadDataByIdentifier","WriteDataByIdentifier","ClearDiagnosticInformation","ReadDTCInformation","InputOutputControlByIdentifier","RoutineControl","RequestDownload","RequestUpload","TransferData","RequestTransferExit","AccessTimingParameter","SecuredDataTransmission","ControlDTCSetting","ResponseOnEvent","LinkControl","can-frames"],n=Math.floor(10*Math.random())+5,a=[...t].sort((()=>Math.random()-.5)).slice(0,n+5);return a.forEach(((t,a)=>{let s;if(a<n)s="Success";else{const e=["Pending","Running","Failure"];s=e[Math.floor(Math.random()*e.length)]}const o=new Date(Date.now()-6e4*Math.random()),r=new Date(o.getTime()+3e4*Math.random());e.push({id:a+1,testResultId:`test-${Math.random().toString(36).substring(2)}`,sequenceId:`seq-${Math.random().toString(36).substring(2)}`,sequenceName:t,parameter:`param-${a}`,name:`name-${a}`,state:s,begin:o.toISOString(),end:r.toISOString(),detail:"Success"===s?"Test passed":`Test ${s.toLowerCase()}`})})),e}function g(e,t){const n=[],a=(e===m.Fp.High?1e5:5e3)/5,s=["G100","G200","G300","G400","G500"];for(const o of t)for(let e=0;e<a;e++){const t=Math.floor(Math.random()*s.length),a=s[t];n.push({id:n.length+1,testResultId:`test-${Math.random().toString(36).substring(2)}`,sequenceId:`seq-${Math.random().toString(36).substring(2)}`,sequenceName:o,parameter:`id=0x${(Math.floor(4095*Math.random())+1).toString(16).padStart(3,"0")};dlc=8;data=hex:${Array(16).fill(0).map((()=>Math.floor(255*Math.random()).toString(16).padStart(2,"0"))).join("")}`,state:"Pending",begin:null,end:null,detail:"",name:`${a}-TestCase${e+1}`})}return n}function h(){const e=[];for(let t=0;t<5;t++){const n=new Date(Date.now()-864e5*t),a=10+Math.floor(20*Math.random()),s=Math.floor(a*(.7+.2*Math.random())),o=a-s;e.push({id:`test-result-${t}-${Date.now()}`,resultFolderName:`测试运行 ${t+1}`,testType:"Case",creationTime:n.toISOString(),completionTime:new Date(n.getTime()+36e5).toISOString(),totalCount:a,successCount:s,failureCount:o})}return e}function v(e){const t=[],n=5+Math.floor(10*Math.random());for(let a=0;a<n;a++){const s=Date.now()-1e3*(n-a),o=["Success","Failure"],r=Math.random()>.2?o[0]:o[1];t.push({id:a+1,name:`Step ${a+1}`,caseResultId:e,timestamp:s,frameTimestamp:s,state:r,begin:new Date(s-500).toISOString(),end:new Date(s).toISOString(),detail:"Success"===r?"Step executed successfully":"Step failed with error"})}return t}let C,y=m.si.Pending,k="",b={id:`test-${Date.now()}`,resultFolderName:"Mock Test Run",testType:"Case",creationTime:(new Date).toISOString(),totalCount:0,successCount:0,failureCount:0},S=[];function T(){void 0!==C&&(clearInterval(C),C=void 0),C=window.setInterval((()=>{if(y===m.si.Running&&b.successCount+b.failureCount<b.totalCount){const e=b.successCount+b.failureCount,t=Math.random()>.2;S[e].state=t?"Success":"Failure",S[e].begin=new Date(Date.now()-3e3).toISOString(),S[e].end=(new Date).toISOString(),t?b.successCount++:b.failureCount++;const n=e+1;n<b.totalCount?(S[n].state="Running",k=`Running test case: ${S[n].sequenceName}`):(y=m.si.Success,k="Test execution completed",b.completionTime=(new Date).toISOString(),void 0!==C&&(clearInterval(C),C=void 0))}else y!==m.si.Running||void 0!==C&&(clearInterval(C),C=void 0)}),500)}const F={getAppInfo(){return K({dataFolder:"D:\\mock\\data\\folder",logFolder:"D:\\mock\\logs\\folder"})},logError:e=>(l.push(e),K(void 0)),exit:()=>(console.log("模拟应用程序退出"),K(void 0)),getLatestInteroperationCaseResults(){return K(f())},generateCases(e,t){let n,a;return"object"===typeof e?(n=e.coverage,a=e.sequenceNames||[]):(n=e,a=t||[]),K(g(n,a))},saveCases(e,t){let n,a;"object"===typeof e?(n=e.coverage,a=e.sequenceNames||[]):(n=e,a=t||[]);const s=g(n,a);console.log("模拟保存测试用例:",s.length);const o=`test-${Date.now()}`;return s.forEach((e=>e.testResultId=o)),K(s)},getSavedCases(){const e=g(m.Fp.Normal,["can-frames","DiagnosticSessionControl"]),t="saved-test-result-id";return e.forEach((e=>{e.testResultId=t;const n=["Pending","Running","Success","Failure"];e.state=n[Math.floor(Math.random()*n.length)]})),K(e)},startTest(){console.log("模拟开始测试"),y=m.si.Running,k="Starting test execution";const e=g(m.Fp.Normal,["can-frames","DiagnosticSessionControl"]);return S=e,b={id:`test-${Date.now()}`,resultFolderName:"Mock Test Run",testType:"Case",creationTime:(new Date).toISOString(),totalCount:e.length,successCount:0,failureCount:0},S.length>0&&(S[0].state="Running",k=`Running test case: ${S[0].sequenceName}`,T()),K(void 0)},stopTest(){return console.log("模拟停止测试"),y=m.si.Failure,k="Test execution stopped by user",b.completionTime=(new Date).toISOString(),K(void 0)},pauseTest(){return console.log("模拟暂停测试"),y=m.si.Paused,k="Test execution paused by user",K(void 0)},resumeTest(){return console.log("模拟恢复测试"),y=m.si.Running,k="Test execution resumed by user",void 0===C&&T(),K(void 0)},getTestStatus(){const e={processState:y,currentOperation:k,testResult:b,caseResults:S};return K(e)},getTestStatusPaged(e){const t={processState:y,currentOperation:k,testResult:b},n=S.length,a=(e.pageNumber-1)*e.pageSize,s=Math.min(a+e.pageSize,n),o=S.slice(a,s),r={...t,pagedCaseResult:{items:o,total:n,pageSize:e.pageSize,pageNumber:e.pageNumber}};return K(r)},getTestResults(){return K(h())},getCases(e){const t=g(m.Fp.Normal,["can-frames","DiagnosticSessionControl"]);return t.forEach((t=>{t.testResultId=e;const n=["Success","Failure"];t.state=n[Math.floor(Math.random()*n.length)],t.begin=new Date(Date.now()-36e5).toISOString(),t.end=new Date(Date.now()-35e5).toISOString()})),K(t)},getCaseSteps(e,t){return K(v(t))},deleteTestResult(e){return console.log(`模拟删除测试结果: ${e}`),K(void 0)},downloadHtmlReport(e){return console.log(`Mock downloading HTML report for test result: ${e}`),Promise.resolve()}};var w=n(1219);const P={selectFolder:()=>K("D:\\mock\\selected\\folder\\path"),openExplorer:e=>(w.nk.success(`[模拟] 已在资源管理器中打开路径: ${e}`),K(void 0))},D=[{name:"CAN-Bus Test Suite",version:"1.0",packages:[{name:"CAN Frame examples",sequences:[{type:"can",description:{name:"can-frames"},preamble:{onConnect:!0,value:"can-preamble-sequence"}}]}]},{name:"CAN-FD Test Suite",version:"1.0",packages:[{name:"CAN Unified Diagnostic Services",sequences:[{type:"uds",description:{name:"can-uds.diagnostic-session-control"},preamble:{onConnect:!0,value:"uds-preamble-sequence"}}]}]}],x={"CAN-Bus Test Suite":'\n<sequences setting="sequence">\n  <description name="CAN Frame examples">Raw CAN Frame test sequences.</description>\n  <sequence type="can">\n    <description name="can-frames">CAN frame</description>\n    <preamble on-connect="true">can-preamble-sequence</preamble>\n  </sequence>\n</sequences>',"CAN-FD Test Suite":'\n<sequences setting="sequence">\n  <description name="CAN Unified Diagnostic Services">UDS test sequences.</description>\n  <sequence type="uds">\n    <description name="can-uds.diagnostic-session-control">UDS session control</description>\n    <preamble on-connect="true">uds-preamble-sequence</preamble>\n  </sequence>\n</sequences>'},R={getBuiltIn:()=>K(D),getXml:(e,t)=>{const n=D.find((t=>t.name===e));if(!n)return V(400,`Test suite '${e}' not found`);const a=n.packages.find((e=>e.name===t));return a?K(x[e]):V(400,`Package '${t}' not found in test suite '${e}'`)}},I=[{name:"VN1630 123456",communicationType:"Can",isConnected:!0},{name:"VN1640 789012",communicationType:"CanFd",isConnected:!0},{name:"TC1001 ABCDEF",communicationType:"Can",isConnected:!0},{name:"TC2001 XYZ789",communicationType:"CanFd",isConnected:!1}];let E={communicationType:"Can",canConfig:{deviceChannelName:"VN1630 123456",dataBitrate:5e5},canFdConfig:{deviceChannelName:"VN1640 789012",arbitrationBitrate:5e5,dataBitrate:2e6}};setInterval((()=>{I.forEach((e=>{Math.random()<.8&&(e.isConnected=!e.isConnected)}))}),3e3);const A={getHardwareConfig:()=>{const e={deviceChannels:I,testPlanConfig:E};return K(e)},updateHardwareConfig:e=>{if(!e)return V(400,"无效的配置数据");E={...E,...e},"Can"===e.communicationType&&e.canConfig?E.canConfig={...e.canConfig}:"CanFd"===e.communicationType&&e.canFdConfig&&(E.canFdConfig={...e.canFdConfig});const t={deviceChannels:I,testPlanConfig:E};return K(t)}};let M={testSuiteName:"CAN-Bus Test Suite",sequencePackageName:"CAN Frame examples",sequencePackageXml:"<sequence><name>CAN Frame examples</name><description>Example sequence</description></sequence>"};const _={getSequenceConfig:()=>K(M),updateSequenceConfig:e=>(M={...M,...e},K(M))};var N=n(1420);const L={processState:N.si.Pending,currentOperation:"",testResult:{id:crypto.randomUUID?.()||"1",resultFolderName:"Mock Test",testType:"Interoperation",creationTime:(new Date).toISOString(),totalCount:20,successCount:0,failureCount:0},caseResults:[]};let O={...L},X=null;function $(){const e=["DiagnosticSessionControl","ECUReset","SecurityAccess","CommunicationControl","ReadDataByIdentifier","can-frames"];return e.map(((e,t)=>({id:t+1,testResultId:O.testResult.id,sequenceId:`seq-${t}`,sequenceName:e,parameter:`param-${t}`,name:`Test ${t+1}`,state:Math.random()>.7?N.si.Success:Math.random()>.5?N.si.Failure:N.si.Pending,begin:new Date(Date.now()-6e4).toISOString(),end:(new Date).toISOString(),detail:"Test details"})))}function q(){X&&(clearInterval(X),X=null)}const B={startTest:()=>(q(),O={processState:N.si.Running,currentOperation:"初始化测试环境",testResult:{id:crypto.randomUUID?.()||"1",resultFolderName:"Mock Interoperation Test",testType:"Interoperation",creationTime:(new Date).toISOString(),totalCount:20,successCount:0,failureCount:0},caseResults:[]},X=window.setInterval((()=>{O.processState===N.si.Running&&(O.testResult.successCount=Math.min(O.testResult.totalCount,O.testResult.successCount+1),O.testResult.failureCount=Math.min(O.testResult.totalCount-O.testResult.successCount,O.testResult.failureCount+(Math.random()>.8?1:0)),O.caseResults=$(),O.currentOperation=`执行测试: ${O.caseResults[Math.floor(Math.random()*O.caseResults.length)].sequenceName}`,O.testResult.successCount+O.testResult.failureCount>=O.testResult.totalCount&&(O.processState=N.si.Success,O.currentOperation="测试已完成",q()))}),1e3),K({success:!0,message:"测试已启动"})),stopTest:()=>(O.processState===N.si.Running&&(O.processState=N.si.Failure,O.currentOperation="测试已停止",q()),K({success:!0,message:"测试已停止"})),getStatus:()=>K({...O})},Z={whiteListFrames:[],selectedNodeName:"",enableNmWakeup:!0,nmWakeupId:1343,nmWakeupIsExt:!1,nmWakeupDlc:8,nmWakeupData:[63,80,255,255,255,255,255,255],nmWakeupCommunicationType:"Can",nmWakeupCycleMs:100,nmWakeupDelayMs:2e3,diagReqId:1841,diagReqIsExt:!1,diagResId:1585,diagTimeoutMs:500,isDutMtuLessThan4096:!1,enableDiagFallbackRequest:!1,diagFallbackRequestPayload:[16,1],securityInfo:{hasDll:!1,dllFileName:void 0,dllSize:0}},W={id:1,testResultId:"00000000-0000-0000-0000-000000000000",sequenceId:"00000000-0000-0000-0000-000000000000",sequenceName:"Test Sequence",name:"mock case result",parameter:"Parameter Value",state:"Success",begin:(new Date).toISOString(),end:(new Date).toISOString(),detail:"Case result details"},z={getCaseConfig(){return K(Z)},getCaseResult(e,t){return K({...W,id:t,testResultId:e})},updateCaseConfig(e){if(e.removeSecurityDll)e.securityInfo={hasDll:!1,dllFileName:void 0,dllSize:0};else if(e.securityDllPath){const t=e.securityDllPath.split("\\").pop()||"SecurityAccess.dll",n=15360;e.securityInfo={hasDll:!0,dllFileName:t,dllSize:n}}return delete e.securityDllPath,delete e.removeSecurityDll,K(e)},importDbc(){return K({whiteListFrames:[{id:291,name:"Engine_Status",dlc:8,isExt:!1,transmitter:"ECM",receivers:["TCM","BCM"]},{id:1110,name:"Transmission_Data",dlc:8,isExt:!0,transmitter:"TCM",receivers:["ECM","ICM"]},{id:1929,name:"Brake_Control",dlc:8,isExt:!1,transmitter:"BCM",receivers:["ECM","TCM","ICM"]}],nodeNames:["ECM","TCM","BCM","ICM"]})},selectSecurityDll(){return K({path:"C:\\fakepath\\SecurityAccess.dll"})}},H=!1,U=(e=100)=>new Promise((t=>setTimeout(t,e))),K=e=>U().then((()=>{const t={data:e,status:200,statusText:"OK",headers:{},config:{headers:{}}};return t})),V=(e=400,t="Error")=>U().then((()=>{const n=new Error(t);return n.response={status:e,data:t},Promise.reject(n)})),j=()=>"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){const t=16*Math.random()|0,n="x"==e?t:3&t|8;return n.toString(16)})),G={testPlan:u,testPlanHistory:p,app:F,explorer:P,testSuite:R,hardware:A,sequence:_,interoperation:B,case:z}},3703:function(e,t,n){n.d(t,{E:function(){return i}});n(8111),n(1701);var a=n(4373),s=n(3144);const o="/api/testPlan",r=e=>e.map((e=>({id:e.id,filePath:e.filePath,planName:e.planName,lastAccessTime:e.lastAccessTime,lastModified:e.lastModified,isDeleted:e.isDeleted}))),i={openInExplorer:()=>s.Xo?s.Z0.testPlan.openInExplorer():a.A.get(`${o}/OpenInExplorer`),open:e=>s.Xo?s.Z0.testPlan.open(e):a.A.post(`${o}/open`,{path:e}),create:e=>s.Xo?s.Z0.testPlan.create(e):a.A.post(`${o}/create`,e),getHistory:()=>s.Xo?s.Z0.testPlanHistory.get().then((e=>{const t=r(e.data);return{...e,data:t}})):a.A.get(`${o}/history`),getCurrentPlan:()=>s.Xo?s.Z0.testPlan.getCurrentPlan():a.A.get(`${o}/current`),close:()=>s.Xo?s.Z0.testPlan.close():a.A.post(`${o}/close`),updateBasicInfo:e=>s.Xo?s.Z0.testPlan.updateBasicInfo(e):a.A.post(`${o}/updateBasicInfo`,{description:e}),checkFileExists:e=>s.Xo?s.Z0.testPlan.checkFileExists?s.Z0.testPlan.checkFileExists(e):Promise.resolve({data:{exists:!0},status:200,statusText:"OK",headers:{},config:{headers:{}}}):a.A.post(`${o}/checkFileExists`,{path:e})}},3711:function(e,t,n){n.d(t,{m:function(){return r}});var a=n(4373),s=n(3144);const o="/api/explorer",r={selectFolder:()=>s.Xo?s.Z0.explorer?.selectFolder()||Promise.reject(new Error("Mock API not implemented")):a.A.get(`${o}/select-folder`),openExplorer:e=>s.Xo?s.Z0.explorer?.openExplorer?.(e)||Promise.reject(new Error("Mock API not implemented")):a.A.get(`${o}/open-explorer`,{params:{path:e}})}},7479:function(e,t,n){n.d(t,{I:function(){return r}});var a=n(4373),s=n(3144);const o="/api/testPlanHistory",r={get:()=>s.Xo?s.Z0.testPlanHistory.get():a.A.get(`${o}/get`),clear:()=>s.Xo?s.Z0.testPlanHistory.clear():a.A.delete(`${o}/clear`),deleteRecord:e=>s.Xo?s.Z0.testPlanHistory.deleteRecord?s.Z0.testPlanHistory.deleteRecord(e):Promise.resolve({data:void 0,status:200,statusText:"OK",headers:{},config:{headers:{}}}):a.A.delete(`${o}/deleteRecord`,{data:{filePath:e}})}},7921:function(e,t,n){var a=n(5130),s=n(6768),o=n(144),r=n(4232),i=n(1387),l=n(2933),c=n(1219),u=n(3703),d=n(7479),p=n(1021),m=(u.E,d.I,p.GQ,n(7477));const f={class:"folder-input"};function g(e,t,n,a,o,r){const i=(0,s.g2)("el-input"),l=(0,s.g2)("el-form-item"),c=(0,s.g2)("el-button"),u=(0,s.g2)("el-form"),d=(0,s.g2)("el-dialog");return(0,s.uX)(),(0,s.Wv)(d,{modelValue:e.dialogVisible,"onUpdate:modelValue":t[3]||(t[3]=t=>e.dialogVisible=t),title:"Create Test Plan",width:"500px"},{footer:(0,s.k6)((()=>[(0,s.Lk)("span",null,[(0,s.bF)(c,{onClick:e.close},{default:(0,s.k6)((()=>t[5]||(t[5]=[(0,s.eW)("Cancel")]))),_:1},8,["onClick"]),(0,s.bF)(c,{type:"primary",onClick:e.handleCreateTestPlan},{default:(0,s.k6)((()=>t[6]||(t[6]=[(0,s.eW)(" Create ")]))),_:1},8,["onClick"])])])),default:(0,s.k6)((()=>[(0,s.bF)(u,{model:e.form,"label-width":"90px"},{default:(0,s.k6)((()=>[(0,s.bF)(l,{label:"Name",required:""},{default:(0,s.k6)((()=>[(0,s.bF)(i,{modelValue:e.form.name,"onUpdate:modelValue":t[0]||(t[0]=t=>e.form.name=t),placeholder:"Enter test plan name"},null,8,["modelValue"])])),_:1}),(0,s.bF)(l,{label:"Folder",required:""},{default:(0,s.k6)((()=>[(0,s.Lk)("div",f,[(0,s.bF)(i,{modelValue:e.form.folder,"onUpdate:modelValue":t[1]||(t[1]=t=>e.form.folder=t),placeholder:"Select save folder"},null,8,["modelValue"]),(0,s.bF)(c,{onClick:e.handleSelectFolder},{default:(0,s.k6)((()=>t[4]||(t[4]=[(0,s.eW)("Browse")]))),_:1},8,["onClick"])])])),_:1}),(0,s.bF)(l,{label:"Description"},{default:(0,s.k6)((()=>[(0,s.bF)(i,{modelValue:e.form.description,"onUpdate:modelValue":t[2]||(t[2]=t=>e.form.description=t),type:"textarea",placeholder:"Enter test plan description"},null,8,["modelValue"])])),_:1})])),_:1},8,["model"])])),_:1},8,["modelValue"])}var h=n(3711),v=(0,s.pM)({name:"CreateTestPlan",emits:["created"],setup(e,{emit:t}){const n=(0,o.KR)(!1),a=(0,o.Kh)({name:"",description:"",folder:""}),s=async()=>{try{const e=await h.m.selectFolder();e.data&&(a.folder=e.data)}catch(e){if(400===e.response?.status&&"UserCanceled"===e.response.data)return;c.nk.error("Failed to select folder")}},r=async()=>{if(!a.name)return void c.nk.warning("Please enter test plan name");if(!a.folder)return void c.nk.warning("Please select save folder");const e=await u.E.create({name:a.name,description:a.description,folder:a.folder});e.data&&(i(),t("created",e.data))},i=()=>{n.value=!1,a.name="",a.description="",a.folder=""},l=()=>{n.value=!0};return{dialogVisible:n,form:a,handleCreateTestPlan:r,handleSelectFolder:s,close:i,show:l}}}),C=n(1241);const y=(0,C.A)(v,[["render",g],["__scopeId","data-v-5f3c0e5c"]]);var k=y,b=n(2616);n(4114);const S={key:0,class:"test-plan-indicator"},T={class:"plan-info"},F=["title"],w={class:"plan-actions"};var P=(0,s.pM)({__name:"TestPlanStatusIndicator",setup(e){const t=(0,i.rd)(),n=(0,i.lq)(),a=b.f.getState(),u=(0,o.KR)(!1),d=(0,o.KR)(!1),p=(0,s.EW)((()=>n.path.startsWith("/test-plan"))),f=(0,s.EW)((()=>!!a.currentPlan)),g=(0,s.EW)((()=>a.currentPlan)),h=async()=>{if(f.value){u.value=!0;try{await t.push("/test-plan")}catch(e){console.error("Failed to navigate to test plan page:",e),c.nk.error("Failed to return to test plan")}finally{u.value=!1}}},v=async()=>{if(f.value)try{const e=await l.s.confirm("Are you sure you want to close the current test plan? Unsaved changes may be lost.","Close Test Plan",{confirmButtonText:"OK",cancelButtonText:"Cancel",type:"warning"});"confirm"===e&&(d.value=!0,await b.f.closeTestPlan())}catch(e){}finally{d.value=!1}};return(e,t)=>{const n=(0,s.g2)("el-icon"),a=(0,s.g2)("el-button");return f.value?((0,s.uX)(),(0,s.CE)("div",S,[(0,s.Lk)("div",T,[(0,s.bF)(n,null,{default:(0,s.k6)((()=>[(0,s.bF)((0,o.R1)(m.Document))])),_:1}),(0,s.Lk)("span",{class:"plan-name",title:g.value.path},(0,r.v_)(g.value.manifest.name),9,F)]),(0,s.Lk)("div",w,[p.value?(0,s.Q3)("",!0):((0,s.uX)(),(0,s.Wv)(a,{key:0,type:"primary",size:"small",onClick:h,loading:u.value},{default:(0,s.k6)((()=>[(0,s.bF)(n,null,{default:(0,s.k6)((()=>[(0,s.bF)((0,o.R1)(m.Back))])),_:1}),t[0]||(t[0]=(0,s.eW)(" Back "))])),_:1},8,["loading"])),(0,s.bF)(a,{type:"danger",size:"small",onClick:v,loading:d.value},{default:(0,s.k6)((()=>[(0,s.bF)(n,null,{default:(0,s.k6)((()=>[(0,s.bF)((0,o.R1)(m.Close))])),_:1}),t[1]||(t[1]=(0,s.eW)(" Close "))])),_:1},8,["loading"])])])):(0,s.Q3)("",!0)}}});const D=(0,C.A)(P,[["__scopeId","data-v-c5caca82"]]);var x=D;const R={class:"top-menu-bar"},I={key:0,class:"empty-recent"},E={class:"recent-plan-name"},A={class:"recent-plan-path"};var M=(0,s.pM)({__name:"TopMenuBar",setup(e){const t=(0,i.rd)(),n=(0,i.lq)(),a=(0,o.KR)(null),f=(0,s.EW)((()=>n.path.startsWith("/test-plan")?"":n.path));b.f.setRouter(t);const g=b.f.getState(),h=(0,s.EW)((()=>g.recentPlans.slice(0,5))),v=async()=>{await b.f.openFromExplorer()},C=async e=>{try{const t=await u.E.checkFileExists(e);t.data.exists?await b.f.openFromPath(e):l.s.confirm("Test plan file does not exist. Do you want to remove it from history?","File Not Found",{confirmButtonText:"Remove",cancelButtonText:"Cancel",type:"warning"}).then((async()=>{await d.I.deleteRecord(e),await b.f.loadRecentPlans()})).catch((()=>{}))}catch(t){console.error("Failed to check file existence:",t)}},y=()=>{a.value?.show()},S=()=>{},T=()=>{l.s.confirm("Are you sure you want to exit the application?","Exit",{confirmButtonText:"OK",cancelButtonText:"Cancel",type:"warning"}).then((()=>{p.GQ.exit().then((()=>{c.nk.info("Application is shutting down...")})).catch((e=>{c.nk.error("Failed to exit the application"),console.error("Error when exiting application:",e)}))})).catch((()=>{}))};return(0,s.sV)((()=>{b.f.loadRecentPlans()})),(e,t)=>{const n=(0,s.g2)("el-icon"),i=(0,s.g2)("el-menu-item"),l=(0,s.g2)("el-divider"),c=(0,s.g2)("el-sub-menu"),u=(0,s.g2)("el-menu");return(0,s.uX)(),(0,s.CE)("div",R,[(0,s.bF)(u,{mode:"horizontal",class:"menu-container",router:"","default-active":f.value},{default:(0,s.k6)((()=>[(0,s.bF)(i,{index:"/",class:"home-menu-item"},{default:(0,s.k6)((()=>[(0,s.bF)(n,null,{default:(0,s.k6)((()=>[(0,s.bF)((0,o.R1)(m.HomeFilled))])),_:1})])),_:1}),(0,s.bF)(c,{index:"file"},{title:(0,s.k6)((()=>t[0]||(t[0]=[(0,s.eW)("File")]))),default:(0,s.k6)((()=>[(0,s.bF)(i,{onClick:v,class:"menu-item"},{default:(0,s.k6)((()=>t[1]||(t[1]=[(0,s.eW)("Open")]))),_:1}),(0,s.bF)(i,{onClick:y,class:"menu-item"},{default:(0,s.k6)((()=>t[2]||(t[2]=[(0,s.eW)("Create")]))),_:1}),(0,s.bF)(l),(0,s.bF)(c,{index:"recent","popper-class":"recent-submenu"},{title:(0,s.k6)((()=>t[3]||(t[3]=[(0,s.eW)("Recent")]))),default:(0,s.k6)((()=>[0===h.value.length?((0,s.uX)(),(0,s.CE)("div",I,t[4]||(t[4]=[(0,s.Lk)("span",null,"No recent items",-1)]))):((0,s.uX)(!0),(0,s.CE)(s.FK,{key:1},(0,s.pI)(h.value,(e=>((0,s.uX)(),(0,s.Wv)(i,{key:e.id,onClick:t=>C(e.filePath),class:"recent-item"},{default:(0,s.k6)((()=>[(0,s.Lk)("span",E,(0,r.v_)(e.planName),1),(0,s.Lk)("span",A,(0,r.v_)(e.filePath),1)])),_:2},1032,["onClick"])))),128))])),_:1}),(0,s.bF)(l),(0,s.bF)(i,{onClick:T,class:"menu-item"},{default:(0,s.k6)((()=>t[5]||(t[5]=[(0,s.eW)("Exit")]))),_:1})])),_:1}),(0,s.bF)(i,{index:"/test-suite",class:"test-suite-menu-item"},{default:(0,s.k6)((()=>t[6]||(t[6]=[(0,s.Lk)("span",{class:"test-suite-text"},"Test Suite",-1)]))),_:1}),(0,s.bF)(i,{index:"/about",class:"about-menu-item"},{default:(0,s.k6)((()=>t[7]||(t[7]=[(0,s.Lk)("span",{class:"about-text"},"About",-1)]))),_:1})])),_:1},8,["default-active"]),(0,s.bF)(x,{class:"status-indicator"}),(0,s.bF)(k,{ref_key:"createDialog",ref:a,onCreated:S},null,512)])}}});const _=(0,C.A)(M,[["__scopeId","data-v-34acd3a4"]]);var N=_;const L={class:"app-container"},O={class:"main-content"},X={class:"content"};var $={__name:"App",setup(e){return(e,t)=>{const n=(0,s.g2)("router-view");return(0,s.uX)(),(0,s.CE)("div",L,[(0,s.bF)(N),(0,s.Lk)("div",O,[(0,s.Lk)("main",X,[(0,s.bF)(n)])])])}}};const q=$;var B=q;const Z={class:"home-view"},W={class:"panel-header"},z={class:"button-group"},H={class:"history-container"};function U(e,t,n,a,o,r){const i=(0,s.g2)("Folder"),l=(0,s.g2)("el-icon"),c=(0,s.g2)("el-button"),u=(0,s.g2)("Plus"),d=(0,s.g2)("test-plan-history"),p=(0,s.g2)("el-card"),m=(0,s.g2)("el-col"),f=(0,s.g2)("test-plan-guide"),g=(0,s.g2)("el-row"),h=(0,s.g2)("create-test-plan");return(0,s.uX)(),(0,s.CE)("div",Z,[(0,s.bF)(g,{class:"layout-container",gutter:20},{default:(0,s.k6)((()=>[(0,s.bF)(m,{span:12,class:"left-panel"},{default:(0,s.k6)((()=>[(0,s.bF)(p,{class:"control-card"},{header:(0,s.k6)((()=>[(0,s.Lk)("div",W,[t[2]||(t[2]=(0,s.Lk)("div",{class:"title-section"},[(0,s.Lk)("h2",null,"Test Plans")],-1)),(0,s.Lk)("div",z,[(0,s.bF)(c,{type:"primary",class:"action-btn",onClick:e.handleOpenTestPlan},{default:(0,s.k6)((()=>[(0,s.bF)(l,null,{default:(0,s.k6)((()=>[(0,s.bF)(i)])),_:1}),t[0]||(t[0]=(0,s.Lk)("span",null,"Open",-1))])),_:1},8,["onClick"]),(0,s.bF)(c,{type:"success",class:"action-btn",onClick:e.showCreateDialog},{default:(0,s.k6)((()=>[(0,s.bF)(l,null,{default:(0,s.k6)((()=>[(0,s.bF)(u)])),_:1}),t[1]||(t[1]=(0,s.Lk)("span",null,"Create",-1))])),_:1},8,["onClick"])])])])),default:(0,s.k6)((()=>[(0,s.Lk)("div",H,[(0,s.bF)(d,{ref:"historyComponent",onOpenPlan:e.handleOpenFromHistory},null,8,["onOpenPlan"])])])),_:1})])),_:1}),(0,s.bF)(m,{span:12,class:"right-panel"},{default:(0,s.k6)((()=>[(0,s.bF)(p,{class:"guide-card"},{header:(0,s.k6)((()=>t[3]||(t[3]=[(0,s.Lk)("div",{class:"panel-header"},[(0,s.Lk)("div",{class:"title-section"},[(0,s.Lk)("h2",null,"Guide")])],-1)]))),default:(0,s.k6)((()=>[(0,s.bF)(f)])),_:1})])),_:1})])),_:1}),(0,s.bF)(h,{ref:"createDialog",onCreated:e.handleTestPlanCreated},null,8,["onCreated"])])}const K={class:"history-section"},V={class:"history-header"},j={key:0,class:"loading-container"},G={class:"history-list"},Q=["onClick"],Y={class:"item-grid"},J={class:"item-name"},ee={class:"item-action"},te=["title"],ne={class:"file-path"},ae={class:"access-time"};function se(e,t,n,o,i,l){const c=(0,s.g2)("font-awesome-icon"),u=(0,s.g2)("el-button"),d=(0,s.g2)("el-skeleton"),p=(0,s.g2)("el-empty"),m=(0,s.g2)("el-tag"),f=(0,s.g2)("el-pagination"),g=(0,s.gN)("loading");return(0,s.uX)(),(0,s.CE)("div",K,[(0,s.Lk)("div",V,[t[0]||(t[0]=(0,s.Lk)("div",{class:"header-title"},[(0,s.Lk)("h3",null,"Recently Opened")],-1)),e.historyList.length>0?((0,s.uX)(),(0,s.Wv)(u,{key:0,type:"text",size:"small",onClick:e.handleClearHistory,class:"clear-btn",title:"Clear history"},{default:(0,s.k6)((()=>[(0,s.bF)(c,{icon:"trash-can"})])),_:1},8,["onClick"])):(0,s.Q3)("",!0)]),e.loading?((0,s.uX)(),(0,s.CE)("div",j,[(0,s.bF)(d,{rows:5})])):e.historyList.length?((0,s.uX)(),(0,s.CE)(s.FK,{key:2},[(0,s.bo)(((0,s.uX)(),(0,s.CE)("div",G,[((0,s.uX)(!0),(0,s.CE)(s.FK,null,(0,s.pI)(e.displayedHistory,((t,n)=>((0,s.uX)(),(0,s.CE)("div",{key:t.id,class:(0,r.C4)(["history-item",{"history-item-alt":n%2===1}]),onClick:n=>e.handleOpenPlan(t.filePath)},[(0,s.Lk)("div",Y,[(0,s.Lk)("div",J,[(0,s.bF)(m,{size:"small",effect:"plain",class:"plan-name"},{default:(0,s.k6)((()=>[(0,s.eW)((0,r.v_)(t.planName),1)])),_:2},1024)]),(0,s.Lk)("div",ee,[(0,s.bF)(u,{type:"text",size:"small",onClick:(0,a.D$)((n=>e.handleOpenFolder(t.filePath)),["stop"]),title:"Open in File Explorer"},{default:(0,s.k6)((()=>[(0,s.bF)(c,{icon:"folder-open"})])),_:2},1032,["onClick"])]),(0,s.Lk)("div",{class:"item-path",title:t.filePath},[(0,s.bF)(c,{icon:"folder"}),(0,s.Lk)("span",ne,(0,r.v_)(t.filePath),1)],8,te),(0,s.Lk)("div",ae,[(0,s.bF)(c,{icon:"clock"}),(0,s.eW)(" "+(0,r.v_)(e.formatDateTime(t.lastAccessTime)),1)])])],10,Q)))),128))])),[[g,e.loading]]),e.historyList.length>e.pageSize?((0,s.uX)(),(0,s.Wv)(f,{key:0,layout:"prev, pager, next",total:e.historyList.length,"page-size":e.pageSize,"current-page":e.currentPage,onCurrentChange:e.handlePageChange,class:"pagination",background:"","hide-on-single-page":""},null,8,["total","page-size","current-page","onCurrentChange"])):(0,s.Q3)("",!0)],64)):((0,s.uX)(),(0,s.Wv)(p,{key:1,description:"No recent items","image-size":150},{image:(0,s.k6)((()=>[(0,s.bF)(c,{icon:"file-circle-exclamation",class:"empty-icon"})])),description:(0,s.k6)((()=>t[1]||(t[1]=[(0,s.Lk)("p",{class:"empty-description"},"No recent items",-1),(0,s.Lk)("p",{class:"empty-tip"},"Recently opened plans will appear here",-1)]))),_:1}))])}var oe=(0,s.pM)({name:"TestPlanHistory",emits:["open-plan"],setup(e,{emit:t,expose:n}){const a=b.f.getState(),r=20,i=(0,o.KR)(1),c=(0,o.KR)(!0),p=(0,s.EW)((()=>a.recentPlans)),m=(0,s.EW)((()=>{const e=(i.value-1)*r,t=e+r;return p.value.slice(e,t)})),f=async()=>{c.value=!0;try{await b.f.loadRecentPlans(),i.value=1}finally{c.value=!1}},g=e=>{i.value=e},v=()=>{l.s.confirm("Are you sure you want to clear all recent items?","Warning",{confirmButtonText:"Confirm",cancelButtonText:"Cancel",type:"warning"}).then((async()=>{await b.f.clearHistory()})).catch((()=>{}))},C=async e=>{try{const n=await u.E.checkFileExists(e);n.data.exists?t("open-plan",e):l.s.confirm("测试计划文件不存在，是否从历史记录中删除该条目？","文件不存在",{confirmButtonText:"删除",cancelButtonText:"取消",type:"warning"}).then((async()=>{await d.I.deleteRecord(e),f()})).catch((()=>{}))}catch(n){console.error("检查文件存在性失败:",n)}},y=async e=>{try{await h.m.openExplorer(e)}catch(t){console.error("Failed to open folder",t)}},k=e=>{const t=new Date(e);return t.toLocaleString("en-US",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"})};return(0,s.sV)(f),n({refresh:f}),{historyList:p,displayedHistory:m,currentPage:i,pageSize:r,loading:c,handleClearHistory:v,handleOpenPlan:C,handleOpenFolder:y,formatDateTime:k,handlePageChange:g}}});const re=(0,C.A)(oe,[["render",se],["__scopeId","data-v-d6b8a97a"]]);var ie=re;const le={class:"guide-content"},ce={class:"step-content"},ue={class:"step-header"},de={class:"step-title"},pe={class:"step-desc"};var me=(0,s.pM)({__name:"TestPlanGuide",setup(e){const t=[{title:"Basic Setting",description:"Configure test plan name, description and select test suite type"},{title:"Hardware Setting",description:"Set up CAN/CANFD hardware type and configure baud rate parameters"},{title:"Interoperation",description:"Verify hardware connection and protocol compatibility"},{title:"Test Cases",description:"Select coverage intensity and generate test cases based on supported services"},{title:"Test Run",description:"Run test cases with start and stop control"},{title:"Report",description:"View test reports and analyze data with detailed results"}];return(e,n)=>{const a=(0,s.g2)("el-timeline-item"),o=(0,s.g2)("el-timeline");return(0,s.uX)(),(0,s.CE)("div",le,[(0,s.bF)(o,null,{default:(0,s.k6)((()=>[((0,s.uX)(),(0,s.CE)(s.FK,null,(0,s.pI)(t,(e=>(0,s.bF)(a,{key:e.title,type:"info",size:"normal",timestamp:""},{default:(0,s.k6)((()=>[(0,s.Lk)("div",ce,[(0,s.Lk)("div",ue,[(0,s.Lk)("span",de,(0,r.v_)(e.title),1)]),(0,s.Lk)("p",pe,(0,r.v_)(e.description),1)])])),_:2},1024))),64))])),_:1})])}}});const fe=(0,C.A)(me,[["__scopeId","data-v-6570f07b"]]);var ge=fe,he=(0,s.pM)({name:"HomeView",components:{CreateTestPlan:k,TestPlanHistory:ie,TestPlanGuide:ge,Folder:m.Folder,Plus:m.Plus},setup(){const e=(0,i.rd)(),t=(0,o.KR)(null),n=(0,o.KR)(null);b.f.setRouter(e);const a=async()=>{await b.f.openFromExplorer(),n.value?.refresh()},s=async e=>{await b.f.openFromPath(e),n.value?.refresh()},r=()=>{t.value?.show()},l=()=>{n.value?.refresh()};return{createDialog:t,historyComponent:n,handleOpenTestPlan:a,showCreateDialog:r,handleTestPlanCreated:l,handleOpenFromHistory:s}}});const ve=(0,C.A)(he,[["render",U],["__scopeId","data-v-dc9ab15c"]]);var Ce=ve,ye=n(687),ke=n(1748);const be={class:"test-suite-container"},Se={class:"suites-list"},Te={class:"suite-header"},Fe={class:"version"},we={class:"packages-list"},Pe=["onClick"];var De=(0,s.pM)({__name:"TestSuiteView",setup(e){const t=(0,o.KR)([]),n=(0,o.KR)(null),i=(0,o.KR)(""),l=(0,o.KR)(-1),u=(0,o.KR)(""),d=async()=>{try{const e=await ke.p.getBuiltIn();t.value=e.data}catch(e){c.nk.error("Failed to load test suites"),console.error("Error loading test suites:",e)}},p=async e=>{n.value?.name!==e.name&&(n.value=e,l.value=-1,u.value="",i.value="")},f=async(e,t,a)=>{n.value=e,l.value=t,u.value=a.name;try{const t=await ke.p.getBuiltInXml(e.name,a.name);i.value=t.data}catch(s){c.nk.error("Failed to load XML content"),console.error("Error loading XML:",s)}};return(0,s.sV)((()=>{d()})),(e,c)=>{const d=(0,s.g2)("el-icon"),g=(0,s.g2)("el-card");return(0,s.uX)(),(0,s.CE)("div",be,[(0,s.Lk)("div",Se,[((0,s.uX)(!0),(0,s.CE)(s.FK,null,(0,s.pI)(t.value,(e=>((0,s.uX)(),(0,s.Wv)(g,{key:e.name,class:(0,r.C4)(["suite-card",{active:n.value?.name===e.name}]),onClick:t=>p(e)},{header:(0,s.k6)((()=>[(0,s.Lk)("div",Te,[(0,s.Lk)("h3",null,(0,r.v_)(e.name),1),(0,s.Lk)("span",Fe,"v"+(0,r.v_)(e.version),1)])])),default:(0,s.k6)((()=>[(0,s.Lk)("div",we,[((0,s.uX)(!0),(0,s.CE)(s.FK,null,(0,s.pI)(e.packages,((t,i)=>((0,s.uX)(),(0,s.CE)("div",{key:i,class:(0,r.C4)(["package-item",{active:n.value?.name===e.name&&l.value===i}]),onClick:(0,a.D$)((n=>f(e,i,t)),["stop"])},[(0,s.bF)(d,null,{default:(0,s.k6)((()=>[(0,s.bF)((0,o.R1)(m.Document))])),_:1}),(0,s.Lk)("span",null,(0,r.v_)(t.name),1)],10,Pe)))),128))])])),_:2},1032,["class","onClick"])))),128))]),n.value?((0,s.uX)(),(0,s.Wv)(ye.A,{key:0,title:`${n.value.name} - ${u.value}`,content:i.value},null,8,["title","content"])):(0,s.Q3)("",!0)])}}});const xe=(0,C.A)(De,[["__scopeId","data-v-0f6f8041"]]);var Re=xe;const Ie=[{path:"/",name:"home",component:Ce},{path:"/test-plan",name:"test-plan",component:()=>n.e(766).then(n.bind(n,2766)),props:e=>({path:e.query.path}),children:[{path:"",name:"test-plan.basic-setting",component:()=>n.e(644).then(n.bind(n,8644))},{path:"basic-settings",name:"test-plan.basic-settings",component:()=>n.e(644).then(n.bind(n,8644))},{path:"hardware",name:"test-plan.hardware",component:()=>n.e(351).then(n.bind(n,7732))},{path:"case-setting",name:"test-plan.case-setting",component:()=>n.e(112).then(n.bind(n,5112))},{path:"interoperation",name:"test-plan.interoperation",component:()=>n.e(295).then(n.bind(n,4295))},{path:"test-cases",name:"test-plan.test-cases",component:()=>n.e(186).then(n.bind(n,2186))},{path:"test-run",name:"test-plan.test-run",component:()=>Promise.all([n.e(828),n.e(67)]).then(n.bind(n,8763))},{path:"test-results",name:"test-plan.test-results",component:()=>Promise.all([n.e(828),n.e(852)]).then(n.bind(n,9252))},{path:"sequence-setting",name:"test-plan.sequence-setting",component:()=>n.e(153).then(n.bind(n,4153))}]},{path:"/test-suite",name:"test-suite",component:Re},{path:"/about",name:"about",component:()=>n.e(929).then(n.bind(n,6929))}],Ee=(0,i.aE)({history:(0,i.LA)(),routes:Ie});Ee.beforeEach((async(e,t,n)=>{if(b.f.setRouter(Ee),e.path.startsWith("/test-plan")||e.path.startsWith("/testplan")){const e=b.f.getState();if(!e.currentPlan){const e=await b.f.getCurrentPlan();if(!e)return n("/"),void c.nk.warning("No test plan is currently open")}}n()}));var Ae=Ee,Me=n(782),_e=(0,Me.y$)({state:{},getters:{},mutations:{},actions:{},modules:{}}),Ne=n(4373);const Le=e=>{if(!e.response||!e.response.data)return e.message||"Unknown error";const t=e.response.data,n=[];t.exceptionMessage&&n.push(t.exceptionMessage);let a=t.innerException;while(a)a.exceptionMessage&&n.push(a.exceptionMessage),a=a.innerException;return 0===n.length?t.message||"An error occurred":n.join("<br>")},Oe=e=>{if(!e.response||!e.response.data)return void c.nk.error(e.message||"Unknown error");const t=Le(e);l.s.alert(t,"Error",{confirmButtonText:"OK",dangerouslyUseHTMLString:!0,closeOnClickModal:!0,closeOnPressEscape:!0,showClose:!0})},Xe=e=>{if(e.response&&e.response.data){if("UserCanceled"===e.response.data)return!0;if("UserCanceled"===e.response.data.message)return!0;if("UserCanceled"===e.response.data.errorCode)return!0}return!1},$e=()=>{Ne.A.interceptors.response.use((e=>e),(e=>Xe(e)?(c.nk.info("Operation cancelled by user"),Promise.reject(e)):(Oe(e),Promise.reject(e))))};var qe=n(2097),Be=(n(4188),n(2721)),Ze=n(8950),We=n(292),ze=n(2353);Ze.Yv.add(ze.Ubc,ze.Uj9,ze.QLR,ze.h8M,ze.Int,ze.sjs,ze.fny,ze.a$,ze.ao0,ze.$Fj,ze.qFF,ze.Yj9,ze.LqK,ze.tdl,ze.GF6,ze.oZK,ze.gr3,ze.skf,ze.DOu);const He=(0,a.Ef)(B);He.component("font-awesome-icon",We.gc);for(const[Ue,Ke]of Object.entries(m))He.component(Ue,Ke);$e(),He.use(_e).use(Ae).use(qe.A,{locale:Be.A,size:"default"}).mount("#app"),He.config.errorHandler=(e,t,n)=>{console.error("Vue 全局错误:",e);const a={message:e instanceof Error?e.message:String(e),stack:e instanceof Error?e.stack:"无堆栈信息",vueHookInfo:n,url:window.location.href};p.GQ.logError(a).catch((e=>{console.error("发送错误到服务器失败:",e)}))},window.addEventListener("unhandledrejection",(e=>{const t={message:e.reason instanceof Error?e.reason.message:"未处理的Promise异常",stack:e.reason instanceof Error?e.reason.stack:"无堆栈信息",url:window.location.href,type:"unhandledrejection"};p.GQ.logError(t).catch((e=>{console.error("发送Promise错误到服务器失败:",e)}))})),window.addEventListener("error",(e=>{if(e.message){const t={message:e.message,codeInfo:`${e.filename}:${e.lineno}:${e.colno}`,url:window.location.href,type:"global-error"};p.GQ.logError(t).catch((e=>{console.error("发送全局错误到服务器失败:",e)}))}}))},8769:function(e,t,n){n.d(t,{Wm:function(){return i}});var a,s=n(4373),o=n(3144);(function(e){e[e["Can"]=0]="Can",e[e["Canfd"]=1]="Canfd"})(a||(a={}));const r="/api/sequenceConfig",i={getSequenceConfig:()=>o.Xo?o.Z0.sequence.getSequenceConfig():s.A.get(`${r}`),updateSequenceConfig:e=>o.Xo?o.Z0.sequence.updateSequenceConfig(e):s.A.post(`${r}`,e)}}},t={};function n(a){var s=t[a];if(void 0!==s)return s.exports;var o=t[a]={id:a,loaded:!1,exports:{}};return e[a].call(o.exports,o,o.exports,n),o.loaded=!0,o.exports}n.m=e,function(){var e=[];n.O=function(t,a,s,o){if(!a){var r=1/0;for(u=0;u<e.length;u++){a=e[u][0],s=e[u][1],o=e[u][2];for(var i=!0,l=0;l<a.length;l++)(!1&o||r>=o)&&Object.keys(n.O).every((function(e){return n.O[e](a[l])}))?a.splice(l--,1):(i=!1,o<r&&(r=o));if(i){e.splice(u--,1);var c=s();void 0!==c&&(t=c)}}return t}o=o||0;for(var u=e.length;u>0&&e[u-1][2]>o;u--)e[u]=e[u-1];e[u]=[a,s,o]}}(),function(){n.n=function(e){var t=e&&e.__esModule?function(){return e["default"]}:function(){return e};return n.d(t,{a:t}),t}}(),function(){n.d=function(e,t){for(var a in t)n.o(t,a)&&!n.o(e,a)&&Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}}(),function(){n.f={},n.e=function(e){return Promise.all(Object.keys(n.f).reduce((function(t,a){return n.f[a](e,t),t}),[]))}}(),function(){n.u=function(e){return"js/"+e+"."+{67:"cd8395db",112:"dc1abed5",153:"8f9ab41e",186:"602861f1",295:"46f815ae",351:"2e6bb7b3",644:"4ba6c9bf",766:"95a4e0c1",828:"c5b13821",852:"c05bc352",929:"245410b5"}[e]+".js"}}(),function(){n.miniCssF=function(e){return"css/"+e+"."+{67:"d5944060",112:"33e69a23",153:"69e46bae",186:"69bdf509",295:"15161d40",351:"e46682e2",644:"8183bc07",766:"1ce99f9b",852:"7937723c",929:"9b8195a7"}[e]+".css"}}(),function(){n.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"===typeof window)return window}}()}(),function(){n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)}}(),function(){var e={},t="fuzz-web:";n.l=function(a,s,o,r){if(e[a])e[a].push(s);else{var i,l;if(void 0!==o)for(var c=document.getElementsByTagName("script"),u=0;u<c.length;u++){var d=c[u];if(d.getAttribute("src")==a||d.getAttribute("data-webpack")==t+o){i=d;break}}i||(l=!0,i=document.createElement("script"),i.charset="utf-8",i.timeout=120,n.nc&&i.setAttribute("nonce",n.nc),i.setAttribute("data-webpack",t+o),i.src=a),e[a]=[s];var p=function(t,n){i.onerror=i.onload=null,clearTimeout(m);var s=e[a];if(delete e[a],i.parentNode&&i.parentNode.removeChild(i),s&&s.forEach((function(e){return e(n)})),t)return t(n)},m=setTimeout(p.bind(null,void 0,{type:"timeout",target:i}),12e4);i.onerror=p.bind(null,i.onerror),i.onload=p.bind(null,i.onload),l&&document.head.appendChild(i)}}}(),function(){n.r=function(e){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}}(),function(){n.nmd=function(e){return e.paths=[],e.children||(e.children=[]),e}}(),function(){n.p="/"}(),function(){if("undefined"!==typeof document){var e=function(e,t,a,s,o){var r=document.createElement("link");r.rel="stylesheet",r.type="text/css",n.nc&&(r.nonce=n.nc);var i=function(n){if(r.onerror=r.onload=null,"load"===n.type)s();else{var a=n&&n.type,i=n&&n.target&&n.target.href||t,l=new Error("Loading CSS chunk "+e+" failed.\n("+a+": "+i+")");l.name="ChunkLoadError",l.code="CSS_CHUNK_LOAD_FAILED",l.type=a,l.request=i,r.parentNode&&r.parentNode.removeChild(r),o(l)}};return r.onerror=r.onload=i,r.href=t,a?a.parentNode.insertBefore(r,a.nextSibling):document.head.appendChild(r),r},t=function(e,t){for(var n=document.getElementsByTagName("link"),a=0;a<n.length;a++){var s=n[a],o=s.getAttribute("data-href")||s.getAttribute("href");if("stylesheet"===s.rel&&(o===e||o===t))return s}var r=document.getElementsByTagName("style");for(a=0;a<r.length;a++){s=r[a],o=s.getAttribute("data-href");if(o===e||o===t)return s}},a=function(a){return new Promise((function(s,o){var r=n.miniCssF(a),i=n.p+r;if(t(r,i))return s();e(a,i,null,s,o)}))},s={524:0};n.f.miniCss=function(e,t){var n={67:1,112:1,153:1,186:1,295:1,351:1,644:1,766:1,852:1,929:1};s[e]?t.push(s[e]):0!==s[e]&&n[e]&&t.push(s[e]=a(e).then((function(){s[e]=0}),(function(t){throw delete s[e],t})))}}}(),function(){var e={524:0};n.f.j=function(t,a){var s=n.o(e,t)?e[t]:void 0;if(0!==s)if(s)a.push(s[2]);else{var o=new Promise((function(n,a){s=e[t]=[n,a]}));a.push(s[2]=o);var r=n.p+n.u(t),i=new Error,l=function(a){if(n.o(e,t)&&(s=e[t],0!==s&&(e[t]=void 0),s)){var o=a&&("load"===a.type?"missing":a.type),r=a&&a.target&&a.target.src;i.message="Loading chunk "+t+" failed.\n("+o+": "+r+")",i.name="ChunkLoadError",i.type=o,i.request=r,s[1](i)}};n.l(r,l,"chunk-"+t,t)}},n.O.j=function(t){return 0===e[t]};var t=function(t,a){var s,o,r=a[0],i=a[1],l=a[2],c=0;if(r.some((function(t){return 0!==e[t]}))){for(s in i)n.o(i,s)&&(n.m[s]=i[s]);if(l)var u=l(n)}for(t&&t(a);c<r.length;c++)o=r[c],n.o(e,o)&&e[o]&&e[o][0](),e[o]=0;return n.O(u)},a=self["webpackChunkfuzz_web"]=self["webpackChunkfuzz_web"]||[];a.forEach(t.bind(null,0)),a.push=t.bind(null,a.push.bind(a))}();var a=n.O(void 0,[504],(function(){return n(7921)}));a=n.O(a)})();
//# sourceMappingURL=app.b40f5e86.js.map