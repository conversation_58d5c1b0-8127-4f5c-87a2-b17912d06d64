using Alsi.Fuzz.Core.Utils;
using Shouldly;
using Xunit.Abstractions;

namespace Alsi.Fuzz.UnitTests.Utils
{
    public class SequenceUtilsTests
    {
        private readonly ITestOutputHelper _testOutputHelper;

        public SequenceUtilsTests(ITestOutputHelper testOutputHelper)
        {
            _testOutputHelper = testOutputHelper;
        }

        [Theory]
        [InlineData("123", 123)]
        [InlineData(" 456 ", 456)]
        [InlineData("0", 0)]
        [InlineData("0x1A", 26)]
        [InlineData("0XFF", 255)]
        [InlineData(" 0x100 ", 256)]
        [InlineData("hex:1A", 26)]
        [InlineData("hex:FF", 255)]
        [InlineData("HEX:100", 256)]
        [InlineData(" hex:ABC ", 2748)]
        public void ParseInt_ValidInput_ReturnsCorrectValue(string input, int expected)
        {
            // Act
            int result = SequenceUtils.ParseInt(input);

            // Assert
            result.ShouldBe(expected);
            _testOutputHelper.WriteLine($"Input: {input}, Result: {result}");
        }

        [Theory]
        [InlineData("abc")]
        [InlineData("0xZZ")]
        [InlineData("")]
        [InlineData("hex:ZZ")]
        [InlineData("2147483648")] // 超出 int 上限
        public void ParseInt_InvalidInput_ThrowsFormatException(string input)
        {
            // Act & Assert
            Should.Throw<Exception>(() => SequenceUtils.ParseInt(input));
            _testOutputHelper.WriteLine($"Successfully threw exception for invalid input: {input}");
        }

        [Theory]
        [InlineData("123", 123)]
        [InlineData(" 45 ", 45)]
        [InlineData("0", 0)]
        [InlineData("0x1A", 26)]
        [InlineData("0XFF", 255)]
        [InlineData("hex:1A", 26)]
        [InlineData("hex:FF", 255)]
        [InlineData(" HEX:A0 ", 160)]
        public void ParseByte_ValidInput_ReturnsCorrectValue(string input, byte expected)
        {
            // Act
            byte result = SequenceUtils.ParseByte(input);

            // Assert
            result.ShouldBe(expected);
            _testOutputHelper.WriteLine($"Input: {input}, Result: {result}");
        }

        [Theory]
        [InlineData("abc")]
        [InlineData("0xZZ")]
        [InlineData("")]
        [InlineData("hex:ZZ")]
        [InlineData("256")] // 超出 byte 上限
        [InlineData("0xFF1")] // 超出 byte 上限
        [InlineData("hex:100")] // 超出 byte 上限
        [InlineData("-1")] // 低于 byte 下限
        public void ParseByte_InvalidInput_ThrowsFormatException(string input)
        {
            // Act & Assert
            Should.Throw<Exception>(() => SequenceUtils.ParseByte(input));
            _testOutputHelper.WriteLine($"Successfully threw exception for invalid input: {input}");
        }

        [Theory]
        [InlineData("1 2 3", new byte[] { 1, 2, 3 })]
        [InlineData("1,2,3", new byte[] { 1, 2, 3 })]
        [InlineData(" 10, 20 , 30 ", new byte[] { 0x10, 0x20, 0x30 })]
        [InlineData("0x01 0x02 0x03", new byte[] { 1, 2, 3 })]
        [InlineData("0x01,0x02,0x03", new byte[] { 1, 2, 3 })]
        [InlineData("0xA 15 0x1F", new byte[] { 10, 0x15, 31 })]
        [InlineData("0xFF", new byte[] { 255 })]
        [InlineData("0xA1B2", new byte[] { 161, 178 })]
        [InlineData("0x1 0x23 0x456", new byte[] { 1, 35, 4, 86 })]
        [InlineData("hex:FF 00 33 11", new byte[] { 255, 0, 51, 17 })]
        [InlineData("hex:FF,00,33,11", new byte[] { 255, 0, 51, 17 })]
        [InlineData("hex:FF003311", new byte[] { 255, 0, 51, 17 })]
        [InlineData("HEX:0102030405", new byte[] { 1, 2, 3, 4, 5 })]
        [InlineData("hex:0", new byte[] { 0 })]
        [InlineData("hex:A", new byte[] { 10 })]
        [InlineData(" hex:FF00 ", new byte[] { 255, 0 })]
        public void ParseBytes_ValidInput_ReturnsCorrectArray(string input, byte[] expected)
        {
            // Act
            byte[] result = SequenceUtils.ParseBytes(input);

            // Assert
            result.ShouldBeEquivalentTo(expected);
            _testOutputHelper.WriteLine($"Input: {input}, Result: {BitConverter.ToString(result).Replace("-", " ")}");
        }

        [Theory]
        [InlineData("abc")]
        [InlineData("0xZZ")]
        [InlineData("256")]
        [InlineData("0x1FG")]
        [InlineData("hex:ZZ")]
        [InlineData("hex:1FG")]
        public void ParseBytes_InvalidInput_ThrowsFormatException(string input)
        {
            // Act & Assert
            Should.Throw<FormatException>(() => SequenceUtils.ParseBytes(input));
            _testOutputHelper.WriteLine($"Successfully threw exception for invalid input: {input}");
        }

        [Theory]
        [InlineData("hex:AA BB repeat:2", new byte[] { 0xAA, 0xBB, 0xAA, 0xBB })]
        [InlineData("hex:AA BB 11 22 repeat:3 take:5", new byte[] { 0xAA, 0xBB, 0x11, 0x22, 0xAA })]
        [InlineData("hex:AA BB 11 22 take:2 repeat:3", new byte[] { 0xAA, 0xBB, 0xAA, 0xBB, 0xAA, 0xBB })] // 修改为先take后repeat
        [InlineData("hex:FF repeat:5 take:3", new byte[] { 0xFF, 0xFF, 0xFF })]
        [InlineData("0x10 0x20 repeat:2", new byte[] { 0x10, 0x20, 0x10, 0x20 })]
        [InlineData("0x01 0x02 0x03 repeat:3 take:7", new byte[] { 0x01, 0x02, 0x03, 0x01, 0x02, 0x03, 0x01 })]
        [InlineData("0x01 0x02 0x03 take:2 repeat:3", new byte[] { 0x01, 0x02, 0x01, 0x02, 0x01, 0x02 })] // 新增用例：先take后repeat
        [InlineData("0x10 0x20 0x30 take:2", new byte[] { 0x10, 0x20 })]
        [InlineData("hex:AABB repeat:2", new byte[] { 0xAA, 0xBB, 0xAA, 0xBB })]
        [InlineData("hex:ABCDEF take:2", new byte[] { 0xAB, 0xCD })]
        [InlineData("REPEAT:3 hex:AA", new byte[] { 0xAA, 0xAA, 0xAA })] // 测试大写标签
        [InlineData("TAKE:1 hex:AA BB", new byte[] { 0xAA })] // 测试大写标签
        [InlineData("take:1 repeat:2 hex:AA BB", new byte[] { 0xAA, 0xAA })] // 测试标签在hex前
        [InlineData("repeat:2 take:3 hex:AA BB", new byte[] { 0xAA, 0xBB, 0xAA })] // 测试标签在hex前
        [InlineData("hex: 33 repeat: 3 take: 2", new byte[] { 0x33, 0x33 })] // 测试标签在hex前
        public void ParseBytes_WithRepeatAndTake_ReturnsCorrectArray(string input, byte[] expected)
        {
            // Act
            byte[] result = SequenceUtils.ParseBytes(input);

            // Assert
            result.ShouldBeEquivalentTo(expected);
            _testOutputHelper.WriteLine($"Input: {input}, Result: {BitConverter.ToString(result).Replace("-", " ")}");
        }

        [Theory]
        [InlineData("hex:AA repeat:0")] // 重复次数不能小于1
        [InlineData("hex:AA take:0")] // take数不能小于1
        [InlineData("hex:AA repeat:-1")] // 负数重复次数
        [InlineData("hex:AA take:-5")] // 负数take值
        public void ParseBytes_WithInvalidRepeatOrTake_ThrowsFormatException(string input)
        {
            // Act & Assert
            Should.Throw<FormatException>(() => SequenceUtils.ParseBytes(input));
            _testOutputHelper.WriteLine($"Successfully threw exception for invalid input: {input}");
        }

        [Fact]
        public void ParseMathBytes_ValidInputWithoutWildcards_ReturnsCorrectArray()
        {
            var list = new List<Tuple<string, byte?[]>> {
                new ("1 2 3", new byte?[] { 1, 2, 3 }),
                new ("1,2,3", new byte?[] { 1, 2, 3 }),
                new (" 10, 20 , 30 ", new byte?[] { 0x10, 0x20, 0x30 }),
                new ("0x01 0x02 0x03", new byte?[] { 1, 2, 3 }),
                new ("0x01,0x02,0x03", new byte?[] { 1, 2, 3 }),
                new ("0xA 15 0x1F", new byte?[] { 10, 0x15, 0x1F }),
                new ("0xFF", new byte?[] { 255 }),
                new ("0xA1B2", new byte?[] { 161, 178 }),
                new ("hex:FF 00 33 11", new byte?[] { 255, 0, 51, 17 }),
                new ("hex:FF,00,33,11", new byte?[] { 255, 0, 51, 17 }),
                new ("hex:FF003311", new byte?[] { 255, 0, 51, 17 }),
                new ("HEX:0102030405", new byte?[] { 1, 2, 3, 4, 5 })
            };

            foreach (var item in list)
            {
                var input = item.Item1;
                var expected = item.Item2;

                // Act
                byte?[] result = SequenceUtils.ParseMatchBytes(input);

                // Assert
                result.ShouldBeEquivalentTo(expected);
                _testOutputHelper.WriteLine($"Input: {input}, Result: {FormatByteNullableArray(result)}");
            }
        }

        [Fact]
        public void ParseMathBytes_InputWithWildcards_ReturnsCorrectArray()
        {
            var list = new List<Tuple<string, byte?[]>> {
                new ( "* 2 3", new byte?[] { null, 2, 3 }),
                new ( "1,**,3", new byte?[] { 1, null, 3 }),
                new ( "** 0xFF *", new byte?[] { null, 255, null }),
                new ( "0x01 * 0x03", new byte?[] { 1, null, 3 }),
                new ( "hex:FF ** 33 *", new byte?[] { 255, null, 51, null }),
                new ( "hex:FF,00,**,11", new byte?[] { 255, 0, null, 17 }),
                new ( "HEX:01**0405", new byte?[] { 1, null, 4, 5 })
            };

            foreach (var item in list)
            {
                var input = item.Item1;
                var expected = item.Item2;
                // Act
                byte?[] result = SequenceUtils.ParseMatchBytes(input);

                // Assert
                result.ShouldBeEquivalentTo(expected);
                _testOutputHelper.WriteLine($"Input: {input}, Result: {FormatByteNullableArray(result)}");
            }

        }

        [Theory]
        [InlineData("* * *")]
        [InlineData("** ** **")]
        [InlineData("hex:* ** *")]
        public void ParseMathBytes_OnlyWildcards_ReturnsNullArray(string input)
        {
            var expected = new byte?[] { null, null, null };

            // Act
            byte?[] result = SequenceUtils.ParseMatchBytes(input);

            // Assert
            result.ShouldBeEquivalentTo(expected);
            _testOutputHelper.WriteLine($"Input: {input}, Result: {FormatByteNullableArray(result)}");
        }

        [Theory]
        [InlineData("abc")]
        [InlineData("0xZZ")]
        [InlineData("256")]
        [InlineData("0x1FG")]
        [InlineData("hex:ZZ")]
        [InlineData("hex:1FG")]
        public void ParseMathBytes_InvalidInput_ThrowsFormatException(string input)
        {
            // Act & Assert
            Should.Throw<FormatException>(() => SequenceUtils.ParseMatchBytes(input));
            _testOutputHelper.WriteLine($"Successfully threw exception for invalid input: {input}");
        }

        [Fact]
        public void ParseMathBytes_WithRepeatAndTake_ReturnsCorrectArray()
        {
            var list = new List<Tuple<string, byte?[]>> {
                new ("hex:AA BB repeat:2", new byte?[] { 0xAA, 0xBB, 0xAA, 0xBB }),
                new ("hex:AA BB 11 ** repeat:2", new byte?[] { 0xAA, 0xBB, 0x11, null, 0xAA, 0xBB, 0x11, null }),
                new ("hex:AA * repeat:3", new byte?[] { 0xAA, null, 0xAA, null, 0xAA, null }),
                new ("hex:AA BB * 22 take:3", new byte?[] { 0xAA, 0xBB, null }),
                new ("* 0x11 0x22 repeat:2 take:3", new byte?[] { null, 0x11, 0x22 }),
                new ("hex:AA BB 11 22 take:2 repeat:2", new byte?[] { 0xAA, 0xBB, 0xAA, 0xBB }),
                new ("repeat:2 take:3 hex:AA * CC", new byte?[] { 0xAA, null, 0xCC })
            };

            foreach (var item in list)
            {
                var input = item.Item1;
                var expected = item.Item2;

                // Act
                byte?[] result = SequenceUtils.ParseMatchBytes(input);

                // Assert
                result.ShouldBeEquivalentTo(expected);
                _testOutputHelper.WriteLine($"Input: {input}, Result: {FormatByteNullableArray(result)}");
            }
        }

        [Theory]
        [InlineData("hex:** repeat:0")] // 重复次数不能小于1
        [InlineData("hex:* take:0")] // take数不能小于1
        [InlineData("hex:** repeat:-1")] // 负数重复次数
        [InlineData("hex:* take:-5")] // 负数take值
        public void ParseMathBytes_WithInvalidRepeatOrTake_ThrowsFormatException(string input)
        {
            // Act & Assert
            Should.Throw<FormatException>(() => SequenceUtils.ParseMatchBytes(input));
            _testOutputHelper.WriteLine($"Successfully threw exception for invalid input: {input}");
        }

        private string FormatByteNullableArray(byte?[] array)
        {
            if (array == null) return "null";
            return string.Join(" ", array.Select(b => b.HasValue ? b.Value.ToString("X2") : "*"));
        }
    }
}
