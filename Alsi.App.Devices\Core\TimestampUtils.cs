﻿using System;

namespace Alsi.App.Devices.Core
{
    public static class TimestampUtils
    {
        public static ulong GetMicroseconds(double milliseconds)
        {
            var microseconds = (ulong)(milliseconds * 1000);
            return microseconds;
        }

        public static ulong GetMicroseconds(DateTime begin)
        {
            return GetMicroseconds((DateTime.Now - begin).TotalMilliseconds);
        }

        public static string GetString(DateTime begin)
        {
            var microseconds = GetMicroseconds(begin);
            return GetString(microseconds);
        }

        public static string GetString(ulong microseconds)
        {
            return (microseconds / 1_000_000.0).ToString("F6");
        }

        public static long Subtract(ulong time1, ulong time2)
        {
            return (long)time1 - (long)time2;
        }

        public static long Subtract(ulong time1, ulong time2, ulong time3)
        {
            return (long)time1 - (long)time2 - (long)time3;
        }
    }
}
