{"RootPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Tester", "ProjectFileName": "Alsi.Fuzz.Tester.csproj", "Configuration": "Debug|AnyCPU", "FrameworkPath": "", "Sources": [{"SourceFile": "Controllers\\TesterController.cs"}, {"SourceFile": "Testers\\PackageTester.cs"}, {"SourceFile": "Testers\\StepTester.cs"}, {"SourceFile": "FuzzTesterAssembly.cs"}, {"SourceFile": "Program.cs"}, {"SourceFile": "Properties\\AssemblyInfo.cs"}, {"SourceFile": "TesterEnv.cs"}, {"SourceFile": "obj\\Debug\\.NETFramework,Version=v4.6.2.AssemblyAttributes.cs"}], "References": [{"Reference": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.App.Database\\bin\\Debug\\Alsi.App.Database.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.App.Database\\bin\\Debug\\Alsi.App.Database.dll"}, {"Reference": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.App.Devices\\bin\\Debug\\Alsi.App.Devices.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.App.Devices\\bin\\Debug\\Alsi.App.Devices.dll"}, {"Reference": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.App\\bin\\Debug\\Alsi.App.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.App\\bin\\Debug\\Alsi.App.dll"}, {"Reference": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.Common.Utils\\bin\\Debug\\net462\\Alsi.Common.Utils.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.Common.Utils\\bin\\Debug\\net462\\Alsi.Common.Utils.dll"}, {"Reference": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Core\\bin\\Debug\\Alsi.Fuzz.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Core\\bin\\Debug\\Alsi.Fuzz.Core.dll"}, {"Reference": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\packages\\FreeSql.3.5.106\\lib\\net451\\FreeSql.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\packages\\FreeSql.Provider.Sqlite.3.5.106\\lib\\net45\\FreeSql.Provider.Sqlite.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6.2\\Microsoft.CSharp.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\packages\\Microsoft.Owin.4.2.2\\lib\\net45\\Microsoft.Owin.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6.2\\mscorlib.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\packages\\Newtonsoft.Json.Bson.1.0.2\\lib\\net45\\Newtonsoft.Json.Bson.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\packages\\Newtonsoft.Json.13.0.3\\lib\\net45\\Newtonsoft.Json.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\packages\\Owin.1.0\\lib\\net40\\Owin.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\packages\\System.Buffers.4.5.1\\lib\\net461\\System.Buffers.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6.2\\System.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6.2\\System.Data.DataSetExtensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6.2\\System.Data.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\packages\\Stub.System.Data.SQLite.Core.NetFramework.1.0.115.5\\lib\\net46\\System.Data.SQLite.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6.2\\System.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\packages\\System.Memory.4.5.5\\lib\\net461\\System.Memory.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\packages\\Microsoft.AspNet.WebApi.Client.6.0.0\\lib\\net45\\System.Net.Http.Formatting.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6.2\\System.Numerics.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\packages\\System.Numerics.Vectors.4.5.0\\lib\\net46\\System.Numerics.Vectors.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\packages\\System.Runtime.CompilerServices.Unsafe.4.5.3\\lib\\net461\\System.Runtime.CompilerServices.Unsafe.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\packages\\System.Threading.Tasks.Extensions.4.5.4\\lib\\net461\\System.Threading.Tasks.Extensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\packages\\Microsoft.AspNet.WebApi.Core.5.3.0\\lib\\net45\\System.Web.Http.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\packages\\Microsoft.AspNet.WebApi.Owin.5.3.0\\lib\\net45\\System.Web.Http.Owin.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6.2\\System.Xml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6.2\\System.Xml.Linq.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}], "Analyzers": [], "Outputs": [{"OutputItemFullPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Tester\\bin\\Debug\\Alsi.Fuzz.Tester.exe", "OutputItemRelativePath": "Alsi.Fuzz.Tester.exe"}, {"OutputItemFullPath": "", "OutputItemRelativePath": ""}], "CopyToOutputEntries": []}