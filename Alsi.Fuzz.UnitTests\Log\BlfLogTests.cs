using Alsi.App.Devices.Core;
using Alsi.Common.Log;

namespace Alsi.Fuzz.UnitTests.Log
{
    public class BlfLogTests : UnitTestBase
    {
        private readonly string _testFilePath;

        public BlfLogTests()
        {
            // 在临时目录创建测试文件
            _testFilePath = Path.Combine(Path.GetTempPath(), $"test_blf_{Guid.NewGuid()}.blf");
        }

        public override void Dispose()
        {
            // 清理测试文件
            if (File.Exists(_testFilePath))
            {
                try
                {
                    File.Delete(_testFilePath);
                }
                catch (IOException)
                {
                    // 文件可能被锁定，等待并再次尝试
                    Thread.Sleep(100);
                    File.Delete(_testFilePath);
                }
            }
            BlfLogReader.CleanUp();

            base.Dispose();
        }

        [Fact]
        public void WriteAndRead_CanFrame_ShouldMatchOriginal()
        {
            // Arrange
            var writer = new BlfLogWriter();
            writer.Initialize(_testFilePath);

            // 创建测试 CAN 帧
            var frame = new CanFrame
            {
                Channel = 1,
                Data = new byte[] { 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08 },
                Dlc = 8,
                Id = 0x123,
                TimeUS = 1000000, // 1秒
                IsTx = true,
                IsCanFd = false
            };

            // Act
            BlfLogWriter.WriteLog(frame);
            writer.Release();

            // 确保文件已经关闭
            Thread.Sleep(100);

            var readFrames = BlfLogReader.ReadBlfFile(_testFilePath);

            // Assert
            Assert.NotEmpty(readFrames);
            var readFrame = readFrames[0];

            Assert.Equal(frame.Channel, readFrame.Channel);
            Assert.Equal(frame.Id, readFrame.Id);
            Assert.Equal(frame.Dlc, readFrame.Dlc);
            Assert.Equal(frame.IsTx, readFrame.IsTx);
            Assert.Equal(frame.TimeUS, readFrame.TimeUS);
            Assert.Equal(frame.Data.Length, readFrame.Data.Take(frame.Data.Length).Count());
            for (int i = 0; i < frame.Data.Length; i++)
            {
                Assert.Equal(frame.Data[i], readFrame.Data[i]);
            }
        }

        [Fact]
        public void WriteAndRead_CanFdFrame_ShouldMatchOriginal()
        {
            // Arrange
            var writer = new BlfLogWriter();
            writer.Initialize(_testFilePath);

            // 创建测试 CANFD 帧
            var frame = new CanFrame
            {
                Channel = 2,
                Data = new byte[] { 0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08, 0x09, 0x0A, 0x0B, 0x0C },
                Dlc = 12,
                Id = 0x456,
                TimeUS = 2000000, // 2秒
                IsTx = false,
                IsCanFd = true,
                IsBrs = true,
                IsEsi = false
            };

            // Act
            BlfLogWriter.WriteLog(frame);
            writer.Release();

            // 确保文件已经关闭
            Thread.Sleep(100);

            var readFrames = BlfLogReader.ReadBlfFile(_testFilePath);

            // Assert
            Assert.NotEmpty(readFrames);
            var readFrame = readFrames[0];

            Assert.Equal(frame.Channel, readFrame.Channel);
            Assert.Equal(frame.Id, readFrame.Id);
            Assert.Equal(frame.Dlc, readFrame.Dlc);
            Assert.Equal(frame.IsTx, readFrame.IsTx);
            Assert.Equal(frame.TimeUS, readFrame.TimeUS);
            Assert.Equal(frame.IsBrs, readFrame.IsBrs);
            Assert.Equal(frame.IsEsi, readFrame.IsEsi);
            Assert.Equal(frame.Data.Length, readFrame.Data.Take(frame.Data.Length).Count());
            for (int i = 0; i < frame.Data.Length; i++)
            {
                Assert.Equal(frame.Data[i], readFrame.Data[i]);
            }
        }

        [Fact]
        public void WriteAndRead_ErrorFrame_ShouldBeDetected()
        {
            // Arrange
            var writer = new BlfLogWriter();
            writer.Initialize(_testFilePath);

            // 创建错误帧
            var frame = new CanFrame
            {
                Channel = 1,
                Data = new byte[] { 0xFF },
                Dlc = 1,
                Id = CanFrame.ErrorFrameId,
                TimeUS = 3000000, // 3秒
                IsTx = false,
                IsCanFd = false
            };

            // Act
            BlfLogWriter.WriteLog(frame);
            writer.Release();

            // 确保文件已经关闭
            Thread.Sleep(100);

            var readFrames = BlfLogReader.ReadBlfFile(_testFilePath);

            // Assert
            Assert.NotEmpty(readFrames);
            var readFrame = readFrames[0];

            Assert.Equal(frame.Channel, readFrame.Channel);
            Assert.Equal(CanFrame.ErrorFrameId, readFrame.Id);
            Assert.Equal(frame.TimeUS, readFrame.TimeUS);
        }

        [Fact]
        public void WriteAndRead_MultipleFrames_ShouldReadAllFrames()
        {
            // Arrange
            var writer = new BlfLogWriter();
            writer.Initialize(_testFilePath);

            // 创建多个帧
            var frames = new[]
            {
                new CanFrame
                {
                    Channel = 1,
                    Data = new byte[] { 0x01, 0x02 },
                    Dlc = 2,
                    Id = 0x100,
                    TimeUS = 1000000,
                    IsTx = true,
                    IsCanFd = false
                },
                new CanFrame
                {
                    Channel = 2,
                    Data = new byte[] { 0x03, 0x04, 0x05, 0x06 },
                    Dlc = 4,
                    Id = 0x200,
                    TimeUS = 2000000,
                    IsTx = false,
                    IsCanFd = false
                },
                new CanFrame
                {
                    Channel = 3,
                    Data = new byte[] { 0x07, 0x08, 0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E },
                    Dlc = 8,
                    Id = 0x300,
                    TimeUS = 3000000,
                    IsTx = true,
                    IsCanFd = true,
                    IsBrs = true,
                    IsEsi = true
                }
            };

            // Act
            writer.Log(frames);
            writer.Release();

            // 确保文件已经关闭
            Thread.Sleep(100);

            var readFrames = BlfLogReader.ReadBlfFile(_testFilePath);

            // Assert
            Assert.Equal(frames.Length, readFrames.Length);

            // 验证每个帧的基本属性
            for (int i = 0; i < frames.Length; i++)
            {
                Assert.Equal(frames[i].Channel, readFrames[i].Channel);
                Assert.Equal(frames[i].Id, readFrames[i].Id);
                Assert.Equal(frames[i].TimeUS, readFrames[i].TimeUS);
            }
        }
    }
}
