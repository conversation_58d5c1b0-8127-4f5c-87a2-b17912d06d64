{"version": 3, "file": "js/828.c5b13821.js", "mappings": "iLAOA,GAA4BA,EAAAA,EAAAA,IAAiB,CAC3CC,OAAQ,eACRC,MAAO,CACLC,MAAO,CAAC,GAEVC,KAAAA,CAAMC,GCFR,MAAMH,EAAQG,EAIRC,GAAUC,EAAAA,EAAAA,KAAoD,KAClE,OAAQL,EAAMC,OACZ,KAAKK,EAAAA,GAAeC,QAClB,MAAO,UACT,KAAKD,EAAAA,GAAeE,QAClB,MAAO,UACT,KAAKF,EAAAA,GAAeG,QAClB,MAAO,SACT,KAAKH,EAAAA,GAAeI,QACpB,QACE,MAAO,O,IAIPC,EAAoBV,IACxB,OAAQA,GACN,KAAKK,EAAAA,GAAeE,QAClB,MAAO,UACT,KAAKF,EAAAA,GAAeI,QAClB,MAAO,UACT,KAAKJ,EAAAA,GAAeC,QAClB,MAAO,SACT,KAAKD,EAAAA,GAAeG,QAClB,MAAO,SACT,QACE,MAAO,U,EAIPG,GAAYP,EAAAA,EAAAA,KAAS,IAClBM,EAAiBX,EAAMC,SDKhC,MAAO,CAACY,EAAUC,KAChB,MAAMC,GAAoBC,EAAAA,EAAAA,IAAkB,UAE5C,OAAQC,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAaH,EAAmB,CACpDI,KAAMf,EAAQgB,MACdC,KAAM,QACNC,MAAO,CAAC,YAAY,SACnB,CACDC,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBC,EAAAA,EAAAA,KAAiBC,EAAAA,EAAAA,IAAiBd,EAAUQ,OAAQ,MAEtDO,EAAG,GACF,EAAG,CAAC,QAAQ,CAEjB,IE5DA,MAAMC,EAAc,EAEpB,O,4GCEO,MAAMC,EAAuB5B,IAClC,OAAQA,GACN,KAAKK,EAAAA,GAAeC,QAClB,MAAO,UACT,KAAKD,EAAAA,GAAeE,QAClB,MAAO,UACT,KAAKF,EAAAA,GAAeG,QAClB,MAAO,SACT,KAAKH,EAAAA,GAAeI,QACpB,QACE,MAAO,O,gBCdb,MAAMoB,EAAa,CACjBC,IAAK,EACLC,MAAO,WAEHC,EAAa,CACjBF,IAAK,EACLC,MAAO,uBAEHE,EAAa,CAAEF,MAAO,cACtBG,EAAa,CAAEH,MAAO,aACtBI,EAAa,CAAEJ,MAAO,aACtBK,EAAa,CAAEL,MAAO,SACtBM,EAAa,CAAEN,MAAO,aACtBO,EAAa,CAAEP,MAAO,SACtBQ,EAAa,CAAER,MAAO,aACtBS,EAAc,CAAET,MAAO,SACvBU,EAAc,CAAEV,MAAO,aACvBW,EAAc,CAAEX,MAAO,yBACvBY,EAAc,CAClBb,IAAK,EACLC,MAAO,wBAEHa,EAAc,CAAC,SACfC,EAAc,CAClBf,IAAK,EACLC,MAAO,wBAEHe,EAAc,CAAC,SACfC,EAAc,CAAEhB,MAAO,iBACvBiB,EAAc,CAClBlB,IAAK,EACLC,MAAO,YAEHkB,EAAc,CAAElB,MAAO,gBACvBmB,EAAc,CAAEnB,MAAO,YACvBoB,EAAc,CAAEpB,MAAO,aACvBqB,EAAc,CAAErB,MAAO,kBACvBsB,EAAc,CAAC,SACfC,EAAc,CAAC,SACfC,EAAc,CAAExB,MAAO,cACvByB,EAAc,CAAEzB,MAAO,iBAW7B,OAA4BlC,EAAAA,EAAAA,IAAiB,CAC3CC,OAAQ,mBACRC,MAAO,CACL0D,QAAS,CAAEvC,KAAMwC,SACjBC,aAAc,CAAC,EACfC,aAAc,CAAC,GAEjBC,MAAO,CAAC,iBAAkB,SAC1B5D,KAAAA,CAAMC,GAAgB4D,KAAMC,ICyB9B,MAAMhE,EAAQG,EAMR4D,EAAOC,EAKPC,GAAgBC,EAAAA,EAAAA,IAAIlE,EAAM0D,SAC1BS,GAAWD,EAAAA,EAAAA,IAAuB,MAClCE,GAAQF,EAAAA,EAAAA,IAAgB,IACxBG,GAAUH,EAAAA,EAAAA,KAAI,IAEpBI,EAAAA,EAAAA,KAAM,IAAMtE,EAAM0D,UAAUa,IAC1BN,EAAc7C,MAAQmD,EAClBA,GAAYvE,EAAM4D,cAAgB5D,EAAM6D,cAC1CW,G,KAIJF,EAAAA,EAAAA,KAAM,IAAML,EAAc7C,QAAQmD,IAChCR,EAAK,iBAAkBQ,GAClBA,GAAUR,EAAK,QAAQ,IAG9B,MAAMS,EAAeC,UACnB,GAAKzE,EAAM4D,cAAiB5D,EAAM6D,aAAlC,CAKAQ,EAAQjD,OAAQ,EAChB,IAEE,MAAOsD,EAAcC,SAAuBC,QAAQC,IAAI,CACtDC,EAAAA,GAAOC,cAAc/E,EAAM4D,aAAc5D,EAAM6D,cAC/CiB,EAAAA,GAAOE,aAAahF,EAAM4D,aAAc5D,EAAM6D,gBAGhDM,EAAS/C,MAAQsD,EAAaO,KAC9Bb,EAAMhD,MAAQuD,EAAcM,I,CAC5B,MAAOC,GACPC,QAAQD,MAAM,4BAA6BA,GAC3CE,EAAAA,GAAUF,MAAM,8B,CAChB,QACAb,EAAQjD,OAAQ,C,OAlBhBgE,EAAAA,GAAUC,QAAQ,8B,EAsBhBC,EAAcA,KAClBrB,EAAc7C,OAAQ,EAEtB+C,EAAS/C,MAAQ,KACjBgD,EAAMhD,MAAQ,EAAE,EAIZmE,EAAkBC,IACtB,IAAKA,EAAY,MAAO,MACxB,IACE,MAAMC,EAAO,IAAIC,KAAKF,GACtB,OAAOC,EAAKE,gB,CACZ,MAAOC,GACP,OAAOJ,C,GAuBLK,EAAsBC,IAC1B,IAAKA,GAAiC,IAAjBA,EAAoB,MAAO,MAGhD,MAAMC,EAAUD,EAAe,IAE/B,MAAO,GAAGC,EAAQC,QAAQ,IAAI,EDpBhC,OCwBAC,EAAAA,EAAAA,KAAU,KACJhC,EAAc7C,OAASpB,EAAM4D,cAAgB5D,EAAM6D,cACrDW,G,ID1BG,CAAC3D,EAAUC,KAChB,MAAMoF,GAAyBlF,EAAAA,EAAAA,IAAkB,eAC3CmF,GAAsBnF,EAAAA,EAAAA,IAAkB,YACxCoF,GAA8BpF,EAAAA,EAAAA,IAAkB,oBAChDqF,GAAyBrF,EAAAA,EAAAA,IAAkB,eAC3CsF,GAAuBtF,EAAAA,EAAAA,IAAkB,aACzCuF,GAAuBvF,EAAAA,EAAAA,IAAkB,aAE/C,OAAQC,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAaqF,EAAsB,CACvDC,WAAYvC,EAAc7C,MAC1B,sBAAuBN,EAAO,KAAOA,EAAO,GAAM2F,GAAkBxC,EAAe7C,MAAQqF,GAC3FC,MAAO,GAAGvC,EAAS/C,OAAOuF,MAAQxC,EAAS/C,OAAOwF,cAAgB,KAClEC,MAAO,MACP,mBAAoB,IACnB,CACDC,QAAQtF,EAAAA,EAAAA,KAAS,IAAM,EACrBuF,EAAAA,EAAAA,IAAoB,OAAQtD,EAAa,EACvCuD,EAAAA,EAAAA,IAAaV,EAAsB,CAAEW,QAAS3B,GAAe,CAC3D/D,SAASC,EAAAA,EAAAA,KAAS,IAAMV,EAAO,KAAOA,EAAO,GAAK,EAChDW,EAAAA,EAAAA,IAAiB,aAEnBE,EAAG,SAITJ,SAASC,EAAAA,EAAAA,KAAS,IAAM,CACrB6C,EAAQjD,QACJH,EAAAA,EAAAA,OAAciG,EAAAA,EAAAA,IAAoB,MAAOpF,EAAY,EACpDkF,EAAAA,EAAAA,IAAad,EAAwB,CACnCiB,KAAM,GACNC,SAAU,UAGbnG,EAAAA,EAAAA,OAAciG,EAAAA,EAAAA,IAAoB,MAAOjF,EAAY,EACpD8E,EAAAA,EAAAA,IAAoB,MAAO7E,EAAY,CACrCpB,EAAO,KAAOA,EAAO,IAAKiG,EAAAA,EAAAA,IAAoB,KAAM,KAAM,eAAgB,KAC1EA,EAAAA,EAAAA,IAAoB,MAAO5E,EAAY,EACrC4E,EAAAA,EAAAA,IAAoB,MAAO3E,EAAY,CACrCtB,EAAO,KAAOA,EAAO,IAAKiG,EAAAA,EAAAA,IAAoB,MAAO,CAAE/E,MAAO,SAAW,cAAe,KACxF+E,EAAAA,EAAAA,IAAoB,MAAO1E,GAAYX,EAAAA,EAAAA,IAAiByC,EAAS/C,OAAOuF,MAAO,MAEjFI,EAAAA,EAAAA,IAAoB,MAAOzE,EAAY,CACrCxB,EAAO,KAAOA,EAAO,IAAKiG,EAAAA,EAAAA,IAAoB,MAAO,CAAE/E,MAAO,SAAW,kBAAmB,KAC5F+E,EAAAA,EAAAA,IAAoB,MAAOxE,GAAYb,EAAAA,EAAAA,IAAiByC,EAAS/C,OAAOwF,cAAe,MAEzFG,EAAAA,EAAAA,IAAoB,MAAOvE,EAAY,CACrC1B,EAAO,KAAOA,EAAO,IAAKiG,EAAAA,EAAAA,IAAoB,MAAO,CAAE/E,MAAO,SAAW,eAAgB,KACzF+E,EAAAA,EAAAA,IAAoB,MAAOtE,GAAaf,EAAAA,EAAAA,IAAiB6D,EAAepB,EAAS/C,OAAOiG,QAAS,MAEnGN,EAAAA,EAAAA,IAAoB,MAAOrE,EAAa,CACtC5B,EAAO,KAAOA,EAAO,IAAKiG,EAAAA,EAAAA,IAAoB,MAAO,CAAE/E,MAAO,SAAW,sBAAuB,KAChG+E,EAAAA,EAAAA,IAAoB,MAAOpE,EAAa,EACtClB,EAAAA,EAAAA,KAAiBC,EAAAA,EAAAA,IAAiB6D,EAAepB,EAAS/C,OAAOkG,MAAQ,IAAK,IAC9EN,EAAAA,EAAAA,IAAaO,EAAAA,EAAc,CACzBtH,MAAOkE,EAAS/C,OAAOnB,OAAS,GAChC+B,MAAO,cACN,KAAM,EAAG,CAAC,cAGhBmC,EAAS/C,OAAOoG,YACZvG,EAAAA,EAAAA,OAAciG,EAAAA,EAAAA,IAAoB,MAAOtE,EAAa,CACrD9B,EAAO,KAAOA,EAAO,IAAKiG,EAAAA,EAAAA,IAAoB,MAAO,CAAE/E,MAAO,SAAW,cAAe,KACxF+E,EAAAA,EAAAA,IAAoB,MAAO,CACzB/E,MAAO,QACP0E,MAAOvC,EAAS/C,OAAOoG,YACtB9F,EAAAA,EAAAA,IAAiByC,EAAS/C,OAAOoG,WAAY,EAAG3E,OAErD4E,EAAAA,EAAAA,IAAoB,IAAI,GAC3BtD,EAAS/C,OAAOsG,SACZzG,EAAAA,EAAAA,OAAciG,EAAAA,EAAAA,IAAoB,MAAOpE,EAAa,CACrDhC,EAAO,KAAOA,EAAO,IAAKiG,EAAAA,EAAAA,IAAoB,MAAO,CAAE/E,MAAO,SAAW,WAAY,KACrF+E,EAAAA,EAAAA,IAAoB,MAAO,CACzB/E,MAAO,QACP0E,MAAOvC,EAAS/C,OAAOsG,SACtBhG,EAAAA,EAAAA,IAAiByC,EAAS/C,MAAMsG,QAAS,EAAG3E,OAEjD0E,EAAAA,EAAAA,IAAoB,IAAI,QAGhCV,EAAAA,EAAAA,IAAoB,MAAO/D,EAAa,CACtClC,EAAO,KAAOA,EAAO,IAAKiG,EAAAA,EAAAA,IAAoB,KAAM,KAAM,SAAU,IAC5C,IAAvB3C,EAAMhD,MAAMuG,SACR1G,EAAAA,EAAAA,OAAciG,EAAAA,EAAAA,IAAoB,MAAOjE,EAAa,EACrD+D,EAAAA,EAAAA,IAAab,EAAqB,CAAEyB,YAAa,4BAElD3G,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAamF,EAAwB,CAAEtE,IAAK,GAAK,CAC9DR,SAASC,EAAAA,EAAAA,KAAS,IAAM,GACrBP,EAAAA,EAAAA,KAAW,IAAOiG,EAAAA,EAAAA,IAAoBW,EAAAA,GAAW,MAAMC,EAAAA,EAAAA,IAAY1D,EAAMhD,OAAQ2G,KACxE9G,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAakF,EAA6B,CAC9DrE,IAAKgG,EAAKC,GACV7G,MAAM8G,EAAAA,EAAAA,IAAOpG,EAAPoG,CAA4BF,EAAK9H,OACvCiI,OAAuB,YAAfH,EAAK9H,OACZ,CACDsB,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBuF,EAAAA,EAAAA,IAAoB,MAAO7D,EAAa,EACtC6D,EAAAA,EAAAA,IAAoB,MAAO5D,EAAa,EACtC4D,EAAAA,EAAAA,IAAoB,MAAO3D,EAAa,EACtC2D,EAAAA,EAAAA,IAAoB,OAAQ1D,GAAa3B,EAAAA,EAAAA,IAAiBmE,EAAmBkC,EAAKI,YAAa,IAC/FpB,EAAAA,EAAAA,IAAoB,OAAQ,CAC1B/E,MAAO,YACP0E,MAAOqB,EAAKpB,OACXjF,EAAAA,EAAAA,IAAiBqG,EAAKpB,MAAO,EAAGrD,GAClCyE,EAAKL,SACDzG,EAAAA,EAAAA,OAAciG,EAAAA,EAAAA,IAAoB,OAAQ,CACzCnF,IAAK,EACL2E,MAAOqB,EAAKL,OACZ1F,MAAO,uBACNN,EAAAA,EAAAA,IAAiBqG,EAAKL,QAAS,EAAGnE,KACrCkE,EAAAA,EAAAA,IAAoB,IAAI,MAE9BV,EAAAA,EAAAA,IAAoB,MAAOvD,EAAa,EACtCwD,EAAAA,EAAAA,IAAaO,EAAAA,EAAc,CACzBtH,MAAO8H,EAAK9H,OACX,KAAM,EAAG,CAAC,mBAKrB0B,EAAG,GACF,KAAM,CAAC,OAAQ,cAChB,SAENA,EAAG,aAKnBA,EAAG,GACF,EAAG,CAAC,aAAc,SAAS,CAEhC,I,UE9RA,MAAMC,GAA2B,OAAgB,EAAQ,CAAC,CAAC,YAAY,qBAEvE,O;;;;;;;;ACAA,MAAMwG,EAYJC,WAAAA,CAAYC,GACVC,KAAKC,UAAYF,EAAQE,UACzBD,KAAKE,MAAQH,EAAQG,OAAS,GAC9BF,KAAKG,WAAaJ,EAAQI,YAAc,GACxCH,KAAKI,WAAaL,EAAQK,YAAc,GACxCJ,KAAKK,iBAAmBN,EAAQO,WAChCN,KAAKO,mBAAqBR,EAAQS,aAClCR,KAAKS,UAAYV,EAAQU,WAAa,OAEtCT,KAAKU,kBAAoB,EACzBV,KAAKW,gBAAkB,EACvBX,KAAKY,gBAAkB,KACvBZ,KAAKa,eAAiB,KACtBb,KAAKc,iBAAmB,KACxBd,KAAKe,YAAcf,KAAKE,MAAMd,OAASY,KAAKG,WAC5CH,KAAKgB,YAAc,EAGfhB,KAAKe,YAAcf,KAAKS,YAC1BT,KAAKgB,YAAchB,KAAKS,UAAYT,KAAKe,aAG3Cf,KAAKiB,YACT,CAMEA,UAAAA,GAiBE,GAfAjB,KAAKC,UAAUiB,UAAY,GAG3BlB,KAAKY,gBAAkBO,SAASC,cAAc,OAE9CC,OAAOC,OAAOtB,KAAKY,gBAAgB7H,MAAO,CACxCwI,KAAM,IACNC,SAAU,OACVC,SAAU,WACVC,UAAW,IACXC,OAAQ,OACRC,UAAW,eAIT5B,KAAKO,mBAAoB,CAC3B,MAAMsB,EAAS7B,KAAKO,qBAChBsB,GACF7B,KAAKY,gBAAgBkB,YAAYD,EAEzC,CAGI7B,KAAKa,eAAiBM,SAASC,cAAc,OAE7CC,OAAOC,OAAOtB,KAAKa,eAAe9H,MAAO,CACvC0I,SAAU,WACVnD,MAAO,SAIT,MAAMyD,EAAe/B,KAAKe,YAAcf,KAAKgB,YAC7ChB,KAAKa,eAAe9H,MAAM4I,OAAS,GAAGI,MAGtC/B,KAAKc,iBAAmBK,SAASC,cAAc,OAE/CC,OAAOC,OAAOtB,KAAKc,iBAAiB/H,MAAO,CACzC0I,SAAU,WACVnD,MAAO,OACP0D,KAAM,MAIRhC,KAAKY,gBAAgBqB,iBAAiB,SAAUjC,KAAKkC,aAAaC,KAAKnC,OAGvEA,KAAKa,eAAeiB,YAAY9B,KAAKc,kBACrCd,KAAKY,gBAAgBkB,YAAY9B,KAAKa,gBACtCb,KAAKC,UAAU6B,YAAY9B,KAAKY,iBAGhCZ,KAAKoC,mBAAmB,EAAGC,KAAKC,IAAI,IAAKtC,KAAKE,MAAMd,QACxD,CAME8C,YAAAA,GACE,MAAMK,EAAYvC,KAAKY,gBAAgB2B,UACjCC,EAAkBxC,KAAKY,gBAAgB6B,aAGvCC,EAAgBH,EAAYvC,KAAKgB,YAGjC2B,EAAaN,KAAKO,IAAI,EAAGP,KAAKQ,MAAMH,EAAgB1C,KAAKG,YAAcH,KAAKI,YAC5E0C,EAAWT,KAAKC,IACpBtC,KAAKE,MAAMd,OACXiD,KAAKU,MAAML,EAAgBF,EAAkBxC,KAAKgB,aAAehB,KAAKG,YAAcH,KAAKI,YAIvFuC,IAAe3C,KAAKU,mBAAqBoC,IAAa9C,KAAKW,kBAC7DX,KAAKoC,mBAAmBO,EAAYG,GACpC9C,KAAKU,kBAAoBiC,EACzB3C,KAAKW,gBAAkBmC,EAE7B,CAQEV,kBAAAA,CAAmBO,EAAYG,GAE7B9C,KAAKc,iBAAiBI,UAAY,GAGlClB,KAAKc,iBAAiB/H,MAAMiK,UAAY,cAAcL,EAAa3C,KAAKG,WAAaH,KAAKgB,iBAG1F,IAAK,IAAIiC,EAAIN,EAAYM,EAAIH,EAAUG,IAAK,CAC1C,MAAMC,EAAOlD,KAAKE,MAAM+C,GAExB,GAAIjD,KAAKK,iBAAkB,CAEzB,MAAM8C,EAAcnD,KAAKK,iBAAiB6C,EAAMD,GAC5CE,IAEFA,EAAYpK,MAAM4I,OAAY3B,KAAKG,WAAaH,KAAKgB,YAA1B,KAC3BmC,EAAYpK,MAAM6I,UAAY,aAC9BuB,EAAYpK,MAAMuF,MAAQ,OAE1B0B,KAAKc,iBAAiBgB,YAAYqB,GAE5C,KAAa,CAEL,MAAMC,EAAMjC,SAASC,cAAc,OACnCC,OAAOC,OAAO8B,EAAIrK,MAAO,CACvB4I,OAAW3B,KAAKG,WAAaH,KAAKgB,YAA1B,KACR1C,MAAO,OACPsD,UAAW,aACXyB,QAAS,MACTC,aAAc,mBAEhBF,EAAIG,YAAcC,KAAKC,UAAUP,GACjClD,KAAKc,iBAAiBgB,YAAYsB,EAC1C,CACA,CACA,CAOEM,WAAAA,CAAYxD,GACVF,KAAKE,MAAQA,GAAS,GACtBF,KAAKe,YAAcf,KAAKE,MAAMd,OAASY,KAAKG,WAG5CH,KAAKgB,YAAc,EACfhB,KAAKe,YAAcf,KAAKS,YAC1BT,KAAKgB,YAAchB,KAAKS,UAAYT,KAAKe,aAIvCf,KAAKa,iBACPb,KAAKa,eAAe9H,MAAM4I,OAAY3B,KAAKe,YAAcf,KAAKgB,YAA3B,MAGrChB,KAAKU,kBAAoB,EACzBV,KAAKW,gBAAkB,EAGvBX,KAAKkC,cACT,CAOEyB,aAAAA,CAAcC,GACRA,GAAS,GAAKA,EAAQ5D,KAAKE,MAAMd,SAEnCY,KAAKY,gBAAgB2B,UAAYqB,EAAQ5D,KAAKG,WAAaH,KAAKgB,YAEtE,CAME6C,OAAAA,GACM7D,KAAKY,iBACPZ,KAAKY,gBAAgBkD,oBAAoB,SAAU9D,KAAKkC,cAEtDlC,KAAKC,YACPD,KAAKC,UAAUiB,UAAY,IAE7BlB,KAAKE,MAAQ,KACbF,KAAKC,UAAY,KACjBD,KAAKY,gBAAkB,KACvBZ,KAAKa,eAAiB,KACtBb,KAAKc,iBAAmB,IAC5B,CAMEiD,OAAAA,GACE/D,KAAKkC,cACT,CAOE8B,kBAAAA,GACE,OAAOhE,KAAKY,eAChB;;;;;;GC3OsB,qBAAXqD,SACTA,OAAOC,UAAY,CACjBrE,kB,cCZJ,MAAMtG,EAAa,CAAEE,MAAO,kCCuBtB0K,EAAc,GACdC,EAAc,GACdC,EAAgB,GDbtB,OAA4B9M,EAAAA,EAAAA,IAAiB,CAC3CC,OAAQ,WACRC,MAAO,CACL6M,MAAO,CAAC,GAEV/I,MAAO,CAAC,eACR5D,KAAAA,CAAMC,GAAgB4D,KAAMC,ICP9B,MAAMhE,EAAQG,EAIR4D,EAAOC,EAKP8I,GAAiB5I,EAAAA,EAAAA,IAAwB,MACzC6I,GAAkB7I,EAAAA,EAAAA,IAAwB,MAChD,IAAI8I,EAAqB,KAMzB,MAAMC,EAAoBA,KACxB,IAAKF,EAAgB3L,MAAO,OAG5B2L,EAAgB3L,MAAMqI,UAAY,GAGlC,MAAMW,EAASV,SAASC,cAAc,OACtCS,EAAO8C,UAAY,aACnB9C,EAAO9I,MAAM4I,OAAS,GAAG0C,MAGzB,MAAMO,EAAWzD,SAASC,cAAc,OACxCwD,EAASrB,YAAc,KACvBqB,EAASD,UAAY,iBACrB9C,EAAOC,YAAY8C,GAGnB,MAAMC,EAAa1D,SAASC,cAAc,OAC1CyD,EAAWtB,YAAc,OACzBsB,EAAWF,UAAY,mBACvB9C,EAAOC,YAAY+C,GAGnB,MAAMC,EAAc3D,SAASC,cAAc,OAC3C0D,EAAYvB,YAAc,YAC1BuB,EAAYH,UAAY,oBACxB9C,EAAOC,YAAYgD,GAGnB,MAAMC,EAAe5D,SAASC,cAAc,OAC5C2D,EAAaxB,YAAc,SAC3BwB,EAAaJ,UAAY,qBACzB9C,EAAOC,YAAYiD,GAGnB,MAAMC,EAAe7D,SAASC,cAAc,OAC5C4D,EAAazB,YAAc,SAC3ByB,EAAaL,UAAY,qBACzB9C,EAAOC,YAAYkD,GAGnBR,EAAgB3L,MAAMiJ,YAAYD,EAAO,EAIrCoD,EAAoBA,KACnBV,EAAe1L,OAAUpB,EAAM6M,MAAMlF,SAG1CsF,IAGID,GACFA,EAAcZ,UAGhBY,EAAgB,IAAI5E,EAAc,CAChCI,UAAWsE,EAAe1L,MAC1BqH,MAAOzI,EAAM6M,MACbnE,WAAYgE,EACZ/D,WAAYgE,EACZ9D,WAAYA,CAAC4C,EAAkBU,KAE7B,MAAMsB,EAAM/D,SAASC,cAAc,OACnC8D,EAAIP,UAAY,WAChBO,EAAIC,QAAU,IAAM3J,EAAK,cAAe0H,GAGxCgC,EAAInM,MAAM4I,OAAS,GAAGwC,MACtBe,EAAInM,MAAMqM,WAAa,GAAGjB,MAC1Be,EAAInM,MAAMuK,aAAeM,IAAUnM,EAAM6M,MAAMlF,OAAS,EAAI,OAAS,oBACrE8F,EAAInM,MAAMsM,gBAAkBnC,EAAKzD,GAAK,IAAM,EAAI,OAAS,UAGzDyF,EAAII,YAAc,KAChBJ,EAAInM,MAAMsM,gBAAkB,SAAS,EAEvCH,EAAIK,WAAa,KACfL,EAAInM,MAAMsM,gBAAkBnC,EAAKzD,GAAK,IAAM,EAAI,OAAS,UAEzDyF,EAAInM,MAAMuK,aAAeM,IAAUnM,EAAM6M,MAAMlF,OAAS,EAAI,OAAS,mBAAmB,EAI1F,MAAMoG,EAASrE,SAASC,cAAc,OACtCoE,EAAOb,UAAY,mBAGnB,MAAMc,EAAQtE,SAASC,cAAc,OACrCqE,EAAMlC,YAAc,IAAIL,EAAKzD,KAC7BgG,EAAMd,UAAY,eAClBa,EAAO1D,YAAY2D,GAGnB,MAAMC,EAAUvE,SAASC,cAAc,OACvCsE,EAAQnC,YAAcL,EAAK9E,KAC3BsH,EAAQf,UAAY,iBACpBa,EAAO1D,YAAY4D,GAGnB,MAAMC,EAAWxE,SAASC,cAAc,OACxCuE,EAASpC,YAAcL,EAAKjE,UAC5B0G,EAASxH,MAAQ+E,EAAKjE,UACtB0G,EAAShB,UAAY,kBACrBa,EAAO1D,YAAY6D,GAGnB,MAAMC,EAAYzE,SAASC,cAAc,OACzCwE,EAAUrC,YAAcL,EAAK/D,QAAU,IACvCyG,EAAUzH,MAAQ+E,EAAK/D,QAAU,GACjCyG,EAAUjB,UAAY,mBACtBa,EAAO1D,YAAY8D,GAGnB,MAAMC,EAAY1E,SAASC,cAAc,OACzCyE,EAAUlB,UAAY,mBAGtB,MAAM9M,EAAUiO,EAAiB5C,EAAKxL,OAChCqO,EAAUC,EAAc9C,EAAKxL,OAE7BuO,EAAQ9E,SAASC,cAAc,QASrC,OARA6E,EAAMtB,UAAY,kBAAkB9M,kCACpCoO,EAAM1C,YAAcwC,EAEpBF,EAAU/D,YAAYmE,GACtBT,EAAO1D,YAAY+D,GAEnBX,EAAIpD,YAAY0D,GAETN,CAAG,IAEZ,EAIEgB,EAAsBA,KACtBzB,EACFA,EAAcf,YAAYjM,EAAM6M,QAEhC6B,EAAAA,EAAAA,KAAS,KACPlB,GAAmB,G,EAMnBa,EAAoBpO,IACxB,OAAQA,GACN,KAAKK,EAAAA,GAAeC,QAClB,MAAO,UACT,KAAKD,EAAAA,GAAeE,QAClB,MAAO,UACT,KAAKF,EAAAA,GAAeG,QAClB,MAAO,SACT,KAAKH,EAAAA,GAAeI,QACpB,QACE,MAAO,O,EAKP6N,EAAiBtO,IACrB,OAAQA,GACN,KAAKK,EAAAA,GAAeC,QAClB,MAAO,SACT,KAAKD,EAAAA,GAAeG,QAClB,MAAO,SACT,KAAKH,EAAAA,GAAeE,QAClB,MAAO,UACT,KAAKF,EAAAA,GAAeI,QAClB,MAAO,UACT,QACE,MAAO,U,ED6Bb,OCxBA4D,EAAAA,EAAAA,KAAM,IAAMtE,EAAM6M,QAAO,CAAC8B,EAAUC,KAE9BD,IAAaC,GAAYD,GAAUhH,SAAWiH,GAAUjH,SAC1D+G,EAAAA,EAAAA,KAAS,KACPD,GAAqB,G,GAGxB,CAAEI,MAAO,UAGZ5I,EAAAA,EAAAA,KAAU,MACRyI,EAAAA,EAAAA,KAAS,KACPlB,GAAmB,GACnB,KAIJsB,EAAAA,EAAAA,KAAY,KACN9B,IACFA,EAAcZ,UACdY,EAAgB,K,IDIb,CAACnM,EAAUC,MACRG,EAAAA,EAAAA,OAAciG,EAAAA,EAAAA,IAAoB,MAAOpF,EAAY,EAC3DiF,EAAAA,EAAAA,IAAoB,MAAO,CACzB/E,MAAO,eACP+M,QAAS,kBACT7K,IAAK6I,GACJ,KAAM,MACThG,EAAAA,EAAAA,IAAoB,MAAO,CACzBgI,QAAS,iBACT7K,IAAK4I,EACL9K,MAAO,iBACN,KAAM,OAGb,I,UElPA,MAAMJ,GAA2B,OAAgB,EAAQ,CAAC,CAAC,YAAY,qBAEvE,O", "sources": ["webpack://fuzz-web/./src/components/common/CaseStateTag.vue?c0c7", "webpack://fuzz-web/./src/components/common/CaseStateTag.vue", "webpack://fuzz-web/./src/components/common/CaseStateTag.vue?4980", "webpack://fuzz-web/./src/utils/status.ts", "webpack://fuzz-web/./src/components/test/CaseDetailDialog.vue?5717", "webpack://fuzz-web/./src/components/test/CaseDetailDialog.vue", "webpack://fuzz-web/./src/components/test/CaseDetailDialog.vue?ca65", "webpack://fuzz-web/../src/virtual-scroll.js", "webpack://fuzz-web/../src/index.js", "webpack://fuzz-web/./src/components/test/CaseList.vue?a767", "webpack://fuzz-web/./src/components/test/CaseList.vue", "webpack://fuzz-web/./src/components/test/CaseList.vue?5681"], "sourcesContent": ["import { defineComponent as _defineComponent } from 'vue'\nimport { toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, openBlock as _openBlock, createBlock as _createBlock } from \"vue\"\n\nimport { computed } from 'vue';\r\nimport { ExecutionState } from '@/api/appApi';\r\n\r\n\nexport default /*@__PURE__*/_defineComponent({\n  __name: 'CaseStateTag',\n  props: {\n    state: {}\n  },\n  setup(__props: any) {\n\r\nconst props = __props;\r\n\r\nconst tagType = computed<'success' | 'warning' | 'danger' | 'info'>(() => {\r\n  switch (props.state) {\r\n    case ExecutionState.Success:\r\n      return 'success';\r\n    case ExecutionState.Running:\r\n      return 'warning';\r\n    case ExecutionState.Failure:\r\n      return 'danger';\r\n    case ExecutionState.Pending:\r\n    default:\r\n      return 'info';\r\n  }\r\n});\r\n\r\nconst getCaseStateName = (state: string): 'Not Run' | 'Running' | 'Passed' | 'Failed' | 'Unknown' => {\r\n  switch (state) {\r\n    case ExecutionState.Running:\r\n      return 'Running';\r\n    case ExecutionState.Pending:\r\n      return 'Not Run';\r\n    case ExecutionState.Success:\r\n      return 'Passed';\r\n    case ExecutionState.Failure:\r\n      return 'Failed';\r\n    default:\r\n      return 'Unknown';\r\n  }\r\n};\r\n\r\nconst stateName = computed(() => {\r\n  return getCaseStateName(props.state);\r\n});\r\n\nreturn (_ctx: any,_cache: any) => {\n  const _component_el_tag = _resolveComponent(\"el-tag\")!\n\n  return (_openBlock(), _createBlock(_component_el_tag, {\n    type: tagType.value,\n    size: \"small\",\n    style: {\"min-width\":\"60px\"}\n  }, {\n    default: _withCtx(() => [\n      _createTextVNode(_toDisplayString(stateName.value), 1)\n    ]),\n    _: 1\n  }, 8, [\"type\"]))\n}\n}\n\n})", "<template>\r\n  <el-tag :type=\"tagType\" size=\"small\" style=\"min-width: 60px;\">\r\n    {{ stateName }}\r\n  </el-tag>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { computed, defineProps } from 'vue';\r\nimport { ExecutionState } from '@/api/appApi';\r\n\r\nconst props = defineProps<{\r\n  state: string;\r\n}>();\r\n\r\nconst tagType = computed<'success' | 'warning' | 'danger' | 'info'>(() => {\r\n  switch (props.state) {\r\n    case ExecutionState.Success:\r\n      return 'success';\r\n    case ExecutionState.Running:\r\n      return 'warning';\r\n    case ExecutionState.Failure:\r\n      return 'danger';\r\n    case ExecutionState.Pending:\r\n    default:\r\n      return 'info';\r\n  }\r\n});\r\n\r\nconst getCaseStateName = (state: string): 'Not Run' | 'Running' | 'Passed' | 'Failed' | 'Unknown' => {\r\n  switch (state) {\r\n    case ExecutionState.Running:\r\n      return 'Running';\r\n    case ExecutionState.Pending:\r\n      return 'Not Run';\r\n    case ExecutionState.Success:\r\n      return 'Passed';\r\n    case ExecutionState.Failure:\r\n      return 'Failed';\r\n    default:\r\n      return 'Unknown';\r\n  }\r\n};\r\n\r\nconst stateName = computed(() => {\r\n  return getCaseStateName(props.state);\r\n});\r\n</script>\r\n", "import script from \"./CaseStateTag.vue?vue&type=script&setup=true&lang=ts\"\nexport * from \"./CaseStateTag.vue?vue&type=script&setup=true&lang=ts\"\n\nconst __exports__ = script;\n\nexport default __exports__", "import { ExecutionState } from '@/api/appApi';\r\n\r\n/**\r\n * 获取执行状态对应的时间线项类型\r\n * @param state 执行状态\r\n * @returns 时间线项类型\r\n */\r\nexport const getTimelineItemType = (state: string): 'primary' | 'success' | 'warning' | 'danger' | 'info' => {\r\n  switch (state) {\r\n    case ExecutionState.Success:\r\n      return 'success';\r\n    case ExecutionState.Running:\r\n      return 'primary';\r\n    case ExecutionState.Failure:\r\n      return 'danger';\r\n    case ExecutionState.Pending:\r\n    default:\r\n      return 'info';\r\n  }\r\n};", "import { defineComponent as _defineComponent } from 'vue'\nimport { resolveComponent as _resolveComponent, createVNode as _createVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, renderList as _renderList, Fragment as _Fragment, unref as _unref, withCtx as _withCtx, createBlock as _createBlock } from \"vue\"\n\nconst _hoisted_1 = {\n  key: 0,\n  class: \"loading\"\n}\nconst _hoisted_2 = {\n  key: 1,\n  class: \"case-detail-content\"\n}\nconst _hoisted_3 = { class: \"basic-info\" }\nconst _hoisted_4 = { class: \"info-grid\" }\nconst _hoisted_5 = { class: \"info-item\" }\nconst _hoisted_6 = { class: \"value\" }\nconst _hoisted_7 = { class: \"info-item\" }\nconst _hoisted_8 = { class: \"value\" }\nconst _hoisted_9 = { class: \"info-item\" }\nconst _hoisted_10 = { class: \"value\" }\nconst _hoisted_11 = { class: \"info-item\" }\nconst _hoisted_12 = { class: \"value status-combined\" }\nconst _hoisted_13 = {\n  key: 0,\n  class: \"info-item full-width\"\n}\nconst _hoisted_14 = [\"title\"]\nconst _hoisted_15 = {\n  key: 1,\n  class: \"info-item full-width\"\n}\nconst _hoisted_16 = [\"title\"]\nconst _hoisted_17 = { class: \"steps-section\" }\nconst _hoisted_18 = {\n  key: 0,\n  class: \"no-steps\"\n}\nconst _hoisted_19 = { class: \"step-content\" }\nconst _hoisted_20 = { class: \"step-row\" }\nconst _hoisted_21 = { class: \"step-left\" }\nconst _hoisted_22 = { class: \"step-timestamp\" }\nconst _hoisted_23 = [\"title\"]\nconst _hoisted_24 = [\"title\"]\nconst _hoisted_25 = { class: \"step-right\" }\nconst _hoisted_26 = { class: \"dialog-footer\" }\n\nimport { ref, watch, onMounted } from 'vue';\r\nimport { ElMessage } from 'element-plus';\r\nimport { CaseResult } from '@/api/interoperationApi';\r\nimport { appApi, CaseStep } from '@/api/appApi';\r\nimport { caseApi } from '@/api/caseApi';\r\nimport { getTimelineItemType } from '@/utils/status';\r\nimport CaseStateTag from '@/components/common/CaseStateTag.vue';\r\n\r\n\nexport default /*@__PURE__*/_defineComponent({\n  __name: 'CaseDetailDialog',\n  props: {\n    visible: { type: Boolean },\n    testResultId: {},\n    caseResultId: {}\n  },\n  emits: [\"update:visible\", \"close\"],\n  setup(__props: any, { emit: __emit }) {\n\r\nconst props = __props;\r\n\r\nconst emit = __emit;\r\n\r\nconst dialogVisible = ref(props.visible);\r\nconst caseData = ref<CaseResult | null>(null);\r\nconst steps = ref<CaseStep[]>([]);\r\nconst loading = ref(false);\r\n\r\nwatch(() => props.visible, (newValue) => {\r\n  dialogVisible.value = newValue;\r\n  if (newValue && props.testResultId && props.caseResultId) {\r\n    loadCaseData();\r\n  }\r\n});\r\n\r\nwatch(() => dialogVisible.value, (newValue) => {\r\n  emit('update:visible', newValue);\r\n  if (!newValue) emit('close');\r\n});\r\n\r\nconst loadCaseData = async () => {\r\n  if (!props.testResultId || !props.caseResultId) {\r\n    ElMessage.warning('Missing required parameters');\r\n    return;\r\n  }\r\n\r\n  loading.value = true;\r\n  try {\r\n    // 并行加载用例数据和步骤数据\r\n    const [caseResponse, stepsResponse] = await Promise.all([\r\n      appApi.getCaseResult(props.testResultId, props.caseResultId),\r\n      appApi.getCaseSteps(props.testResultId, props.caseResultId)\r\n    ]);\r\n\r\n    caseData.value = caseResponse.data;\r\n    steps.value = stepsResponse.data;\r\n  } catch (error) {\r\n    console.error('Failed to load case data:', error);\r\n    ElMessage.error('Failed to load case details');\r\n  } finally {\r\n    loading.value = false;\r\n  }\r\n};\r\n\r\nconst closeDialog = () => {\r\n  dialogVisible.value = false;\r\n  // 清空数据\r\n  caseData.value = null;\r\n  steps.value = [];\r\n};\r\n\r\n// 格式化方法保持不变\r\nconst formatDateTime = (dateString?: string | null) => {\r\n  if (!dateString) return 'N/A';\r\n  try {\r\n    const date = new Date(dateString);\r\n    return date.toLocaleString();\r\n  } catch (e) {\r\n    return dateString;\r\n  }\r\n};\r\n\r\nconst formatTime = (dateString: string) => {\r\n  try {\r\n    const date = new Date(dateString);\r\n    return date.toLocaleTimeString();\r\n  } catch (e) {\r\n    return dateString;\r\n  }\r\n};\r\n\r\nconst formatTimestamp = (timestamp: number) => {\r\n  try {\r\n    const date = new Date(timestamp);\r\n    return date.toLocaleString();\r\n  } catch (e) {\r\n    return timestamp.toString();\r\n  }\r\n};\r\n\r\n// 添加新的微秒格式化方法\r\nconst formatMicroseconds = (microseconds: number): string => {\r\n  if (!microseconds && microseconds !== 0) return 'N/A';\r\n\r\n  // 转换为秒并保留6位小数\r\n  const seconds = microseconds / 1000000;\r\n  // 使用toFixed(6)确保始终有6位小数\r\n  return `${seconds.toFixed(6)}`;\r\n};\r\n\r\n// 组件挂载时，如果对话框是可见的且有必要参数，则加载数据\r\nonMounted(() => {\r\n  if (dialogVisible.value && props.testResultId && props.caseResultId) {\r\n    loadCaseData();\r\n  }\r\n});\r\n\nreturn (_ctx: any,_cache: any) => {\n  const _component_el_skeleton = _resolveComponent(\"el-skeleton\")!\n  const _component_el_empty = _resolveComponent(\"el-empty\")!\n  const _component_el_timeline_item = _resolveComponent(\"el-timeline-item\")!\n  const _component_el_timeline = _resolveComponent(\"el-timeline\")!\n  const _component_el_button = _resolveComponent(\"el-button\")!\n  const _component_el_dialog = _resolveComponent(\"el-dialog\")!\n\n  return (_openBlock(), _createBlock(_component_el_dialog, {\n    modelValue: dialogVisible.value,\n    \"onUpdate:modelValue\": _cache[0] || (_cache[0] = ($event: any) => ((dialogVisible).value = $event)),\n    title: `${caseData.value?.name || caseData.value?.sequenceName || ''}`,\n    width: \"60%\",\n    \"destroy-on-close\": \"\"\n  }, {\n    footer: _withCtx(() => [\n      _createElementVNode(\"span\", _hoisted_26, [\n        _createVNode(_component_el_button, { onClick: closeDialog }, {\n          default: _withCtx(() => _cache[9] || (_cache[9] = [\n            _createTextVNode(\"Close\")\n          ])),\n          _: 1\n        })\n      ])\n    ]),\n    default: _withCtx(() => [\n      (loading.value)\n        ? (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [\n            _createVNode(_component_el_skeleton, {\n              rows: 10,\n              animated: \"\"\n            })\n          ]))\n        : (_openBlock(), _createElementBlock(\"div\", _hoisted_2, [\n            _createElementVNode(\"div\", _hoisted_3, [\n              _cache[7] || (_cache[7] = _createElementVNode(\"h4\", null, \"Information\", -1)),\n              _createElementVNode(\"div\", _hoisted_4, [\n                _createElementVNode(\"div\", _hoisted_5, [\n                  _cache[1] || (_cache[1] = _createElementVNode(\"div\", { class: \"label\" }, \"Case Name:\", -1)),\n                  _createElementVNode(\"div\", _hoisted_6, _toDisplayString(caseData.value?.name), 1)\n                ]),\n                _createElementVNode(\"div\", _hoisted_7, [\n                  _cache[2] || (_cache[2] = _createElementVNode(\"div\", { class: \"label\" }, \"Sequence Name:\", -1)),\n                  _createElementVNode(\"div\", _hoisted_8, _toDisplayString(caseData.value?.sequenceName), 1)\n                ]),\n                _createElementVNode(\"div\", _hoisted_9, [\n                  _cache[3] || (_cache[3] = _createElementVNode(\"div\", { class: \"label\" }, \"Start Time:\", -1)),\n                  _createElementVNode(\"div\", _hoisted_10, _toDisplayString(formatDateTime(caseData.value?.begin)), 1)\n                ]),\n                _createElementVNode(\"div\", _hoisted_11, [\n                  _cache[4] || (_cache[4] = _createElementVNode(\"div\", { class: \"label\" }, \"End Time / Status:\", -1)),\n                  _createElementVNode(\"div\", _hoisted_12, [\n                    _createTextVNode(_toDisplayString(formatDateTime(caseData.value?.end)) + \" \", 1),\n                    _createVNode(CaseStateTag, {\n                      state: caseData.value?.state || '',\n                      class: \"status-tag\"\n                    }, null, 8, [\"state\"])\n                  ])\n                ]),\n                (caseData.value?.parameter)\n                  ? (_openBlock(), _createElementBlock(\"div\", _hoisted_13, [\n                      _cache[5] || (_cache[5] = _createElementVNode(\"div\", { class: \"label\" }, \"Parameter:\", -1)),\n                      _createElementVNode(\"div\", {\n                        class: \"value\",\n                        title: caseData.value?.parameter\n                      }, _toDisplayString(caseData.value?.parameter), 9, _hoisted_14)\n                    ]))\n                  : _createCommentVNode(\"\", true),\n                (caseData.value?.detail)\n                  ? (_openBlock(), _createElementBlock(\"div\", _hoisted_15, [\n                      _cache[6] || (_cache[6] = _createElementVNode(\"div\", { class: \"label\" }, \"Detail:\", -1)),\n                      _createElementVNode(\"div\", {\n                        class: \"value\",\n                        title: caseData.value?.detail\n                      }, _toDisplayString(caseData.value.detail), 9, _hoisted_16)\n                    ]))\n                  : _createCommentVNode(\"\", true)\n              ])\n            ]),\n            _createElementVNode(\"div\", _hoisted_17, [\n              _cache[8] || (_cache[8] = _createElementVNode(\"h4\", null, \"Steps\", -1)),\n              (steps.value.length === 0)\n                ? (_openBlock(), _createElementBlock(\"div\", _hoisted_18, [\n                    _createVNode(_component_el_empty, { description: \"No steps available\" })\n                  ]))\n                : (_openBlock(), _createBlock(_component_el_timeline, { key: 1 }, {\n                    default: _withCtx(() => [\n                      (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(steps.value, (step) => {\n                        return (_openBlock(), _createBlock(_component_el_timeline_item, {\n                          key: step.id,\n                          type: _unref(getTimelineItemType)(step.state),\n                          hollow: step.state !== 'Success'\n                        }, {\n                          default: _withCtx(() => [\n                            _createElementVNode(\"div\", _hoisted_19, [\n                              _createElementVNode(\"div\", _hoisted_20, [\n                                _createElementVNode(\"div\", _hoisted_21, [\n                                  _createElementVNode(\"span\", _hoisted_22, _toDisplayString(formatMicroseconds(step.timestamp)), 1),\n                                  _createElementVNode(\"span\", {\n                                    class: \"step-name\",\n                                    title: step.name\n                                  }, _toDisplayString(step.name), 9, _hoisted_23),\n                                  (step.detail)\n                                    ? (_openBlock(), _createElementBlock(\"span\", {\n                                        key: 0,\n                                        title: step.detail,\n                                        class: \"step-detail-inline\"\n                                      }, _toDisplayString(step.detail), 9, _hoisted_24))\n                                    : _createCommentVNode(\"\", true)\n                                ]),\n                                _createElementVNode(\"div\", _hoisted_25, [\n                                  _createVNode(CaseStateTag, {\n                                    state: step.state\n                                  }, null, 8, [\"state\"])\n                                ])\n                              ])\n                            ])\n                          ]),\n                          _: 2\n                        }, 1032, [\"type\", \"hollow\"]))\n                      }), 128))\n                    ]),\n                    _: 1\n                  }))\n            ])\n          ]))\n    ]),\n    _: 1\n  }, 8, [\"modelValue\", \"title\"]))\n}\n}\n\n})", "<template>\r\n  <el-dialog v-model=\"dialogVisible\" :title=\"`${caseData?.name || caseData?.sequenceName || ''}`\" width=\"60%\"\r\n    destroy-on-close>\r\n    <div v-if=\"loading\" class=\"loading\">\r\n      <el-skeleton :rows=\"10\" animated />\r\n    </div>\r\n\r\n    <div v-else class=\"case-detail-content\">\r\n      <!-- 基本信息区域 -->\r\n      <div class=\"basic-info\">\r\n        <h4>Information</h4>\r\n        <div class=\"info-grid\">\r\n          <div class=\"info-item\">\r\n            <div class=\"label\">Case Name:</div>\r\n            <div class=\"value\">{{ caseData?.name }}</div>\r\n          </div>\r\n          <div class=\"info-item\">\r\n            <div class=\"label\">Sequence Name:</div>\r\n            <div class=\"value\">{{ caseData?.sequenceName }}</div>\r\n          </div>\r\n          <div class=\"info-item\">\r\n            <div class=\"label\">Start Time:</div>\r\n            <div class=\"value\">{{ formatDateTime(caseData?.begin) }}</div>\r\n          </div>\r\n          <div class=\"info-item\">\r\n            <div class=\"label\">End Time / Status:</div>\r\n            <div class=\"value status-combined\">\r\n              {{ formatDateTime(caseData?.end) }}\r\n              <CaseStateTag :state=\"caseData?.state || ''\" class=\"status-tag\" />\r\n            </div>\r\n          </div>\r\n          <div v-if=\"caseData?.parameter\" class=\"info-item full-width\">\r\n            <div class=\"label\">Parameter:</div>\r\n            <div class=\"value\" :title=\"caseData?.parameter\">{{ caseData?.parameter }}</div>\r\n          </div>\r\n          <div v-if=\"caseData?.detail\" class=\"info-item full-width\">\r\n            <div class=\"label\">Detail:</div>\r\n            <div class=\"value\" :title=\"caseData?.detail\">{{ caseData.detail }}</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 步骤列表区域 -->\r\n      <div class=\"steps-section\">\r\n        <h4>Steps</h4>\r\n\r\n        <div v-if=\"steps.length === 0\" class=\"no-steps\">\r\n          <el-empty description=\"No steps available\" />\r\n        </div>\r\n\r\n        <el-timeline v-else>\r\n          <el-timeline-item v-for=\"step in steps\" :key=\"step.id\" :type=\"getTimelineItemType(step.state)\"\r\n            :hollow=\"step.state !== 'Success'\">\r\n            <div class=\"step-content\">\r\n              <div class=\"step-row\">\r\n                <div class=\"step-left\">\r\n                  <span class=\"step-timestamp\">{{ formatMicroseconds(step.timestamp) }}</span>\r\n                  <span class=\"step-name\" :title=\"step.name\" >{{ step.name }}</span>\r\n                  <span v-if=\"step.detail\" :title=\"step.detail\" class=\"step-detail-inline\">{{ step.detail }}</span>\r\n                </div>\r\n                <div class=\"step-right\">\r\n                  <CaseStateTag :state=\"step.state\" />\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </el-timeline-item>\r\n        </el-timeline>\r\n      </div>\r\n    </div>\r\n\r\n    <template #footer>\r\n      <span class=\"dialog-footer\">\r\n        <el-button @click=\"closeDialog\">Close</el-button>\r\n      </span>\r\n    </template>\r\n  </el-dialog>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { ref, watch, defineProps, defineEmits, onMounted } from 'vue';\r\nimport { ElMessage } from 'element-plus';\r\nimport { CaseResult } from '@/api/interoperationApi';\r\nimport { appApi, CaseStep } from '@/api/appApi';\r\nimport { caseApi } from '@/api/caseApi';\r\nimport { getTimelineItemType } from '@/utils/status';\r\nimport CaseStateTag from '@/components/common/CaseStateTag.vue';\r\n\r\nconst props = defineProps<{\r\n  visible: boolean;\r\n  testResultId?: string | null;\r\n  caseResultId?: number | null;\r\n}>();\r\n\r\nconst emit = defineEmits<{\r\n  (e: 'update:visible', value: boolean): void;\r\n  (e: 'close'): void;\r\n}>();\r\n\r\nconst dialogVisible = ref(props.visible);\r\nconst caseData = ref<CaseResult | null>(null);\r\nconst steps = ref<CaseStep[]>([]);\r\nconst loading = ref(false);\r\n\r\nwatch(() => props.visible, (newValue) => {\r\n  dialogVisible.value = newValue;\r\n  if (newValue && props.testResultId && props.caseResultId) {\r\n    loadCaseData();\r\n  }\r\n});\r\n\r\nwatch(() => dialogVisible.value, (newValue) => {\r\n  emit('update:visible', newValue);\r\n  if (!newValue) emit('close');\r\n});\r\n\r\nconst loadCaseData = async () => {\r\n  if (!props.testResultId || !props.caseResultId) {\r\n    ElMessage.warning('Missing required parameters');\r\n    return;\r\n  }\r\n\r\n  loading.value = true;\r\n  try {\r\n    // 并行加载用例数据和步骤数据\r\n    const [caseResponse, stepsResponse] = await Promise.all([\r\n      appApi.getCaseResult(props.testResultId, props.caseResultId),\r\n      appApi.getCaseSteps(props.testResultId, props.caseResultId)\r\n    ]);\r\n\r\n    caseData.value = caseResponse.data;\r\n    steps.value = stepsResponse.data;\r\n  } catch (error) {\r\n    console.error('Failed to load case data:', error);\r\n    ElMessage.error('Failed to load case details');\r\n  } finally {\r\n    loading.value = false;\r\n  }\r\n};\r\n\r\nconst closeDialog = () => {\r\n  dialogVisible.value = false;\r\n  // 清空数据\r\n  caseData.value = null;\r\n  steps.value = [];\r\n};\r\n\r\n// 格式化方法保持不变\r\nconst formatDateTime = (dateString?: string | null) => {\r\n  if (!dateString) return 'N/A';\r\n  try {\r\n    const date = new Date(dateString);\r\n    return date.toLocaleString();\r\n  } catch (e) {\r\n    return dateString;\r\n  }\r\n};\r\n\r\nconst formatTime = (dateString: string) => {\r\n  try {\r\n    const date = new Date(dateString);\r\n    return date.toLocaleTimeString();\r\n  } catch (e) {\r\n    return dateString;\r\n  }\r\n};\r\n\r\nconst formatTimestamp = (timestamp: number) => {\r\n  try {\r\n    const date = new Date(timestamp);\r\n    return date.toLocaleString();\r\n  } catch (e) {\r\n    return timestamp.toString();\r\n  }\r\n};\r\n\r\n// 添加新的微秒格式化方法\r\nconst formatMicroseconds = (microseconds: number): string => {\r\n  if (!microseconds && microseconds !== 0) return 'N/A';\r\n\r\n  // 转换为秒并保留6位小数\r\n  const seconds = microseconds / 1000000;\r\n  // 使用toFixed(6)确保始终有6位小数\r\n  return `${seconds.toFixed(6)}`;\r\n};\r\n\r\n// 组件挂载时，如果对话框是可见的且有必要参数，则加载数据\r\nonMounted(() => {\r\n  if (dialogVisible.value && props.testResultId && props.caseResultId) {\r\n    loadCaseData();\r\n  }\r\n});\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.loading {\r\n  min-height: 200px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.case-detail-content {\r\n  padding: 0 20px;\r\n}\r\n\r\n.basic-info {\r\n  margin-bottom: 20px;\r\n\r\n  h4 {\r\n    margin-top: 0;\r\n    margin-bottom: 16px;\r\n    font-size: 16px;\r\n    color: #303133;\r\n    border-bottom: 1px solid #ebeef5;\r\n    padding-bottom: 8px;\r\n  }\r\n\r\n  .info-grid {\r\n    display: grid;\r\n    grid-template-columns: 1fr 1fr;\r\n    gap: 12px 24px;\r\n\r\n    .info-item {\r\n      .label {\r\n        font-size: 13px;\r\n        color: #909399;\r\n        margin-bottom: 4px;\r\n      }\r\n\r\n      .value {\r\n        font-size: 14px;\r\n        color: #303133;\r\n        word-break: break-word;\r\n\r\n        &.status-combined {\r\n          display: flex;\r\n          align-items: center;\r\n          \r\n          .status-tag {\r\n            margin-left: 12px;\r\n          }\r\n        }\r\n      }\r\n\r\n      &.full-width {\r\n        grid-column: span 2;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.steps-section {\r\n  h4 {\r\n    margin-top: 20px;\r\n    margin-bottom: 16px;\r\n    font-size: 16px;\r\n    color: #303133;\r\n    border-bottom: 1px solid #ebeef5;\r\n    padding-bottom: 8px;\r\n  }\r\n\r\n  .no-steps {\r\n    padding: 20px 0;\r\n  }\r\n\r\n  .step-content {\r\n    font-size: 13px;\r\n\r\n    .step-row {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      width: 100%;\r\n      padding: 8px 4px;\r\n      transition: background-color 0.2s;\r\n    }\r\n\r\n    .step-left {\r\n      display: flex;\r\n      align-items: center;\r\n      flex: 1;\r\n      min-width: 0; // 防止flex子元素溢出\r\n    }\r\n\r\n    .step-right {\r\n      margin-left: 10px;\r\n      flex-shrink: 0;\r\n    }\r\n\r\n    .step-timestamp {\r\n      min-width: 80px;\r\n      display: inline-block;\r\n      color: #606266;\r\n      margin-right: 12px;\r\n      flex-shrink: 0;\r\n    }\r\n\r\n    .step-name {\r\n      min-width: 80px;\r\n      font-weight: 500;\r\n      display: inline-block;\r\n      color: #303133;\r\n      margin-right: 8px;\r\n      overflow: hidden;\r\n      text-overflow: ellipsis;\r\n      white-space: nowrap;\r\n      max-width: 200px; /* 限制最大宽度，可根据实际情况调整 */\r\n    }\r\n\r\n    .step-detail-inline {\r\n      color: #606266;\r\n      overflow: hidden;\r\n      text-overflow: ellipsis;\r\n      white-space: nowrap;\r\n    }\r\n  }\r\n\r\n  .el-timeline-item:nth-child(odd) .step-row {\r\n    background-color: #f5f5fa;\r\n  }\r\n\r\n  .el-timeline-item:nth-child(even) .step-row {\r\n    background-color: #f5f5fa;\r\n  }\r\n\r\n  :deep(.el-timeline-item__node) {\r\n    margin-top: 10px;\r\n  }\r\n\r\n  :deep(.el-timeline-item__tail) {\r\n    margin-top: 10px;\r\n  }\r\n}\r\n</style>\r\n", "import script from \"./CaseDetailDialog.vue?vue&type=script&setup=true&lang=ts\"\nexport * from \"./CaseDetailDialog.vue?vue&type=script&setup=true&lang=ts\"\n\nimport \"./CaseDetailDialog.vue?vue&type=style&index=0&id=a931aa76&scoped=true&lang=scss\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-a931aa76\"]])\n\nexport default __exports__", "/**\r\n * js-booster - High-performance frontend library\r\n * VirtualScroll - Virtual scrolling implementation\r\n * @version 1.1.1\r\n * <AUTHOR>\r\n * @license MIT\r\n */\r\n\r\nclass VirtualScroll {\r\n  /**\r\n   * Create a virtual scroll instance\r\n   * @param {Object} options Configuration options\r\n   * @param {HTMLElement} options.container Scroll container element\r\n   * @param {Array} options.items Data items to display\r\n   * @param {number} [options.itemHeight=20] Height of each list item (pixels)\r\n   * @param {number} [options.bufferSize=10] Number of buffer items outside the visible area\r\n   * @param {Function} [options.renderItem] Custom item rendering function\r\n   * @param {Function} [options.renderHeader] Custom header rendering function\r\n   * @param {number} [options.maxHeight=26840000] Maximum height in pixels for the content wrapper\r\n   */\r\n  constructor(options) {\r\n    this.container = options.container;\r\n    this.items = options.items || [];\r\n    this.itemHeight = options.itemHeight || 20;\r\n    this.bufferSize = options.bufferSize || 10;\r\n    this.customRenderItem = options.renderItem;\r\n    this.customRenderHeader = options.renderHeader;\r\n    this.maxHeight = options.maxHeight || 26840000; // Add maximum height limit to prevent DOM height overflow\r\n\r\n    this.visibleStartIndex = 0;\r\n    this.visibleEndIndex = 0;\r\n    this.scrollContainer = null;\r\n    this.contentWrapper = null;\r\n    this.contentContainer = null;\r\n    this.totalHeight = this.items.length * this.itemHeight;\r\n    this.heightScale = 1; // Height scaling factor\r\n\r\n    // If total height exceeds maximum height, calculate scaling factor\r\n    if (this.totalHeight > this.maxHeight) {\r\n      this.heightScale = this.maxHeight / this.totalHeight;\r\n    }\r\n\r\n    this.initialize();\r\n  }\r\n\r\n  /**\r\n   * Initialize virtual scroll component\r\n   * @private\r\n   */\r\n  initialize() {\r\n    // Clear container\r\n    this.container.innerHTML = '';\r\n\r\n    // Create scroll container\r\n    this.scrollContainer = document.createElement('div');\r\n    // Add inline styles\r\n    Object.assign(this.scrollContainer.style, {\r\n      flex: '1',\r\n      overflow: 'auto',\r\n      position: 'relative',\r\n      minHeight: '0',\r\n      height: '100%',\r\n      boxSizing: 'border-box'\r\n    });\r\n\r\n    // If there's a custom header render function, render the header\r\n    if (this.customRenderHeader) {\r\n      const header = this.customRenderHeader();\r\n      if (header) {\r\n        this.scrollContainer.appendChild(header);\r\n      }\r\n    }\r\n\r\n    // Create content wrapper\r\n    this.contentWrapper = document.createElement('div');\r\n    // Add inline styles\r\n    Object.assign(this.contentWrapper.style, {\r\n      position: 'relative',\r\n      width: '100%'\r\n    });\r\n\r\n    // Use scaled height to ensure it doesn't exceed browser limits\r\n    const scaledHeight = this.totalHeight * this.heightScale;\r\n    this.contentWrapper.style.height = `${scaledHeight}px`;\r\n\r\n    // Create content container\r\n    this.contentContainer = document.createElement('div');\r\n    // Add inline styles\r\n    Object.assign(this.contentContainer.style, {\r\n      position: 'absolute',\r\n      width: '100%',\r\n      left: '0'\r\n    });\r\n\r\n    // Add scroll event listener\r\n    this.scrollContainer.addEventListener('scroll', this.handleScroll.bind(this));\r\n\r\n    // Assemble DOM\r\n    this.contentWrapper.appendChild(this.contentContainer);\r\n    this.scrollContainer.appendChild(this.contentWrapper);\r\n    this.container.appendChild(this.scrollContainer);\r\n\r\n    // Render initial visible items\r\n    this.renderVisibleItems(0, Math.min(100, this.items.length));\r\n  }\r\n\r\n  /**\r\n   * Handle scroll event\r\n   * @private\r\n   */\r\n  handleScroll() {\r\n    const scrollTop = this.scrollContainer.scrollTop;\r\n    const containerHeight = this.scrollContainer.clientHeight;\r\n\r\n    // Consider scaling factor in calculations\r\n    const realScrollTop = scrollTop / this.heightScale;\r\n\r\n    // Calculate visible range\r\n    const startIndex = Math.max(0, Math.floor(realScrollTop / this.itemHeight) - this.bufferSize);\r\n    const endIndex = Math.min(\r\n      this.items.length,\r\n      Math.ceil((realScrollTop + containerHeight / this.heightScale) / this.itemHeight) + this.bufferSize\r\n    );\r\n\r\n    // Only update when visible range changes\r\n    if (startIndex !== this.visibleStartIndex || endIndex !== this.visibleEndIndex) {\r\n      this.renderVisibleItems(startIndex, endIndex);\r\n      this.visibleStartIndex = startIndex;\r\n      this.visibleEndIndex = endIndex;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Render visible items\r\n   * @param {number} startIndex Start index\r\n   * @param {number} endIndex End index\r\n   * @private\r\n   */\r\n  renderVisibleItems(startIndex, endIndex) {\r\n    // Clear content container\r\n    this.contentContainer.innerHTML = '';\r\n\r\n    // Set position considering scaling factor\r\n    this.contentContainer.style.transform = `translateY(${startIndex * this.itemHeight * this.heightScale}px)`;\r\n\r\n    // Render visible items\r\n    for (let i = startIndex; i < endIndex; i++) {\r\n      const item = this.items[i];\r\n\r\n      if (this.customRenderItem) {\r\n        // Use custom render function\r\n        const itemElement = this.customRenderItem(item, i);\r\n        if (itemElement) {\r\n          // Only set necessary height styles, other styles are determined by the caller\r\n          itemElement.style.height = `${this.itemHeight * this.heightScale}px`;\r\n          itemElement.style.boxSizing = 'border-box';\r\n          itemElement.style.width = '100%';\r\n\r\n          this.contentContainer.appendChild(itemElement);\r\n        }\r\n      } else {\r\n        // Use default rendering - very simple default implementation\r\n        const row = document.createElement('div');\r\n        Object.assign(row.style, {\r\n          height: `${this.itemHeight * this.heightScale}px`,\r\n          width: '100%',\r\n          boxSizing: 'border-box',\r\n          padding: '8px',\r\n          borderBottom: '1px solid #eee'\r\n        });\r\n        row.textContent = JSON.stringify(item);\r\n        this.contentContainer.appendChild(row);\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Update data items and re-render\r\n   * @param {Array} items New data items array\r\n   * @public\r\n   */\r\n  updateItems(items) {\r\n    this.items = items || [];\r\n    this.totalHeight = this.items.length * this.itemHeight;\r\n\r\n    // Recalculate scaling factor\r\n    this.heightScale = 1;\r\n    if (this.totalHeight > this.maxHeight) {\r\n      this.heightScale = this.maxHeight / this.totalHeight;\r\n    }\r\n\r\n    // Ensure height is set correctly\r\n    if (this.contentWrapper) {\r\n      this.contentWrapper.style.height = `${this.totalHeight * this.heightScale}px`;\r\n    }\r\n\r\n    this.visibleStartIndex = 0;\r\n    this.visibleEndIndex = 0;\r\n\r\n    // Force recalculation of visible items\r\n    this.handleScroll();\r\n  }\r\n\r\n  /**\r\n   * Scroll to specified index\r\n   * @param {number} index Index of the item to scroll to\r\n   * @public\r\n   */\r\n  scrollToIndex(index) {\r\n    if (index >= 0 && index < this.items.length) {\r\n      // Apply scaling factor when scrolling\r\n      this.scrollContainer.scrollTop = index * this.itemHeight * this.heightScale;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Destroy component, remove event listeners, etc.\r\n   * @public\r\n   */\r\n  destroy() {\r\n    if (this.scrollContainer) {\r\n      this.scrollContainer.removeEventListener('scroll', this.handleScroll);\r\n    }\r\n    if (this.container) {\r\n      this.container.innerHTML = '';\r\n    }\r\n    this.items = null;\r\n    this.container = null;\r\n    this.scrollContainer = null;\r\n    this.contentWrapper = null;\r\n    this.contentContainer = null;\r\n  }\r\n\r\n  /**\r\n   * Refresh virtual scroll, re-render current visible items\r\n   * @public\r\n   */\r\n  refresh() {\r\n    this.handleScroll();\r\n  }\r\n\r\n  /**\r\n   * Get scroll container element\r\n   * @returns {HTMLElement} Scroll container element\r\n   * @public\r\n   */\r\n  getScrollContainer() {\r\n    return this.scrollContainer;\r\n  }\r\n}\r\n\r\n// Export VirtualScroll class\r\nexport { VirtualScroll };\r\n", "/**\r\n * js-booster - High-performance frontend library\r\n * @version 1.1.1\r\n * <AUTHOR>\r\n * @license MIT\r\n */\r\n\r\nimport { VirtualScroll } from './virtual-scroll';\r\n\r\n// Export all components\r\nexport { VirtualScroll };\r\n\r\n// If in browser environment, add to global object\r\nif (typeof window !== 'undefined') {\r\n  window.JsBooster = {\r\n    VirtualScroll\r\n  };\r\n}\r\n", "import { defineComponent as _defineComponent } from 'vue'\nimport { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\"\n\nconst _hoisted_1 = { class: \"cases-list-container case-list\" }\n\nimport { ref, onMounted, onUnmounted, watch, nextTick } from 'vue';\nimport { VirtualScroll } from 'js-booster';\nimport { CaseResult, ExecutionState } from '@/api/interoperationApi';\n\nconst ITEM_HEIGHT = 36; // 行高\nconst BUFFER_SIZE = 20; // 缓冲区大小\nconst HEADER_HEIGHT = 40; // 表头高度\n\n// 渲染固定表头\n\nexport default /*@__PURE__*/_defineComponent({\n  __name: 'CaseList',\n  props: {\n    cases: {}\n  },\n  emits: [\"view-detail\"],\n  setup(__props: any, { emit: __emit }) {\n\nconst props = __props;\n\nconst emit = __emit;\n\n// 虚拟滚动相关\nconst casesContainer = ref<HTMLElement | null>(null);\nconst headerContainer = ref<HTMLElement | null>(null);\nlet virtualScroll: any = null;\nconst renderFixedHeader = () => {\n  if (!headerContainer.value) return;\n\n  // 清空表头容器\n  headerContainer.value.innerHTML = '';\n\n  // 创建表头\n  const header = document.createElement('div');\n  header.className = 'header-row';\n  header.style.height = `${HEADER_HEIGHT}px`; // 动态高度\n\n  // ID 列\n  const idHeader = document.createElement('div');\n  idHeader.textContent = 'ID';\n  idHeader.className = 'header-cell-id';\n  header.appendChild(idHeader);\n\n  // 名称列\n  const nameHeader = document.createElement('div');\n  nameHeader.textContent = 'Name';\n  nameHeader.className = 'header-cell-name';\n  header.appendChild(nameHeader);\n\n  // 参数列\n  const paramHeader = document.createElement('div');\n  paramHeader.textContent = 'Parameter';\n  paramHeader.className = 'header-cell-param';\n  header.appendChild(paramHeader);\n\n  // 详情列\n  const detailHeader = document.createElement('div');\n  detailHeader.textContent = 'Detail';\n  detailHeader.className = 'header-cell-detail';\n  header.appendChild(detailHeader);\n\n  // 状态列\n  const statusHeader = document.createElement('div');\n  statusHeader.textContent = 'Status';\n  statusHeader.className = 'header-cell-status';\n  header.appendChild(statusHeader);\n\n  // 添加到表头容器\n  headerContainer.value.appendChild(header);\n};\n\n// 初始化虚拟滚动\nconst initVirtualScroll = () => {\n  if (!casesContainer.value || !props.cases.length) return;\n\n  // 渲染固定表头\n  renderFixedHeader();\n\n  // 如果已经存在虚拟滚动实例，先销毁\n  if (virtualScroll) {\n    virtualScroll.destroy();\n  }\n\n  virtualScroll = new VirtualScroll({\n    container: casesContainer.value,\n    items: props.cases,\n    itemHeight: ITEM_HEIGHT,\n    bufferSize: BUFFER_SIZE,\n    renderItem: (item: CaseResult, index: number) => {\n      // 创建主容器\n      const div = document.createElement('div');\n      div.className = 'case-row';\n      div.onclick = () => emit('view-detail', item);\n\n      // 设置动态样式\n      div.style.height = `${ITEM_HEIGHT}px`;\n      div.style.lineHeight = `${ITEM_HEIGHT}px`;\n      div.style.borderBottom = index === props.cases.length - 1 ? 'none' : '1px solid #ebeef5';\n      div.style.backgroundColor = item.id % 2 === 0 ? '#fff' : '#fafafa';\n\n      // 添加悬停效果\n      div.onmouseover = () => {\n        div.style.backgroundColor = '#f5f7fa';\n      };\n      div.onmouseout = () => {\n        div.style.backgroundColor = item.id % 2 === 0 ? '#fff' : '#fafafa';\n        // 确保鼠标移出时保持最后一个项目没有底部边框\n        div.style.borderBottom = index === props.cases.length - 1 ? 'none' : '1px solid #ebeef5';\n      };\n\n      // 创建行内容\n      const rowDiv = document.createElement('div');\n      rowDiv.className = 'case-row-content';\n\n      // ID\n      const idDiv = document.createElement('div');\n      idDiv.textContent = `#${item.id}`;\n      idDiv.className = 'case-cell-id';\n      rowDiv.appendChild(idDiv);\n\n      // 名称\n      const nameDiv = document.createElement('div');\n      nameDiv.textContent = item.name;\n      nameDiv.className = 'case-cell-name';\n      rowDiv.appendChild(nameDiv);\n\n      // 参数\n      const paramDiv = document.createElement('div');\n      paramDiv.textContent = item.parameter;\n      paramDiv.title = item.parameter;\n      paramDiv.className = 'case-cell-param';\n      rowDiv.appendChild(paramDiv);\n\n      // 详情列\n      const detailDiv = document.createElement('div');\n      detailDiv.textContent = item.detail || '-';\n      detailDiv.title = item.detail || '';\n      detailDiv.className = 'case-cell-detail';\n      rowDiv.appendChild(detailDiv);\n\n      // 状态\n      const statusDiv = document.createElement('div');\n      statusDiv.className = 'case-cell-status';\n\n      // 创建状态标签\n      const tagType = getStatusTagType(item.state);\n      const tagText = getStatusText(item.state);\n\n      const tagEl = document.createElement('span');\n      tagEl.className = `el-tag el-tag--${tagType} el-tag--small case-status-tag`;\n      tagEl.textContent = tagText;\n\n      statusDiv.appendChild(tagEl);\n      rowDiv.appendChild(statusDiv);\n\n      div.appendChild(rowDiv);\n\n      return div;\n    }\n  });\n};\n\n// 更新虚拟滚动数据\nconst updateVirtualScroll = () => {\n  if (virtualScroll) {\n    virtualScroll.updateItems(props.cases);\n  } else {\n    nextTick(() => {\n      initVirtualScroll();\n    });\n  }\n};\n\n// 获取状态对应的标签类型\nconst getStatusTagType = (state: string): string => {\n  switch (state) {\n    case ExecutionState.Success:\n      return 'success';\n    case ExecutionState.Running:\n      return 'warning';\n    case ExecutionState.Failure:\n      return 'danger';\n    case ExecutionState.Pending:\n    default:\n      return 'info';\n  }\n};\n\n// 获取状态的文本描述\nconst getStatusText = (state: string): string => {\n  switch (state) {\n    case ExecutionState.Success:\n      return 'Passed';\n    case ExecutionState.Failure:\n      return 'Failed';\n    case ExecutionState.Running:\n      return 'Running';\n    case ExecutionState.Pending:\n      return 'Pending';\n    default:\n      return 'Unknown';\n  }\n};\n\n// 监听 cases 变化，更新虚拟滚动 - 移除深度监听提升性能\nwatch(() => props.cases, (newCases, oldCases) => {\n  // 只有在数组引用变化或长度变化时才更新\n  if (newCases !== oldCases || newCases?.length !== oldCases?.length) {\n    nextTick(() => {\n      updateVirtualScroll();\n    });\n  }\n}, { flush: 'post' });\n\n// 组件挂载时初始化虚拟滚动\nonMounted(() => {\n  nextTick(() => {\n    initVirtualScroll();\n  });\n});\n\n// 组件卸载时销毁虚拟滚动\nonUnmounted(() => {\n  if (virtualScroll) {\n    virtualScroll.destroy();\n    virtualScroll = null;\n  }\n});\n\nreturn (_ctx: any,_cache: any) => {\n  return (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [\n    _createElementVNode(\"div\", {\n      class: \"cases-header\",\n      ref_key: \"headerContainer\",\n      ref: headerContainer\n    }, null, 512),\n    _createElementVNode(\"div\", {\n      ref_key: \"casesContainer\",\n      ref: casesContainer,\n      class: \"cases-content\"\n    }, null, 512)\n  ]))\n}\n}\n\n})", "<template>\n  <div class=\"cases-list-container case-list\">\n    <!-- 固定表头 -->\n    <div class=\"cases-header\" ref=\"headerContainer\"></div>\n    <!-- 虚拟滚动内容容器 -->\n    <div ref=\"casesContainer\" class=\"cases-content\"></div>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport { ref, onMounted, onUnmounted, watch, nextTick, defineProps, defineEmits } from 'vue';\nimport { VirtualScroll } from 'js-booster';\nimport { CaseResult, ExecutionState } from '@/api/interoperationApi';\n\nconst props = defineProps<{\n  cases: CaseResult[];\n}>();\n\nconst emit = defineEmits<{\n  (e: 'view-detail', caseResult: CaseResult): void;\n}>();\n\n// 虚拟滚动相关\nconst casesContainer = ref<HTMLElement | null>(null);\nconst headerContainer = ref<HTMLElement | null>(null);\nlet virtualScroll: any = null;\nconst ITEM_HEIGHT = 36; // 行高\nconst BUFFER_SIZE = 20; // 缓冲区大小\nconst HEADER_HEIGHT = 40; // 表头高度\n\n// 渲染固定表头\nconst renderFixedHeader = () => {\n  if (!headerContainer.value) return;\n\n  // 清空表头容器\n  headerContainer.value.innerHTML = '';\n\n  // 创建表头\n  const header = document.createElement('div');\n  header.className = 'header-row';\n  header.style.height = `${HEADER_HEIGHT}px`; // 动态高度\n\n  // ID 列\n  const idHeader = document.createElement('div');\n  idHeader.textContent = 'ID';\n  idHeader.className = 'header-cell-id';\n  header.appendChild(idHeader);\n\n  // 名称列\n  const nameHeader = document.createElement('div');\n  nameHeader.textContent = 'Name';\n  nameHeader.className = 'header-cell-name';\n  header.appendChild(nameHeader);\n\n  // 参数列\n  const paramHeader = document.createElement('div');\n  paramHeader.textContent = 'Parameter';\n  paramHeader.className = 'header-cell-param';\n  header.appendChild(paramHeader);\n\n  // 详情列\n  const detailHeader = document.createElement('div');\n  detailHeader.textContent = 'Detail';\n  detailHeader.className = 'header-cell-detail';\n  header.appendChild(detailHeader);\n\n  // 状态列\n  const statusHeader = document.createElement('div');\n  statusHeader.textContent = 'Status';\n  statusHeader.className = 'header-cell-status';\n  header.appendChild(statusHeader);\n\n  // 添加到表头容器\n  headerContainer.value.appendChild(header);\n};\n\n// 初始化虚拟滚动\nconst initVirtualScroll = () => {\n  if (!casesContainer.value || !props.cases.length) return;\n\n  // 渲染固定表头\n  renderFixedHeader();\n\n  // 如果已经存在虚拟滚动实例，先销毁\n  if (virtualScroll) {\n    virtualScroll.destroy();\n  }\n\n  virtualScroll = new VirtualScroll({\n    container: casesContainer.value,\n    items: props.cases,\n    itemHeight: ITEM_HEIGHT,\n    bufferSize: BUFFER_SIZE,\n    renderItem: (item: CaseResult, index: number) => {\n      // 创建主容器\n      const div = document.createElement('div');\n      div.className = 'case-row';\n      div.onclick = () => emit('view-detail', item);\n\n      // 设置动态样式\n      div.style.height = `${ITEM_HEIGHT}px`;\n      div.style.lineHeight = `${ITEM_HEIGHT}px`;\n      div.style.borderBottom = index === props.cases.length - 1 ? 'none' : '1px solid #ebeef5';\n      div.style.backgroundColor = item.id % 2 === 0 ? '#fff' : '#fafafa';\n\n      // 添加悬停效果\n      div.onmouseover = () => {\n        div.style.backgroundColor = '#f5f7fa';\n      };\n      div.onmouseout = () => {\n        div.style.backgroundColor = item.id % 2 === 0 ? '#fff' : '#fafafa';\n        // 确保鼠标移出时保持最后一个项目没有底部边框\n        div.style.borderBottom = index === props.cases.length - 1 ? 'none' : '1px solid #ebeef5';\n      };\n\n      // 创建行内容\n      const rowDiv = document.createElement('div');\n      rowDiv.className = 'case-row-content';\n\n      // ID\n      const idDiv = document.createElement('div');\n      idDiv.textContent = `#${item.id}`;\n      idDiv.className = 'case-cell-id';\n      rowDiv.appendChild(idDiv);\n\n      // 名称\n      const nameDiv = document.createElement('div');\n      nameDiv.textContent = item.name;\n      nameDiv.className = 'case-cell-name';\n      rowDiv.appendChild(nameDiv);\n\n      // 参数\n      const paramDiv = document.createElement('div');\n      paramDiv.textContent = item.parameter;\n      paramDiv.title = item.parameter;\n      paramDiv.className = 'case-cell-param';\n      rowDiv.appendChild(paramDiv);\n\n      // 详情列\n      const detailDiv = document.createElement('div');\n      detailDiv.textContent = item.detail || '-';\n      detailDiv.title = item.detail || '';\n      detailDiv.className = 'case-cell-detail';\n      rowDiv.appendChild(detailDiv);\n\n      // 状态\n      const statusDiv = document.createElement('div');\n      statusDiv.className = 'case-cell-status';\n\n      // 创建状态标签\n      const tagType = getStatusTagType(item.state);\n      const tagText = getStatusText(item.state);\n\n      const tagEl = document.createElement('span');\n      tagEl.className = `el-tag el-tag--${tagType} el-tag--small case-status-tag`;\n      tagEl.textContent = tagText;\n\n      statusDiv.appendChild(tagEl);\n      rowDiv.appendChild(statusDiv);\n\n      div.appendChild(rowDiv);\n\n      return div;\n    }\n  });\n};\n\n// 更新虚拟滚动数据\nconst updateVirtualScroll = () => {\n  if (virtualScroll) {\n    virtualScroll.updateItems(props.cases);\n  } else {\n    nextTick(() => {\n      initVirtualScroll();\n    });\n  }\n};\n\n// 获取状态对应的标签类型\nconst getStatusTagType = (state: string): string => {\n  switch (state) {\n    case ExecutionState.Success:\n      return 'success';\n    case ExecutionState.Running:\n      return 'warning';\n    case ExecutionState.Failure:\n      return 'danger';\n    case ExecutionState.Pending:\n    default:\n      return 'info';\n  }\n};\n\n// 获取状态的文本描述\nconst getStatusText = (state: string): string => {\n  switch (state) {\n    case ExecutionState.Success:\n      return 'Passed';\n    case ExecutionState.Failure:\n      return 'Failed';\n    case ExecutionState.Running:\n      return 'Running';\n    case ExecutionState.Pending:\n      return 'Pending';\n    default:\n      return 'Unknown';\n  }\n};\n\n// 监听 cases 变化，更新虚拟滚动 - 移除深度监听提升性能\nwatch(() => props.cases, (newCases, oldCases) => {\n  // 只有在数组引用变化或长度变化时才更新\n  if (newCases !== oldCases || newCases?.length !== oldCases?.length) {\n    nextTick(() => {\n      updateVirtualScroll();\n    });\n  }\n}, { flush: 'post' });\n\n// 组件挂载时初始化虚拟滚动\nonMounted(() => {\n  nextTick(() => {\n    initVirtualScroll();\n  });\n});\n\n// 组件卸载时销毁虚拟滚动\nonUnmounted(() => {\n  if (virtualScroll) {\n    virtualScroll.destroy();\n    virtualScroll = null;\n  }\n});\n</script>\n\n<style scoped lang=\"scss\">\n.cases-list-container {\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  position: relative;\n  font-size: 13px;\n}\n\n.cases-header {\n  position: sticky;\n  top: 0;\n  z-index: 10;\n  background-color: #f5f7fa;\n}\n\n.cases-content {\n  flex: 1;\n  overflow: auto;\n  position: relative;\n}\n</style>\n", "import script from \"./CaseList.vue?vue&type=script&setup=true&lang=ts\"\nexport * from \"./CaseList.vue?vue&type=script&setup=true&lang=ts\"\n\nimport \"./CaseList.vue?vue&type=style&index=0&id=795f583a&scoped=true&lang=scss\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-795f583a\"]])\n\nexport default __exports__"], "names": ["_defineComponent", "__name", "props", "state", "setup", "__props", "tagType", "computed", "ExecutionState", "Success", "Running", "Failure", "Pending", "getCaseStateName", "stateName", "_ctx", "_cache", "_component_el_tag", "_resolveComponent", "_openBlock", "_createBlock", "type", "value", "size", "style", "default", "_withCtx", "_createTextVNode", "_toDisplayString", "_", "__exports__", "getTimelineItemType", "_hoisted_1", "key", "class", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_hoisted_5", "_hoisted_6", "_hoisted_7", "_hoisted_8", "_hoisted_9", "_hoisted_10", "_hoisted_11", "_hoisted_12", "_hoisted_13", "_hoisted_14", "_hoisted_15", "_hoisted_16", "_hoisted_17", "_hoisted_18", "_hoisted_19", "_hoisted_20", "_hoisted_21", "_hoisted_22", "_hoisted_23", "_hoisted_24", "_hoisted_25", "_hoisted_26", "visible", "Boolean", "testResultId", "caseResultId", "emits", "emit", "__emit", "dialogVisible", "ref", "caseData", "steps", "loading", "watch", "newValue", "loadCaseData", "async", "caseResponse", "stepsResponse", "Promise", "all", "appApi", "getCaseResult", "getCaseSteps", "data", "error", "console", "ElMessage", "warning", "closeDialog", "formatDateTime", "dateString", "date", "Date", "toLocaleString", "e", "formatMicroseconds", "microseconds", "seconds", "toFixed", "onMounted", "_component_el_skeleton", "_component_el_empty", "_component_el_timeline_item", "_component_el_timeline", "_component_el_button", "_component_el_dialog", "modelValue", "$event", "title", "name", "sequenceName", "width", "footer", "_createElementVNode", "_createVNode", "onClick", "_createElementBlock", "rows", "animated", "begin", "end", "CaseStateTag", "parameter", "_createCommentVNode", "detail", "length", "description", "_Fragment", "_renderList", "step", "id", "_unref", "hollow", "timestamp", "VirtualScroll", "constructor", "options", "this", "container", "items", "itemHeight", "bufferSize", "customRenderItem", "renderItem", "customRenderHeader", "renderHeader", "maxHeight", "visibleStartIndex", "visibleEndIndex", "scrollContainer", "contentWrapper", "contentContainer", "totalHeight", "heightScale", "initialize", "innerHTML", "document", "createElement", "Object", "assign", "flex", "overflow", "position", "minHeight", "height", "boxSizing", "header", "append<PERSON><PERSON><PERSON>", "scaledHeight", "left", "addEventListener", "handleScroll", "bind", "renderVisibleItems", "Math", "min", "scrollTop", "containerHeight", "clientHeight", "realScrollTop", "startIndex", "max", "floor", "endIndex", "ceil", "transform", "i", "item", "itemElement", "row", "padding", "borderBottom", "textContent", "JSON", "stringify", "updateItems", "scrollToIndex", "index", "destroy", "removeEventListener", "refresh", "getScrollContainer", "window", "JsBooster", "ITEM_HEIGHT", "BUFFER_SIZE", "HEADER_HEIGHT", "cases", "casesContainer", "headerContainer", "virtualScroll", "renderFixedHeader", "className", "idHeader", "nameHeader", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "statusH<PERSON>er", "initVirtualScroll", "div", "onclick", "lineHeight", "backgroundColor", "on<PERSON><PERSON>ver", "onmouseout", "rowDiv", "idDiv", "nameDiv", "paramDiv", "detailDiv", "statusDiv", "getStatusTagType", "tagText", "getStatusText", "tagEl", "updateVirtualScroll", "nextTick", "newCases", "oldCases", "flush", "onUnmounted", "ref_key"], "sourceRoot": ""}