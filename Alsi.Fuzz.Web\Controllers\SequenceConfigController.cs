using Alsi.Fuzz.Core.Models.TestPlans;
using Alsi.Fuzz.Core.Service;
using Alsi.Fuzz.Core.Storage;
using System.Linq;
using System.Threading.Tasks;
using System.Web.Http;

namespace Alsi.Fuzz.Web.Controllers
{
    public class SequenceConfigController : WebControllerBase
    {
        private readonly ITestPlanService _testPlanService;

        public SequenceConfigController()
        {
            var historyService = new TestPlanHistoryService();
            _testPlanService = new TestPlanService(new TestPlanStorage(), historyService);
        }

        [HttpGet]
        public IHttpActionResult GetSequenceConfig()
        {
            var currentPlan = TestPlanManager.Instance.GetCurrentPlan();
            var selectedSequence = currentPlan?.Config?.SequenceConfigList.FirstOrDefault(x => x.IsSelected);
            if (selectedSequence == null)
            {
                return Ok(new SequenceConfig());
            }

            return Ok(new SequenceConfigDto
            {
                SequencePackageName = selectedSequence.SequencePackageName,
                TestSuiteName = selectedSequence.TestSuiteName,
                SequencePackageXml = selectedSequence.SequencePackageXml
            });
        }

        [HttpPost]
        public async Task<IHttpActionResult> UpdateSequenceConfig([FromBody] SequenceConfigDto request)
        {
            var path = TestPlanManager.Instance.GetCurrentPlanPath();

            FileLocker.UnlockFile();
            try
            {
                var updatedPlan = await _testPlanService.UpdateSequenceConfigAsync(path, request);

                // 更新当前计划中的序列配置
                var currentPlan = TestPlanManager.Instance.GetCurrentPlan();
                if (currentPlan != null)
                {
                    currentPlan.Config.SequenceConfigList = updatedPlan.Config.SequenceConfigList;
                }

                return GetSequenceConfig();
            }
            finally
            {
                FileLocker.LockFile(path);
            }
        }
    }
}
