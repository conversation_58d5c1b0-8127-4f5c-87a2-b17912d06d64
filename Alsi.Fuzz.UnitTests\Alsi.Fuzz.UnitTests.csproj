<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>

    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>
    <Platforms>AnyCPU;x64</Platforms>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="coverlet.collector" Version="3.2.0" />
    <PackageReference Include="FreeSql.Provider.Sqlite" Version="3.5.106" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.5.0" />
    <PackageReference Include="Shouldly" Version="4.3.0" />
    <PackageReference Include="xunit" Version="2.9.3" />
    <PackageReference Include="xunit.runner.visualstudio" Version="3.0.2">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Xunit.SkippableFact" Version="1.5.23" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\Alsi.Common\Alsi.App.Database\Alsi.App.Database.csproj" />
    <ProjectReference Include="..\..\Alsi.Common\Alsi.App\Alsi.App.csproj" />
    <ProjectReference Include="..\..\Alsi.Common\Alsi.Common.Utils\Alsi.Common.Utils.csproj" />
    <ProjectReference Include="..\Alsi.App.Devices\Alsi.App.Devices.csproj" />
    <ProjectReference Include="..\Alsi.Fuzz.Core\Alsi.Fuzz.Core.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Using Include="Xunit" />
  </ItemGroup>

</Project>
