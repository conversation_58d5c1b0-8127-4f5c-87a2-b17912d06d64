[{"D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\main.ts": "1", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\App.vue": "2", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\api\\appApi.ts": "3", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\store\\index.ts": "4", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\mock\\mockApi.ts": "5", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\components\\layout\\TopMenuBar.vue": "6", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\components\\layout\\TestPlanStatusIndicator.vue": "7", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\components\\TestPlan\\CreateTestPlan.vue": "8", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\api\\testPlanApi.ts": "9", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\api\\testPlanHistoryApi.ts": "10", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\services\\testPlanService.ts": "11", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\utils\\errorHandler.ts": "12", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\mock\\modules\\mockTestPlanHistoryApi.ts": "13", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\mock\\modules\\mockTestPlanApi.ts": "14", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\api\\explorerApi.ts": "15", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\mock\\mockData.ts": "16", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\mock\\modules\\mockAppApi.ts": "17", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\mock\\modules\\mockExplorerApi.ts": "18", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\mock\\modules\\mockInteroperationApi.ts": "19", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\router\\index.ts": "20", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\mock\\modules\\mockTestSuiteApi.ts": "21", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\mock\\modules\\mockHardwareApi.ts": "22", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\api\\sequenceApi.ts": "23", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\mock\\modules\\mockSequenceApi.ts": "24", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\mock\\modules\\mockCaseApi.ts": "25", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\api\\index.ts": "26", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\views\\TestSuiteView.vue": "27", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\views\\HomeView.vue": "28", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\views\\AboutView.vue": "29", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\views\\TestPlanView.vue": "30", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\views\\testplan\\SequenceSetting.vue": "31", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\views\\testplan\\TestResults.vue": "32", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\views\\testplan\\TestCases.vue": "33", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\views\\testplan\\TestRun.vue": "34", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\views\\testplan\\HardwareSetting.vue": "35", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\views\\testplan\\Interoperation.vue": "36", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\views\\testplan\\CaseSetting.vue": "37", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\views\\testplan\\BasicSetting.vue": "38", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\api\\interoperationApi.ts": "39", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\components\\TestPlan\\TestPlanHistory.vue": "40", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\components\\Guide\\TestPlanGuide.vue": "41", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\components\\TestSuite\\XmlViewer.vue": "42", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\components\\TestCases\\InteroperationResultPanel.vue": "43", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\components\\TestCases\\GeneratedCasesPanel.vue": "44", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\components\\common\\CaseStateTag.vue": "45", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\components\\test\\TestMonitor.vue": "46", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\components\\layout\\SideNav.vue": "47", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\components\\test\\CaseDetailDialog.vue": "48", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\components\\hardware\\HardwareConfigPanel.vue": "49", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\api\\testSuiteApi.ts": "50", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\api\\caseApi.ts": "51", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\components\\hardware\\DeviceChannelSelect.vue": "52", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\components\\XmlEditor\\XmlEditor.vue": "53", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\components\\common\\TestStateTag.vue": "54", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\components\\common\\TimeDisplay.vue": "55", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\api\\hardwareApi.ts": "56", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\utils\\status.ts": "57", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\components\\test\\CaseList.vue": "58"}, {"size": 3535, "mtime": 1745817850348, "results": "59", "hashOfConfig": "60"}, {"size": 704, "mtime": 1741341347115, "results": "61", "hashOfConfig": "60"}, {"size": 7852, "mtime": 1748238184620, "results": "62", "hashOfConfig": "60"}, {"size": 145, "mtime": 1741244740186, "results": "63", "hashOfConfig": "60"}, {"size": 2269, "mtime": 1743146692910, "results": "64", "hashOfConfig": "60"}, {"size": 7358, "mtime": 1745825808132, "results": "65", "hashOfConfig": "60"}, {"size": 3092, "mtime": 1745825487211, "results": "66", "hashOfConfig": "60"}, {"size": 3192, "mtime": 1745819762310, "results": "67", "hashOfConfig": "60"}, {"size": 3890, "mtime": 1745822105743, "results": "68", "hashOfConfig": "60"}, {"size": 1500, "mtime": 1745822128311, "results": "69", "hashOfConfig": "60"}, {"size": 4848, "mtime": 1745819678229, "results": "70", "hashOfConfig": "60"}, {"size": 2910, "mtime": 1745819950839, "results": "71", "hashOfConfig": "60"}, {"size": 1400, "mtime": 1745821858884, "results": "72", "hashOfConfig": "60"}, {"size": 5401, "mtime": 1745821844067, "results": "73", "hashOfConfig": "60"}, {"size": 808, "mtime": 1742432115229, "results": "74", "hashOfConfig": "60"}, {"size": 2712, "mtime": 1741750667778, "results": "75", "hashOfConfig": "60"}, {"size": 16897, "mtime": 1748238340959, "results": "76", "hashOfConfig": "60"}, {"size": 656, "mtime": 1742436235907, "results": "77", "hashOfConfig": "60"}, {"size": 4124, "mtime": 1743057039836, "results": "78", "hashOfConfig": "60"}, {"size": 2829, "mtime": 1742800003905, "results": "79", "hashOfConfig": "60"}, {"size": 2260, "mtime": 1743136653047, "results": "80", "hashOfConfig": "60"}, {"size": 2450, "mtime": 1745822993625, "results": "81", "hashOfConfig": "60"}, {"size": 864, "mtime": 1743395162896, "results": "82", "hashOfConfig": "60"}, {"size": 736, "mtime": 1743395161642, "results": "83", "hashOfConfig": "60"}, {"size": 3339, "mtime": 1746259278498, "results": "84", "hashOfConfig": "60"}, {"size": 275, "mtime": 1741325847333, "results": "85", "hashOfConfig": "60"}, {"size": 3984, "mtime": 1743395974247, "results": "86", "hashOfConfig": "60"}, {"size": 4490, "mtime": 1741763348943, "results": "87", "hashOfConfig": "60"}, {"size": 5334, "mtime": 1745818324170, "results": "88", "hashOfConfig": "60"}, {"size": 2651, "mtime": 1745824864006, "results": "89", "hashOfConfig": "60"}, {"size": 5581, "mtime": 1745817868437, "results": "90", "hashOfConfig": "60"}, {"size": 15862, "mtime": 1746259699862, "results": "91", "hashOfConfig": "60"}, {"size": 7218, "mtime": 1745475021995, "results": "92", "hashOfConfig": "60"}, {"size": 14245, "mtime": 1748244246934, "results": "93", "hashOfConfig": "60"}, {"size": 973, "mtime": 1743559764545, "results": "94", "hashOfConfig": "60"}, {"size": 7846, "mtime": 1744340336536, "results": "95", "hashOfConfig": "60"}, {"size": 28811, "mtime": 1746259338059, "results": "96", "hashOfConfig": "60"}, {"size": 5294, "mtime": 1743559746808, "results": "97", "hashOfConfig": "60"}, {"size": 2669, "mtime": 1743057265140, "results": "98", "hashOfConfig": "60"}, {"size": 9462, "mtime": 1745825657018, "results": "99", "hashOfConfig": "60"}, {"size": 2777, "mtime": 1743127629622, "results": "100", "hashOfConfig": "60"}, {"size": 923, "mtime": 1743395349655, "results": "101", "hashOfConfig": "60"}, {"size": 8644, "mtime": 1742802731527, "results": "102", "hashOfConfig": "60"}, {"size": 8976, "mtime": 1745474964055, "results": "103", "hashOfConfig": "60"}, {"size": 1167, "mtime": 1744341497227, "results": "104", "hashOfConfig": "60"}, {"size": 2778, "mtime": 1746259680804, "results": "105", "hashOfConfig": "60"}, {"size": 2788, "mtime": 1743561147847, "results": "106", "hashOfConfig": "60"}, {"size": 8802, "mtime": 1745737887919, "results": "107", "hashOfConfig": "60"}, {"size": 16386, "mtime": 1745822877688, "results": "108", "hashOfConfig": "60"}, {"size": 1127, "mtime": 1743395995008, "results": "109", "hashOfConfig": "60"}, {"size": 2911, "mtime": 1745731782678, "results": "110", "hashOfConfig": "60"}, {"size": 1797, "mtime": 1741859254423, "results": "111", "hashOfConfig": "60"}, {"size": 4582, "mtime": 1741763378617, "results": "112", "hashOfConfig": "60"}, {"size": 1218, "mtime": 1743144160454, "results": "113", "hashOfConfig": "60"}, {"size": 2961, "mtime": 1742791170488, "results": "114", "hashOfConfig": "60"}, {"size": 1601, "mtime": 1745822786455, "results": "115", "hashOfConfig": "60"}, {"size": 557, "mtime": 1742784552594, "results": "116", "hashOfConfig": "60"}, {"size": 6819, "mtime": 1746257165394, "results": "117", "hashOfConfig": "60"}, {"filePath": "118", "messages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "120"}, "fjrot7", {"filePath": "121", "messages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "125", "messages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "129"}, {"filePath": "130", "messages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "132"}, {"filePath": "133", "messages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "135"}, {"filePath": "136", "messages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "138"}, {"filePath": "139", "messages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "141"}, {"filePath": "142", "messages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "144"}, {"filePath": "145", "messages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "147"}, {"filePath": "148", "messages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "150"}, {"filePath": "151", "messages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "155"}, {"filePath": "156", "messages": "157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "158", "messages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "162"}, {"filePath": "163", "messages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "165", "messages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "167", "messages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "171", "messages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "173", "messages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "177"}, {"filePath": "178", "messages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "180"}, {"filePath": "181", "messages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "183", "messages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "185"}, {"filePath": "186", "messages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "188", "messages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "190"}, {"filePath": "191", "messages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "193", "messages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "195"}, {"filePath": "196", "messages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "198"}, {"filePath": "199", "messages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "201"}, {"filePath": "202", "messages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "204", "messages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "206", "messages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "208"}, {"filePath": "209", "messages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "211"}, {"filePath": "212", "messages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "216", "messages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "218"}, {"filePath": "219", "messages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "221", "messages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "223", "messages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "225"}, {"filePath": "226", "messages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "228"}, {"filePath": "229", "messages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "231", "messages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "233", "messages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "235", "messages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "237"}, {"filePath": "238", "messages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "240"}, {"filePath": "241", "messages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "243"}, {"filePath": "244", "messages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "246"}, {"filePath": "247", "messages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "249", "messages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "251", "messages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "253", "messages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "255", "messages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "257", "messages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "259", "messages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "261"}, "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\main.ts", ["262", "263", "264", "265"], "import { createApp } from 'vue'\nimport App from './App.vue'\nimport router from './router'\nimport store from './store'\nimport { appApi, type ErrorData } from './api/appApi' // 导入 appApi 而不是 axios\nimport { setupErrorHandler } from './utils/errorHandler' // 导入错误处理器\n\n// 引入 Element Plus\nimport ElementPlus from 'element-plus'\nimport 'element-plus/dist/index.css'\nimport zhCn from 'element-plus/dist/locale/zh-cn.mjs'\n\n// 设置 Element Plus 主题变量\nimport * as ElementPlusIconsVue from '@element-plus/icons-vue'\nimport './styles/element-variables.css' // 需要创建这个文件来自定义主题\n\n// 引入 FontAwesome\nimport { library } from '@fortawesome/fontawesome-svg-core'\nimport { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'\nimport {\n  faCogs, faFolderOpen, faPlus, faFileAlt,\n  faHistory, faTrashCan, faFileExcel, faClock,\n  faFolder, faChartBar, faProjectDiagram,\n  faClockRotateLeft, faFileCircleExclamation,\n  faAngleDown, faAngleUp, faExpand, faCompress,\n  faUpRightAndDownLeftFromCenter, faDownLeftAndUpRightToCenter\n} from '@fortawesome/free-solid-svg-icons'\n\n// 添加需要使用的图标到库中\nlibrary.add(\n  faCogs, faFolderOpen, faPlus, faFileAlt,\n  faHistory, faTrashCan, faFileExcel, faClock,\n  faFolder, faChartBar, faProjectDiagram,\n  faClockRotateLeft, faFileCircleExclamation,\n  faAngleDown, faAngleUp, faExpand, faCompress,\n  faUpRightAndDownLeftFromCenter, faDownLeftAndUpRightToCenter\n)\n\nconst app = createApp(App)\n\n// 全局注册 FontAwesome 组件\napp.component('font-awesome-icon', FontAwesomeIcon)\n\n// 全局注册所有图标\nfor (const [key, component] of Object.entries(ElementPlusIconsVue)) {\n  app.component(key, component)\n}\n\n// 设置全局错误处理\nsetupErrorHandler()\n\napp.use(store)\n   .use(router)\n   .use(ElementPlus, {\n     locale: zhCn,\n     size: 'default'\n   })\n   .mount('#app')\n\n// 定义 sendError 类型\ntype SendErrorType = Error | unknown;\n\n// 全局异常处理\napp.config.errorHandler = (err: unknown, vm, info) => {\n  // 控制台输出错误\n  console.error(\"Vue 全局错误:\", err);\n\n  // 将错误发送到后端\n  const errorData: ErrorData = {\n    message: err instanceof Error ? err.message : String(err),\n    stack: err instanceof Error ? err.stack : \"无堆栈信息\",\n    vueHookInfo: info, // 更新字段名\n    url: window.location.href,\n  };\n\n  appApi.logError(errorData).catch((sendError: SendErrorType) => {\n    console.error(\"发送错误到服务器失败:\", sendError);\n  });\n};\n\n// 捕获未处理的Promise异常\nwindow.addEventListener(\"unhandledrejection\", (event) => {\n  const errorData: ErrorData = {\n    message:\n      event.reason instanceof Error\n        ? event.reason.message\n        : \"未处理的Promise异常\",\n    stack: event.reason instanceof Error ? event.reason.stack : \"无堆栈信息\",\n    url: window.location.href,\n    type: \"unhandledrejection\",\n  };\n\n  appApi.logError(errorData).catch((sendError: SendErrorType) => {\n    console.error(\"发送Promise错误到服务器失败:\", sendError);\n  });\n});\n\n// 捕获全局JS错误\nwindow.addEventListener(\"error\", (event) => {\n  // 过滤资源加载错误\n  if (event.message) {\n    const errorData: ErrorData = {\n      message: event.message,\n      codeInfo: `${event.filename}:${event.lineno}:${event.colno}`,\n      url: window.location.href,\n      type: \"global-error\",\n    };\n\n    appApi.logError(errorData).catch((sendError: SendErrorType) => {\n      console.error(\"发送全局错误到服务器失败:\", sendError);\n    });\n  }\n});\n", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\App.vue", [], "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\api\\appApi.ts", [], "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\store\\index.ts", [], "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\mock\\mockApi.ts", ["266"], "import { AxiosResponse, InternalAxiosRequestConfig } from 'axios';\r\nimport { mockTestPlanApi } from './modules/mockTestPlanApi';\r\nimport { mockTestPlanHistoryApi } from './modules/mockTestPlanHistoryApi';\r\nimport { mockAppApi } from './modules/mockAppApi';\r\nimport { mockExplorerApi } from './modules/mockExplorerApi';\r\nimport { mockTestSuiteApi } from './modules/mockTestSuiteApi';\r\nimport { mockHardwareApi } from './modules/mockHardwareApi';\r\nimport { mockSequenceApi } from './modules/mockSequenceApi';\r\nimport { mockInteroperationApi } from './modules/mockInteroperationApi';\r\nimport { mockCaseApi } from './modules/mockCaseApi';\r\n\r\n// 控制是否启用Mock\r\nexport const USE_MOCK = process.env.NODE_ENV === 'development';\r\n\r\n// 模拟网络延迟\r\nconst delay = (ms = 100) => new Promise(resolve => setTimeout(resolve, ms));\r\n\r\n// 创建一个符合AxiosResponse类型的响应对象\r\nexport const mockSuccess = <T>(data: T): Promise<AxiosResponse<T>> => {\r\n  return delay().then(() => {\r\n    // 创建一个符合AxiosResponse要求的对象\r\n    const response: AxiosResponse<T> = {\r\n      data,\r\n      status: 200,\r\n      statusText: 'OK',\r\n      headers: {},\r\n      config: {\r\n        headers: {} // 添加必要的headers属性\r\n      } as InternalAxiosRequestConfig\r\n    };\r\n    return response;\r\n  });\r\n};\r\n\r\n// 模拟错误响应\r\nexport const mockError = (status = 400, message = 'Error'): Promise<never> => {\r\n  return delay().then(() => {\r\n    const error: any = new Error(message);\r\n    error.response = {\r\n      status,\r\n      data: message\r\n    };\r\n    return Promise.reject(error);\r\n  });\r\n};\r\n\r\n// 随机生成一个UUID\r\nexport const generateId = () => {\r\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\r\n    const r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);\r\n    return v.toString(16);\r\n  });\r\n};\r\n\r\n// 模拟API实现\r\nexport const mockApi = {\r\n  // 导出各个模块化的API\r\n  testPlan: mockTestPlanApi,\r\n  testPlanHistory: mockTestPlanHistoryApi,\r\n  app: mockAppApi,\r\n  explorer: mockExplorerApi,\r\n  testSuite: mockTestSuiteApi,\r\n  hardware: mockHardwareApi,\r\n  sequence: mockSequenceApi,\r\n  interoperation: mockInteroperationApi,  // 添加互操作测试API\r\n  case: mockCaseApi\r\n};\r\n", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\components\\layout\\TopMenuBar.vue", ["267", "268"], "<template>\r\n  <div class=\"top-menu-bar\">\r\n    <el-menu mode=\"horizontal\" class=\"menu-container\" router :default-active=\"activeIndex\">\r\n      <el-menu-item index=\"/\" class=\"home-menu-item\">\r\n        <el-icon>\r\n          <HomeFilled />\r\n        </el-icon>\r\n      </el-menu-item>\r\n      <el-sub-menu index=\"file\">\r\n        <template #title>File</template>\r\n        <el-menu-item @click=\"handleOpen\" class=\"menu-item\">Open</el-menu-item>\r\n        <el-menu-item @click=\"handleCreate\" class=\"menu-item\">Create</el-menu-item>\r\n        <el-divider />\r\n        <el-sub-menu index=\"recent\" popper-class=\"recent-submenu\">\r\n          <template #title>Recent</template>\r\n          <div v-if=\"recentPlans.length === 0\" class=\"empty-recent\">\r\n            <span>No recent items</span>\r\n          </div>\r\n          <template v-else>\r\n            <el-menu-item v-for=\"plan in recentPlans\" :key=\"plan.id\" @click=\"handleOpenFromHistory(plan.filePath)\"\r\n              class=\"recent-item\">\r\n              <span class=\"recent-plan-name\">{{ plan.planName }}</span>\r\n              <span class=\"recent-plan-path\">{{ plan.filePath }}</span>\r\n            </el-menu-item>\r\n          </template>\r\n        </el-sub-menu>\r\n        <el-divider />\r\n        <el-menu-item @click=\"handleExit\" class=\"menu-item\">Exit</el-menu-item>\r\n      </el-sub-menu>\r\n\r\n      <el-menu-item index=\"/test-suite\" class=\"test-suite-menu-item\">\r\n        <span class=\"test-suite-text\">Test Suite</span>\r\n      </el-menu-item>\r\n\r\n      <el-menu-item index=\"/about\" class=\"about-menu-item\">\r\n        <span class=\"about-text\">About</span>\r\n      </el-menu-item>\r\n    </el-menu>\r\n\r\n    <!-- 添加测试计划状态指示器 -->\r\n    <test-plan-status-indicator class=\"status-indicator\" />\r\n\r\n    <create-test-plan ref=\"createDialog\" @created=\"handleTestPlanCreated\" />\r\n  </div>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { ref, onMounted, computed } from 'vue'\r\nimport { useRouter, useRoute } from 'vue-router'\r\nimport { ElMessageBox, ElMessage } from 'element-plus'\r\nimport { appApi } from '@/api'\r\nimport { HomeFilled } from '@element-plus/icons-vue'\r\nimport CreateTestPlan from '@/components/TestPlan/CreateTestPlan.vue'\r\nimport { testPlanService } from '@/services/testPlanService'\r\nimport { testPlanApi } from '@/api/testPlanApi'\r\nimport { testPlanHistoryApi } from '@/api/testPlanHistoryApi'\r\nimport TestPlanStatusIndicator from './TestPlanStatusIndicator.vue'\r\n\r\nconst router = useRouter()\r\nconst route = useRoute()\r\nconst createDialog = ref<InstanceType<typeof CreateTestPlan> | null>(null)\r\n\r\n// Compute active menu index based on current route\r\nconst activeIndex = computed(() => {\r\n  // When on test plan page, don't highlight any menu item\r\n  if (route.path.startsWith('/test-plan')) {\r\n    return ''\r\n  }\r\n  return route.path\r\n})\r\n\r\n// Set router\r\ntestPlanService.setRouter(router)\r\n\r\n// Get service state\r\nconst state = testPlanService.getState()\r\nconst recentPlans = computed(() => state.recentPlans.slice(0, 5)) // Only show 5 most recent\r\n\r\n// Handle open\r\nconst handleOpen = async () => {\r\n  await testPlanService.openFromExplorer()\r\n}\r\n\r\n// Handle opening from history\r\nconst handleOpenFromHistory = async (path: string) => {\r\n  try {\r\n    // First check if file exists\r\n    const response = await testPlanApi.checkFileExists(path);\r\n\r\n    if (response.data.exists) {\r\n      // File exists, open normally\r\n      await testPlanService.openFromPath(path);\r\n    } else {\r\n      // File does not exist, prompt user\r\n      ElMessageBox.confirm(\r\n        'Test plan file does not exist. Do you want to remove it from history?',\r\n        'File Not Found',\r\n        {\r\n          confirmButtonText: 'Remove',\r\n          cancelButtonText: 'Cancel',\r\n          type: 'warning'\r\n        }\r\n      ).then(async () => {\r\n        // User chose to remove\r\n        await testPlanHistoryApi.deleteRecord(path);\r\n        // Refresh history\r\n        await testPlanService.loadRecentPlans();\r\n      }).catch(() => {\r\n        // User canceled, do nothing\r\n      });\r\n    }\r\n  } catch (error) {\r\n    console.error('Failed to check file existence:', error);\r\n  }\r\n}\r\n\r\n// Handle create\r\nconst handleCreate = () => {\r\n  createDialog.value?.show()\r\n}\r\n\r\n// Handle test plan creation completed\r\nconst handleTestPlanCreated = () => {\r\n  // Refresh state is handled by the service, no additional action needed\r\n}\r\n\r\n// Handle exit application\r\nconst handleExit = () => {\r\n  ElMessageBox.confirm(\r\n    'Are you sure you want to exit the application?',\r\n    'Exit',\r\n    {\r\n      confirmButtonText: 'OK',\r\n      cancelButtonText: 'Cancel',\r\n      type: 'warning'\r\n    }\r\n  ).then(() => {\r\n    // 确认退出时调用后端API\r\n    appApi.exit()\r\n      .then(() => {\r\n        ElMessage.info('Application is shutting down...');\r\n        // 在实际环境中，这里不会被执行到，因为应用已经退出\r\n      })\r\n      .catch((error) => {\r\n        ElMessage.error('Failed to exit the application');\r\n        console.error('Error when exiting application:', error);\r\n      });\r\n  }).catch(() => {\r\n    // 用户取消退出，不执行任何操作\r\n  });\r\n}\r\n\r\nonMounted(() => {\r\n  testPlanService.loadRecentPlans()\r\n})\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.top-menu-bar {\r\n  border-bottom: 1px solid var(--el-border-color-light);\r\n  user-select: none;\r\n  display: flex; /* 添加flex布局 */\r\n  align-items: center; /* 垂直居中 */\r\n}\r\n\r\n.menu-container {\r\n  height: 40px;\r\n  line-height: 40px;\r\n  display: flex;\r\n  justify-content: flex-start;\r\n  flex-grow: 1; /* 让菜单占据剩余空间 */\r\n}\r\n\r\n.status-indicator {\r\n  margin-left: auto; /* 将状态指示器推到右侧 */\r\n}\r\n\r\n// 添加自定义样式，减少菜单下拉框与菜单项之间的间距\r\n:deep(.el-menu--horizontal) {\r\n  .el-sub-menu .el-menu--popup {\r\n    margin-top: 0;\r\n  }\r\n\r\n  // 二级菜单项左对齐\r\n  .el-menu-item,\r\n  .el-sub-menu__title {\r\n    text-align: left;\r\n    justify-content: flex-start;\r\n  }\r\n}\r\n\r\n:deep(.el-menu-item) {\r\n  user-select: none;\r\n}\r\n\r\n// 修改关于菜单项样式，确保正确显示\r\n.about-menu-item {\r\n  min-width: unset !important;\r\n  padding: 0 20px !important;\r\n  height: 40px !important;\r\n  line-height: 40px !important;\r\n  overflow: visible !important;\r\n}\r\n\r\n.about-text {\r\n  white-space: nowrap !important;\r\n  display: inline-block;\r\n  font-size: 14px;\r\n}\r\n\r\n// 保留图标样式，以防将来需要添加其他图标\r\n.el-icon {\r\n  margin-right: 5px;\r\n  vertical-align: middle;\r\n}\r\n\r\n.home-menu-item {\r\n  padding: 0 10px;\r\n\r\n  .el-icon {\r\n    font-size: 16px;\r\n    margin-right: 0;\r\n    color: var(--el-color-primary);\r\n  }\r\n}\r\n\r\n.recent-submenu {\r\n  min-width: 300px;\r\n}\r\n\r\n.empty-recent {\r\n  padding: 12px 20px;\r\n  color: var(--el-text-color-secondary);\r\n  font-size: 14px;\r\n}\r\n\r\n.recent-item {\r\n  display: flex;\r\n  padding: 8px 16px;\r\n  flex-direction: row;\r\n  gap: 10px;\r\n\r\n  .recent-plan-name {\r\n    font-size: 12px;\r\n  }\r\n\r\n  .recent-plan-path {\r\n    font-size: 12px;\r\n    color: var(--el-text-color-secondary);\r\n    white-space: nowrap;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n  }\r\n}\r\n\r\n.test-suite-menu-item {\r\n  min-width: unset !important;\r\n  padding: 0 20px !important;\r\n  height: 40px !important;\r\n  line-height: 40px !important;\r\n}\r\n\r\n.test-suite-text {\r\n  white-space: nowrap !important;\r\n  display: inline-block;\r\n  font-size: 14px;\r\n}\r\n</style>\r\n", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\components\\layout\\TestPlanStatusIndicator.vue", ["269"], "<template>\n  <div v-if=\"hasOpenTestPlan\" class=\"test-plan-indicator\">\n    <div class=\"plan-info\">\n      <el-icon><Document /></el-icon>\n      <span class=\"plan-name\" :title=\"currentPlan.path\">{{ currentPlan.manifest.name }}</span>\n    </div>\n    <div class=\"plan-actions\">\n      <el-button\n        v-if=\"!isOnTestPlanPage\"\n        type=\"primary\"\n        size=\"small\"\n        @click=\"handleReturnToPlan\"\n        :loading=\"isNavigating\"\n      >\n        <el-icon><Back /></el-icon>\n        Back\n      </el-button>\n      <el-button\n        type=\"danger\"\n        size=\"small\"\n        @click=\"handleClosePlan\"\n        :loading=\"isClosing\"\n      >\n        <el-icon><Close /></el-icon>\n        Close\n      </el-button>\n    </div>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport { computed, ref } from 'vue';\nimport { useRouter, useRoute } from 'vue-router';\nimport { ElMessageBox, ElMessage } from 'element-plus';\nimport { Document, Back, Close } from '@element-plus/icons-vue';\nimport { testPlanService } from '@/services/testPlanService';\n\nconst router = useRouter();\nconst route = useRoute();\nconst state = testPlanService.getState();\nconst isNavigating = ref(false);\nconst isClosing = ref(false);\n\n// Check if currently on test plan page\nconst isOnTestPlanPage = computed(() => route.path.startsWith('/test-plan'));\n\n// Check if a test plan is currently open\nconst hasOpenTestPlan = computed(() => !!state.currentPlan);\nconst currentPlan = computed(() => state.currentPlan);\n\n// Return to test plan page\nconst handleReturnToPlan = async () => {\n  if (!hasOpenTestPlan.value) return;\n\n  isNavigating.value = true;\n  try {\n    await router.push('/test-plan');\n  } catch (error) {\n    console.error('Failed to navigate to test plan page:', error);\n    ElMessage.error('Failed to return to test plan');\n  } finally {\n    isNavigating.value = false;\n  }\n};\n\n// 关闭测试计划\nconst handleClosePlan = async () => {\n  if (!hasOpenTestPlan.value) return;\n\n  try {\n    const result = await ElMessageBox.confirm(\n      'Are you sure you want to close the current test plan? Unsaved changes may be lost.',\n      'Close Test Plan',\n      {\n        confirmButtonText: 'OK',\n        cancelButtonText: 'Cancel',\n        type: 'warning'\n      }\n    );\n\n    if (result === 'confirm') {\n      isClosing.value = true;\n      await testPlanService.closeTestPlan();\n    }\n  } catch (error) {\n    // User canceled the operation, do nothing\n  } finally {\n    isClosing.value = false;\n  }\n};\n</script>\n\n<style scoped>\n.test-plan-indicator {\n  display: flex;\n  align-items: center;\n  padding: 0 15px;\n  margin-left: 10px;\n  height: 40px;\n  border-left: 1px solid var(--el-border-color-light);\n  border-bottom: 1px solid var(--el-menu-border-color);\n}\n\n.plan-info {\n  display: flex;\n  align-items: center;\n  margin-right: 15px;\n  color: var(--el-text-color-secondary);\n}\n\n.plan-name {\n  margin-left: 5px;\n  font-weight: 500;\n  max-width: 150px;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  color: var(--el-text-color-primary);\n}\n\n.plan-actions {\n  display: flex;\n  gap: 8px;\n}\n</style>\n", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\components\\TestPlan\\CreateTestPlan.vue", ["270"], "<template>\r\n  <el-dialog v-model=\"dialogVisible\" title=\"Create Test Plan\" width=\"500px\">\r\n    <el-form :model=\"form\" label-width=\"90px\">\r\n      <el-form-item label=\"Name\" required>\r\n        <el-input v-model=\"form.name\" placeholder=\"Enter test plan name\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"Folder\" required>\r\n        <div class=\"folder-input\">\r\n          <el-input v-model=\"form.folder\" placeholder=\"Select save folder\" />\r\n          <el-button @click=\"handleSelectFolder\">Browse</el-button>\r\n        </div>\r\n      </el-form-item>\r\n      <el-form-item label=\"Description\">\r\n        <el-input v-model=\"form.description\" type=\"textarea\" placeholder=\"Enter test plan description\" />\r\n      </el-form-item>\r\n    </el-form>\r\n    <template #footer>\r\n      <span>\r\n        <el-button @click=\"close\">Cancel</el-button>\r\n        <el-button type=\"primary\" @click=\"handleCreateTestPlan\">\r\n          Create\r\n        </el-button>\r\n      </span>\r\n    </template>\r\n  </el-dialog>\r\n</template>\r\n\r\n<script lang=\"ts\">\r\nimport { defineComponent, ref, reactive } from 'vue';\r\nimport { ElMessage } from 'element-plus';\r\nimport { testPlanApi } from '@/api';\r\nimport { explorerApi } from '@/api/explorerApi';\r\n\r\nexport default defineComponent({\r\n  name: 'CreateTestPlan',\r\n  emits: ['created'],\r\n  setup(props, { emit }) {\r\n    const dialogVisible = ref(false);\r\n    const form = reactive({\r\n      name: '',\r\n      description: '',\r\n      folder: ''\r\n    });\r\n\r\n    const handleSelectFolder = async () => {\r\n      try {\r\n        const response = await explorerApi.selectFolder();\r\n        if (response.data) {\r\n          form.folder = response.data;\r\n        }\r\n      } catch (error: any) {\r\n        if (error.response?.status === 400 && error.response.data === \"UserCanceled\") {\r\n          // 用户取消选择，不需要提示\r\n          return;\r\n        }\r\n        ElMessage.error(\"Failed to select folder\");\r\n      }\r\n    };\r\n\r\n    const handleCreateTestPlan = async () => {\r\n      if (!form.name) {\r\n        ElMessage.warning(\"Please enter test plan name\");\r\n        return;\r\n      }\r\n\r\n      if (!form.folder) {\r\n        ElMessage.warning(\"Please select save folder\");\r\n        return;\r\n      }\r\n\r\n      const response = await testPlanApi.create({\r\n        name: form.name,\r\n        description: form.description,\r\n        folder: form.folder\r\n      });\r\n\r\n      if (response.data) {\r\n        close();\r\n        emit('created', response.data);\r\n      }\r\n    };\r\n\r\n    const close = () => {\r\n      dialogVisible.value = false;\r\n      form.name = '';\r\n      form.description = '';\r\n      form.folder = '';\r\n    };\r\n\r\n    const show = () => {\r\n      dialogVisible.value = true;\r\n    };\r\n\r\n    return {\r\n      dialogVisible,\r\n      form,\r\n      handleCreateTestPlan,\r\n      handleSelectFolder,\r\n      close,\r\n      show\r\n    };\r\n  }\r\n});\r\n</script>\r\n\r\n<style scoped>\r\n.folder-input {\r\n  display: flex;\r\n  gap: 8px;\r\n  width: 100%;\r\n  /* 确保容器占满整个宽度 */\r\n}\r\n\r\n.folder-input .el-input {\r\n  flex: 1;\r\n  /* 让输入框占据除按钮外的所有空间 */\r\n}\r\n\r\n:deep(.el-form-item__content) {\r\n  width: 100%;\r\n  /* 确保表单项内容区域占满整个宽度 */\r\n}\r\n</style>\r\n", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\api\\testPlanApi.ts", ["271", "272", "273"], "import axios, { AxiosResponse } from 'axios'\r\nimport { USE_MOCK, mockApi } from '@/mock/mockApi'\r\nimport { TestPlanHistory } from '@/api/testPlanHistoryApi';\r\n\r\n// 定义测试计划相关的类型\r\nexport interface TestPlanManifest {\r\n  name: string;\r\n  description: string;\r\n  created?: string;\r\n  modified?: string;\r\n}\r\n\r\nexport interface TestPlan {\r\n  path: string;\r\n  manifest: TestPlanManifest;\r\n  config?: any;\r\n  [key: string]: any;\r\n}\r\n\r\n// 调整TestPlanHistoryItem类型，与TestPlanHistory保持一致\r\nexport interface TestPlanHistoryItem {\r\n  id: string;\r\n  filePath: string; // 与path同义\r\n  planName: string;\r\n  lastAccessTime: string; // 与lastAccessed同义\r\n  lastModified: string;\r\n  isDeleted: boolean;\r\n}\r\n\r\nconst BASE_URL = '/api/testPlan'\r\n\r\n// 类型转换辅助函数，将TestPlanHistory转换为TestPlanHistoryItem\r\nconst convertToHistoryItems = (histories: TestPlanHistory[]): TestPlanHistoryItem[] => {\r\n  return histories.map(history => ({\r\n    id: history.id,\r\n    filePath: history.filePath,\r\n    planName: history.planName,\r\n    lastAccessTime: history.lastAccessTime,\r\n    lastModified: history.lastModified,\r\n    isDeleted: history.isDeleted\r\n  }));\r\n};\r\n\r\nexport const testPlanApi = {\r\n  // 在文件浏览器中打开测试计划\r\n  openInExplorer: (): Promise<AxiosResponse<TestPlan>> => {\r\n    if (USE_MOCK) {\r\n      return mockApi.testPlan.openInExplorer();\r\n    }\r\n    return axios.get(`${BASE_URL}/OpenInExplorer`);\r\n  },\r\n\r\n  // 从指定路径打开测试计划\r\n  open: (path: string): Promise<AxiosResponse<TestPlan>> => {\r\n    if (USE_MOCK) {\r\n      return mockApi.testPlan.open(path);\r\n    }\r\n    return axios.post(`${BASE_URL}/open`, { path });\r\n  },\r\n\r\n  // 创建测试计划\r\n  create: (testPlanData: { name: string; description: string; folder: string }): Promise<AxiosResponse<TestPlan>> => {\r\n    if (USE_MOCK) {\r\n      return mockApi.testPlan.create(testPlanData);\r\n    }\r\n    return axios.post(`${BASE_URL}/create`, testPlanData);\r\n  },\r\n\r\n  // 获取测试计划历史记录 - 修改调用到历史API\r\n  getHistory: (): Promise<AxiosResponse<TestPlanHistoryItem[]>> => {\r\n    if (USE_MOCK) {\r\n      // 改为调用历史API而不是测试计划API\r\n      return mockApi.testPlanHistory.get().then(response => {\r\n        const convertedData = convertToHistoryItems(response.data);\r\n        return {\r\n          ...response,\r\n          data: convertedData\r\n        };\r\n      });\r\n    }\r\n    return axios.get(`${BASE_URL}/history`);\r\n  },\r\n\r\n  // 获取当前测试计划\r\n  getCurrentPlan: (): Promise<AxiosResponse<TestPlan>> => {\r\n    if (USE_MOCK) {\r\n      return mockApi.testPlan.getCurrentPlan();\r\n    }\r\n    return axios.get(`${BASE_URL}/current`);\r\n  },\r\n\r\n  // 关闭测试计划\r\n  close: (): Promise<AxiosResponse<void>> => {\r\n    if (USE_MOCK) {\r\n      return mockApi.testPlan.close();\r\n    }\r\n    return axios.post(`${BASE_URL}/close`);\r\n  },\r\n\r\n  // 更新测试计划基本信息\r\n  updateBasicInfo: (description: string): Promise<AxiosResponse<TestPlan>> => {\r\n    if (USE_MOCK) {\r\n      return mockApi.testPlan.updateBasicInfo(description);\r\n    }\r\n    return axios.post(`${BASE_URL}/updateBasicInfo`, { description });\r\n  },\r\n\r\n  // 检查文件是否存在\r\n  checkFileExists: (path: string): Promise<AxiosResponse<{exists: boolean}>> => {\r\n    if (USE_MOCK) {\r\n      // 模拟实现，可以根据需要调整\r\n      if (mockApi.testPlan.checkFileExists) {\r\n        return mockApi.testPlan.checkFileExists(path);\r\n      } else {\r\n        // 创建一个符合AxiosResponse类型的对象\r\n        return Promise.resolve({\r\n          data: { exists: true },\r\n          status: 200,\r\n          statusText: 'OK',\r\n          headers: {},\r\n          config: { headers: {} } as any\r\n        });\r\n      }\r\n    }\r\n    return axios.post(`${BASE_URL}/checkFileExists`, { path });\r\n  }\r\n}\r\n\r\nexport default testPlanApi\r\n", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\api\\testPlanHistoryApi.ts", ["274"], "import axios, { AxiosResponse } from 'axios'\r\nimport { USE_MOCK, mockApi } from '@/mock/mockApi'\r\n\r\n// 定义历史记录类型\r\nexport interface TestPlanHistory {\r\n  id: string;\r\n  filePath: string;\r\n  planName: string;\r\n  lastAccessTime: string;\r\n  lastModified: string;\r\n  isDeleted: boolean;\r\n}\r\n\r\nconst BASE_URL = '/api/testPlanHistory'\r\n\r\nexport const testPlanHistoryApi = {\r\n  // 获取测试计划历史记录\r\n  get: (): Promise<AxiosResponse<TestPlanHistory[]>> => {\r\n    if (USE_MOCK) {\r\n      return mockApi.testPlanHistory.get();\r\n    }\r\n    return axios.get(`${BASE_URL}/get`);\r\n  },\r\n\r\n  // 清空历史记录\r\n  clear: (): Promise<AxiosResponse<void>> => {\r\n    if (USE_MOCK) {\r\n      return mockApi.testPlanHistory.clear();\r\n    }\r\n    return axios.delete(`${BASE_URL}/clear`);\r\n  },\r\n\r\n  // 删除单个历史记录\r\n  deleteRecord: (filePath: string): Promise<AxiosResponse<void>> => {\r\n    if (USE_MOCK) {\r\n      // 模拟实现，可以根据需要调整\r\n      if (mockApi.testPlanHistory.deleteRecord) {\r\n        return mockApi.testPlanHistory.deleteRecord(filePath);\r\n      } else {\r\n        // 创建一个符合AxiosResponse类型的对象\r\n        return Promise.resolve({\r\n          data: undefined,\r\n          status: 200,\r\n          statusText: 'OK',\r\n          headers: {},\r\n          config: { headers: {} } as any\r\n        });\r\n      }\r\n    }\r\n    return axios.delete(`${BASE_URL}/deleteRecord`, { data: { filePath } });\r\n  }\r\n}\r\n\r\nexport default testPlanHistoryApi\r\n", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\services\\testPlanService.ts", ["275", "276", "277", "278", "279", "280", "281", "282"], "import { reactive, readonly } from 'vue';\r\nimport { ElMessage } from 'element-plus';\r\nimport { Router } from 'vue-router';\r\nimport { testPlanApi, type TestPlan } from '@/api/testPlanApi';\r\nimport { testPlanHistoryApi, type TestPlanHistory } from '@/api/testPlanHistoryApi';\r\n\r\n// 定义状态接口\r\ninterface TestPlanState {\r\n  currentPlan: TestPlan | null;\r\n  recentPlans: TestPlanHistory[];\r\n  isLoading: boolean;\r\n}\r\n\r\n// 创建内部状态\r\nconst state = reactive<TestPlanState>({\r\n  currentPlan: null,\r\n  recentPlans: [],\r\n  isLoading: false\r\n});\r\n\r\n// 测试计划服务类\r\nclass TestPlanServiceClass {\r\n  private router: Router | null = null;\r\n\r\n  // 初始化路由\r\n  setRouter(router: Router) {\r\n    this.router = router;\r\n  }\r\n\r\n  // 获取只读状态\r\n  getState() {\r\n    return readonly(state);\r\n  }\r\n\r\n  // 打开文件浏览器选择测试计划\r\n  async openFromExplorer() {\r\n    state.isLoading = true;\r\n    try {\r\n      const response = await testPlanApi.openInExplorer();\r\n      if (response.data) {\r\n        state.currentPlan = response.data;\r\n        ElMessage.success(\"Test plan opened successfully\");\r\n        this.navigateToTestPlan();\r\n        await this.loadRecentPlans();\r\n        return response.data;\r\n      }\r\n    } finally {\r\n      state.isLoading = false;\r\n    }\r\n    return null;\r\n  }\r\n\r\n  // 从历史记录中打开测试计划\r\n  async openFromPath(path: string) {\r\n    state.isLoading = true;\r\n    try {\r\n      const response = await testPlanApi.open(path);\r\n      if (response.data) {\r\n        state.currentPlan = response.data;\r\n        ElMessage.success(\"Test plan opened successfully\");\r\n        this.navigateToTestPlan();\r\n        await this.loadRecentPlans();\r\n        return response.data;\r\n      }\r\n    } finally {\r\n      state.isLoading = false;\r\n    }\r\n    return null;\r\n  }\r\n\r\n  // 创建新的测试计划\r\n  async createTestPlan(testPlanData: { name: string; description: string; folder: string }) {\r\n    state.isLoading = true;\r\n    try {\r\n      const response = await testPlanApi.create(testPlanData);\r\n      if (response.data) {\r\n        state.currentPlan = response.data;\r\n        ElMessage.success(\"Test plan created successfully\");\r\n        this.navigateToTestPlan();\r\n        await this.loadRecentPlans();\r\n        return response.data;\r\n      }\r\n    } catch (error: any) {\r\n      ElMessage.error(\"Failed to create test plan\");\r\n      console.error(error);\r\n    } finally {\r\n      state.isLoading = false;\r\n    }\r\n    return null;\r\n  }\r\n\r\n  // 关闭测试计划\r\n  async closeTestPlan() {\r\n    try {\r\n      await testPlanApi.close();\r\n      state.currentPlan = null;\r\n      ElMessage.success(\"Test plan closed successfully\");\r\n      if (this.router) {\r\n        this.router.push('/');\r\n      }\r\n    } catch (error: any) {\r\n      ElMessage.error(\"Failed to close test plan\");\r\n      console.error(error);\r\n    }\r\n  }\r\n\r\n  // 加载最近的测试计划\r\n  async loadRecentPlans() {\r\n    try {\r\n      const response = await testPlanHistoryApi.get();\r\n      state.recentPlans = response.data;\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Failed to load recent plans:', error);\r\n      return [];\r\n    }\r\n  }\r\n\r\n  // 清空历史记录\r\n  async clearHistory() {\r\n    try {\r\n      await testPlanHistoryApi.clear();\r\n      state.recentPlans = [];\r\n      ElMessage.success(\"History cleared\");\r\n      return true;\r\n    } catch (error) {\r\n      ElMessage.error(\"Failed to clear history\");\r\n      console.error(error);\r\n      return false;\r\n    }\r\n  }\r\n\r\n  // 更新测试计划基本信息\r\n  async updateBasicInfo(description: string) {\r\n    state.isLoading = true;\r\n    try {\r\n      const response = await testPlanApi.updateBasicInfo(description);\r\n      if (response.data) {\r\n        state.currentPlan = response.data;\r\n        ElMessage.success(\"Description updated successfully\");\r\n        return response.data;\r\n      }\r\n    } catch (error) {\r\n      ElMessage.error(\"Failed to update description\");\r\n      console.error('Error updating description:', error);\r\n    } finally {\r\n      state.isLoading = false;\r\n    }\r\n    return null;\r\n  }\r\n\r\n  // 获取当前测试计划\r\n  async getCurrentPlan() {\r\n    if (state.currentPlan) {\r\n      return state.currentPlan;\r\n    }\r\n\r\n    state.isLoading = true;\r\n    try {\r\n      const response = await testPlanApi.getCurrentPlan();\r\n      state.currentPlan = response.data;\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Error loading current plan:', error);\r\n      return null;\r\n    } finally {\r\n      state.isLoading = false;\r\n    }\r\n  }\r\n\r\n  // 导航到测试计划页面\r\n  private navigateToTestPlan() {\r\n    if (this.router) {\r\n      this.router.push({ name: 'test-plan.basic-setting' });\r\n    }\r\n  }\r\n}\r\n\r\n// 创建服务实例\r\nexport const testPlanService = new TestPlanServiceClass();\r\n", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\utils\\errorHandler.ts", ["283", "284", "285"], "import axios from 'axios';\nimport { ElMessage, ElMessageBox } from 'element-plus';\n\n// 格式化错误信息，显示所有层级的异常\nconst formatErrorMessage = (error: any): string => {\n  if (!error.response || !error.response.data) {\n    return error.message || 'Unknown error';\n  }\n\n  const errorData = error.response.data;\n  const errorMessages = [];\n\n  // 添加主异常信息\n  if (errorData.exceptionMessage) {\n    errorMessages.push(errorData.exceptionMessage);\n  }\n\n  // 递归添加所有内部异常信息\n  let currentException = errorData.innerException;\n  while (currentException) {\n    if (currentException.exceptionMessage) {\n      errorMessages.push(currentException.exceptionMessage);\n    }\n    currentException = currentException.innerException;\n  }\n\n  // 如果没有找到任何异常信息，返回通用错误消息\n  if (errorMessages.length === 0) {\n    return errorData.message || 'An error occurred';\n  }\n\n  // 返回所有异常信息，每个一行\n  return errorMessages.join('<br>');\n};\n\n// 显示详细错误信息\nconst showDetailedError = (error: any): void => {\n  if (!error.response || !error.response.data) {\n    ElMessage.error(error.message || 'Unknown error');\n    return;\n  }\n\n  // 获取格式化的错误信息\n  const errorMessage = formatErrorMessage(error);\n\n  // 使用对话框显示详细错误信息\n  ElMessageBox.alert(\n    errorMessage,\n    'Error',\n    {\n      confirmButtonText: 'OK',\n      dangerouslyUseHTMLString: true,\n      closeOnClickModal: true,  // 允许点击空白区域关闭\n      closeOnPressEscape: true, // 允许按ESC键关闭\n      showClose: true           // 显示右上角关闭按钮\n    }\n  );\n};\n\n// 检查是否为用户取消操作\nconst isUserCanceled = (error: any): boolean => {\n  // 检查错误响应数据\n  if (error.response && error.response.data) {\n    // 检查直接等于字符串的情况\n    if (error.response.data === 'UserCanceled') {\n      return true;\n    }\n\n    // 检查错误消息字段\n    if (error.response.data.message === 'UserCanceled') {\n      return true;\n    }\n\n    // 检查错误代码字段\n    if (error.response.data.errorCode === 'UserCanceled') {\n      return true;\n    }\n  }\n\n  return false;\n};\n\n// 设置响应拦截器\nexport const setupErrorHandler = (): void => {\n  axios.interceptors.response.use(\n    response => response,\n    error => {\n      // 检查是否为用户取消操作\n      if (isUserCanceled(error)) {\n        // 用户取消操作，显示信息提示而不是错误\n        ElMessage.info(\"Operation cancelled by user\");\n\n        // 继续抛出错误，以便调用者可以进行额外处理\n        return Promise.reject(error);\n      }\n\n      // 处理其他错误\n      showDetailedError(error);\n\n      // 继续抛出错误，以便调用者可以进行额外处理\n      return Promise.reject(error);\n    }\n  );\n};\n\nexport default setupErrorHandler;\n", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\mock\\modules\\mockTestPlanHistoryApi.ts", [], "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\mock\\modules\\mockTestPlanApi.ts", ["286", "287", "288"], "import { TestPlan } from '@/api/testPlanApi';\r\nimport { mockSuccess, mockError, generateId } from '../mockApi';\r\nimport { testPlans, testPlanHistoryItems } from '../mockData';\r\nimport { AxiosResponse } from 'axios';\r\n\r\n// 添加当前测试计划的状态管理\r\nlet currentTestPlan: TestPlan | null = null;\r\n\r\n// 测试计划 API 模拟实现\r\nexport const mockTestPlanApi = {\r\n  // 在文件浏览器中打开测试计划\r\n  openInExplorer: (): Promise<AxiosResponse<TestPlan>> => {\r\n    // 随机选择一个测试计划\r\n    const randomIndex = Math.floor(Math.random() * testPlans.length);\r\n    const selectedPlan = testPlans[randomIndex];\r\n    currentTestPlan = selectedPlan; // 设置当前测试计划\r\n\r\n    // 记录历史记录\r\n    updateTestPlanHistory(selectedPlan.path, selectedPlan.name);\r\n    return mockSuccess(selectedPlan);\r\n  },\r\n\r\n  // 从指定路径打开测试计划\r\n  open: (path: string): Promise<AxiosResponse<TestPlan>> => {\r\n    // 检查路径是否存在于历史记录中\r\n    const historyItem = testPlanHistoryItems.find(item => item.filePath === path);\r\n    let plan: TestPlan;\r\n\r\n    // 如果没找到匹配的历史记录，返回第一个测试计划\r\n    if (!historyItem) {\r\n      plan = testPlans[0];\r\n      // 添加新的历史记录\r\n      updateTestPlanHistory(path, plan.name);\r\n    } else {\r\n      // 查找匹配的测试计划\r\n      const matchingPlan = testPlans.find(p => p.manifest.name === historyItem.planName);\r\n      plan = matchingPlan || testPlans[0];\r\n      // 更新访问时间\r\n      updateTestPlanHistory(path, matchingPlan?.manifest.name || \"\");\r\n    }\r\n\r\n    currentTestPlan = plan; // 设置当前测试计划\r\n    return mockSuccess(plan);\r\n  },\r\n\r\n  // 创建测试计划\r\n  create: (data: any): Promise<AxiosResponse<TestPlan>> => {\r\n    // 创建新的测试计划\r\n    const now = new Date().toISOString();\r\n\r\n    // 创建文件路径\r\n    const filePath = `D:\\\\TestPlans\\\\${data.name.replace(/\\s+/g, '_').toLowerCase()}.fzp`;\r\n\r\n    const newPlan: TestPlan = {\r\n      path: filePath,\r\n      manifest: {\r\n        name: data.name,\r\n        description: data.description,\r\n        created: now,\r\n        modified: now\r\n      },\r\n      config: {\r\n        targetDevice: \"Default\",\r\n        protocol: \"CAN\",\r\n        testCases: []\r\n      }\r\n    };\r\n\r\n    // 保存到内存中\r\n    testPlans.push(newPlan);\r\n\r\n    // 添加到历史记录\r\n    updateTestPlanHistory(filePath, data.name);\r\n\r\n    currentTestPlan = newPlan;\r\n    // 返回创建结果\r\n    return mockSuccess(newPlan);\r\n  },\r\n\r\n  // 关闭测试计划\r\n  close: (): Promise<AxiosResponse<void>> => {\r\n    currentTestPlan = null; // 清除当前测试计划\r\n    console.log(\"模拟关闭测试计划\");\r\n    return mockSuccess(undefined);\r\n  },\r\n\r\n  // 获取当前测试计划\r\n  getCurrentPlan: (): Promise<AxiosResponse<TestPlan>> => {\r\n    if (!currentTestPlan) {\r\n      return mockError(404, \"No test plan is currently open\");\r\n    }\r\n    return mockSuccess(currentTestPlan);\r\n  },\r\n\r\n  // 更新测试计划基本信息\r\n  updateBasicInfo: (description: string): Promise<AxiosResponse<TestPlan>> => {\r\n    if (!currentTestPlan) {\r\n      return mockError(404, \"No test plan is currently open\");\r\n    }\r\n\r\n    // 更新描述和修改时间\r\n    currentTestPlan.manifest.description = description;\r\n    currentTestPlan.manifest.modified = new Date().toISOString();\r\n\r\n    // 如果有对应的历史记录，也更新历史记录的修改时间\r\n    const historyItem = testPlanHistoryItems.find(item => item.filePath === currentTestPlan?.path);\r\n    if (historyItem) {\r\n      historyItem.lastModified = new Date().toISOString();\r\n    }\r\n\r\n    return mockSuccess(currentTestPlan);\r\n  },\r\n\r\n  // 检查文件是否存在\r\n  checkFileExists: (path: string): Promise<AxiosResponse<{exists: boolean}>> => {\r\n    // 模拟实现：随机返回文件存在或不存在\r\n    // 在实际应用中，可以根据需要调整逻辑\r\n    // 这里我们假设90%的文件存在，10%的文件不存在\r\n    const exists = Math.random() > 0.1;\r\n\r\n    // 也可以根据特定路径模拟不同的结果\r\n    // 例如：特定路径总是返回不存在\r\n    // if (path.includes('nonexistent')) {\r\n    //   exists = false;\r\n    // }\r\n\r\n    return mockSuccess({ exists });\r\n  }\r\n};\r\n\r\n// 辅助函数：更新测试计划历史记录\r\nfunction updateTestPlanHistory(filePath: string, planName: string): void {\r\n  const now = new Date().toISOString();\r\n  const existingItem = testPlanHistoryItems.find(item => item.filePath === filePath);\r\n\r\n  if (existingItem) {\r\n    // 更新现有记录\r\n    existingItem.lastAccessTime = now;\r\n    existingItem.lastModified = now;\r\n    existingItem.isDeleted = false;\r\n  } else {\r\n    // 添加新记录\r\n    testPlanHistoryItems.push({\r\n      id: generateId(),\r\n      filePath,\r\n      planName,\r\n      lastAccessTime: now,\r\n      lastModified: now,\r\n      isDeleted: false\r\n    });\r\n  }\r\n\r\n  // 重新排序历史记录，按最后访问时间降序排序\r\n  testPlanHistoryItems.sort((a, b) => {\r\n    if (a.isDeleted && !b.isDeleted) return 1;  // 已删除的放后面\r\n    if (!a.isDeleted && b.isDeleted) return -1; // 未删除的放前面\r\n    // 都是未删除或都是已删除的情况下，按最后访问时间降序排序\r\n    return new Date(b.lastAccessTime).getTime() - new Date(a.lastAccessTime).getTime();\r\n  });\r\n}\r\n", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\api\\explorerApi.ts", [], "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\mock\\mockData.ts", [], "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\mock\\modules\\mockAppApi.ts", ["289", "290", "291", "292", "293", "294", "295", "296", "297", "298"], "import { \r\n  ErrorData, \r\n  AppInfo, \r\n  TesterSnapshot, \r\n  TestResult, \r\n  ExecutionState, \r\n  CaseStep, \r\n  isTesterCompleted,\r\n  PagedQuery,          // 添加这两个导入\r\n  TesterSnapshotResponse,\r\n  PagedResult\r\n} from '@/api/appApi';\r\nimport { mockSuccess } from '../mockApi';\r\nimport { errorLogs } from '../mockData';\r\nimport { AxiosResponse } from 'axios';\r\nimport { CaseResult, ExecutionStateString } from '@/api/interoperationApi';\r\nimport { CoverageType, GenerateCasesRequest } from '@/api/appApi';\r\n\r\n// 生成模拟的互操作测试结果\r\nfunction generateMockInteroperationResults(): CaseResult[] {\r\n  const results: CaseResult[] = [];\r\n\r\n  // 预定义的序列名称数组 - 确保唯一性\r\n  const uniqueSequences = [\r\n    'DiagnosticSessionControl',\r\n    'ECUReset',\r\n    'SecurityAccess',\r\n    'CommunicationControl',\r\n    'ReadDataByIdentifier',\r\n    'WriteDataByIdentifier',\r\n    'ClearDiagnosticInformation',\r\n    'ReadDTCInformation',\r\n    'InputOutputControlByIdentifier',\r\n    'RoutineControl',\r\n    'RequestDownload',\r\n    'RequestUpload',\r\n    'TransferData',\r\n    'RequestTransferExit',\r\n    'AccessTimingParameter',\r\n    'SecuredDataTransmission',\r\n    'ControlDTCSetting',\r\n    'ResponseOnEvent',\r\n    'LinkControl',\r\n    'can-frames'\r\n  ];\r\n\r\n  // 生成随机的执行状态分布\r\n  const successCount = Math.floor(Math.random() * 10) + 5; // 5-15个成功结果\r\n\r\n  // 随机选择一些序列作为测试结果\r\n  const selectedSequences = [...uniqueSequences]\r\n    .sort(() => Math.random() - 0.5) // 随机打乱顺序\r\n    .slice(0, successCount + 5); // 多取5个以便有一些非成功状态的结果\r\n\r\n  // 为每个选定的序列创建一个结果\r\n  selectedSequences.forEach((sequenceName, index) => {\r\n    // 决定状态 - 前successCount个为Success，其余随机\r\n    let state: ExecutionStateString;\r\n    if (index < successCount) {\r\n      state = 'Success';\r\n    } else {\r\n      // 随机选择非Success状态\r\n      const otherStates: ExecutionStateString[] = ['Pending', 'Running', 'Failure'];\r\n      state = otherStates[Math.floor(Math.random() * otherStates.length)];\r\n    }\r\n\r\n    // 创建结果对象\r\n    const begin = new Date(Date.now() - Math.random() * 60000);\r\n    const end = new Date(begin.getTime() + Math.random() * 30000);\r\n\r\n    results.push({\r\n      id: index + 1,\r\n      testResultId: `test-${Math.random().toString(36).substring(2)}`,\r\n      sequenceId: `seq-${Math.random().toString(36).substring(2)}`,\r\n      sequenceName: sequenceName, // 使用唯一的序列名称\r\n      parameter: `param-${index}`,\r\n      name: `name-${index}`,\r\n      state: state,\r\n      begin: begin.toISOString(),\r\n      end: end.toISOString(),\r\n      detail: state === 'Success' ? 'Test passed' : `Test ${state.toLowerCase()}`\r\n    });\r\n  });\r\n\r\n  return results;\r\n}\r\n\r\n// 生成模拟的测试用例\r\nfunction generateMockTestCases(coverage: CoverageType, sequenceNames: string[]): CaseResult[] {\r\n  const results: CaseResult[] = [];\r\n  // 提高用例数量 - High覆盖模式生成100000个用例，Normal模式生成5000个\r\n  const caseCount = (coverage === CoverageType.High ? 100000 : 5000) / 5;\r\n\r\n  // 定义一些可能的前缀组\r\n  const prefixGroups = ['G100', 'G200', 'G300', 'G400', 'G500'];\r\n\r\n  // 仅为选定的序列生成测试用例\r\n  for (const sequenceName of sequenceNames) {\r\n    // 为每个选定的序列生成测试用例\r\n    for (let i = 0; i < caseCount; i++) {\r\n      // 随机选择一个前缀组\r\n      const prefixIndex = Math.floor(Math.random() * prefixGroups.length);\r\n      const prefix = prefixGroups[prefixIndex];\r\n\r\n      results.push({\r\n        id: results.length + 1,\r\n        testResultId: `test-${Math.random().toString(36).substring(2)}`,\r\n        sequenceId: `seq-${Math.random().toString(36).substring(2)}`,\r\n        sequenceName: sequenceName,\r\n        parameter: `id=0x${(Math.floor(Math.random() * 4095) + 1).toString(16).padStart(3, '0')};dlc=8;data=hex:${Array(16).fill(0).map(() => Math.floor(Math.random() * 255).toString(16).padStart(2, '0')).join('')}`,\r\n        state: 'Pending',\r\n        begin: null,\r\n        end: null,\r\n        detail: '',\r\n        name: `${prefix}-TestCase${i + 1}`\r\n      });\r\n    }\r\n  }\r\n\r\n  return results;\r\n}\r\n\r\n// 生成模拟的测试结果列表\r\nfunction generateMockTestResults(): TestResult[] {\r\n  const results: TestResult[] = [];\r\n\r\n  // 生成5个模拟测试结果\r\n  for (let i = 0; i < 5; i++) {\r\n    const creationTime = new Date(Date.now() - i * 86400000); // 每个相差一天\r\n    const totalCount = 10 + Math.floor(Math.random() * 20);\r\n    const successCount = Math.floor(totalCount * (0.7 + Math.random() * 0.2)); // 70-90% 成功率\r\n    const failureCount = totalCount - successCount;\r\n\r\n    results.push({\r\n      id: `test-result-${i}-${Date.now()}`,\r\n      resultFolderName: `测试运行 ${i + 1}`,\r\n      testType: 'Case',\r\n      creationTime: creationTime.toISOString(),\r\n      completionTime: new Date(creationTime.getTime() + 3600000).toISOString(), // 一小时后完成\r\n      totalCount,\r\n      successCount,\r\n      failureCount\r\n    });\r\n  }\r\n\r\n  return results;\r\n}\r\n\r\n// 生成模拟的用例步骤\r\nfunction generateMockCaseSteps(caseResultId: number): CaseStep[] {\r\n  const steps: CaseStep[] = [];\r\n  const stepCount = 5 + Math.floor(Math.random() * 10); // 5-15个步骤\r\n\r\n  for (let i = 0; i < stepCount; i++) {\r\n    const timestamp = Date.now() - (stepCount - i) * 1000; // 每步相差1秒\r\n    const stateOptions = ['Success', 'Failure'];\r\n    const state = Math.random() > 0.2 ? stateOptions[0] : stateOptions[1]; // 80%成功率\r\n\r\n    steps.push({\r\n      id: i + 1,\r\n      name: `Step ${i + 1}`,\r\n      caseResultId: caseResultId,\r\n      timestamp: timestamp,\r\n      frameTimestamp: timestamp,\r\n      state: state,\r\n      begin: new Date(timestamp - 500).toISOString(),\r\n      end: new Date(timestamp).toISOString(),\r\n      detail: state === 'Success' ? 'Step executed successfully' : 'Step failed with error'\r\n    });\r\n  }\r\n\r\n  return steps;\r\n}\r\n\r\n// 模拟测试状态\r\nlet mockProcessState: ExecutionState = ExecutionState.Pending;\r\nlet mockCurrentOperation = '';\r\nlet mockTestResult: TestResult = {\r\n  id: `test-${Date.now()}`,\r\n  resultFolderName: 'Mock Test Run',\r\n  testType: 'Case',\r\n  creationTime: new Date().toISOString(),\r\n  totalCount: 0,\r\n  successCount: 0,\r\n  failureCount: 0\r\n};\r\nlet mockCaseResults: CaseResult[] = [];\r\n\r\n// 增加全局变量记录当前正在执行的定时器\r\nlet progressInterval: number | undefined = undefined;\r\n\r\n// 启动进度更新的函数\r\nfunction startProgressUpdate() {\r\n  // 先清除可能存在的定时器\r\n  if (progressInterval !== undefined) {\r\n    clearInterval(progressInterval);\r\n    progressInterval = undefined;\r\n  }\r\n\r\n  // 创建新的定时器\r\n  progressInterval = window.setInterval(() => {\r\n    if (mockProcessState === ExecutionState.Running && mockTestResult.successCount + mockTestResult.failureCount < mockTestResult.totalCount) {\r\n      const currentIndex = mockTestResult.successCount + mockTestResult.failureCount;\r\n\r\n      // 将当前用例标记为已完成\r\n      const isSuccess = Math.random() > 0.2;\r\n      mockCaseResults[currentIndex].state = isSuccess ? 'Success' : 'Failure';\r\n      mockCaseResults[currentIndex].begin = new Date(Date.now() - 3000).toISOString();\r\n      mockCaseResults[currentIndex].end = new Date().toISOString();\r\n\r\n      // 更新测试结果统计\r\n      if (isSuccess) {\r\n        mockTestResult.successCount++;\r\n      } else {\r\n        mockTestResult.failureCount++;\r\n      }\r\n\r\n      // 更新当前正在执行的用例\r\n      const nextIndex = currentIndex + 1;\r\n      if (nextIndex < mockTestResult.totalCount) {\r\n        mockCaseResults[nextIndex].state = 'Running';\r\n        mockCurrentOperation = `Running test case: ${mockCaseResults[nextIndex].sequenceName}`;\r\n      } else {\r\n        mockProcessState = ExecutionState.Success;\r\n        mockCurrentOperation = 'Test execution completed';\r\n        mockTestResult.completionTime = new Date().toISOString();\r\n        if (progressInterval !== undefined) {\r\n          clearInterval(progressInterval);\r\n          progressInterval = undefined;\r\n        }\r\n      }\r\n    } else if (mockProcessState !== ExecutionState.Running) {\r\n      // 如果不是运行状态，暂停执行但不清除定时器\r\n      // 这里不做任何操作，定时器继续存在但不产生效果\r\n    } else {\r\n      if (progressInterval !== undefined) {\r\n        clearInterval(progressInterval);\r\n        progressInterval = undefined;\r\n      }\r\n    }\r\n  }, 500);\r\n}\r\n\r\n// 保存模拟状态\r\nexport const mockAppApi = {\r\n  // 获取应用信息\r\n  getAppInfo(): Promise<AxiosResponse<AppInfo>> {\r\n    return mockSuccess({\r\n      dataFolder: 'D:\\\\mock\\\\data\\\\folder',\r\n      logFolder: 'D:\\\\mock\\\\logs\\\\folder'\r\n    });\r\n  },\r\n\r\n  // 记录错误\r\n  logError: (data: ErrorData): Promise<AxiosResponse<void>> => {\r\n    // 将错误添加到内存中\r\n    errorLogs.push(data);\r\n    return mockSuccess(undefined);\r\n  },\r\n\r\n  // 退出应用程序\r\n  exit: (): Promise<AxiosResponse<void>> => {\r\n    console.log(\"模拟应用程序退出\");\r\n    // 在模拟环境中，我们不能真正退出应用，只打印日志\r\n    return mockSuccess(undefined);\r\n  },\r\n\r\n  // 获取最新互操作测试结果 - 修改确保序列名称唯一\r\n  getLatestInteroperationCaseResults(): Promise<AxiosResponse<CaseResult[]>> {\r\n    return mockSuccess(generateMockInteroperationResults());\r\n  },\r\n\r\n  // 生成测试用例 - 更新为支持序列筛选\r\n  generateCases(coverageType: CoverageType | GenerateCasesRequest, sequenceNames?: string[]): Promise<AxiosResponse<CaseResult[]>> {\r\n    // 处理两种不同的参数传递方式\r\n    let actualCoverageType: CoverageType;\r\n    let actualSequenceNames: string[];\r\n\r\n    if (typeof coverageType === 'object') {\r\n      // 如果传入的是GenerateCasesRequest对象\r\n      actualCoverageType = coverageType.coverage;\r\n      actualSequenceNames = coverageType.sequenceNames || [];\r\n    } else {\r\n      // 如果分别传入了两个参数\r\n      actualCoverageType = coverageType;\r\n      actualSequenceNames = sequenceNames || [];\r\n    }\r\n\r\n    return mockSuccess(generateMockTestCases(actualCoverageType, actualSequenceNames));\r\n  },\r\n\r\n  // 保存测试用例 - 与生成用例类似，但会有保存的操作\r\n  saveCases(coverageType: CoverageType | GenerateCasesRequest, sequenceNames?: string[]): Promise<AxiosResponse<CaseResult[]>> {\r\n    // 处理两种不同的参数传递方式\r\n    let actualCoverageType: CoverageType;\r\n    let actualSequenceNames: string[];\r\n\r\n    if (typeof coverageType === 'object') {\r\n      actualCoverageType = coverageType.coverage;\r\n      actualSequenceNames = coverageType.sequenceNames || [];\r\n    } else {\r\n      actualCoverageType = coverageType;\r\n      actualSequenceNames = sequenceNames || [];\r\n    }\r\n\r\n    const cases = generateMockTestCases(actualCoverageType, actualSequenceNames);\r\n    console.log(\"模拟保存测试用例:\", cases.length);\r\n\r\n    // 模拟给每个用例分配一个测试结果ID\r\n    const testResultId = `test-${Date.now()}`;\r\n    cases.forEach(c => c.testResultId = testResultId);\r\n\r\n    return mockSuccess(cases);\r\n  },\r\n\r\n  // 获取保存的测试用例\r\n  getSavedCases(): Promise<AxiosResponse<CaseResult[]>> {\r\n    // 模拟已保存的测试用例，使用相同的测试用例生成函数但固定测试结果ID\r\n    const savedCases = generateMockTestCases(CoverageType.Normal, ['can-frames', 'DiagnosticSessionControl']);\r\n    const testResultId = 'saved-test-result-id';\r\n    savedCases.forEach(c => {\r\n      c.testResultId = testResultId;\r\n      // 随机分配一些不同的状态\r\n      const states: ExecutionStateString[] = ['Pending', 'Running', 'Success', 'Failure'];\r\n      c.state = states[Math.floor(Math.random() * states.length)];\r\n    });\r\n\r\n    return mockSuccess(savedCases);\r\n  },\r\n\r\n  // 开始测试\r\n  startTest(): Promise<AxiosResponse<void>> {\r\n    console.log(\"模拟开始测试\");\r\n    mockProcessState = ExecutionState.Running;\r\n    mockCurrentOperation = 'Starting test execution';\r\n\r\n    // 重置测试进度\r\n    const savedCases = generateMockTestCases(CoverageType.Normal, ['can-frames', 'DiagnosticSessionControl']);\r\n    mockCaseResults = savedCases;\r\n    mockTestResult = {\r\n      id: `test-${Date.now()}`,\r\n      resultFolderName: 'Mock Test Run',\r\n      testType: 'Case',\r\n      creationTime: new Date().toISOString(),\r\n      totalCount: savedCases.length,\r\n      successCount: 0,\r\n      failureCount: 0\r\n    };\r\n\r\n    // 启动模拟测试进度更新\r\n    if (mockCaseResults.length > 0) {\r\n      mockCaseResults[0].state = 'Running';\r\n      mockCurrentOperation = `Running test case: ${mockCaseResults[0].sequenceName}`;\r\n\r\n      // 启动进度更新\r\n      startProgressUpdate();\r\n    }\r\n\r\n    return mockSuccess(undefined);\r\n  },\r\n\r\n  // 停止测试\r\n  stopTest(): Promise<AxiosResponse<void>> {\r\n    console.log(\"模拟停止测试\");\r\n    mockProcessState = ExecutionState.Failure;\r\n    mockCurrentOperation = 'Test execution stopped by user';\r\n    mockTestResult.completionTime = new Date().toISOString();\r\n    return mockSuccess(undefined);\r\n  },\r\n\r\n  // 暂停测试\r\n  pauseTest(): Promise<AxiosResponse<void>> {\r\n    console.log(\"模拟暂停测试\");\r\n    mockProcessState = ExecutionState.Paused;\r\n    mockCurrentOperation = 'Test execution paused by user';\r\n    return mockSuccess(undefined);\r\n  },\r\n\r\n  // 恢复测试\r\n  resumeTest(): Promise<AxiosResponse<void>> {\r\n    console.log(\"模拟恢复测试\");\r\n    mockProcessState = ExecutionState.Running;\r\n    mockCurrentOperation = 'Test execution resumed by user';\r\n\r\n    // 如果没有活动的定时器，重新启动进度更新\r\n    if (progressInterval === undefined) {\r\n      startProgressUpdate();\r\n    }\r\n\r\n    return mockSuccess(undefined);\r\n  },\r\n\r\n  // 获取测试状态\r\n  getTestStatus(): Promise<AxiosResponse<TesterSnapshot>> {\r\n    // 移除方法实现，保持与接口一致\r\n    const snapshot: TesterSnapshot = {\r\n      processState: mockProcessState,\r\n      currentOperation: mockCurrentOperation,\r\n      testResult: mockTestResult,\r\n      caseResults: mockCaseResults\r\n    };\r\n\r\n    return mockSuccess(snapshot);\r\n  },\r\n\r\n  // 新增 getTestStatusPaged 方法\r\n  getTestStatusPaged(pagedQuery: PagedQuery): Promise<AxiosResponse<TesterSnapshotResponse>> {\r\n    const snapshot = {\r\n      processState: mockProcessState,\r\n      currentOperation: mockCurrentOperation,\r\n      testResult: mockTestResult\r\n    };\r\n    \r\n    // 实现分页逻辑\r\n    const totalCases = mockCaseResults.length;\r\n    const startIndex = (pagedQuery.pageNumber - 1) * pagedQuery.pageSize;\r\n    const endIndex = Math.min(startIndex + pagedQuery.pageSize, totalCases);\r\n    const pagedItems = mockCaseResults.slice(startIndex, endIndex);\r\n    \r\n    const response: TesterSnapshotResponse = {\r\n      ...snapshot,\r\n      pagedCaseResult: {\r\n        items: pagedItems,\r\n        total: totalCases,\r\n        pageSize: pagedQuery.pageSize,\r\n        pageNumber: pagedQuery.pageNumber\r\n      }\r\n    };\r\n    \r\n    return mockSuccess(response);\r\n  },\r\n\r\n  // 获取测试结果列表\r\n  getTestResults(): Promise<AxiosResponse<TestResult[]>> {\r\n    return mockSuccess(generateMockTestResults());\r\n  },\r\n\r\n  // 获取测试用例列表\r\n  getCases(testResultId: string): Promise<AxiosResponse<CaseResult[]>> {\r\n    // 针对特定测试结果ID生成用例\r\n    const caseResults = generateMockTestCases(CoverageType.Normal, ['can-frames', 'DiagnosticSessionControl']);\r\n\r\n    // 确保所有用例都关联到这个测试结果ID\r\n    caseResults.forEach(c => {\r\n      c.testResultId = testResultId;\r\n      // 随机分配状态\r\n      const states: ExecutionStateString[] = ['Success', 'Failure'];\r\n      c.state = states[Math.floor(Math.random() * states.length)];\r\n      c.begin = new Date(Date.now() - 3600000).toISOString();\r\n      c.end = new Date(Date.now() - 3500000).toISOString();\r\n    });\r\n\r\n    return mockSuccess(caseResults);\r\n  },\r\n\r\n  // 获取用例步骤列表\r\n  getCaseSteps(testResultId: string, caseResultId: number): Promise<AxiosResponse<CaseStep[]>> {\r\n    return mockSuccess(generateMockCaseSteps(caseResultId));\r\n  },\r\n\r\n  // 删除测试结果\r\n  deleteTestResult(testResultId: string): Promise<AxiosResponse<void>> {\r\n    console.log(`模拟删除测试结果: ${testResultId}`);\r\n    // 在模拟环境中，我们从生成的测试结果中过滤掉指定ID的结果\r\n    return mockSuccess(undefined);\r\n  },\r\n\r\n  // 添加下载HTML报告的mock方法\r\n  downloadHtmlReport(testResultId: string): Promise<void> {\r\n    console.log(`Mock downloading HTML report for test result: ${testResultId}`);\r\n    // 在mock模式下，我们只需返回一个成功的Promise\r\n    return Promise.resolve();\r\n  }\r\n};\r\n", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\mock\\modules\\mockExplorerApi.ts", [], "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\mock\\modules\\mockInteroperationApi.ts", [], "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\router\\index.ts", [], "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\mock\\modules\\mockTestSuiteApi.ts", [], "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\mock\\modules\\mockHardwareApi.ts", [], "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\api\\sequenceApi.ts", [], "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\mock\\modules\\mockSequenceApi.ts", ["299"], "import { mockSuccess } from '../mockApi';\r\n\r\n// 存储当前的序列配置数据（模拟数据库）\r\nlet currentSequenceConfig = {\r\n  testSuiteName: 'CAN-Bus Test Suite',\r\n  sequencePackageName: 'CAN Frame examples',\r\n  sequencePackageXml: '<sequence><name>CAN Frame examples</name><description>Example sequence</description></sequence>'\r\n};\r\n\r\nexport const mockSequenceApi = {\r\n  // 获取序列配置\r\n  getSequenceConfig: () => {\r\n    return mockSuccess(currentSequenceConfig);\r\n  },\r\n\r\n  // 更新序列配置 \r\n  updateSequenceConfig: (config: any) => {\r\n    // 更新当前配置\r\n    currentSequenceConfig = {\r\n      ...currentSequenceConfig,\r\n      ...config\r\n    };\r\n    return mockSuccess(currentSequenceConfig);\r\n  }\r\n};\r\n", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\mock\\modules\\mockCaseApi.ts", ["300"], "import { mockSuccess } from '../mockApi';\r\nimport { CaseConfigDto, WhiteListFrame } from '@/api/caseApi';\r\nimport { CaseResult } from '@/api/interoperationApi';\r\n\r\nconst defaultConfig: CaseConfigDto = {\r\n  whiteListFrames: [], // 修改为空数组\r\n  selectedNodeName: '', // 新增：默认没有选中节点\r\n  enableNmWakeup: true,\r\n  nmWakeupId: 0x53F,\r\n  nmWakeupIsExt: false,\r\n  nmWakeupDlc: 8,\r\n  nmWakeupData: [0x3F, 0x50, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF],\r\n  nmWakeupCommunicationType: 'Can',\r\n  nmWakeupCycleMs: 100,\r\n  nmWakeupDelayMs: 2000,\r\n  diagReqId: 0x731,\r\n  diagReqIsExt: false,\r\n  diagResId: 0x631,\r\n  diagTimeoutMs: 500,\r\n  isDutMtuLessThan4096: false, // 新增：默认不限制MTU大小\r\n  enableDiagFallbackRequest: false, // 默认不启用备用诊断请求\r\n  diagFallbackRequestPayload: [0x10, 0x01], // 默认备用诊断请求数据\r\n\r\n  // 安全配置信息\r\n  securityInfo: {\r\n    hasDll: false,\r\n    dllFileName: undefined,\r\n    dllSize: 0\r\n  }\r\n};\r\n\r\n// 模拟一个CaseResult对象，用于返回单个用例详情\r\nconst mockSingleCaseResult: CaseResult = {\r\n  id: 1,\r\n  testResultId: '00000000-0000-0000-0000-000000000000',\r\n  sequenceId: '00000000-0000-0000-0000-000000000000',\r\n  sequenceName: 'Test Sequence',\r\n  name: 'mock case result',\r\n  parameter: 'Parameter Value',\r\n  state: 'Success',\r\n  begin: new Date().toISOString(),\r\n  end: new Date().toISOString(),\r\n  detail: 'Case result details'\r\n};\r\n\r\nexport const mockCaseApi = {\r\n  getCaseConfig() {\r\n    return mockSuccess(defaultConfig);\r\n  },\r\n\r\n  getCaseResult(testResultId: string, caseResultId: number) {\r\n    // 简单模拟，实际应用中可以根据ID返回不同的结果\r\n    return mockSuccess({\r\n      ...mockSingleCaseResult,\r\n      id: caseResultId,\r\n      testResultId: testResultId\r\n    });\r\n  },\r\n\r\n  updateCaseConfig(config: CaseConfigDto) {\r\n    // 处理安全DLL操作\r\n    if (config.removeSecurityDll) {\r\n      // 移除DLL\r\n      config.securityInfo = {\r\n        hasDll: false,\r\n        dllFileName: undefined,\r\n        dllSize: 0\r\n      };\r\n    } else if (config.securityDllPath) {\r\n      // 选择新的DLL - 模拟DLL文件已加载\r\n      const dllName = config.securityDllPath.split('\\\\').pop() || 'SecurityAccess.dll';\r\n      const mockDllSize = 15360; // 15KB\r\n\r\n      config.securityInfo = {\r\n        hasDll: true,\r\n        dllFileName: dllName,\r\n        dllSize: mockDllSize\r\n      };\r\n    }\r\n\r\n    // 清除操作标记，模拟后端API的行为\r\n    delete config.securityDllPath;\r\n    delete config.removeSecurityDll;\r\n\r\n    return mockSuccess(config);\r\n  },\r\n\r\n  importDbc() {\r\n    return mockSuccess({\r\n      whiteListFrames: [ // 更新帧列表，添加发送者和接收者信息\r\n        { id: 0x123, name: 'Engine_Status', dlc: 8, isExt: false, transmitter: 'ECM', receivers: ['TCM', 'BCM'] },\r\n        { id: 0x456, name: 'Transmission_Data', dlc: 8, isExt: true, transmitter: 'TCM', receivers: ['ECM', 'ICM'] },\r\n        { id: 0x789, name: 'Brake_Control', dlc: 8, isExt: false, transmitter: 'BCM', receivers: ['ECM', 'TCM', 'ICM'] }\r\n      ],\r\n      nodeNames: ['ECM', 'TCM', 'BCM', 'ICM'] // 添加节点名称列表\r\n    });\r\n  },\r\n\r\n  // 新增：选择安全DLL文件\r\n  selectSecurityDll() {\r\n    return mockSuccess({\r\n      path: \"C:\\\\fakepath\\\\SecurityAccess.dll\"\r\n    });\r\n  }\r\n};\r\n", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\api\\index.ts", [], "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\views\\TestSuiteView.vue", ["301", "302"], "<template>\r\n  <div class=\"test-suite-container\">\r\n    <!-- 左侧测试套件列表 -->\r\n    <div class=\"suites-list\">\r\n      <el-card v-for=\"suite in testSuites\" \r\n               :key=\"suite.name\" \r\n               class=\"suite-card\"\r\n               :class=\"{ 'active': selectedSuite?.name === suite.name }\"\r\n               @click=\"handleSuiteClick(suite)\">\r\n        <template #header>\r\n          <div class=\"suite-header\">\r\n            <h3>{{ suite.name }}</h3>\r\n            <span class=\"version\">v{{ suite.version }}</span>\r\n          </div>\r\n        </template>\r\n        <div class=\"packages-list\">\r\n          <div v-for=\"(sequencePackage, index) in suite.packages\" \r\n               :key=\"index\" \r\n               class=\"package-item\"\r\n               :class=\"{ 'active': selectedSuite?.name === suite.name && selectedPackageIndex === index }\"\r\n               @click.stop=\"handlePackageClick(suite, index, sequencePackage)\">\r\n            <el-icon><Document /></el-icon>\r\n            <span>{{ sequencePackage.name }}</span>\r\n          </div>\r\n        </div>\r\n      </el-card>\r\n    </div>\r\n\r\n    <!-- 右侧XML查看器 -->\r\n    <XmlViewer \r\n      v-if=\"selectedSuite\"\r\n      :title=\"`${selectedSuite.name} - ${selectedPackageName}`\"\r\n      :content=\"xmlContent\"\r\n    />\r\n  </div>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { ref, onMounted } from 'vue'\r\nimport { ElMessage } from 'element-plus'\r\nimport { Document } from '@element-plus/icons-vue'\r\nimport XmlViewer from '@/components/TestSuite/XmlViewer.vue'\r\nimport { testSuiteApi, type TestSuite, type SequencePackage } from '@/api/testSuiteApi'\r\n\r\nconst testSuites = ref<TestSuite[]>([])\r\nconst selectedSuite = ref<TestSuite | null>(null)\r\nconst xmlContent = ref('')\r\nconst selectedPackageIndex = ref<number>(-1)\r\nconst selectedPackageName = ref('')\r\n\r\nconst loadTestSuites = async () => {\r\n  try {\r\n    const response = await testSuiteApi.getBuiltIn()\r\n    testSuites.value = response.data\r\n  } catch (error) {\r\n    ElMessage.error('Failed to load test suites')\r\n    console.error('Error loading test suites:', error)\r\n  }\r\n}\r\n\r\nconst handleSuiteClick = async (suite: TestSuite) => {\r\n  if (selectedSuite.value?.name === suite.name) return\r\n  selectedSuite.value = suite\r\n  selectedPackageIndex.value = -1\r\n  selectedPackageName.value = ''\r\n  xmlContent.value = ''\r\n}\r\n\r\nconst handlePackageClick = async (suite: TestSuite, index: number, sequencePackage: SequencePackage) => {\r\n  selectedSuite.value = suite\r\n  selectedPackageIndex.value = index\r\n  selectedPackageName.value = sequencePackage.name\r\n  try {\r\n    const response = await testSuiteApi.getBuiltInXml(suite.name, sequencePackage.name)\r\n    xmlContent.value = response.data\r\n  } catch (error) {\r\n    ElMessage.error('Failed to load XML content')\r\n    console.error('Error loading XML:', error)\r\n  }\r\n}\r\n\r\nonMounted(() => {\r\n  loadTestSuites()\r\n})\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.test-suite-container {\r\n  padding: 20px;\r\n  display: flex;\r\n  gap: 20px;\r\n  height: calc(100vh - 80px);\r\n}\r\n\r\n.suites-list {\r\n  width: 300px;\r\n  overflow-y: auto;\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 20px;\r\n}\r\n\r\n.suite-card {\r\n  cursor: pointer;\r\n  \r\n  &.active {\r\n    border-color: var(--el-color-primary);\r\n  }\r\n}\r\n\r\n.suite-header {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n\r\n  h3 {\r\n    margin: 0;\r\n    flex: 1;\r\n  }\r\n\r\n  .version {\r\n    color: var(--el-text-color-secondary);\r\n    font-size: 0.9em;\r\n  }\r\n}\r\n\r\n.packages-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 8px;\r\n}\r\n\r\n.package-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  padding: 8px;\r\n  border-radius: 4px;\r\n  \r\n  &:hover {\r\n    background-color: var(--el-fill-color-light);\r\n  }\r\n\r\n  .el-icon {\r\n    color: var(--el-text-color-secondary);\r\n  }\r\n  \r\n  &.active {\r\n    background-color: var(--el-color-primary-light-9);\r\n    color: var(--el-color-primary);\r\n    \r\n    .el-icon {\r\n      color: var(--el-color-primary);\r\n    }\r\n  }\r\n}\r\n</style>\r\n", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\views\\HomeView.vue", [], "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\views\\AboutView.vue", ["303", "304"], "<template>\n  <div class=\"about-container\">\n    <el-card class=\"about-card\">\n      <div class=\"about-content\">\n        <div>\n          <h2>Fuzz</h2>\n          <p class=\"description\">Fuzz is a professional fuzzing tool mainly used for automotive communication\n            protocol testing.</p>\n        </div>\n\n        <div>\n          <h2>Features</h2>\n          <ul class=\"feature-list\">\n            <li><el-icon class=\"feature-icon\">\n                <Check />\n              </el-icon>Support for CAN/CANFD protocol testing</li>\n            <li><el-icon class=\"feature-icon\">\n                <Check />\n              </el-icon>Support for ISO 11898, ISO 14229 and ISO 15765 related testing</li>\n            <li><el-icon class=\"feature-icon\">\n                <Check />\n              </el-icon>Interoperation test to detect services supported by the device</li>\n            <li><el-icon class=\"feature-icon\">\n                <Check />\n              </el-icon>Automatic generation and execution of targeted test cases</li>\n            <li><el-icon class=\"feature-icon\">\n                <Check />\n              </el-icon>Local storage of test results and logs</li>\n          </ul>\n        </div>\n\n        <!-- 应用信息部分 -->\n        <div v-if=\"appInfo\">\n          <h2>Application Info</h2>\n          <div class=\"info-list\">\n            <div class=\"info-item\">\n              <span class=\"label\">Data Folder:</span>\n              <span class=\"folder-path\">{{ appInfo.dataFolder }}</span>\n              <el-button \n                type=\"text\" \n                size=\"small\" \n                @click=\"openFolder(appInfo.dataFolder)\"\n                title=\"Open in File Explorer\"\n                class=\"folder-btn\"\n              >\n                <font-awesome-icon icon=\"folder-open\" />\n              </el-button>\n            </div>\n            <div class=\"info-item\">\n              <span class=\"label\">Log Folder:</span>\n              <span class=\"folder-path\">{{ appInfo.logFolder }}</span>\n              <el-button \n                type=\"text\" \n                size=\"small\" \n                @click=\"openFolder(appInfo.logFolder)\"\n                title=\"Open in File Explorer\"\n                class=\"folder-btn\"\n              >\n                <font-awesome-icon icon=\"folder-open\" />\n              </el-button>\n            </div>\n          </div>\n        </div>\n\n        <div class=\"copyright-info\">\n          <p>Copyright © 2025, ALPS System Integration(Dalian) Co., Ltd.</p>\n          <p>All rights reserved.</p>\n        </div>\n      </div>\n    </el-card>\n  </div>\n</template>\n\n<script setup>\nimport { ref, onMounted } from 'vue';\nimport { Check } from '@element-plus/icons-vue';\nimport { appApi } from '@/api/appApi';\nimport { explorerApi } from '@/api/explorerApi';\n\n// 用于存储应用信息\nconst appInfo = ref(null);\n\n// 获取应用信息\nconst fetchAppInfo = async () => {\n  try {\n    const response = await appApi.getAppInfo();\n    appInfo.value = response.data;\n  } catch (error) {\n    console.error('Failed to fetch app info:', error);\n  }\n};\n\n// 在文件浏览器中打开文件夹\nconst openFolder = async (path) => {\n  try {\n    await explorerApi.openExplorer(path);\n  } catch (error) {\n    console.error('Failed to open folder:', error);\n  }\n};\n\n// 组件挂载时获取应用信息\nonMounted(fetchAppInfo);\n</script>\n\n<style scoped>\n.about-container {\n  height: 100%;\n  padding: 20px;\n  box-sizing: border-box;\n  background-color: #f5f7fa;\n}\n\n.about-card {\n  height: 100%;\n  border-radius: 8px;\n  display: flex;\n  flex-direction: column;\n}\n\n.panel-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  min-height: 32px;\n}\n\n.title-section h2 {\n  margin: 0;\n  font-weight: 600;\n  color: var(--el-text-color-primary);\n  font-size: 20px;\n  border-bottom: none;\n}\n\n.about-content {\n  flex: 1;\n  padding: 20px;\n  overflow-y: auto;\n  line-height: 1.6;\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n}\n\n.about-content::-webkit-scrollbar {\n  width: 6px;\n}\n\n.about-content::-webkit-scrollbar-thumb {\n  background-color: var(--el-border-color-light);\n  border-radius: 3px;\n}\n\nh2 {\n  font-size: 18px;\n  color: var(--el-color-primary);\n  border-bottom: 1px solid var(--el-border-color-light);\n  padding-bottom: 8px;\n  margin-bottom: 16px;\n  margin-top: 0;\n}\n\n.description {\n  font-size: 14px;\n  color: var(--el-text-color-primary);\n  margin-left: 8px;\n}\n\n.feature-list {\n  padding-left: 0;\n  list-style-type: none;\n}\n\n.feature-list li {\n  padding: 8px 0;\n  font-size: 14px;\n  display: flex;\n  align-items: center;\n  color: var(--el-text-color-primary);\n}\n\n.feature-icon {\n  color: var(--el-color-success);\n  margin-right: 10px;\n}\n\n.info-list {\n  padding-left: 8px;\n}\n\n.info-item {\n  display: flex;\n  align-items: center;\n  margin-bottom: 10px;\n  font-size: 14px;\n}\n\n.label {\n  min-width: 100px;\n  color: var(--el-text-color-primary);\n}\n\n.folder-path {\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  color: var(--el-text-color-secondary);\n  margin-right: 10px;\n}\n\n.folder-btn {\n  padding: 2px;\n}\n\n.copyright-info {\n  margin-top: auto;\n  padding-top: 20px;\n  margin-bottom: 0;\n  color: var(--el-text-color-secondary);\n  font-size: 12px;\n  text-align: center;\n  border-top: 1px solid var(--el-border-color-lighter);\n}\n\ncopyright-info p {\n  margin: 5px 0;\n}\n</style>\n", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\views\\TestPlanView.vue", [], "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\views\\testplan\\SequenceSetting.vue", ["305", "306"], "<template>\r\n  <div class=\"sequence-setting-container\" v-loading=\"loading\">\r\n    <!-- 左侧配置区域 -->\r\n    <div class=\"left-panel\">\r\n      <el-form :model=\"config\" label-width=\"160px\" label-position=\"top\">\r\n        <el-form-item label=\"Test Suite\">\r\n          <el-select v-model=\"selectedSuiteName\" placeholder=\"Select Test Suite\" @change=\"handleSuiteChange\"\r\n            style=\"width: 100%\">\r\n            <el-option v-for=\"suite in testSuites\" :key=\"suite.name\" :label=\"`${suite.name} v${suite.version}`\"\r\n              :value=\"suite.name\" />\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"Sequence Package\">\r\n          <el-select v-model=\"selectedPackageName\" placeholder=\"Select Sequence Package\" :disabled=\"!selectedSuiteName\"\r\n            style=\"width: 100%\" @change=\"handlePackageChange\">\r\n            <el-option v-for=\"(sequencePackage, index) in currentPackages\" :key=\"index\"\r\n              :label=\"sequencePackage.name\" :value=\"sequencePackage.name\" />\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <el-form-item>\r\n          <el-button type=\"primary\" @click=\"handleSave\">Save</el-button>\r\n          <el-button @click=\"resetToDefault\" style=\"margin-left: 10px;\">Reset</el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n    </div>\r\n\r\n    <!-- 右侧XML预览区域 -->\r\n    <div class=\"right-panel\">\r\n      <XmlViewer v-if=\"selectedSuiteName && selectedPackageName\"\r\n        :title=\"`${selectedSuiteName} - ${selectedPackageName}`\"\r\n        :content=\"xmlContent\"\r\n        @update:content=\"xmlContent = $event\" />\r\n\r\n      <el-empty style=\"background: #DDD2;\" v-else description=\"No sequence content\" :image-size=\"150\" />\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { ref, computed, onMounted } from 'vue';\r\nimport { ElMessage } from 'element-plus';\r\nimport { testSuiteApi, type TestSuite, type SequencePackage } from '@/api/testSuiteApi';\r\nimport { sequenceApi, type SequenceConfigData } from '@/api/sequenceApi';\r\nimport XmlViewer from '@/components/TestSuite/XmlViewer.vue';\r\n\r\nconst loading = ref(true);\r\nconst testSuites = ref<TestSuite[]>([]);\r\nconst selectedSuiteName = ref<string>('');\r\nconst selectedPackageName = ref<string>('');\r\nconst config = ref<SequenceConfigData>({\r\n  testSuiteName: '',\r\n  sequencePackageName: '',\r\n});\r\n\r\nconst defaultConfig = {\r\n  testSuiteName: '',\r\n  sequencePackageName: ''\r\n};\r\n\r\nconst currentPackages = computed<SequencePackage[]>(() => {\r\n  const suite = testSuites.value.find(s => s.name === selectedSuiteName.value);\r\n  return suite?.packages || [];\r\n});\r\n\r\nconst xmlContent = ref('');\r\n\r\nconst handlePackageChange = async () => {\r\n  if (!selectedSuiteName.value || !selectedPackageName.value) return;\r\n\r\n  try {\r\n    const response = await testSuiteApi.getXml(selectedSuiteName.value, selectedPackageName.value);\r\n    xmlContent.value = response.data;\r\n  } catch (error) {\r\n    ElMessage.error('Failed to load XML content');\r\n    console.error('Error loading XML:', error);\r\n  }\r\n};\r\n\r\nconst handleSuiteChange = () => {\r\n  selectedPackageName.value = '';\r\n  xmlContent.value = '';\r\n};\r\n\r\nconst loadTestSuites = async () => {\r\n  try {\r\n    const response = await testSuiteApi.getBuiltIn();\r\n    testSuites.value = response.data;\r\n  } catch (error) {\r\n    ElMessage.error('Failed to load test suites');\r\n  }\r\n};\r\n\r\nconst loadConfig = async () => {\r\n  loading.value = true;\r\n  try {\r\n    const response = await sequenceApi.getSequenceConfig();\r\n    config.value = response.data;\r\n    selectedSuiteName.value = config.value.testSuiteName || '';\r\n    selectedPackageName.value = config.value.sequencePackageName || '';\r\n\r\n    // 加载已选择包的XML内容\r\n    if (selectedSuiteName.value && selectedPackageName.value) {\r\n      await handlePackageChange();\r\n    }\r\n  } catch (error) {\r\n    ElMessage.error('Failed to load configuration');\r\n  } finally {\r\n    loading.value = false;\r\n  }\r\n};\r\n\r\nconst handleSave = async () => {\r\n  if (!selectedSuiteName.value || !selectedPackageName.value) {\r\n    ElMessage.warning('Please select both test suite and sequence package');\r\n    return;\r\n  }\r\n\r\n  loading.value = true;\r\n  try {\r\n    const newConfig: SequenceConfigData = {\r\n      testSuiteName: selectedSuiteName.value,\r\n      sequencePackageName: selectedPackageName.value,\r\n      sequencePackageXml: xmlContent.value\r\n    };\r\n    await sequenceApi.updateSequenceConfig(newConfig);\r\n    ElMessage.success('Save successful');\r\n    // 重新加载XML内容\r\n    await handlePackageChange();\r\n  }\r\n  catch (error) {\r\n    // 错误处理已由全局拦截器处理，这里不需要额外显示错误消息\r\n    console.error('Save failed:', error);\r\n  } finally {\r\n    loading.value = false;\r\n  }\r\n};\r\n\r\nconst resetToDefault = () => {\r\n  selectedSuiteName.value = defaultConfig.testSuiteName;\r\n  selectedPackageName.value = defaultConfig.sequencePackageName;\r\n};\r\n\r\nonMounted(async () => {\r\n  await loadTestSuites();\r\n  await loadConfig();\r\n});\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.sequence-setting-container {\r\n  display: flex;\r\n  gap: 20px;\r\n  padding: 20px;\r\n  flex: 1;\r\n}\r\n\r\n.left-panel {\r\n  width: 300px;\r\n  min-width: 300px;\r\n  background-color: #ffffff;\r\n}\r\n\r\n.right-panel {\r\n  flex: 1;\r\n  min-width: 0;\r\n  background-color: #ffffff;\r\n  overflow: hidden;\r\n  display: flex;\r\n  flex-direction: column;\r\n  border-radius: 2px;\r\n\r\n  :deep(.el-empty) {\r\n    flex: 1;\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: center;\r\n    margin: 0;\r\n  }\r\n}\r\n\r\n:deep(.el-form-item:last-child) {\r\n  margin-bottom: 0;\r\n}\r\n\r\n</style>\r\n", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\views\\testplan\\TestResults.vue", ["307", "308", "309", "310", "311", "312"], "<template>\r\n  <div class=\"test-results-container\">\r\n    <!-- 主内容区域 - 单页布局 -->\r\n    <div class=\"content-area\">\r\n      <!-- 测试结果列表页面 -->\r\n      <div v-if=\"!showCaseDetail\" class=\"test-results-page\">\r\n        <div class=\"page-header\">\r\n          <h3>Test Results</h3>\r\n        </div>\r\n\r\n        <!-- 测试结果表格 -->\r\n        <div class=\"results-table-container\">\r\n          <div v-if=\"loadingTestResults\" class=\"loading-container\">\r\n            <el-skeleton :rows=\"5\" animated />\r\n          </div>\r\n\r\n          <div v-else-if=\"testResults.length === 0\" class=\"empty-container\">\r\n            <el-empty description=\"No test results found\" />\r\n          </div>\r\n\r\n          <table v-else class=\"results-table\">\r\n            <thead>\r\n              <tr>\r\n                <th class=\"column-name\">Name</th>\r\n                <th class=\"column-start-time\">Start Time</th>\r\n                <th class=\"column-duration\">Duration</th>\r\n                <th class=\"column-passed\">Passed</th>\r\n                <th class=\"column-failed\">Failed</th>\r\n                <th class=\"column-total\">Total</th>\r\n                <th class=\"column-actions\">Actions</th>\r\n              </tr>\r\n            </thead>\r\n            <tbody>\r\n              <tr\r\n                v-for=\"(row, index) in testResults\"\r\n                :key=\"row.id\"\r\n                :class=\"{ 'row-stripe': index % 2 === 1 }\"\r\n                @click=\"handleRowClick(row)\"\r\n              >\r\n                <td class=\"column-name\">\r\n                  <el-tooltip :content=\"row.resultFolderName\" placement=\"top\" :show-after=\"500\">\r\n                    <span class=\"result-name text-ellipsis\">{{ row.resultFolderName }}</span>\r\n                  </el-tooltip>\r\n                </td>\r\n                <td class=\"column-start-time\">\r\n                  <el-tooltip :content=\"formatDateFull(row.creationTime)\" placement=\"top\" :show-after=\"500\">\r\n                    <span>{{ formatDateCompact(row.creationTime) }}</span>\r\n                  </el-tooltip>\r\n                </td>\r\n                <td class=\"column-duration\">\r\n                  <span>{{ calculateDuration(row.creationTime, row.end) }}</span>\r\n                </td>\r\n                <td class=\"column-passed\">\r\n                  <span class=\"success-count\">{{ row.successCount }}</span>\r\n                </td>\r\n                <td class=\"column-failed\">\r\n                  <span class=\"failure-count\">{{ row.failureCount }}</span>\r\n                </td>\r\n                <td class=\"column-total\">\r\n                  <span class=\"total-count\">{{ row.totalCount }}</span>\r\n                </td>\r\n                <td class=\"column-actions\">\r\n                  <div class=\"action-buttons\">\r\n                    <el-button\r\n                      type=\"primary\"\r\n                      size=\"small\"\r\n                      @click.stop=\"downloadHtmlReport(row.id)\"\r\n                      :title=\"'Download Report'\"\r\n                    >\r\n                      <el-icon><Download /></el-icon>\r\n                    </el-button>\r\n\r\n                    <el-button\r\n                      type=\"danger\"\r\n                      size=\"small\"\r\n                      @click.stop=\"confirmDelete(row)\"\r\n                      :title=\"'Delete Result'\"\r\n                    >\r\n                      <el-icon><Delete /></el-icon>\r\n                    </el-button>\r\n                  </div>\r\n                </td>\r\n              </tr>\r\n              <tr v-if=\"testResults.length === 0\">\r\n                <td colspan=\"7\" class=\"empty-row\">No data</td>\r\n              </tr>\r\n            </tbody>\r\n          </table>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 测试用例详情页面 -->\r\n      <div v-else class=\"test-cases-page\">\r\n        <div class=\"page-header\">\r\n          <el-button\r\n            type=\"primary\"\r\n            size=\"small\"\r\n            @click=\"showCaseDetail = false\"\r\n            class=\"back-button\"\r\n          >\r\n            <el-icon><Back /></el-icon>\r\n            Back\r\n          </el-button>\r\n          <h3>{{ selectedTestName }}</h3>\r\n        </div>\r\n\r\n        <div class=\"case-list-container\">\r\n          <CaseList\r\n            :cases=\"caseResults\"\r\n            @view-detail=\"viewCaseDetail\"\r\n          />\r\n\r\n          <!-- 加载状态 -->\r\n          <div v-if=\"loadingCases\" class=\"loading-overlay\">\r\n            <el-loading :visible=\"true\" />\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 添加用例详情对话框组件 -->\r\n    <CaseDetailDialog\r\n      v-model:visible=\"detailDialogVisible\"\r\n      :testResultId=\"selectedTestId\"\r\n      :caseResultId=\"selectedCaseId\"\r\n      @close=\"closeDetailDialog\"\r\n    />\r\n  </div>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { ref, onMounted, computed } from 'vue';\r\nimport { ElMessage, ElMessageBox } from 'element-plus';\r\nimport { appApi, TestResult } from '@/api/appApi';\r\nimport { CaseResult } from '@/api/interoperationApi';\r\nimport CaseList from '@/components/test/CaseList.vue';\r\nimport CaseDetailDialog from '@/components/test/CaseDetailDialog.vue';\r\nimport { Delete, Download, Back } from '@element-plus/icons-vue';\r\n\r\n// 状态变量\r\nconst testResults = ref<TestResult[]>([]);\r\nconst caseResults = ref<CaseResult[]>([]);\r\nconst selectedTestId = ref<string | null>(null);\r\nconst selectedCaseId = ref<number | null>(null);\r\nconst showCaseDetail = ref(false);\r\n\r\n// 加载状态\r\nconst loadingTestResults = ref(true);\r\nconst loadingCases = ref(false);\r\nconst downloadingReport = ref(false);\r\n\r\n// 用例详情对话框相关\r\nconst detailDialogVisible = ref(false);\r\n\r\n// 计算属性：获取选中的测试名称\r\nconst selectedTestName = computed(() => {\r\n  if (!selectedTestId.value) return '';\r\n  const selectedTest = testResults.value.find(test => test.id === selectedTestId.value);\r\n  return selectedTest ? selectedTest.resultFolderName : '';\r\n});\r\n\r\n// 获取测试结果列表\r\nconst fetchTestResults = async () => {\r\n  loadingTestResults.value = true;\r\n  try {\r\n    const response = await appApi.getTestResults();\r\n    testResults.value = response.data;\r\n  } catch (error) {\r\n    console.error('获取测试结果列表失败:', error);\r\n    ElMessage.error('Failed to fetch test results');\r\n  } finally {\r\n    loadingTestResults.value = false;\r\n  }\r\n};\r\n\r\n// 处理表格行点击\r\nconst handleRowClick = (row: TestResult) => {\r\n  selectTestResult(row.id);\r\n};\r\n\r\n// 选择测试结果\r\nconst selectTestResult = async (testResultId: string) => {\r\n  selectedTestId.value = testResultId;\r\n  await fetchCases(testResultId);\r\n  showCaseDetail.value = true;\r\n};\r\n\r\n// 获取测试用例列表\r\nconst fetchCases = async (testResultId: string) => {\r\n  loadingCases.value = true;\r\n  caseResults.value = []; // 清空之前的用例\r\n\r\n  try {\r\n    const response = await appApi.getCases(testResultId);\r\n    caseResults.value = response.data;\r\n  } catch (error) {\r\n    console.error('获取测试用例列表失败:', error);\r\n    ElMessage.error('Failed to fetch case results');\r\n  } finally {\r\n    loadingCases.value = false;\r\n  }\r\n};\r\n\r\n// 确认删除\r\nconst confirmDelete = (result: TestResult) => {\r\n  ElMessageBox.confirm(\r\n    `Are you sure you want to delete test result \"${result.resultFolderName}\"?`,\r\n    'Warning',\r\n    {\r\n      confirmButtonText: 'Delete',\r\n      cancelButtonText: 'Cancel',\r\n      type: 'warning',\r\n    }\r\n  )\r\n    .then(() => {\r\n      deleteTestResult(result.id);\r\n    })\r\n    .catch(() => {\r\n      // 用户取消\r\n    });\r\n};\r\n\r\n// 删除测试结果\r\nconst deleteTestResult = async (testResultId: string) => {\r\n  try {\r\n    await appApi.deleteTestResult(testResultId);\r\n    ElMessage.success('Test result deleted successfully');\r\n\r\n    // 刷新测试结果列表\r\n    await fetchTestResults();\r\n\r\n    // 如果删除的是当前选中的测试结果，返回到结果列表\r\n    if (selectedTestId.value === testResultId) {\r\n      selectedTestId.value = null;\r\n      caseResults.value = [];\r\n      showCaseDetail.value = false;\r\n    }\r\n  } catch (error) {\r\n    console.error('删除测试结果失败:', error);\r\n    ElMessage.error('Failed to delete test result');\r\n  }\r\n};\r\n\r\n// 下载HTML报告\r\nconst downloadHtmlReport = async (testResultId: string) => {\r\n  if (!testResultId) {\r\n    ElMessage.warning('No test result selected for report generation');\r\n    return;\r\n  }\r\n\r\n  downloadingReport.value = true;\r\n  try {\r\n    await appApi.downloadHtmlReport(testResultId);\r\n    ElMessage.success('Report downloaded successfully');\r\n  } catch (error) {\r\n    console.error('Download report failed:', error);\r\n    ElMessage.error('Failed to download test report');\r\n  } finally {\r\n    downloadingReport.value = false;\r\n  }\r\n};\r\n\r\n// 查看用例详情\r\nconst viewCaseDetail = (caseResult: CaseResult) => {\r\n  selectedCaseId.value = caseResult.id;\r\n  detailDialogVisible.value = true;\r\n};\r\n\r\n// 关闭详情对话框\r\nconst closeDetailDialog = () => {\r\n  detailDialogVisible.value = false;\r\n  selectedCaseId.value = null;\r\n};\r\n\r\n// 格式化日期 - 完整格式（用于tooltip）\r\nconst formatDateFull = (dateString?: string | null) => {\r\n  if (!dateString) return null;\r\n  try {\r\n    const date = new Date(dateString);\r\n    return date.toLocaleString();\r\n  } catch (e) {\r\n    return dateString;\r\n  }\r\n};\r\n\r\n// 格式化日期 - 紧凑格式（用于表格显示）\r\nconst formatDateCompact = (dateString?: string | null) => {\r\n  if (!dateString) return null;\r\n  try {\r\n    const date = new Date(dateString);\r\n    // 只显示时间部分，或者只显示日期和时间的小时和分钟\r\n    const today = new Date();\r\n    const isToday = date.getDate() === today.getDate() &&\r\n                    date.getMonth() === today.getMonth() &&\r\n                    date.getFullYear() === today.getFullYear();\r\n\r\n    if (isToday) {\r\n      // 如果是今天，只显示时间\r\n      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', second: '2-digit' });\r\n    } else {\r\n      // 否则显示日期和时间，但格式更紧凑\r\n      return date.toLocaleDateString([], { month: '2-digit', day: '2-digit' }) +\r\n             ' ' +\r\n             date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });\r\n    }\r\n  } catch (e) {\r\n    return dateString;\r\n  }\r\n};\r\n\r\n// 保留原来的formatDate函数以兼容其他地方的调用\r\nconst formatDate = formatDateFull;\r\n\r\n// 计算持续时间\r\nconst calculateDuration = (startTime?: string | null, endTime?: string | null) => {\r\n  if (!startTime || !endTime) return '-';\r\n\r\n  try {\r\n    const start = new Date(startTime);\r\n    const end = new Date(endTime);\r\n    const durationMs = end.getTime() - start.getTime();\r\n\r\n    if (durationMs < 0) return '-'; // 防止负值\r\n\r\n    if (durationMs < 1000) {\r\n      return `${durationMs}ms`;\r\n    } else if (durationMs < 60000) {\r\n      const seconds = Math.floor(durationMs / 1000);\r\n      return `${seconds}s`;\r\n    } else if (durationMs < 3600000) {\r\n      const minutes = Math.floor(durationMs / 60000);\r\n      const seconds = Math.floor((durationMs % 60000) / 1000);\r\n      return `${minutes}m ${seconds}s`;\r\n    } else {\r\n      const hours = Math.floor(durationMs / 3600000);\r\n      const minutes = Math.floor((durationMs % 3600000) / 60000);\r\n      return `${hours}h ${minutes}m`;\r\n    }\r\n  } catch (e) {\r\n    console.error('Error calculating duration:', e);\r\n    return '-';\r\n  }\r\n};\r\n\r\n// 组件挂载时获取测试结果列表\r\nonMounted(() => {\r\n  fetchTestResults();\r\n});\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.test-results-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  flex: 1;\r\n  padding: 20px;\r\n}\r\n\r\n/* 内容区域样式 */\r\n.content-area {\r\n  flex: 1;\r\n  min-height: 0;\r\n  display: flex;\r\n  flex-direction: column;\r\n  width: 100%;\r\n  min-width: 0; /* 允许容器缩小到小于内容宽度 */\r\n\r\n  .test-results-page, .test-cases-page {\r\n    flex: 1;\r\n    display: flex;\r\n    flex-direction: column;\r\n    overflow: hidden;\r\n  }\r\n\r\n  .page-header {\r\n    display: flex;\r\n    align-items: center;\r\n    margin-bottom: 16px;\r\n\r\n    h2, h3 {\r\n      margin: 0;\r\n      font-size: 16px;\r\n      font-weight: 500;\r\n      color: #303133;\r\n    }\r\n\r\n    .back-button {\r\n      margin-right: 16px;\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .el-icon {\r\n        margin-right: 4px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .results-table-container {\r\n    flex: 1;\r\n    overflow: auto;\r\n    border: 1px solid #ebeef5;\r\n    border-radius: 4px;\r\n    box-shadow: none;\r\n    width: 100%;\r\n    min-width: 0; /* 允许容器缩小到小于内容宽度 */\r\n\r\n    .loading-container, .empty-container {\r\n      padding: 40px;\r\n      display: flex;\r\n      justify-content: center;\r\n      align-items: center;\r\n    }\r\n\r\n    .result-name {\r\n      font-weight: 500;\r\n      color: var(--el-color-primary);\r\n      font-size: 12px;\r\n    }\r\n\r\n    .text-ellipsis {\r\n      max-width: 100%;\r\n      overflow: hidden;\r\n      text-overflow: ellipsis;\r\n      white-space: nowrap;\r\n      display: inline-block;\r\n      width: 100%; /* 确保占满整个单元格 */\r\n    }\r\n\r\n    .success-count {\r\n      color: var(--el-color-success);\r\n      font-weight: 500;\r\n      font-size: 12px;\r\n    }\r\n\r\n    .failure-count {\r\n      color: var(--el-color-danger);\r\n      font-weight: 500;\r\n      font-size: 12px;\r\n    }\r\n\r\n    .total-count {\r\n      color: var(--el-color-primary);\r\n      font-weight: 500;\r\n      font-size: 12px;\r\n    }\r\n\r\n    .action-buttons {\r\n      display: flex;\r\n      justify-content: center;\r\n      gap: 4px;\r\n\r\n      .el-button {\r\n        padding: 5px 8px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .case-list-container {\r\n    flex: 1;\r\n    position: relative;\r\n    border: 1px solid #ebeef5;\r\n    border-radius: 4px;\r\n    overflow: hidden;\r\n\r\n    .loading-overlay {\r\n      position: absolute;\r\n      top: 0;\r\n      left: 0;\r\n      right: 0;\r\n      bottom: 0;\r\n      background-color: rgba(255, 255, 255, 0.7);\r\n      display: flex;\r\n      justify-content: center;\r\n      align-items: center;\r\n      z-index: 100;\r\n    }\r\n  }\r\n}\r\n\r\n/* 普通表格样式 */\r\n.results-table {\r\n  width: 100%;\r\n  border-collapse: collapse;\r\n  font-size: 12px;\r\n  table-layout: auto;\r\n\r\n  /* 表头样式 */\r\n  thead {\r\n    tr {\r\n      background-color: #f5f7fa;\r\n\r\n      th {\r\n        color: #606266;\r\n        font-size: 12px;\r\n        height: 40px;\r\n        padding: 4px 6px;\r\n        font-weight: bold;\r\n        text-align: left;\r\n        border-bottom: 1px solid #ebeef5;\r\n      }\r\n    }\r\n  }\r\n\r\n  /* 表格内容样式 */\r\n  tbody {\r\n    tr {\r\n      cursor: pointer;\r\n      height: 36px;\r\n      border-bottom: 1px solid #ebeef5;\r\n\r\n      &:hover {\r\n        background-color: #f5f7fa;\r\n      }\r\n\r\n      &.row-stripe {\r\n        background-color: #fafafa;\r\n\r\n        &:hover {\r\n          background-color: #f5f7fa;\r\n        }\r\n      }\r\n\r\n      td {\r\n        padding: 0 6px;\r\n        text-align: left;\r\n      }\r\n    }\r\n  }\r\n\r\n  /* 列对齐方式 */\r\n  .column-name {\r\n    text-align: left;\r\n    min-width: 80px;\r\n  }\r\n\r\n  .column-start-time {\r\n    text-align: left;\r\n    min-width: 80px;\r\n  }\r\n\r\n  .column-duration {\r\n    text-align: center;\r\n    min-width: 70px;\r\n  }\r\n\r\n  .column-passed, .column-failed, .column-total {\r\n    text-align: center;\r\n    min-width: 50px;\r\n  }\r\n\r\n  .column-actions {\r\n    text-align: center;\r\n    min-width: 60px;\r\n  }\r\n\r\n  /* 空数据行样式 */\r\n  .empty-row {\r\n    text-align: center;\r\n    padding: 20px;\r\n    color: #909399;\r\n  }\r\n\r\n  /* 优化表格在窄屏幕上的显示 */\r\n  @media (max-width: 768px) {\r\n    font-size: 11px;\r\n\r\n    thead th, tbody td {\r\n      padding: 0 4px;\r\n    }\r\n  }\r\n}\r\n\r\n/* 响应式设计 */\r\n@media (max-width: 768px) {\r\n  .content-area {\r\n    padding: 8px;\r\n\r\n    .page-header {\r\n      flex-direction: column;\r\n      align-items: flex-start;\r\n      gap: 8px;\r\n\r\n      .back-button {\r\n        margin-right: 0;\r\n      }\r\n    }\r\n  }\r\n}\r\n</style>\r\n", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\views\\testplan\\TestCases.vue", ["313", "314", "315", "316", "317"], "<template>\r\n  <div class=\"test-cases-container\">\r\n    <!-- 顶部工具栏 -->\r\n    <div class=\"toolbar\">\r\n      <div class=\"coverage-options\">\r\n        <span>Coverage:</span>\r\n        <el-radio-group v-model=\"selectedCoverage\" size=\"small\">\r\n          <el-radio :label=\"CoverageType.Normal\">Normal</el-radio>\r\n          <el-radio :label=\"CoverageType.High\">High</el-radio>\r\n        </el-radio-group>\r\n      </div>\r\n      <div class=\"action-buttons\">\r\n        <el-button\r\n          type=\"primary\"\r\n          @click=\"generateTestCases\"\r\n          :loading=\"generating\"\r\n          :disabled=\"selectedSequences.length === 0\"\r\n          size=\"small\">\r\n          Generate\r\n        </el-button>\r\n        <el-button\r\n          type=\"success\"\r\n          @click=\"saveTestCases\"\r\n          :loading=\"saving\"\r\n          :disabled=\"!canSave\"\r\n          size=\"small\">\r\n          Save\r\n        </el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 下方内容区域 -->\r\n    <div class=\"content-area\">\r\n      <!-- 左侧：互操作测试结果 -->\r\n      <InteroperationResultPanel\r\n        :results=\"successfulResults\"\r\n        :loading=\"loading\"\r\n        v-model:selectedSequences=\"selectedSequences\"\r\n      />\r\n\r\n      <!-- 右侧：生成的测试用例 -->\r\n      <GeneratedCasesPanel\r\n        :cases=\"generatedCases\"\r\n        :generating=\"generating\"\r\n        :show-empty-message=\"showGenerationEmpty\"\r\n        :estimated-time=\"estimatedExecutionTime\"\r\n      />\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { ref, computed, onMounted, watch } from 'vue';\r\nimport { ElMessage } from 'element-plus';\r\nimport { appApi, CoverageType, ExecutionState } from '@/api/appApi';\r\nimport { CaseResult } from '@/api/interoperationApi';\r\nimport InteroperationResultPanel from '@/components/TestCases/InteroperationResultPanel.vue';\r\nimport GeneratedCasesPanel from '@/components/TestCases/GeneratedCasesPanel.vue';\r\n\r\n// 状态变量\r\nconst interoperationResults = ref<CaseResult[]>([]);\r\nconst generatedCases = ref<CaseResult[]>([]);\r\nconst loading = ref(true);\r\nconst generating = ref(false);\r\nconst saving = ref(false);\r\nconst selectedCoverage = ref(CoverageType.Normal);\r\nconst showGenerationEmpty = ref(false);\r\nconst selectedSequences = ref<string[]>([]);\r\nconst hasGeneratedCases = ref(false);\r\n\r\n// 只显示成功的测试结果 - 移除副作用\r\nconst successfulResults = computed(() => {\r\n  return interoperationResults.value.filter(result => result.state === ExecutionState.Success);\r\n});\r\n\r\n// 是否可以保存测试用例\r\nconst canSave = computed(() => {\r\n  return hasGeneratedCases.value && generatedCases.value.length > 0 && !saving.value;\r\n});\r\n\r\n// 监听成功的测试结果变化，初始化选中状态\r\nwatch(successfulResults, (results) => {\r\n  if (results.length > 0 && selectedSequences.value.length === 0) {\r\n    selectedSequences.value = results.map(item => item.sequenceName);\r\n  }\r\n}, { immediate: true });\r\n\r\n// 获取最新互操作测试结果\r\nconst fetchInteroperationResults = async () => {\r\n  loading.value = true;\r\n  try {\r\n    const response = await appApi.getLatestInteroperationCaseResults();\r\n    interoperationResults.value = response.data;\r\n  } catch (error) {\r\n    console.error('获取测试结果失败:', error);\r\n    ElMessage.error('Failed to fetch interoperation results');\r\n  } finally {\r\n    loading.value = false;\r\n  }\r\n};\r\n\r\n// 生成测试用例\r\nconst generateTestCases = async () => {\r\n  // 验证至少选中一个序列\r\n  if (selectedSequences.value.length === 0) {\r\n    ElMessage.warning('Please select at least one sequence');\r\n    return;\r\n  }\r\n\r\n  generating.value = true;\r\n  showGenerationEmpty.value = true;\r\n  hasGeneratedCases.value = false;\r\n\r\n  // 先清空现有的用例列表，确保视图更新\r\n  generatedCases.value = [];\r\n\r\n  try {\r\n    const response = await appApi.generateCases(selectedCoverage.value, selectedSequences.value);\r\n    // 使用新数组替换而不是修改现有数组，确保响应式更新\r\n    generatedCases.value = [...response.data];\r\n    hasGeneratedCases.value = true;\r\n    ElMessage.success(`Successfully generated ${response.data.length} test cases`);\r\n    console.log('Generated cases:', generatedCases.value.length);\r\n  } catch (error) {\r\n    console.error('生成测试用例失败:', error);\r\n    ElMessage.error('Failed to generate test cases');\r\n  } finally {\r\n    generating.value = false;\r\n  }\r\n};\r\n\r\n// 保存测试用例\r\nconst saveTestCases = async () => {\r\n  if (!hasGeneratedCases.value || generatedCases.value.length === 0) {\r\n    ElMessage.warning('Please generate test cases first');\r\n    return;\r\n  }\r\n\r\n  saving.value = true;\r\n  try {\r\n    const response = await appApi.saveCases(selectedCoverage.value, selectedSequences.value);\r\n    ElMessage.success(`Successfully saved ${response.data.length} test cases`);\r\n\r\n    // 更新UI中的测试用例数据，使其包含保存后可能更新的信息（如测试结果ID）\r\n    // 使用新数组替换而不是修改现有数组，确保响应式更新\r\n    generatedCases.value = [...response.data];\r\n    console.log('Saved cases:', generatedCases.value.length);\r\n  } catch (error) {\r\n    console.error('保存测试用例失败:', error);\r\n    ElMessage.error('Failed to save test cases');\r\n  } finally {\r\n    saving.value = false;\r\n  }\r\n};\r\n\r\n// 计算预估执行时间(毫秒)\r\nconst estimatedExecutionTime = computed(() => {\r\n  if (!generatedCases.value.length || !interoperationResults.value.length) {\r\n    return 0;\r\n  }\r\n\r\n  // 创建一个Map来存储每个sequence的执行时间\r\n  const sequenceTimeMap = new Map<string, number>();\r\n\r\n  // 从互操作结果中计算每个sequence的执行时间\r\n  interoperationResults.value.forEach(result => {\r\n    if (result.begin && result.end && result.sequenceName) {\r\n      const executionTime = new Date(result.end).getTime() - new Date(result.begin).getTime();\r\n      sequenceTimeMap.set(result.sequenceName, executionTime);\r\n    }\r\n  });\r\n\r\n  // 累加生成用例中每个sequence对应的执行时间\r\n  const totalTime = generatedCases.value.reduce((sum, testCase) => {\r\n    const sequenceTime = sequenceTimeMap.get(testCase.sequenceName) || 0;\r\n    return sum + sequenceTime;\r\n  }, 0);\r\n\r\n  return totalTime;\r\n});\r\n\r\n// 组件挂载时获取最新互操作测试结果\r\nonMounted(() => {\r\n  fetchInteroperationResults();\r\n});\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.test-cases-container {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  padding: 20px;\r\n}\r\n\r\n/* 顶部工具栏样式 */\r\n.toolbar {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 8px 12px;\r\n  border-bottom: 1px solid #dcdfe6;\r\n  margin-bottom: 12px;\r\n\r\n  .coverage-options {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 10px;\r\n\r\n    span {\r\n      font-size: 13px;\r\n      color: #606266;\r\n    }\r\n  }\r\n\r\n  .action-buttons {\r\n    display: flex;\r\n    gap: 8px;\r\n  }\r\n}\r\n\r\n/* 下方内容区域样式 */\r\n.content-area {\r\n  display: flex;\r\n  flex: 1;\r\n  min-height: 0; /* 确保内容能正确滚动 */\r\n  gap: 12px;\r\n}\r\n\r\n/* 响应式布局 */\r\n@media (max-width: 768px) {\r\n  .content-area {\r\n    flex-direction: column;\r\n  }\r\n}\r\n</style>\r\n", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\views\\testplan\\TestRun.vue", [], "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\views\\testplan\\HardwareSetting.vue", [], "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\views\\testplan\\Interoperation.vue", ["318", "319", "320"], "<template>\r\n  <div class=\"interoperation-container\">\r\n    <div class=\"toolbar\">\r\n      <h3>Interoperation Test</h3>\r\n      <div class=\"action-buttons\">\r\n        <el-button type=\"success\" size=\"small\" :loading=\"starting\" @click=\"startTestExecution\" :disabled=\"isRunning\">\r\n          Start\r\n        </el-button>\r\n        <el-button type=\"danger\" size=\"small\" :loading=\"stopping\" @click=\"stopTestExecution\" :disabled=\"!isRunning\">\r\n          Stop\r\n        </el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 使用TestMonitor组件显示执行状态进度 -->\r\n    <TestMonitor :run-status=\"runStatus\" :visible=\"hasEverStarted\" />\r\n\r\n    <el-scrollbar v-if=\"displayedCases.length\" height=\"100%\">\r\n      <div class=\"cases-list\">\r\n        <div v-for=\"(item, index) in displayedCases\" :key=\"index\" class=\"case-item\">\r\n          <div class=\"case-header\">\r\n            <div class=\"case-sequence\">\r\n              <div>{{ item.sequenceName }}</div>\r\n              <div v-if=\"item.detail\" class=\"case-detail\">\r\n                <div class=\"detail-content\">{{ item.detail }}</div>\r\n              </div>\r\n            </div>\r\n            <div class=\"case-actions\">\r\n              <div class=\"case-status\">\r\n                <CaseStateTag :state=\"item.state\" />\r\n              </div>\r\n              <el-button \r\n                type=\"primary\" \r\n                size=\"small\" \r\n                plain \r\n                @click=\"viewCaseDetail(item)\">\r\n                Open\r\n              </el-button>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </el-scrollbar>\r\n\r\n    <el-empty v-else description=\"No items\" :image-size=\"150\" />\r\n\r\n    <!-- 添加用例详情对话框组件 -->\r\n    <CaseDetailDialog\r\n      v-model:visible=\"detailDialogVisible\"\r\n      :testResultId=\"testResultId\"\r\n      :caseResultId=\"selectedCaseId\"\r\n      @close=\"closeDetailDialog\"\r\n    />\r\n  </div>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { ref, onMounted, onUnmounted, computed, defineComponent } from 'vue';\r\nimport { ElMessage, ElEmpty } from 'element-plus';\r\nimport { CaseResult, interoperationApi, ExecutionState, isTesterCompleted } from '@/api/interoperationApi';\r\nimport CaseStateTag from '@/components/common/CaseStateTag.vue';\r\nimport TestMonitor from '@/components/test/TestMonitor.vue';\r\nimport { TesterSnapshot } from '@/api/appApi';\r\n// 导入用例详情对话框组件\r\nimport CaseDetailDialog from '@/components/test/CaseDetailDialog.vue';\r\n\r\n// 定义组件名称\r\ndefineComponent({\r\n  name: 'InteroperationView'\r\n});\r\n\r\n// 状态变量\r\nconst loading = ref(true);\r\nconst starting = ref(false);\r\nconst stopping = ref(false);\r\nconst hasEverStarted = ref(false);\r\nconst runStatus = ref<TesterSnapshot>({\r\n  processState: ExecutionState.Pending,\r\n  currentOperation: '',\r\n  testResult: {\r\n    id: '',\r\n    resultFolderName: '',\r\n    testType: '',\r\n    creationTime: '',\r\n    totalCount: 0,\r\n    successCount: 0,\r\n    failureCount: 0\r\n  },\r\n  caseResults: []\r\n});\r\n\r\n// 用例详情对话框相关\r\nconst detailDialogVisible = ref(false);\r\nconst selectedCaseId = ref<number | null>(null);\r\nconst testResultId = ref<string | null>(null);\r\n\r\n// 状态轮询定时器\r\nlet statusPollingTimer: number | null = null;\r\n\r\n// 计算属性\r\nconst isRunning = computed(() => {\r\n  return runStatus.value.processState === ExecutionState.Running;\r\n});\r\n\r\nconst displayedCases = computed(() => {\r\n  return runStatus.value.caseResults || [];\r\n});\r\n\r\n// 查看用例详情\r\nconst viewCaseDetail = (caseResult: CaseResult) => {\r\n  testResultId.value = caseResult.testResultId;\r\n  selectedCaseId.value = caseResult.id;\r\n  detailDialogVisible.value = true;\r\n};\r\n\r\n// 关闭详情对话框\r\nconst closeDetailDialog = () => {\r\n  detailDialogVisible.value = false;\r\n  selectedCaseId.value = null;\r\n};\r\n\r\n// 开始测试执行\r\nconst startTestExecution = async () => {\r\n  starting.value = true;\r\n  try {\r\n    await interoperationApi.startTest();\r\n    hasEverStarted.value = true;\r\n    ElMessage.success('Interoperation test started');\r\n\r\n    // 立即获取状态并开始轮询\r\n    await fetchTestStatus();\r\n    startStatusPolling();\r\n  } catch (error) {\r\n    console.error('Failed to start interoperation test:', error);\r\n    ElMessage.error('Failed to start interoperation test');\r\n  } finally {\r\n    starting.value = false;\r\n  }\r\n};\r\n\r\n// 停止测试执行\r\nconst stopTestExecution = async () => {\r\n  stopping.value = true;\r\n  try {\r\n    await interoperationApi.stopTest();\r\n    ElMessage.success('Interoperation test stopped');\r\n\r\n    // 立即更新状态\r\n    await fetchTestStatus();\r\n  } catch (error) {\r\n    console.error('Failed to stop interoperation test:', error);\r\n    ElMessage.error('Failed to stop interoperation test');\r\n  } finally {\r\n    stopping.value = false;\r\n  }\r\n};\r\n\r\n// 获取测试状态\r\nconst fetchTestStatus = async () => {\r\n  try {\r\n    const response = await interoperationApi.getStatus();\r\n    runStatus.value = response.data;\r\n\r\n    // 如果测试完成则停止轮询\r\n    if (isTesterCompleted(runStatus.value) && statusPollingTimer) {\r\n      stopStatusPolling();\r\n    }\r\n    loading.value = false;\r\n  } catch (error) {\r\n    console.error('Failed to fetch interoperation test status:', error);\r\n  }\r\n};\r\n\r\n// 开始状态轮询\r\nconst startStatusPolling = () => {\r\n  // 清除可能存在的轮询定时器\r\n  stopStatusPolling();\r\n  statusPollingTimer = window.setInterval(fetchTestStatus, 300);\r\n};\r\n\r\n// 停止状态轮询\r\nconst stopStatusPolling = () => {\r\n  if (statusPollingTimer) {\r\n    clearInterval(statusPollingTimer);\r\n    statusPollingTimer = null;\r\n  }\r\n};\r\n\r\n// 组件挂载时获取测试状态\r\nonMounted(() => {\r\n  fetchTestStatus().then(() => {\r\n    // 如果测试正在运行，开始轮询\r\n    if (isRunning.value) {\r\n      hasEverStarted.value = true;\r\n      startStatusPolling();\r\n    }\r\n  });\r\n});\r\n\r\n// 组件卸载时停止轮询\r\nonUnmounted(() => {\r\n  stopStatusPolling();\r\n});\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.interoperation-container {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  padding: 20px;\r\n}\r\n\r\n/* 顶部工具栏样式 */\r\n.toolbar {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 8px 12px;\r\n  border-bottom: 1px solid #dcdfe6;\r\n  margin-bottom: 12px;\r\n\r\n  h3 {\r\n    margin: 0;\r\n    font-size: 16px;\r\n    font-weight: 500;\r\n    color: #303133;\r\n  }\r\n\r\n  .action-buttons {\r\n    display: flex;\r\n    gap: 8px;\r\n  }\r\n}\r\n\r\n.cases-list {\r\n  padding: 8px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 8px;\r\n}\r\n\r\n.case-item {\r\n  border: 1px solid #ebeef5;\r\n  border-radius: 4px;\r\n  padding: 12px;\r\n  background-color: #fff;\r\n  transition: all 0.3s;\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 10px;\r\n\r\n  &:hover {\r\n    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n  }\r\n}\r\n\r\n.case-header {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n\r\n  .case-index {\r\n    font-weight: bold;\r\n    width: 40px;\r\n    color: #909399;\r\n  }\r\n\r\n  .case-sequence {\r\n    display: flex;\r\n    flex-direction: row;\r\n    flex: 1;\r\n    font-weight: 500;\r\n    color: #303133;\r\n  }\r\n}\r\n\r\n.case-actions {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px; /* 按钮和状态标签之间的间距 */\r\n}\r\n\r\n.case-status {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.case-body {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n\r\n  .case-parameter {\r\n    flex: 1;\r\n    color: #606266;\r\n    font-size: 13px;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    white-space: nowrap;\r\n    max-width: 60vw;\r\n  }\r\n\r\n  .case-group {\r\n    margin-left: 8px;\r\n  }\r\n}\r\n\r\n.case-detail {\r\n  font-size: 12px;\r\n  margin-left: 10px;\r\n  display: flex;\r\n  align-items: center;\r\n\r\n  .detail-content {\r\n    color: #909399;\r\n    word-break: break-word;\r\n    flex: 1;\r\n  }\r\n}\r\n</style>\r\n", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\views\\testplan\\CaseSetting.vue", ["321", "322", "323", "324", "325"], "<template>\r\n  <div class=\"case-setting-container\" v-loading=\"loading\">\r\n    <!-- Top Toolbar -->\r\n    <div class=\"toolbar top-toolbar\">\r\n      <div class=\"toolbar-left\">\r\n        <!-- 展开/收起全部按钮 -->\r\n        <el-button @click=\"expandAll\" type=\"primary\" size=\"small\" class=\"expand-button\">\r\n          <font-awesome-icon icon=\"up-right-and-down-left-from-center\" /><span class=\"button-text\">Expand All</span>\r\n        </el-button>\r\n        <el-button @click=\"collapseAll\" type=\"primary\" size=\"small\" class=\"collapse-button\">\r\n          <font-awesome-icon icon=\"down-left-and-up-right-to-center\" /><span class=\"button-text\">Collapse All</span>\r\n        </el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Content Area -->\r\n    <div class=\"content-area\">\r\n      <el-collapse v-model=\"activeNames\">\r\n        <div class=\"card-container\">\r\n          <el-collapse-item title=\"Case Setting\" name=\"case\" class=\"custom-card\">\r\n            <el-form :model=\"config\" label-width=\"160px\" label-position=\"top\">\r\n              <!-- Import DBC 按钮 -->\r\n              <el-form-item>\r\n                <el-button @click=\"handleImportDbc\" :loading=\"importLoading\" type=\"primary\" size=\"small\" style=\"margin-bottom: 15px;\">\r\n                  Import DBC\r\n                </el-button>\r\n              </el-form-item>\r\n\r\n              <!-- 节点选择 -->\r\n              <el-form-item label=\"Target ECU (DUT)\" v-if=\"nodeNames.length > 0\">\r\n                <el-select\r\n                  v-model=\"selectedNode\"\r\n                  placeholder=\"Select target ECU\"\r\n                  style=\"width: 100%;\"\r\n                  @change=\"handleNodeChange\"\r\n                >\r\n                  <el-option\r\n                    v-for=\"node in nodeNames\"\r\n                    :key=\"node\"\r\n                    :label=\"node\"\r\n                    :value=\"node\"\r\n                  />\r\n                </el-select>\r\n              </el-form-item>\r\n\r\n              <el-form-item :label=\"`White List Frames (${filteredFrames.length})`\">\r\n                <div class=\"import-note\" v-if=\"allFrames.length === 0\">\r\n                  No frames imported. Please use \"Import DBC\" button above to add frames.\r\n                </div>\r\n                <div v-else-if=\"!selectedNode\" class=\"node-selection-required\">\r\n                  Please select a target ECU to view related frames\r\n                </div>\r\n                <el-table\r\n                  :data=\"filteredFrames\"\r\n                  style=\"width: 100%\"\r\n                  v-else\r\n                  :max-height=\"400\"\r\n                  border\r\n                >\r\n                  <el-table-column label=\"ID\" min-width=\"120\">\r\n                    <template #default=\"{row}\">\r\n                      0x{{ row.id.toString(16).toUpperCase().padStart(3, '0') }}\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column prop=\"name\" label=\"Name\" min-width=\"180\" />\r\n                  <el-table-column label=\"DLC\" min-width=\"80\">\r\n                    <template #default=\"{row}\">\r\n                      0x{{ row.dlc.toString(16).toUpperCase() }}\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column label=\"Ext Flag\" min-width=\"120\">\r\n                    <template #default=\"{row}\">\r\n                      {{ row.isExt ? 'CAN Extended' : 'CAN Standard' }}\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column prop=\"transmitter\" label=\"Transmitter\" min-width=\"120\" />\r\n                  <el-table-column label=\"Receivers\" min-width=\"180\">\r\n                    <template #default=\"{row}\">\r\n                      {{ row.receivers.join(', ') }}\r\n                    </template>\r\n                  </el-table-column>\r\n                </el-table>\r\n              </el-form-item>\r\n            </el-form>\r\n          </el-collapse-item>\r\n        </div>\r\n\r\n        <div class=\"card-container\">\r\n          <el-collapse-item title=\"NM Wake Up Setting\" name=\"nmWakeup\" class=\"custom-card\">\r\n            <el-form :model=\"config\" label-width=\"160px\" label-position=\"top\">\r\n              <!-- 第一行：Enable NM Wake Up -->\r\n              <el-form-item label=\"Enable NM Wake Up\">\r\n                <el-switch v-model=\"config.enableNmWakeup\" />\r\n              </el-form-item>\r\n\r\n              <div :class=\"{ 'disabled-form-content': !config.enableNmWakeup }\">\r\n                <!-- 表格布局：ID、IsExtFlag、Frame Type、DLC、Data -->\r\n                <el-table :data=\"[{}]\" border style=\"width: 100%; margin-bottom: 15px;\" :show-header=\"true\" class=\"nm-table\">\r\n                  <el-table-column label=\"ID\" width=\"180\">\r\n                    <template #default>\r\n                      <el-input v-model=\"nmWakeupIdInput\" placeholder=\"Enter ID in hex (e.g.: 53F)\"\r\n                        @change=\"handleNmWakeupIdChange\" :disabled=\"!config.enableNmWakeup\" />\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column label=\"Ext Flag\" width=\"100\">\r\n                    <template #default>\r\n                      <el-switch v-model=\"config.nmWakeupIsExt\" :disabled=\"!config.enableNmWakeup\" />\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column label=\"Frame Type\" width=\"200\">\r\n                    <template #default>\r\n                      <el-select\r\n                        v-model=\"nmWakeupFrameType\"\r\n                        :disabled=\"!config.enableNmWakeup\">\r\n                        <el-option value=\"CAN Frame\" label=\"CAN Frame\" />\r\n                        <el-option value=\"CANFD Frame\" label=\"CANFD Frame\" />\r\n                      </el-select>\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column label=\"DLC\" width=\"150\">\r\n                    <template #default>\r\n                      <el-input v-model=\"nmWakeupDlcInput\" placeholder=\"Enter DLC in hex (e.g.: 0x8)\"\r\n                        @change=\"handleNmWakeupDlcChange\" :disabled=\"!config.enableNmWakeup\" />\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column label=\"Data\">\r\n                    <template #default>\r\n                      <el-input v-model=\"nmWakeupDataInput\"\r\n                        placeholder=\"Enter data bytes (e.g.: 0x3F,0x50,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF)\"\r\n                        @change=\"handleNmWakeupDataChange\" :disabled=\"!config.enableNmWakeup\" />\r\n                    </template>\r\n                  </el-table-column>\r\n                </el-table>\r\n\r\n                <!-- 第三行：Cycle、Wake Up Delay -->\r\n                <div class=\"nm-wakeup-row\">\r\n                  <!-- Cycle -->\r\n                  <el-form-item label=\"Cycle (ms)\" class=\"nm-wakeup-item\">\r\n                    <el-input-number v-model=\"config.nmWakeupCycleMs\" :min=\"1\" :max=\"60000\" style=\"width: 200px\"\r\n                      :disabled=\"!config.enableNmWakeup\" />\r\n                  </el-form-item>\r\n\r\n                  <!-- Wake Up Delay -->\r\n                  <el-form-item label=\"Wake Up Delay (ms)\" class=\"nm-wakeup-item\">\r\n                    <el-input-number v-model=\"config.nmWakeupDelayMs\" :min=\"0\" :max=\"60000\" style=\"width: 200px\"\r\n                      :disabled=\"!config.enableNmWakeup\" />\r\n                  </el-form-item>\r\n                </div>\r\n              </div>\r\n            </el-form>\r\n          </el-collapse-item>\r\n        </div>\r\n\r\n        <div class=\"card-container\">\r\n          <el-collapse-item title=\"UDS Setting\" name=\"uds\" class=\"custom-card\">\r\n            <el-form :model=\"config\" label-width=\"160px\" label-position=\"top\">\r\n              <!-- 第一行：请求ID、Is Ext Flag、响应ID -->\r\n              <div class=\"uds-row\">\r\n                <el-form-item label=\"Diagnostic Request ID\" class=\"uds-item\">\r\n                  <el-input v-model=\"diagReqIdInput\" placeholder=\"Enter ID in hex (e.g.: 731)\"\r\n                    @change=\"handleDiagReqIdChange\" />\r\n                </el-form-item>\r\n\r\n                <el-form-item label=\"Ext Flag\" class=\"uds-item\">\r\n                  <el-switch v-model=\"config.diagReqIsExt\" />\r\n                </el-form-item>\r\n\r\n                <el-form-item label=\"Diagnostic Response ID\" class=\"uds-item\">\r\n                  <el-input v-model=\"diagResIdInput\" placeholder=\"Enter ID in hex (e.g.: 631)\"\r\n                    @change=\"handleDiagResIdChange\" />\r\n                </el-form-item>\r\n              </div>\r\n\r\n              <!-- 第二行：超时和 MTU 设置 -->\r\n              <div class=\"uds-row\">\r\n                <el-form-item label=\"Diagnostic Timeout (ms)\" class=\"uds-item\">\r\n                  <el-input-number v-model=\"config.diagTimeoutMs\" :min=\"1\" :max=\"60000\" style=\"width: 200px\" />\r\n                </el-form-item>\r\n\r\n                <el-form-item label=\"DUT MTU < 4096 bytes\" class=\"uds-item\">\r\n                  <el-switch v-model=\"config.isDutMtuLessThan4096\" />\r\n                </el-form-item>\r\n              </div>\r\n\r\n              <!-- 第三行：备用诊断请求设置 -->\r\n              <div class=\"uds-row\">\r\n                <el-form-item label=\"Enable Fallback Request\" class=\"uds-item\">\r\n                  <el-switch v-model=\"config.enableDiagFallbackRequest\" />\r\n                </el-form-item>\r\n\r\n                <el-form-item label=\"Fallback Request Payload\" class=\"uds-item\">\r\n                  <el-input v-model=\"diagFallbackRequestPayloadInput\"\r\n                    placeholder=\"Enter data bytes (e.g.: 0x10,0x01)\"\r\n                    @change=\"handleDiagFallbackRequestPayloadChange\"\r\n                    :disabled=\"!config.enableDiagFallbackRequest\" />\r\n                </el-form-item>\r\n              </div>\r\n            </el-form>\r\n          </el-collapse-item>\r\n        </div>\r\n\r\n        <div class=\"card-container\">\r\n          <el-collapse-item title=\"Security Access Setting\" name=\"security\" class=\"custom-card\">\r\n            <el-form :model=\"config\" label-width=\"160px\" label-position=\"top\">\r\n              <el-form-item label=\"Security Access Dll\">\r\n                <div class=\"security-input-group\">\r\n                  <el-input v-model=\"securityDllDisplay\" placeholder=\"No Dll selected\" readonly\r\n                    class=\"security-dll-input\"></el-input>\r\n                  <div class=\"security-buttons\">\r\n                    <el-button v-if=\"!config.securityInfo?.hasDll\" @click=\"selectDll\" type=\"primary\" :icon=\"Plus\"\r\n                      title=\"Select DLL\" />\r\n                    <el-button v-if=\"config.securityInfo?.hasDll\" @click=\"removeDll\" type=\"danger\" :icon=\"Delete\"\r\n                      title=\"Remove DLL\" />\r\n                  </div>\r\n                </div>\r\n                <div v-if=\"selectedDllName\" class=\"selected-dll-info\">\r\n                  DLL selected. Click Save to apply.\r\n                </div>\r\n                <div v-if=\"shouldRemoveDll\" class=\"selected-dll-info\">\r\n                  DLL marked for removal. Click Save to apply.\r\n                </div>\r\n              </el-form-item>\r\n            </el-form>\r\n          </el-collapse-item>\r\n        </div>\r\n      </el-collapse>\r\n    </div>\r\n\r\n    <!-- Bottom Toolbar -->\r\n    <div class=\"toolbar bottom-toolbar\">\r\n      <el-button type=\"primary\" @click=\"handleSave\">Save</el-button>\r\n      <el-button @click=\"resetToDefault\" style=\"margin-left: 10px;\">Reset</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { ref, onMounted, computed } from 'vue';\r\nimport { ElMessage } from 'element-plus';\r\nimport { Delete, Plus, ArrowDown, ArrowUp } from '@element-plus/icons-vue';\r\nimport { caseApi, CaseConfigDto, WhiteListFrame } from '@/api/caseApi';\r\n\r\nconst loading = ref(false);\r\nconst config = ref<CaseConfigDto>({\r\n  whiteListFrames: [],\r\n  enableNmWakeup: true,\r\n  nmWakeupId: 0x53F,\r\n  nmWakeupIsExt: false,\r\n  nmWakeupDlc: 8,\r\n  nmWakeupData: [0x3F, 0x50, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF],\r\n  nmWakeupCommunicationType: 'Can',\r\n  nmWakeupCycleMs: 100,\r\n  nmWakeupDelayMs: 2000,\r\n  diagReqId: 0x731,\r\n  diagReqIsExt: false,\r\n  diagResId: 0x631,\r\n  diagTimeoutMs: 500,\r\n  isDutMtuLessThan4096: false,\r\n  enableDiagFallbackRequest: false,\r\n  diagFallbackRequestPayload: [0x10, 0x01],\r\n  securityInfo: {\r\n    hasDll: false,\r\n    dllFileName: undefined,\r\n    dllSize: 0\r\n  }\r\n});\r\n\r\n// 新增变量\r\nconst nodeNames = ref<string[]>([]);\r\nconst selectedNode = ref<string>('');\r\nconst allFrames = ref<WhiteListFrame[]>([]);\r\n\r\n// 根据选定节点过滤帧\r\nconst filteredFrames = computed(() => {\r\n  if (!selectedNode.value) return [];\r\n\r\n  return allFrames.value.filter(frame =>\r\n    // 选中的节点是发送者或接收者之一\r\n    frame.transmitter === selectedNode.value ||\r\n    frame.receivers.includes(selectedNode.value)\r\n  );\r\n});\r\n\r\nconst activeNames = ref(['case', 'nmWakeup', 'uds', 'security']); // 默认展开所有分组\r\n\r\nconst nmWakeupIdInput = ref('');\r\nconst nmWakeupDlcInput = ref('');\r\nconst nmWakeupDataInput = ref('');\r\nconst diagReqIdInput = ref('');\r\nconst diagResIdInput = ref('');\r\nconst diagFallbackRequestPayloadInput = ref('');\r\n\r\n// 用于显示和选择 Frame Type\r\nconst nmWakeupFrameType = computed({\r\n  get: () => {\r\n    return config.value.nmWakeupCommunicationType === 'Can' ? 'CAN Frame' : 'CANFD Frame';\r\n  },\r\n  set: (value: string) => {\r\n    config.value.nmWakeupCommunicationType = value === 'CAN Frame' ? 'Can' : 'Canfd';\r\n  }\r\n});\r\n\r\nconst selectedDllPath = ref<string | null>(null);\r\nconst selectedDllName = ref<string | null>(null);\r\nconst shouldRemoveDll = ref(false);\r\n\r\nconst defaultConfig: CaseConfigDto = {\r\n  whiteListFrames: [], // 修改为空数组\r\n  enableNmWakeup: true,\r\n  nmWakeupId: 0x53F,\r\n  nmWakeupIsExt: false,\r\n  nmWakeupDlc: 8,\r\n  nmWakeupData: [0x3F, 0x50, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF],\r\n  nmWakeupCommunicationType: 'Can',\r\n  nmWakeupCycleMs: 100,\r\n  nmWakeupDelayMs: 2000,\r\n  diagReqId: 0x731,\r\n  diagReqIsExt: false,\r\n  diagResId: 0x631,\r\n  diagTimeoutMs: 500,\r\n  isDutMtuLessThan4096: false,\r\n  enableDiagFallbackRequest: false,\r\n  diagFallbackRequestPayload: [0x10, 0x01],\r\n  securityInfo: {\r\n    hasDll: false,\r\n    dllFileName: undefined,\r\n    dllSize: 0\r\n  }\r\n};\r\n\r\nconst arrayToHexString = (arr: number[]) => {\r\n  return arr.map(n => '0x' + n.toString(16).toUpperCase()).join(',');\r\n};\r\n\r\nconst parseHexString = (str: string): number[] => {\r\n  try {\r\n    return str.split(',')\r\n      .map(s => s.trim())\r\n      .filter(s => s)\r\n      .map(s => parseInt(s, 16));\r\n  } catch {\r\n    return [];\r\n  }\r\n};\r\n\r\nconst handleNmWakeupIdChange = (value: string) => {\r\n  try {\r\n    // 移除可能的 0x 前缀\r\n    const cleanValue = value.replace(/^0x/i, '');\r\n    const id = parseInt(cleanValue, 16);\r\n    if (isNaN(id)) {\r\n      ElMessage.error('Invalid ID format');\r\n      return;\r\n    }\r\n    config.value.nmWakeupId = id;\r\n    // 确保显示带有 0x 前缀\r\n    nmWakeupIdInput.value = '0x' + id.toString(16).toUpperCase();\r\n  } catch (error) {\r\n    ElMessage.error('Invalid input value');\r\n  }\r\n};\r\n\r\nconst handleNmWakeupDlcChange = (value: string) => {\r\n  try {\r\n    // 移除可能的 0x 前缀\r\n    const cleanValue = value.replace(/^0x/i, '');\r\n    const dlc = parseInt(cleanValue, 16);\r\n    if (isNaN(dlc)) {\r\n      ElMessage.error('Invalid DLC format');\r\n      return;\r\n    }\r\n    if (dlc < 0 || dlc > 64) {\r\n      ElMessage.warning('DLC should be between 0 and 64');\r\n      return;\r\n    }\r\n    config.value.nmWakeupDlc = dlc;\r\n    // 确保显示带有 0x 前缀\r\n    nmWakeupDlcInput.value = '0x' + dlc.toString(16).toUpperCase();\r\n  } catch (error) {\r\n    ElMessage.error('Invalid input value');\r\n  }\r\n};\r\n\r\nconst handleNmWakeupDataChange = (value: string) => {\r\n  try {\r\n    const bytes = value.split(',')\r\n      .map(s => s.trim())\r\n      .filter(s => s)\r\n      .map(s => parseInt(s, 16));\r\n\r\n    if (bytes.length > 8) {\r\n      ElMessage.warning('Maximum 8 bytes allowed');\r\n      return;\r\n    }\r\n    if (bytes.some(b => isNaN(b) || b < 0 || b > 0xFF)) {\r\n      ElMessage.warning('Each byte must be between 0x00 and 0xFF');\r\n      return;\r\n    }\r\n    config.value.nmWakeupData = bytes;\r\n  } catch (error) {\r\n    ElMessage.error('Invalid input format');\r\n  }\r\n};\r\n\r\nconst handleDiagReqIdChange = (value: string) => {\r\n  try {\r\n    // 移除可能的 0x 前缀\r\n    const cleanValue = value.replace(/^0x/i, '');\r\n    const id = parseInt(cleanValue, 16);\r\n    if (isNaN(id)) {\r\n      ElMessage.error('Invalid ID format');\r\n      return;\r\n    }\r\n    config.value.diagReqId = id;\r\n    // 确保显示带有 0x 前缀\r\n    diagReqIdInput.value = '0x' + id.toString(16).toUpperCase();\r\n  } catch (error) {\r\n    ElMessage.error('Invalid input value');\r\n  }\r\n};\r\n\r\nconst handleDiagResIdChange = (value: string) => {\r\n  try {\r\n    // 移除可能的 0x 前缀\r\n    const cleanValue = value.replace(/^0x/i, '');\r\n    const id = parseInt(cleanValue, 16);\r\n    if (isNaN(id)) {\r\n      ElMessage.error('Invalid ID format');\r\n      return;\r\n    }\r\n    config.value.diagResId = id;\r\n    // 确保显示带有 0x 前缀\r\n    diagResIdInput.value = '0x' + id.toString(16).toUpperCase();\r\n  } catch (error) {\r\n    ElMessage.error('Invalid input value');\r\n  }\r\n};\r\n\r\nconst handleDiagFallbackRequestPayloadChange = (value: string) => {\r\n  try {\r\n    const bytes = value.split(',')\r\n      .map(s => s.trim())\r\n      .filter(s => s)\r\n      .map(s => parseInt(s, 16));\r\n\r\n    if (bytes.some(b => isNaN(b) || b < 0 || b > 0xFF)) {\r\n      ElMessage.warning('Each byte must be between 0x00 and 0xFF');\r\n      return;\r\n    }\r\n    config.value.diagFallbackRequestPayload = bytes;\r\n  } catch (error) {\r\n    ElMessage.error('Invalid input format');\r\n  }\r\n};\r\n\r\nconst resetToDefault = () => {\r\n  config.value = JSON.parse(JSON.stringify(defaultConfig));\r\n  nmWakeupIdInput.value = '0x' + defaultConfig.nmWakeupId.toString(16).toUpperCase();\r\n  nmWakeupDlcInput.value = '0x' + defaultConfig.nmWakeupDlc.toString(16).toUpperCase();\r\n  nmWakeupDataInput.value = arrayToHexString(defaultConfig.nmWakeupData);\r\n  diagReqIdInput.value = '0x' + defaultConfig.diagReqId.toString(16).toUpperCase();\r\n  diagResIdInput.value = '0x' + defaultConfig.diagResId.toString(16).toUpperCase();\r\n  diagFallbackRequestPayloadInput.value = arrayToHexString(defaultConfig.diagFallbackRequestPayload);\r\n  selectedDllPath.value = null;\r\n  selectedDllName.value = null;\r\n  shouldRemoveDll.value = false;\r\n\r\n  // 重置节点和帧相关状态\r\n  nodeNames.value = [];\r\n  selectedNode.value = '';\r\n  allFrames.value = [];\r\n};\r\n\r\nconst loadConfig = async () => {\r\n  loading.value = true;\r\n  try {\r\n    const response = await caseApi.getCaseConfig();\r\n    config.value = response.data;\r\n    nmWakeupIdInput.value = '0x' + response.data.nmWakeupId.toString(16).toUpperCase();\r\n    nmWakeupDataInput.value = arrayToHexString(response.data.nmWakeupData);\r\n    diagReqIdInput.value = '0x' + response.data.diagReqId.toString(16).toUpperCase();\r\n    diagResIdInput.value = '0x' + response.data.diagResId.toString(16).toUpperCase();\r\n\r\n    // 设置备用诊断请求数据\r\n    if (response.data.diagFallbackRequestPayload) {\r\n      diagFallbackRequestPayloadInput.value = arrayToHexString(response.data.diagFallbackRequestPayload);\r\n    } else {\r\n      diagFallbackRequestPayloadInput.value = '0x10,0x01';\r\n    }\r\n\r\n    // 确保新增字段有默认值\r\n    if (response.data.nmWakeupIsExt === undefined) {\r\n      config.value.nmWakeupIsExt = false;\r\n    }\r\n    if (response.data.nmWakeupDlc === undefined) {\r\n      config.value.nmWakeupDlc = 8;\r\n    }\r\n    if (response.data.diagReqIsExt === undefined) {\r\n      config.value.diagReqIsExt = false;\r\n    }\r\n    if (response.data.enableDiagFallbackRequest === undefined) {\r\n      config.value.enableDiagFallbackRequest = false;\r\n    }\r\n    if (response.data.diagFallbackRequestPayload === undefined) {\r\n      config.value.diagFallbackRequestPayload = [0x10, 0x01];\r\n    }\r\n\r\n    // 设置 DLC 输入框的值\r\n    nmWakeupDlcInput.value = '0x' + config.value.nmWakeupDlc.toString(16).toUpperCase();\r\n\r\n    // 如果已存在帧，尝试提取节点列表\r\n    if (response.data.whiteListFrames.length > 0) {\r\n      const frames = response.data.whiteListFrames;\r\n      allFrames.value = frames;\r\n\r\n      // 提取所有唯一的节点名\r\n      const nodes = new Set<string>();\r\n      frames.forEach(frame => {\r\n        if (frame.transmitter) nodes.add(frame.transmitter);\r\n        frame.receivers?.forEach(r => nodes.add(r));\r\n      });\r\n\r\n      nodeNames.value = Array.from(nodes);\r\n\r\n      // 如果保存了选中的节点，恢复选中状态\r\n      if (response.data.selectedNodeName && nodeNames.value.includes(response.data.selectedNodeName)) {\r\n        selectedNode.value = response.data.selectedNodeName;\r\n      }\r\n      // 否则，如果只有一个节点，自动选择\r\n      else if (nodeNames.value.length === 1) {\r\n        selectedNode.value = nodeNames.value[0];\r\n      }\r\n    }\r\n  } catch (error) {\r\n    ElMessage.error('Failed to load configuration');\r\n  } finally {\r\n    loading.value = false;\r\n  }\r\n};\r\n\r\n// 处理节点改变\r\nconst handleNodeChange = (value: string) => {\r\n  // 更新过滤后的帧列表 (通过 computed 自动实现)\r\n  if (!value) {\r\n    ElMessage.warning('Please select a target ECU');\r\n  }\r\n};\r\n\r\nconst handleSave = async () => {\r\n  loading.value = true;\r\n  try {\r\n    // 准备提交数据\r\n    const submitData = { ...config.value };\r\n\r\n    // 修改: 保存所有帧数据，而不只是筛选后的数据\r\n    submitData.whiteListFrames = allFrames.value;\r\n\r\n    // 保存选中的节点名称\r\n    submitData.selectedNodeName = selectedNode.value;\r\n\r\n    if (selectedDllPath.value) {\r\n      submitData.securityDllPath = selectedDllPath.value;\r\n    }\r\n\r\n    if (shouldRemoveDll.value) {\r\n      submitData.removeSecurityDll = true;\r\n    }\r\n\r\n    const result = await caseApi.updateCaseConfig(submitData);\r\n    config.value = result.data;\r\n\r\n    selectedDllPath.value = null;\r\n    selectedDllName.value = null;\r\n    shouldRemoveDll.value = false;\r\n\r\n    ElMessage.success('Save successful');\r\n  } catch (error) {\r\n    ElMessage.error('Save failed');\r\n  } finally {\r\n    loading.value = false;\r\n  }\r\n};\r\n\r\nconst importLoading = ref(false);\r\n\r\nconst handleImportDbc = async () => {\r\n  importLoading.value = true;\r\n  try {\r\n    const response = await caseApi.importDbc();\r\n\r\n    // 保存所有帧和节点信息\r\n    allFrames.value = response.data.whiteListFrames;\r\n    nodeNames.value = response.data.nodeNames;\r\n\r\n    // 重置选择的节点\r\n    selectedNode.value = '';\r\n\r\n    // 清空当前已保存的帧列表，等待用户选择节点\r\n    config.value.whiteListFrames = [];\r\n\r\n    ElMessage.success('DBC file imported successfully. Please select a target ECU.');\r\n  } catch (error: any) {\r\n    if (error.response?.data === 'UserCanceled') {\r\n      return;\r\n    }\r\n    ElMessage.error(error.response?.data === 'InvalidFileFormat'\r\n      ? 'Invalid DBC file format'\r\n      : 'Failed to import DBC file');\r\n  } finally {\r\n    importLoading.value = false;\r\n  }\r\n};\r\n\r\nconst formatFileSize = (bytes?: number): string => {\r\n  if (!bytes) return '0 B';\r\n\r\n  const units = ['B', 'KB', 'MB', 'GB'];\r\n  let size = bytes;\r\n  let unitIndex = 0;\r\n\r\n  while (size >= 1024 && unitIndex < units.length - 1) {\r\n    size /= 1024;\r\n    unitIndex++;\r\n  }\r\n\r\n  return `${size.toFixed(2)} ${units[unitIndex]}`;\r\n};\r\n\r\nconst selectDll = async () => {\r\n  try {\r\n    const response = await caseApi.selectSecurityDll();\r\n    const dllPath = response.data.path;\r\n    selectedDllPath.value = dllPath;\r\n    selectedDllName.value = dllPath.split('\\\\').pop() || dllPath;\r\n  } catch (error: any) {\r\n    if (error.response?.data === 'UserCanceled') {\r\n      return;\r\n    }\r\n    ElMessage.error(error.response?.data === 'InvalidFileFormat'\r\n      ? 'Invalid DLL file format'\r\n      : 'Failed to select DLL file');\r\n  }\r\n};\r\n\r\nconst removeDll = () => {\r\n  shouldRemoveDll.value = true;\r\n};\r\n\r\nconst securityDllDisplay = computed(() => {\r\n  if (selectedDllName.value) {\r\n    return selectedDllName.value;\r\n  }\r\n\r\n  if (shouldRemoveDll.value) {\r\n    return 'DLL will be removed after save';\r\n  }\r\n\r\n  return config.value.securityInfo?.hasDll\r\n    ? `${config.value.securityInfo.dllFileName} (${formatFileSize(config.value.securityInfo.dllSize)})`\r\n    : '';\r\n});\r\n\r\n\r\n\r\n// 展开全部面板\r\nconst expandAll = () => {\r\n  activeNames.value = ['case', 'nmWakeup', 'uds', 'security'];\r\n};\r\n\r\n// 收起全部面板\r\nconst collapseAll = () => {\r\n  activeNames.value = [];\r\n};\r\n\r\nonMounted(() => {\r\n  loadConfig();\r\n});\r\n</script>\r\n\r\n<style scoped>\r\n.case-setting-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  flex: 1;\r\n  background-color: #ffffff;\r\n  border-radius: 8px;\r\n  padding: 0 10px;\r\n}\r\n\r\n.toolbar {\r\n  padding: 15px 0;\r\n  display: flex;\r\n  background-color: #ffffff;\r\n  z-index: 10;\r\n}\r\n\r\n.top-toolbar {\r\n  border-bottom: 1px solid #e4e7ed;\r\n  position: sticky;\r\n  top: 0;\r\n}\r\n\r\n.bottom-toolbar {\r\n  border-top: 1px solid #e4e7ed;\r\n  position: sticky;\r\n  bottom: 0;\r\n}\r\n\r\n.content-area {\r\n  flex: 1;\r\n  overflow-y: auto;\r\n  padding: 10px 0;\r\n}\r\n\r\n/* 卡片容器样式 */\r\n.card-container {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n/* 自定义卡片样式 */\r\n.custom-card {\r\n  border: 1px solid #e4e7ed;\r\n  border-radius: 4px;\r\n  overflow: hidden;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\r\n  background-color: #fff;\r\n}\r\n\r\n:deep(.el-collapse-item__header) {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  background-color: #f5f7fa;\r\n  padding: 12px 15px;\r\n  border-bottom: 1px solid #e4e7ed;\r\n  transition: background-color 0.3s;\r\n}\r\n\r\n:deep(.el-collapse) {\r\n  border: none;\r\n}\r\n\r\n:deep(.el-collapse-item__wrap) {\r\n  padding: 15px;\r\n  border: none;\r\n  background-color: #ffffff;\r\n}\r\n\r\n.disabled-form-content {\r\n  opacity: 0.6;\r\n  pointer-events: none;\r\n}\r\n\r\n.disabled-form-content :deep(.el-input__wrapper),\r\n.disabled-form-content :deep(.el-input-number__wrapper),\r\n.disabled-form-content :deep(.el-select) {\r\n  cursor: not-allowed;\r\n}\r\n\r\n.security-config {\r\n  padding: 10px;\r\n}\r\n\r\n.dll-info {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.security-input-group {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.security-dll-input {\r\n  width: 400px;\r\n}\r\n\r\n.security-buttons {\r\n  margin-left: 10px;\r\n  display: flex;\r\n  gap: 5px;\r\n}\r\n\r\n.selected-dll-info {\r\n  margin-left: 10px;\r\n  margin-top: 5px;\r\n  font-size: 12px;\r\n  color: var(--el-color-warning);\r\n}\r\n\r\n.warning-text {\r\n  color: #E6A23C;\r\n}\r\n\r\n.node-selection-required {\r\n  color: #E6A23C;\r\n  text-align: center;\r\n  padding: 20px;\r\n  border: 1px dashed #dcdfe6;\r\n  border-radius: 4px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.import-note {\r\n  color: #909399;\r\n  font-style: italic;\r\n  text-align: center;\r\n}\r\n\r\n.white-list-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  width: 100%;\r\n}\r\n\r\n.toolbar-left {\r\n  display: flex;\r\n  gap: 15px;\r\n}\r\n\r\n.expand-button,\r\n.collapse-button {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 5px;\r\n}\r\n\r\n.button-text {\r\n  margin-left: 8px; /* 增加图标和文本之间的水平距离 */\r\n}\r\n\r\n/* NM 唤醒表格样式 */\r\n.nm-table {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n:deep(.nm-table .el-table__header) {\r\n  font-weight: bold;\r\n}\r\n\r\n:deep(.nm-table .el-table__cell) {\r\n  padding: 8px;\r\n}\r\n\r\n:deep(.nm-table .el-input__wrapper),\r\n:deep(.nm-table .el-select) {\r\n  width: 100%;\r\n}\r\n\r\n/* NM Wake Up 设置的行布局 */\r\n.nm-wakeup-row {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 15px;\r\n  margin-bottom: 10px;\r\n  justify-content: flex-start; /* 左对齐 */\r\n}\r\n\r\n.nm-wakeup-item {\r\n  flex: 0 0 auto; /* 不自动拉伸，保持原始大小 */\r\n}\r\n\r\n/* ID、Is Ext Flag、Frame Type、DLC 的宽度 */\r\n.nm-wakeup-item:nth-child(1) {\r\n  width: 180px;\r\n}\r\n\r\n.nm-wakeup-item:nth-child(2) {\r\n  width: 80px;\r\n}\r\n\r\n.nm-wakeup-item:nth-child(3) {\r\n  width: 200px;\r\n}\r\n\r\n.nm-wakeup-item:nth-child(4) {\r\n  width: 150px;\r\n}\r\n\r\n/* Data 字段随页面大小变化 */\r\n.nm-wakeup-item:nth-child(5) {\r\n  flex: 1 1 auto; /* 可以拉伸和收缩 */\r\n  min-width: 300px;\r\n}\r\n\r\n/* 第三行的元素左对齐 */\r\n.nm-wakeup-row:nth-child(2) .nm-wakeup-item {\r\n  width: 180px;\r\n}\r\n\r\n/* UDS Setting 设置的行布局 */\r\n.uds-row {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 15px;\r\n  margin-bottom: 10px;\r\n  justify-content: flex-start; /* 左对齐 */\r\n}\r\n\r\n.uds-item {\r\n  flex: 0 0 auto; /* 不自动拉伸，保持原始大小 */\r\n  min-width: 150px;\r\n}\r\n\r\n/* 请求ID、Is Ext Flag、响应ID 的宽度 */\r\n.uds-row:first-child .uds-item:nth-child(1) {\r\n  width: 200px;\r\n}\r\n\r\n.uds-row:first-child .uds-item:nth-child(2) {\r\n  width: 100px;\r\n}\r\n\r\n.uds-row:first-child .uds-item:nth-child(3) {\r\n  width: 200px;\r\n}\r\n\r\n/* 超时和 MTU 设置的宽度 */\r\n.uds-row:nth-child(2) .uds-item {\r\n  width: 200px;\r\n}\r\n\r\n\r\n</style>\r\n", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\views\\testplan\\BasicSetting.vue", [], "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\api\\interoperationApi.ts", [], "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\components\\TestPlan\\TestPlanHistory.vue", ["326", "327"], "<template>\r\n  <div class=\"history-section\">\r\n    <div class=\"history-header\">\r\n      <div class=\"header-title\">\r\n        <h3>Recently Opened</h3>\r\n      </div>\r\n      <el-button\r\n        v-if=\"historyList.length > 0\"\r\n        type=\"text\"\r\n        size=\"small\"\r\n        @click=\"handleClearHistory\"\r\n        class=\"clear-btn\"\r\n        title=\"Clear history\"\r\n      >\r\n        <font-awesome-icon icon=\"trash-can\" />\r\n      </el-button>\r\n    </div>\r\n\r\n    <!-- 显示加载状态 -->\r\n    <div v-if=\"loading\" class=\"loading-container\">\r\n      <el-skeleton :rows=\"5\" />\r\n    </div>\r\n\r\n    <el-empty\r\n      v-else-if=\"!historyList.length\"\r\n      description=\"No recent items\"\r\n      :image-size=\"150\"\r\n    >\r\n      <template #image>\r\n        <font-awesome-icon icon=\"file-circle-exclamation\" class=\"empty-icon\" />\r\n      </template>\r\n      <template #description>\r\n        <p class=\"empty-description\">No recent items</p>\r\n        <p class=\"empty-tip\">Recently opened plans will appear here</p>\r\n      </template>\r\n    </el-empty>\r\n\r\n    <template v-else>\r\n      <div class=\"history-list\" v-loading=\"loading\">\r\n        <div\r\n          v-for=\"(item, index) in displayedHistory\"\r\n          :key=\"item.id\"\r\n          class=\"history-item\"\r\n          :class=\"{ 'history-item-alt': index % 2 === 1 }\"\r\n          @click=\"handleOpenPlan(item.filePath)\"\r\n        >\r\n          <div class=\"item-grid\">\r\n            <!-- 左上角: 计划名称 -->\r\n            <div class=\"item-name\">\r\n              <el-tag size=\"small\" effect=\"plain\" class=\"plan-name\">{{ item.planName }}</el-tag>\r\n            </div>\r\n\r\n            <!-- 右上角: 打开文件夹按钮 -->\r\n            <div class=\"item-action\">\r\n              <el-button\r\n                type=\"text\"\r\n                size=\"small\"\r\n                @click.stop=\"handleOpenFolder(item.filePath)\"\r\n                title=\"Open in File Explorer\"\r\n              >\r\n                <font-awesome-icon icon=\"folder-open\" />\r\n              </el-button>\r\n            </div>\r\n\r\n            <!-- 左下角: 文件路径 -->\r\n            <div class=\"item-path\" :title=\"item.filePath\">\r\n              <font-awesome-icon icon=\"folder\" />\r\n              <span class=\"file-path\">{{ item.filePath }}</span>\r\n            </div>\r\n\r\n            <!-- 右下角: 最近访问时间 -->\r\n            <div class=\"access-time\">\r\n              <font-awesome-icon icon=\"clock\" />\r\n              {{ formatDateTime(item.lastAccessTime) }}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <el-pagination\r\n        v-if=\"historyList.length > pageSize\"\r\n        layout=\"prev, pager, next\"\r\n        :total=\"historyList.length\"\r\n        :page-size=\"pageSize\"\r\n        :current-page=\"currentPage\"\r\n        @current-change=\"handlePageChange\"\r\n        class=\"pagination\"\r\n        background\r\n        hide-on-single-page\r\n      />\r\n    </template>\r\n  </div>\r\n</template>\r\n\r\n<script lang=\"ts\">\r\nimport { defineComponent, ref, onMounted, computed } from 'vue';\r\nimport { testPlanService } from '@/services/testPlanService';\r\nimport { ElMessageBox } from 'element-plus';\r\nimport { explorerApi } from '@/api/explorerApi';\r\nimport { testPlanApi } from '@/api/testPlanApi';\r\nimport { testPlanHistoryApi } from '@/api/testPlanHistoryApi';\r\n\r\nexport default defineComponent({\r\n  name: 'TestPlanHistory',\r\n  emits: ['open-plan'],\r\n  setup(props, { emit, expose }) {\r\n    const state = testPlanService.getState();\r\n    const pageSize = 20;\r\n    const currentPage = ref(1);\r\n    const loading = ref(true); // 确保初始时为加载状态\r\n\r\n    const historyList = computed(() => state.recentPlans);\r\n\r\n    const displayedHistory = computed(() => {\r\n      const start = (currentPage.value - 1) * pageSize;\r\n      const end = start + pageSize;\r\n      return historyList.value.slice(start, end);\r\n    });\r\n\r\n    const fetchHistory = async () => {\r\n      loading.value = true; // 开始加载时设置loading为true\r\n      try {\r\n        await testPlanService.loadRecentPlans();\r\n        currentPage.value = 1; // 重置页码\r\n      } finally {\r\n        loading.value = false; // 加载完成后设置loading为false\r\n      }\r\n    };\r\n\r\n    const handlePageChange = (page: number) => {\r\n      currentPage.value = page;\r\n    };\r\n\r\n    const handleClearHistory = () => {\r\n      ElMessageBox.confirm(\r\n        'Are you sure you want to clear all recent items?',\r\n        'Warning',\r\n        {\r\n          confirmButtonText: 'Confirm',\r\n          cancelButtonText: 'Cancel',\r\n          type: 'warning'\r\n        }\r\n      ).then(async () => {\r\n        await testPlanService.clearHistory();\r\n      }).catch(() => {\r\n        // 用户取消操作，不需要做任何处理\r\n      });\r\n    };\r\n\r\n    const handleOpenPlan = async (path: string) => {\r\n      try {\r\n        // 先检查文件是否存在\r\n        const response = await testPlanApi.checkFileExists(path);\r\n\r\n        if (response.data.exists) {\r\n          // 文件存在，正常打开\r\n          emit('open-plan', path);\r\n        } else {\r\n          // 文件不存在，提示用户\r\n          ElMessageBox.confirm(\r\n            '测试计划文件不存在，是否从历史记录中删除该条目？',\r\n            '文件不存在',\r\n            {\r\n              confirmButtonText: '删除',\r\n              cancelButtonText: '取消',\r\n              type: 'warning'\r\n            }\r\n          ).then(async () => {\r\n            // 用户选择删除\r\n            await testPlanHistoryApi.deleteRecord(path);\r\n            // 刷新历史记录\r\n            fetchHistory();\r\n          }).catch(() => {\r\n            // 用户取消，不做任何操作\r\n          });\r\n        }\r\n      } catch (error) {\r\n        console.error('检查文件存在性失败:', error);\r\n      }\r\n    };\r\n\r\n    const handleOpenFolder = async (path: string) => {\r\n      try {\r\n        await explorerApi.openExplorer(path);\r\n      } catch (error) {\r\n        console.error('Failed to open folder', error);\r\n      }\r\n    };\r\n\r\n    const formatDateTime = (dateTimeStr: string) => {\r\n      const date = new Date(dateTimeStr);\r\n      return date.toLocaleString('en-US', {\r\n        year: 'numeric',\r\n        month: '2-digit',\r\n        day: '2-digit',\r\n        hour: '2-digit',\r\n        minute: '2-digit'\r\n      });\r\n    };\r\n\r\n    // 组件挂载时获取历史记录\r\n    onMounted(fetchHistory);\r\n\r\n    // 暴露刷新方法给父组件\r\n    expose({\r\n      refresh: fetchHistory\r\n    });\r\n\r\n    return {\r\n      historyList,\r\n      displayedHistory,\r\n      currentPage,\r\n      pageSize,\r\n      loading, // 返回loading变量\r\n      handleClearHistory,\r\n      handleOpenPlan,\r\n      handleOpenFolder,\r\n      formatDateTime,\r\n      handlePageChange\r\n    };\r\n  }\r\n});\r\n</script>\r\n\r\n<style scoped>\r\n.history-section {\r\n  display: flex;\r\n  flex-direction: column;\r\n  height: 100%;\r\n}\r\n\r\n.history-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 16px;\r\n  padding-bottom: 12px;\r\n  border-bottom: 1px solid var(--el-border-color-lighter);\r\n}\r\n\r\n.header-title {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.header-title h3 {\r\n  margin: 0;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n}\r\n\r\n.header-icon {\r\n  color: var(--el-color-primary);\r\n  font-size: 18px;\r\n}\r\n\r\n.clear-btn {\r\n  color: var(--el-text-color-primary);\r\n  padding: 4px;\r\n  font-size: 16px;\r\n}\r\n\r\n.empty-icon {\r\n  font-size: 60px;\r\n  color: #DDD;\r\n}\r\n\r\n.empty-description {\r\n  margin: 10px 0 0;\r\n  font-size: 16px;\r\n  color: var(--el-text-color-secondary);\r\n}\r\n\r\n.empty-tip {\r\n  margin: 5px 0 0;\r\n  font-size: 14px;\r\n  color: var(--el-text-color-placeholder);\r\n}\r\n\r\n.history-list {\r\n  flex: 1;\r\n  overflow-y: auto;\r\n}\r\n\r\n.history-item {\r\n  padding: 12px 16px;\r\n  margin-bottom: 8px;\r\n  border-radius: 6px;\r\n  background-color: var(--el-bg-color-page);\r\n  border-left: 3px solid var(--el-color-primary-light-5);\r\n  cursor: pointer;\r\n}\r\n\r\n.history-item:hover {\r\n  background-color: var(--el-color-primary-light-9);\r\n}\r\n\r\n.history-item-alt {\r\n  background-color: var(--el-fill-color-lighter);\r\n  border-left-color: var(--el-color-success-light-5);\r\n}\r\n\r\n.history-item-alt:hover {\r\n  background-color: var(--el-color-success-light-9);\r\n}\r\n\r\n.item-grid {\r\n  display: grid;\r\n  grid-template-columns: 1fr auto;\r\n  grid-template-rows: auto auto;\r\n  grid-gap: 8px;\r\n  width: 100%;\r\n}\r\n\r\n.item-name {\r\n  grid-column: 1;\r\n  grid-row: 1;\r\n}\r\n\r\n.item-action {\r\n  margin-right: -10px;\r\n  grid-column: 2;\r\n  grid-row: 1;\r\n  justify-self: end;\r\n}\r\n\r\n.item-path {\r\n  grid-column: 1;\r\n  grid-row: 2;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n  color: var(--el-text-color-secondary);\r\n  font-size: 13px;\r\n}\r\n\r\n.access-time {\r\n  grid-column: 2;\r\n  grid-row: 2;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n  font-size: 12px;\r\n  color: var(--el-text-color-secondary);\r\n  justify-self: end;\r\n}\r\n\r\n.plan-name {\r\n  font-weight: 500;\r\n  padding: 4px 8px;\r\n  max-width: 200px;\r\n  overflow: hidden;\r\n  white-space: nowrap;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n.file-path {\r\n  overflow: hidden;\r\n  white-space: nowrap;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n/* 移除不再需要的样式 */\r\n.item-content,\r\n.item-header,\r\n.item-actions {\r\n  display: none;\r\n}\r\n\r\n.pagination {\r\n  margin-top: 16px;\r\n  text-align: center;\r\n}\r\n\r\n/* 添加加载容器样式 */\r\n.loading-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  padding: 20px;\r\n  min-height: 200px;\r\n}\r\n\r\n</style>\r\n", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\components\\Guide\\TestPlanGuide.vue", [], "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\components\\TestSuite\\XmlViewer.vue", [], "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\components\\TestCases\\InteroperationResultPanel.vue", ["328", "329"], "<template>\r\n  <div class=\"result-panel\">\r\n    <div class=\"panel-header\">\r\n      <h3>Interoperation Results</h3>\r\n      <div class=\"panel-subtitle\">Showing only successful sequences</div>\r\n    </div>\r\n    \r\n    <!-- 新增: 显示重复序列名称警告 -->\r\n    <div v-if=\"duplicateSequences.length > 0\" class=\"duplicate-warning\">\r\n      <el-alert\r\n        title=\"Warning: Duplicate sequence names detected\"\r\n        type=\"warning\"\r\n        :closable=\"false\"\r\n        show-icon\r\n      >\r\n        <template #default>\r\n          Found duplicate names: {{ duplicateSequences.join(', ') }}.<br />\r\n          This may cause selection issues.\r\n        </template>\r\n      </el-alert>\r\n    </div>\r\n    \r\n    <div v-if=\"loading\" class=\"loading-indicator\">\r\n      <el-icon class=\"is-loading\"><Loading /></el-icon> Loading...\r\n    </div>\r\n    \r\n    <div v-else-if=\"results.length === 0\" class=\"empty-message\">\r\n      No successful results available\r\n    </div>\r\n    \r\n    <div v-else class=\"results-list\">\r\n      <!-- 全选复选框 -->\r\n      <div class=\"select-all-row\">\r\n        <el-checkbox \r\n          v-model=\"isAllSelected\"\r\n          :indeterminate=\"isIndeterminate\"\r\n          @change=\"handleSelectAllChange\"\r\n          size=\"small\"\r\n        >\r\n          Select All\r\n        </el-checkbox>\r\n      </div>\r\n      \r\n      <!-- 优化后的单行项布局 -->\r\n      <div v-for=\"(item, index) in results\" \r\n           :key=\"item.id || `item-${index}`\" \r\n           class=\"list-item\" \r\n           :class=\"{ 'striped': index % 2 === 1, 'duplicate': isDuplicate(item.sequenceName) }\"\r\n           @click.stop=\"handleItemClick(index)\">\r\n        <!-- 使用直接的勾选元素 -->\r\n        <div class=\"item-checkbox\">\r\n          <el-checkbox \r\n            v-model=\"selectedItems[index]\"\r\n            size=\"small\"\r\n            @click.stop\r\n            @change=\"val => handleItemSelect(val, index, item)\"\r\n          />\r\n        </div>\r\n        \r\n        <div class=\"item-name\">\r\n          {{ item.sequenceName }}\r\n          <el-tag v-if=\"isDuplicate(item.sequenceName)\" size=\"small\" type=\"warning\" effect=\"dark\" class=\"duplicate-tag\">\r\n            Duplicate\r\n          </el-tag>\r\n        </div>\r\n        \r\n        <div class=\"item-status\">\r\n          <CaseStateTag :state=\"item.state\" />\r\n        </div>\r\n        \r\n        <div class=\"item-duration\">\r\n          <TimeDisplay \r\n            :begin=\"item.begin\" \r\n            :end=\"item.end\" \r\n            :showDuration=\"true\"\r\n          />\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { defineProps, defineEmits, ref, computed, watch, onMounted } from 'vue';\r\nimport { Loading } from '@element-plus/icons-vue';\r\nimport { CaseResult, ExecutionStateString } from '@/api/interoperationApi';\r\nimport { ElAlert } from 'element-plus';\r\nimport CaseStateTag from '@/components/common/CaseStateTag.vue';\r\nimport TimeDisplay from '@/components/common/TimeDisplay.vue';\r\n\r\nconst props = defineProps<{\r\n  results: CaseResult[];\r\n  loading: boolean;\r\n  selectedSequences?: string[];\r\n}>();\r\n\r\nconst emit = defineEmits<{\r\n  (e: 'update:selectedSequences', value: string[]): void;\r\n}>();\r\n\r\n// 选中的项目数组\r\nconst selectedItems = ref<boolean[]>([]);\r\n\r\n// 初始化选中项，在 onMounted 中处理\r\nonMounted(() => {\r\n  initializeSelectedItems();\r\n});\r\n\r\n// 监听结果变化，更新选中状态\r\nwatch(() => props.results, () => {\r\n  initializeSelectedItems();\r\n}, { immediate: false });\r\n\r\n// 监听外部传入的已选中序列\r\nwatch(() => props.selectedSequences, (newSelected) => {\r\n  if (newSelected && props.results.length > 0) {\r\n    selectedItems.value = props.results.map(item => \r\n      newSelected.includes(item.sequenceName)\r\n    );\r\n  }\r\n}, { deep: true });\r\n\r\n// 初始化选中状态函数 - 默认全选\r\nfunction initializeSelectedItems() {\r\n  if (props.results.length > 0) {\r\n    // 默认全选\r\n    selectedItems.value = new Array(props.results.length).fill(true);\r\n    updateSelectedSequences();\r\n  }\r\n}\r\n\r\n// 计算全选状态\r\nconst isAllSelected = computed(() => {\r\n  return selectedItems.value.length > 0 && selectedItems.value.every(selected => selected);\r\n});\r\n\r\n// 计算部分选中状态\r\nconst isIndeterminate = computed(() => {\r\n  return selectedItems.value.some(selected => selected) && !isAllSelected.value;\r\n});\r\n\r\n// 处理全选/取消全选\r\nconst handleSelectAllChange = (val: boolean) => {\r\n  selectedItems.value = new Array(props.results.length).fill(val);\r\n  updateSelectedSequences();\r\n};\r\n\r\n// 新增: 点击项目时切换对应的复选框状态\r\nconst handleItemClick = (index: number) => {\r\n  selectedItems.value[index] = !selectedItems.value[index];\r\n  updateSelectedSequences();\r\n};\r\n\r\n// 处理单个项目选中/取消选中\r\nconst handleItemSelect = (val: boolean, index: number, item: CaseResult) => {\r\n  selectedItems.value[index] = val;\r\n  updateSelectedSequences();\r\n};\r\n\r\n// 更新选中的序列列表并触发事件 - 修正为使用索引跟踪\r\nconst updateSelectedSequences = () => {\r\n  // 根据索引获取选中的结果项，然后提取其序列名称\r\n  const selected = props.results\r\n    .filter((_, index) => selectedItems.value[index])\r\n    .map(item => item.sequenceName);\r\n  \r\n  // 使用Set去重，确保不会因为重复的sequenceName导致问题\r\n  const uniqueSelected = [...new Set(selected)];\r\n  \r\n  emit('update:selectedSequences', uniqueSelected);\r\n};\r\n\r\n// 获取状态文本\r\nconst getStatusText = (state: ExecutionStateString): string => {\r\n  switch (state) {\r\n    case 'Success':\r\n      return 'Passed';\r\n    case 'Failure':\r\n      return 'Failed';\r\n    case 'Running':\r\n      return 'Running';\r\n    case 'Pending':\r\n      return 'Pending';\r\n    default:\r\n      return 'Unknown';\r\n  }\r\n};\r\n\r\n// 检测重复的序列名称\r\nconst duplicateSequences = computed(() => {\r\n  if (!props.results || props.results.length === 0) return [];\r\n\r\n  const nameCounts = new Map<string, number>();\r\n  const duplicates = new Set<string>();\r\n\r\n  // 统计每个序列名称出现的次数\r\n  props.results.forEach(result => {\r\n    const count = nameCounts.get(result.sequenceName) || 0;\r\n    nameCounts.set(result.sequenceName, count + 1);\r\n    \r\n    // 如果出现次数超过1，则是重复的\r\n    if (count > 0) {\r\n      duplicates.add(result.sequenceName);\r\n    }\r\n  });\r\n\r\n  return Array.from(duplicates);\r\n});\r\n\r\n// 检查特定的序列名称是否重复\r\nconst isDuplicate = (name: string) => {\r\n  return duplicateSequences.value.includes(name);\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.result-panel {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  overflow: hidden;\r\n  border: 1px solid #dcdfe6;\r\n  border-radius: 4px;\r\n}\r\n\r\n.panel-header {\r\n  padding: 8px 12px;\r\n  border-bottom: 1px solid #ebeef5;\r\n  \r\n  h3 {\r\n    margin: 0;\r\n    font-size: 15px;\r\n    font-weight: 500;\r\n    color: #303133;\r\n  }\r\n  \r\n  .panel-subtitle {\r\n    font-size: 12px;\r\n    color: #909399;\r\n    margin-top: 2px;\r\n  }\r\n}\r\n\r\n.select-all-row {\r\n  padding: 8px 12px;\r\n  border-bottom: 1px solid #ebeef5;\r\n  background-color: var(--el-fill-color-light);\r\n}\r\n\r\n.results-list {\r\n  flex: 1;\r\n  overflow-y: auto;\r\n}\r\n\r\n.list-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 8px 12px;\r\n  border-bottom: 1px solid #ebeef5;\r\n  cursor: pointer;\r\n  transition: background-color 0.2s;\r\n  \r\n  &:last-child {\r\n    border-bottom: none;\r\n  }\r\n  \r\n  &.striped {\r\n    background-color: #fafafa;\r\n  }\r\n  \r\n  &:hover {\r\n    background-color: #f5f7fa;\r\n  }\r\n  \r\n  /* 单行布局样式 */\r\n  .item-checkbox {\r\n    flex: 0 0 auto;\r\n    margin-right: 10px;\r\n  }\r\n  \r\n  .item-name {\r\n    flex: 1;\r\n    font-weight: 500;\r\n    color: #303133;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    white-space: nowrap;\r\n  }\r\n  \r\n  .item-status {\r\n    flex: 0 0 80px;\r\n    text-align: right;\r\n    margin-right: 10px;\r\n  }\r\n  \r\n  .item-duration {\r\n    flex: 0 0 50px;\r\n    text-align: right;\r\n    color: #909399;\r\n    font-size: 12px;\r\n    min-width: 120px;\r\n  }\r\n}\r\n\r\n.loading-indicator, .empty-message {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  flex: 1;\r\n  color: #909399;\r\n  font-size: 13px;\r\n  \r\n  .el-icon {\r\n    margin-right: 6px;\r\n  }\r\n}\r\n\r\n.duplicate-warning {\r\n  margin: 0 12px;\r\n  padding-top: 12px;\r\n}\r\n\r\n.duplicate-tag {\r\n  margin-left: 8px;\r\n  font-size: 10px;\r\n  line-height: 1;\r\n  padding: 2px 4px;\r\n  border-radius: 2px;\r\n}\r\n\r\n.list-item {\r\n  // ...existing code...\r\n  \r\n  &.duplicate {\r\n    background-color: rgba(255, 229, 100, 0.1);\r\n    \r\n    &:hover {\r\n      background-color: rgba(255, 229, 100, 0.2);\r\n    }\r\n  }\r\n}\r\n</style>\r\n", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\components\\TestCases\\GeneratedCasesPanel.vue", ["330"], "<template>\r\n  <div class=\"case-panel\">\r\n    <div class=\"panel-header\">\r\n      <div class=\"header-main\">\r\n        <h3>Generated Test Cases</h3>\r\n      </div>\r\n      <div v-if=\"cases.length > 0\" class=\"case-info\">\r\n        <span class=\"case-count\">Generated: {{ cases.length }} cases</span>\r\n        <span class=\"estimated-time\">\r\n          Estimated Time: {{ formatEstimatedTimeMs(estimatedTime) }}\r\n        </span>\r\n      </div>\r\n    </div>\r\n\r\n    <div v-if=\"generating\" class=\"loading-indicator\">\r\n      <el-icon class=\"is-loading\"><Loading /></el-icon> Generating...\r\n    </div>\r\n\r\n    <div v-else-if=\"cases.length === 0 && showEmptyMessage\" class=\"empty-message\">\r\n      Click Generate to create test cases\r\n    </div>\r\n\r\n    <div v-else-if=\"cases.length > 0\" class=\"results-list\">\r\n      <div v-for=\"group in groupSummary\"\r\n           :key=\"group.groupPrefix\"\r\n           class=\"group-item\">\r\n        <div class=\"group-header\" @click=\"toggleGroup(group.groupPrefix)\">\r\n          <div class=\"group-info\">\r\n            <el-icon class=\"expand-icon\" :class=\"{ 'is-expanded': expandedGroups[group.groupPrefix] }\">\r\n              <CaretRight />\r\n            </el-icon>\r\n            <span class=\"group-name\">{{ group.groupPrefix }}</span>\r\n            <span class=\"group-count\">({{ group.count }} cases)</span>\r\n            <span class=\"group-time\">\r\n              Estimated Time: {{ formatEstimatedTimeMs(getGroupEstimatedTime(group.groupPrefix, group.count)) }}\r\n            </span>\r\n          </div>\r\n        </div>\r\n\r\n        <div v-if=\"expandedGroups[group.groupPrefix]\" class=\"group-content\">\r\n          <div v-if=\"loadingGroups[group.groupPrefix]\" class=\"group-loading\">\r\n            <el-icon class=\"is-loading\"><Loading /></el-icon> Loading...\r\n          </div>\r\n\r\n          <template v-else-if=\"loadedGroups[group.groupPrefix]\">\r\n            <div v-for=\"(item, index) in loadedGroups[group.groupPrefix]\"\r\n                 :key=\"item.id || index\"\r\n                 class=\"list-item\"\r\n                 :class=\"{ 'striped': index % 2 === 1 }\">\r\n              <div class=\"item-header\">\r\n                <div class=\"sequence-group\">\r\n                  <span class=\"sequence-name\">{{ item.id }}</span>\r\n                  <span class=\"group-badge\">{{ item.name }}</span>\r\n                </div>\r\n              </div>\r\n              <div class=\"parameter-text\" :title=\"`${item.parameter}`\">{{ item.parameter }}</div>\r\n            </div>\r\n          </template>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { defineProps, ref, computed, reactive, watch } from 'vue';\r\nimport { Loading, CaretRight } from '@element-plus/icons-vue';\r\nimport { CaseResult } from '@/api/interoperationApi';\r\n\r\nconst props = defineProps<{\r\n  cases: CaseResult[];\r\n  generating: boolean;\r\n  showEmptyMessage: boolean;\r\n  estimatedTime: number; // 单位:毫秒\r\n}>();\r\n\r\n// 分组展开状态\r\nconst expandedGroups = ref<Record<string, boolean>>({});\r\n// 已加载的分组内容\r\nconst loadedGroups = reactive<Record<string, CaseResult[]>>({});\r\n// 正在加载的分组\r\nconst loadingGroups = reactive<Record<string, boolean>>({});\r\n\r\n// 计算分组摘要信息（不包含具体items）\r\nconst groupSummary = computed(() => {\r\n  // 按前缀统计每个组的数量\r\n  const groups: Record<string, number> = {};\r\n\r\n  for (const item of props.cases) {\r\n    const nameParts = (item.name || '').split('-');\r\n    const prefix = nameParts.length > 0 ? nameParts[0] : item.name;\r\n\r\n    if (!groups[prefix]) {\r\n      groups[prefix] = 0;\r\n    }\r\n    groups[prefix]++;\r\n  }\r\n\r\n  // 转换为数组并排序\r\n  return Object.entries(groups).map(([groupPrefix, count]) => ({\r\n    groupPrefix,\r\n    count\r\n  })).sort((a, b) => a.groupPrefix.localeCompare(b.groupPrefix));\r\n});\r\n\r\n// 获取分组的预估时间\r\nconst getGroupEstimatedTime = (groupPrefix: string, groupCount: number) => {\r\n  // 根据组内用例数量计算时间\r\n  const timePerCase = props.estimatedTime / props.cases.length;\r\n  return timePerCase * groupCount;\r\n};\r\n\r\n// 加载指定分组的数据\r\nconst loadGroupData = (groupPrefix: string) => {\r\n  // 标记为加载中\r\n  loadingGroups[groupPrefix] = true;\r\n\r\n  // 模拟异步加载（实际上是从cases中筛选）\r\n  setTimeout(() => {\r\n    const groupItems = props.cases.filter(item => {\r\n      const nameParts = (item.name || '').split('-');\r\n      const prefix = nameParts.length > 0 ? nameParts[0] : item.name;\r\n      return prefix === groupPrefix;\r\n    });\r\n\r\n    // 保存加载的数据\r\n    loadedGroups[groupPrefix] = groupItems;\r\n    loadingGroups[groupPrefix] = false;\r\n  }, 100); // 添加小延迟以便显示加载状态\r\n};\r\n\r\n// 控制分组展开/收起\r\nconst toggleGroup = (groupPrefix: string) => {\r\n  const isCurrentlyExpanded = expandedGroups.value[groupPrefix];\r\n\r\n  // 切换展开状态\r\n  expandedGroups.value[groupPrefix] = !isCurrentlyExpanded;\r\n\r\n  // 如果是展开且还未加载数据，则加载数据\r\n  if (!isCurrentlyExpanded && !loadedGroups[groupPrefix]) {\r\n    loadGroupData(groupPrefix);\r\n  }\r\n};\r\n\r\n// 监听cases属性变化，重置组件状态\r\nwatch(() => props.cases, (newCases) => {\r\n  console.log('Cases changed:', newCases.length);\r\n  // 清空已加载的分组数据\r\n  Object.keys(loadedGroups).forEach(key => {\r\n    delete loadedGroups[key];\r\n  });\r\n\r\n  // 清空加载状态\r\n  Object.keys(loadingGroups).forEach(key => {\r\n    delete loadingGroups[key];\r\n  });\r\n\r\n  // 重置展开状态\r\n  expandedGroups.value = {};\r\n}, { deep: true });\r\n\r\n// 格式化预估时间(毫秒)，最多显示两个单位\r\nconst formatEstimatedTimeMs = (ms: number) => {\r\n  ms = Math.floor(ms);\r\n\r\n  if (ms < 1000) {\r\n    return `${ms}ms`;\r\n  } else if (ms < 60000) {\r\n    const seconds = Math.floor(ms / 1000);\r\n    const remainingMs = Math.floor(ms % 1000);\r\n    return `${seconds}s ${remainingMs}ms`;\r\n  } else if (ms < 3600000) {\r\n    const minutes = Math.floor(ms / 60000);\r\n    const seconds = Math.floor((ms % 60000) / 1000);\r\n    return `${minutes}m ${seconds}s`;\r\n  } else {\r\n    const hours = Math.floor(ms / 3600000);\r\n    const minutes = Math.floor((ms % 3600000) / 60000);\r\n    return `${hours}h ${minutes}m`;\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.case-panel {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  overflow: hidden;\r\n  border: 1px solid #dcdfe6;\r\n  border-radius: 4px;\r\n}\r\n\r\n.panel-header {\r\n  padding: 8px 12px;\r\n  border-bottom: 1px solid #ebeef5;\r\n\r\n  h3 {\r\n    margin: 0;\r\n    font-size: 15px;\r\n    font-weight: 500;\r\n    color: #303133;\r\n  }\r\n\r\n  .case-info {\r\n    margin-top: 2px;\r\n    font-size: 12px;\r\n    color: #909399;\r\n    display: flex;\r\n    gap: 12px;\r\n  }\r\n}\r\n\r\n.results-list {\r\n  flex: 1;\r\n  overflow-y: auto;\r\n\r\n  .list-item {\r\n    padding: 8px 12px;\r\n    border-bottom: 1px solid #ebeef5;\r\n    font-size: 13px;\r\n\r\n    &:last-child {\r\n      border-bottom: none;\r\n    }\r\n\r\n    &.striped {\r\n      background-color: #fafafa;\r\n    }\r\n\r\n    &:hover {\r\n      background-color: #f5f7fa;\r\n    }\r\n  }\r\n}\r\n\r\n.item-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.sequence-group {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n\r\n  .sequence-name {\r\n    font-weight: 500;\r\n    color: #303133;\r\n  }\r\n\r\n  .group-badge {\r\n    font-size: 12px;\r\n    padding: 1px 5px;\r\n    background-color: #ecf5ff;\r\n    color: #409EFF;\r\n    border-radius: 3px;\r\n  }\r\n}\r\n\r\n.parameter-text {\r\n  margin-top: 4px;\r\n  font-size: 12px;\r\n  color: #606266;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  max-width: 30vw;\r\n}\r\n\r\n.loading-indicator, .empty-message {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  flex: 1;\r\n  color: #909399;\r\n  font-size: 13px;\r\n\r\n  .el-icon {\r\n    margin-right: 6px;\r\n  }\r\n}\r\n\r\n.header-main {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.group-item {\r\n  border-bottom: 1px solid #ebeef5;\r\n\r\n  &:last-child {\r\n    border-bottom: none;\r\n  }\r\n}\r\n\r\n.group-header {\r\n  padding: 8px 12px;\r\n  background-color: #f5f7fa;\r\n  cursor: pointer;\r\n  user-select: none;\r\n\r\n  &:hover {\r\n    background-color: #ecf5ff;\r\n  }\r\n\r\n  .group-info {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n\r\n    .expand-icon {\r\n      transition: transform 0.2s;\r\n      font-size: 16px;\r\n\r\n      &.is-expanded {\r\n        transform: rotate(90deg);\r\n      }\r\n    }\r\n\r\n    .group-name {\r\n      font-weight: 500;\r\n      color: #303133;\r\n    }\r\n\r\n    .group-count {\r\n      color: #909399;\r\n      font-size: 12px;\r\n    }\r\n\r\n    .group-time {\r\n      color: #909399;\r\n      font-size: 12px;\r\n      margin-left: auto;\r\n    }\r\n  }\r\n}\r\n\r\n.group-content {\r\n  .list-item {\r\n    padding-left: 32px;\r\n  }\r\n}\r\n\r\n.group-loading {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  padding: 16px;\r\n  color: #909399;\r\n  font-size: 13px;\r\n\r\n  .el-icon {\r\n    margin-right: 6px;\r\n  }\r\n}\r\n</style>\r\n", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\components\\common\\CaseStateTag.vue", [], "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\components\\test\\TestMonitor.vue", [], "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\components\\layout\\SideNav.vue", [], "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\components\\test\\CaseDetailDialog.vue", ["331", "332", "333", "334"], "<template>\r\n  <el-dialog v-model=\"dialogVisible\" :title=\"`${caseData?.name || caseData?.sequenceName || ''}`\" width=\"60%\"\r\n    destroy-on-close>\r\n    <div v-if=\"loading\" class=\"loading\">\r\n      <el-skeleton :rows=\"10\" animated />\r\n    </div>\r\n\r\n    <div v-else class=\"case-detail-content\">\r\n      <!-- 基本信息区域 -->\r\n      <div class=\"basic-info\">\r\n        <h4>Information</h4>\r\n        <div class=\"info-grid\">\r\n          <div class=\"info-item\">\r\n            <div class=\"label\">Case Name:</div>\r\n            <div class=\"value\">{{ caseData?.name }}</div>\r\n          </div>\r\n          <div class=\"info-item\">\r\n            <div class=\"label\">Sequence Name:</div>\r\n            <div class=\"value\">{{ caseData?.sequenceName }}</div>\r\n          </div>\r\n          <div class=\"info-item\">\r\n            <div class=\"label\">Start Time:</div>\r\n            <div class=\"value\">{{ formatDateTime(caseData?.begin) }}</div>\r\n          </div>\r\n          <div class=\"info-item\">\r\n            <div class=\"label\">End Time / Status:</div>\r\n            <div class=\"value status-combined\">\r\n              {{ formatDateTime(caseData?.end) }}\r\n              <CaseStateTag :state=\"caseData?.state || ''\" class=\"status-tag\" />\r\n            </div>\r\n          </div>\r\n          <div v-if=\"caseData?.parameter\" class=\"info-item full-width\">\r\n            <div class=\"label\">Parameter:</div>\r\n            <div class=\"value\" :title=\"caseData?.parameter\">{{ caseData?.parameter }}</div>\r\n          </div>\r\n          <div v-if=\"caseData?.detail\" class=\"info-item full-width\">\r\n            <div class=\"label\">Detail:</div>\r\n            <div class=\"value\" :title=\"caseData?.detail\">{{ caseData.detail }}</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 步骤列表区域 -->\r\n      <div class=\"steps-section\">\r\n        <h4>Steps</h4>\r\n\r\n        <div v-if=\"steps.length === 0\" class=\"no-steps\">\r\n          <el-empty description=\"No steps available\" />\r\n        </div>\r\n\r\n        <el-timeline v-else>\r\n          <el-timeline-item v-for=\"step in steps\" :key=\"step.id\" :type=\"getTimelineItemType(step.state)\"\r\n            :hollow=\"step.state !== 'Success'\">\r\n            <div class=\"step-content\">\r\n              <div class=\"step-row\">\r\n                <div class=\"step-left\">\r\n                  <span class=\"step-timestamp\">{{ formatMicroseconds(step.timestamp) }}</span>\r\n                  <span class=\"step-name\" :title=\"step.name\" >{{ step.name }}</span>\r\n                  <span v-if=\"step.detail\" :title=\"step.detail\" class=\"step-detail-inline\">{{ step.detail }}</span>\r\n                </div>\r\n                <div class=\"step-right\">\r\n                  <CaseStateTag :state=\"step.state\" />\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </el-timeline-item>\r\n        </el-timeline>\r\n      </div>\r\n    </div>\r\n\r\n    <template #footer>\r\n      <span class=\"dialog-footer\">\r\n        <el-button @click=\"closeDialog\">Close</el-button>\r\n      </span>\r\n    </template>\r\n  </el-dialog>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { ref, watch, defineProps, defineEmits, onMounted } from 'vue';\r\nimport { ElMessage } from 'element-plus';\r\nimport { CaseResult } from '@/api/interoperationApi';\r\nimport { appApi, CaseStep } from '@/api/appApi';\r\nimport { caseApi } from '@/api/caseApi';\r\nimport { getTimelineItemType } from '@/utils/status';\r\nimport CaseStateTag from '@/components/common/CaseStateTag.vue';\r\n\r\nconst props = defineProps<{\r\n  visible: boolean;\r\n  testResultId?: string | null;\r\n  caseResultId?: number | null;\r\n}>();\r\n\r\nconst emit = defineEmits<{\r\n  (e: 'update:visible', value: boolean): void;\r\n  (e: 'close'): void;\r\n}>();\r\n\r\nconst dialogVisible = ref(props.visible);\r\nconst caseData = ref<CaseResult | null>(null);\r\nconst steps = ref<CaseStep[]>([]);\r\nconst loading = ref(false);\r\n\r\nwatch(() => props.visible, (newValue) => {\r\n  dialogVisible.value = newValue;\r\n  if (newValue && props.testResultId && props.caseResultId) {\r\n    loadCaseData();\r\n  }\r\n});\r\n\r\nwatch(() => dialogVisible.value, (newValue) => {\r\n  emit('update:visible', newValue);\r\n  if (!newValue) emit('close');\r\n});\r\n\r\nconst loadCaseData = async () => {\r\n  if (!props.testResultId || !props.caseResultId) {\r\n    ElMessage.warning('Missing required parameters');\r\n    return;\r\n  }\r\n\r\n  loading.value = true;\r\n  try {\r\n    // 并行加载用例数据和步骤数据\r\n    const [caseResponse, stepsResponse] = await Promise.all([\r\n      appApi.getCaseResult(props.testResultId, props.caseResultId),\r\n      appApi.getCaseSteps(props.testResultId, props.caseResultId)\r\n    ]);\r\n\r\n    caseData.value = caseResponse.data;\r\n    steps.value = stepsResponse.data;\r\n  } catch (error) {\r\n    console.error('Failed to load case data:', error);\r\n    ElMessage.error('Failed to load case details');\r\n  } finally {\r\n    loading.value = false;\r\n  }\r\n};\r\n\r\nconst closeDialog = () => {\r\n  dialogVisible.value = false;\r\n  // 清空数据\r\n  caseData.value = null;\r\n  steps.value = [];\r\n};\r\n\r\n// 格式化方法保持不变\r\nconst formatDateTime = (dateString?: string | null) => {\r\n  if (!dateString) return 'N/A';\r\n  try {\r\n    const date = new Date(dateString);\r\n    return date.toLocaleString();\r\n  } catch (e) {\r\n    return dateString;\r\n  }\r\n};\r\n\r\nconst formatTime = (dateString: string) => {\r\n  try {\r\n    const date = new Date(dateString);\r\n    return date.toLocaleTimeString();\r\n  } catch (e) {\r\n    return dateString;\r\n  }\r\n};\r\n\r\nconst formatTimestamp = (timestamp: number) => {\r\n  try {\r\n    const date = new Date(timestamp);\r\n    return date.toLocaleString();\r\n  } catch (e) {\r\n    return timestamp.toString();\r\n  }\r\n};\r\n\r\n// 添加新的微秒格式化方法\r\nconst formatMicroseconds = (microseconds: number): string => {\r\n  if (!microseconds && microseconds !== 0) return 'N/A';\r\n\r\n  // 转换为秒并保留6位小数\r\n  const seconds = microseconds / 1000000;\r\n  // 使用toFixed(6)确保始终有6位小数\r\n  return `${seconds.toFixed(6)}`;\r\n};\r\n\r\n// 组件挂载时，如果对话框是可见的且有必要参数，则加载数据\r\nonMounted(() => {\r\n  if (dialogVisible.value && props.testResultId && props.caseResultId) {\r\n    loadCaseData();\r\n  }\r\n});\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.loading {\r\n  min-height: 200px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.case-detail-content {\r\n  padding: 0 20px;\r\n}\r\n\r\n.basic-info {\r\n  margin-bottom: 20px;\r\n\r\n  h4 {\r\n    margin-top: 0;\r\n    margin-bottom: 16px;\r\n    font-size: 16px;\r\n    color: #303133;\r\n    border-bottom: 1px solid #ebeef5;\r\n    padding-bottom: 8px;\r\n  }\r\n\r\n  .info-grid {\r\n    display: grid;\r\n    grid-template-columns: 1fr 1fr;\r\n    gap: 12px 24px;\r\n\r\n    .info-item {\r\n      .label {\r\n        font-size: 13px;\r\n        color: #909399;\r\n        margin-bottom: 4px;\r\n      }\r\n\r\n      .value {\r\n        font-size: 14px;\r\n        color: #303133;\r\n        word-break: break-word;\r\n\r\n        &.status-combined {\r\n          display: flex;\r\n          align-items: center;\r\n          \r\n          .status-tag {\r\n            margin-left: 12px;\r\n          }\r\n        }\r\n      }\r\n\r\n      &.full-width {\r\n        grid-column: span 2;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.steps-section {\r\n  h4 {\r\n    margin-top: 20px;\r\n    margin-bottom: 16px;\r\n    font-size: 16px;\r\n    color: #303133;\r\n    border-bottom: 1px solid #ebeef5;\r\n    padding-bottom: 8px;\r\n  }\r\n\r\n  .no-steps {\r\n    padding: 20px 0;\r\n  }\r\n\r\n  .step-content {\r\n    font-size: 13px;\r\n\r\n    .step-row {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      width: 100%;\r\n      padding: 8px 4px;\r\n      transition: background-color 0.2s;\r\n    }\r\n\r\n    .step-left {\r\n      display: flex;\r\n      align-items: center;\r\n      flex: 1;\r\n      min-width: 0; // 防止flex子元素溢出\r\n    }\r\n\r\n    .step-right {\r\n      margin-left: 10px;\r\n      flex-shrink: 0;\r\n    }\r\n\r\n    .step-timestamp {\r\n      min-width: 80px;\r\n      display: inline-block;\r\n      color: #606266;\r\n      margin-right: 12px;\r\n      flex-shrink: 0;\r\n    }\r\n\r\n    .step-name {\r\n      min-width: 80px;\r\n      font-weight: 500;\r\n      display: inline-block;\r\n      color: #303133;\r\n      margin-right: 8px;\r\n      overflow: hidden;\r\n      text-overflow: ellipsis;\r\n      white-space: nowrap;\r\n      max-width: 200px; /* 限制最大宽度，可根据实际情况调整 */\r\n    }\r\n\r\n    .step-detail-inline {\r\n      color: #606266;\r\n      overflow: hidden;\r\n      text-overflow: ellipsis;\r\n      white-space: nowrap;\r\n    }\r\n  }\r\n\r\n  .el-timeline-item:nth-child(odd) .step-row {\r\n    background-color: #f5f5fa;\r\n  }\r\n\r\n  .el-timeline-item:nth-child(even) .step-row {\r\n    background-color: #f5f5fa;\r\n  }\r\n\r\n  :deep(.el-timeline-item__node) {\r\n    margin-top: 10px;\r\n  }\r\n\r\n  :deep(.el-timeline-item__tail) {\r\n    margin-top: 10px;\r\n  }\r\n}\r\n</style>\r\n", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\components\\hardware\\HardwareConfigPanel.vue", ["335", "336", "337", "338", "339", "340", "341", "342", "343", "344", "345", "346", "347", "348"], "<template>\r\n  <div class=\"hardware-config-panel\">\r\n    <el-form :model=\"form\" label-position=\"top\">\r\n      <el-form-item label=\"Bus Type\">\r\n        <el-radio-group v-model=\"form.communicationType\" @change=\"handleValueChange\">\r\n          <el-radio :label=\"'Can'\">CAN</el-radio>\r\n          <el-radio :label=\"'CanFd'\">CANFD</el-radio>\r\n        </el-radio-group>\r\n      </el-form-item>\r\n\r\n      <template v-if=\"form.communicationType === 'Can'\">\r\n        <el-form-item label=\"Device Channel\">\r\n          <div class=\"device-channel-container\">\r\n            <device-channel-select v-model=\"form.canConfig.deviceChannelName\" :devices=\"canDevices\"\r\n              @change=\"handleValueChange\" />\r\n            <el-tag\r\n              :key=\"updateTimestamp\"\r\n              class=\"device-status-tag\"\r\n              size=\"small\"\r\n              :type=\"getDeviceConnectStatus(form.canConfig.deviceChannelName).isConnected ? 'success' : 'danger'\">\r\n              <el-icon class=\"status-icon\">\r\n                <component :is=\"getDeviceConnectStatus(form.canConfig.deviceChannelName).isConnected ? 'Connection' : 'CircleClose'\" />\r\n              </el-icon>\r\n              <span class=\"status-text\">{{ getDeviceConnectStatus(form.canConfig.deviceChannelName).isConnected ? 'Connected' : 'Disconnected' }}</span>\r\n            </el-tag>\r\n          </div>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"Baud Rate\">\r\n          <div class=\"select-container\">\r\n            <el-select\r\n              v-model=\"form.canConfig.dataBitrate\"\r\n              placeholder=\"Select Baud Rate\"\r\n              class=\"full-width-select\"\r\n              @change=\"handleValueChange\">\r\n              <el-option v-for=\"rate in baudRates\" :key=\"rate\" :label=\"`${rate / 1000} kbit/s`\" :value=\"rate\">\r\n              </el-option>\r\n            </el-select>\r\n          </div>\r\n        </el-form-item>\r\n      </template>\r\n\r\n      <template v-else>\r\n        <el-form-item label=\"Device Channel\">\r\n          <div class=\"device-channel-container\">\r\n            <device-channel-select v-model=\"form.canFdConfig.deviceChannelName\" :devices=\"canFdDevices\"\r\n              @change=\"handleValueChange\" />\r\n            <el-tag class=\"device-status-tag\" size=\"small\"\r\n              :type=\"getDeviceConnectStatus(form.canFdConfig.deviceChannelName).isConnected ? 'success' : 'danger'\">\r\n              <el-icon class=\"status-icon\">\r\n                <component :is=\"getDeviceConnectStatus(form.canFdConfig.deviceChannelName).isConnected ? 'Connection' : 'CircleClose'\" />\r\n              </el-icon>\r\n              <span class=\"status-text\">{{ getDeviceConnectStatus(form.canFdConfig.deviceChannelName).isConnected ? 'Connected' : 'Disconnected' }}</span>\r\n            </el-tag>\r\n          </div>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"Arbitration Phase Baud Rate\">\r\n          <div class=\"select-container\">\r\n            <el-select\r\n              v-model=\"form.canFdConfig.arbitrationBitrate\"\r\n              placeholder=\"Select Arbitration Phase Baud Rate\"\r\n              class=\"full-width-select\"\r\n              @change=\"handleValueChange\">\r\n              <el-option v-for=\"rate in baudRates\" :key=\"rate\" :label=\"`${rate / 1000} kbit/s`\" :value=\"rate\">\r\n              </el-option>\r\n            </el-select>\r\n          </div>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"Data Phase Baud Rate\">\r\n          <div class=\"select-container\">\r\n            <el-select\r\n              v-model=\"form.canFdConfig.dataBitrate\"\r\n              placeholder=\"Select Data Phase Baud Rate\"\r\n              class=\"full-width-select\"\r\n              @change=\"handleValueChange\">\r\n              <el-option v-for=\"rate in fdDataRates\" :key=\"rate\" :label=\"`${rate / 1000} kbit/s`\" :value=\"rate\">\r\n              </el-option>\r\n            </el-select>\r\n          </div>\r\n        </el-form-item>\r\n      </template>\r\n\r\n      <el-form-item v-if=\"hasUnsavedChanges\" class=\"save-status\">\r\n        <span class=\"status-text\">\r\n          <el-icon v-if=\"isSaving\">\r\n            <Loading />\r\n          </el-icon>\r\n          <el-icon v-else-if=\"saveSuccess\" class=\"success-icon\">\r\n            <CircleCheck />\r\n          </el-icon>\r\n          {{ saveStatusText }}\r\n        </span>\r\n      </el-form-item>\r\n    </el-form>\r\n  </div>\r\n</template>\r\n\r\n<script lang=\"ts\">\r\nimport {\r\n  ref,\r\n  computed,\r\n  onMounted,\r\n  onUnmounted,\r\n  watch,\r\n  nextTick,\r\n  defineComponent\r\n} from 'vue';\r\nimport {\r\n  Connection,\r\n  CircleClose\r\n} from '@element-plus/icons-vue';\r\nimport { Refresh, Loading } from '@element-plus/icons-vue';\r\nimport { ElMessage } from 'element-plus';\r\nimport { hardwareApi } from '@/api/hardwareApi';\r\nimport { debounce } from 'lodash';\r\nimport DeviceChannelSelect from './DeviceChannelSelect.vue';\r\n\r\nexport default defineComponent({\r\n  components: {\r\n    DeviceChannelSelect,\r\n    Connection,\r\n    CircleClose\r\n  },\r\n\r\n  props: {\r\n    testPlanId: {\r\n      type: String,\r\n      required: false\r\n    }\r\n  },\r\n\r\n  setup(props) {\r\n    // 添加更新时间戳\r\n    const updateTimestamp = ref(Date.now());\r\n\r\n    // 表单数据\r\n    const form = ref({\r\n      communicationType: 'Can',\r\n      canConfig: {\r\n        deviceChannelName: '',\r\n        dataBitrate: 500000\r\n      },\r\n      canFdConfig: {\r\n        deviceChannelName: '',\r\n        arbitrationBitrate: 500000,\r\n        dataBitrate: 2000000\r\n      }\r\n    });\r\n\r\n    // 保存状态管理\r\n    const originalForm = ref({});\r\n    const hasUnsavedChanges = ref(false);\r\n    const isSaving = ref(false);\r\n    const saveSuccess = ref(false);\r\n    const saveStatusText = ref('');\r\n\r\n    // 状态管理\r\n    const isLoading = ref(false);\r\n    const isRefreshing = ref(false);     // 手动刷新状态（影响UI）\r\n    const hardwareConfig = ref<any>(null);\r\n    const deviceChannels = ref<any[]>([]);\r\n    const lastDeviceChannels = ref<any[]>([]);\r\n\r\n    // 保存状态消息的定时器\r\n    let saveMessageTimer: any = null;\r\n\r\n    // 过滤设备列表\r\n    const canDevices = computed(() => {\r\n      return deviceChannels.value.filter(device =>\r\n        device.communicationType === 'Can' || device.communicationType === 'CanFd');\r\n    });\r\n\r\n    const canFdDevices = computed(() => {\r\n      return deviceChannels.value.filter(device =>\r\n        device.communicationType === 'CanFd');\r\n    });\r\n\r\n    // 配置选项\r\n    const baudRates = ref([125000, 250000, 500000, 1000000]);\r\n    const fdDataRates = ref([1000000, 2000000, 4000000, 8000000]);\r\n\r\n    // 轮询计时器和状态\r\n    let pollingTimer: any = null;\r\n    let pollingActive = true;           // 控制轮询是否活跃\r\n\r\n    // 检查并保存配置变更\r\n    const checkAndSaveConfig = debounce(async () => {\r\n      if (!hasUnsavedChanges.value) return;\r\n\r\n      try {\r\n        isSaving.value = true;\r\n        saveStatusText.value = 'Saving...';\r\n\r\n        const configToSave = {\r\n          communicationType: form.value.communicationType,\r\n          canConfig: form.value.communicationType === 'Can' ? form.value.canConfig : undefined,\r\n          canFdConfig: form.value.communicationType === 'CanFd' ? form.value.canFdConfig : undefined\r\n        };\r\n\r\n        await hardwareApi.updateHardwareConfig(configToSave);\r\n\r\n        // 更新原始表单状态，标记为已保存\r\n        originalForm.value = JSON.parse(JSON.stringify(form.value));\r\n        hasUnsavedChanges.value = false;\r\n        saveSuccess.value = true;\r\n        saveStatusText.value = 'Save successful';\r\n\r\n        // 3秒后隐藏成功消息\r\n        if (saveMessageTimer) clearTimeout(saveMessageTimer);\r\n        saveMessageTimer = setTimeout(() => {\r\n          if (saveSuccess.value && !hasUnsavedChanges.value) {\r\n            saveStatusText.value = '';\r\n          }\r\n        }, 3000);\r\n      } catch (error) {\r\n        console.error('Failed to save hardware config:', error);\r\n        saveStatusText.value = 'Save failed, please retry';\r\n        ElMessage.error('Failed to save hardware configuration');\r\n      } finally {\r\n        isSaving.value = false;\r\n      }\r\n    }, 500);\r\n\r\n    // 处理表单值变化\r\n    const handleValueChange = () => {\r\n      // 检测表单值是否与原始值不同\r\n      const currentFormString = JSON.stringify(form.value);\r\n      const originalFormString = JSON.stringify(originalForm.value);\r\n\r\n      if (currentFormString !== originalFormString) {\r\n        hasUnsavedChanges.value = true;\r\n        saveSuccess.value = false;\r\n        saveStatusText.value = 'Configuration modified, saving...';\r\n        checkAndSaveConfig();\r\n      }\r\n\r\n      // 同时处理通道类型切换导致的设备限制\r\n      handleCommunicationTypeChange();\r\n    };\r\n\r\n    // 加载硬件配置\r\n    const loadHardwareConfig = async () => {\r\n      if (!pollingActive) return;\r\n\r\n      try {\r\n        isLoading.value = true;\r\n        const response = await hardwareApi.getHardwareConfig();\r\n\r\n        // 检查设备列表是否有变化\r\n        const newDeviceChannels = response.data.deviceChannels || [];\r\n        const devicesChanged = hasDeviceListChanged(lastDeviceChannels.value, newDeviceChannels);\r\n\r\n        if (devicesChanged) {\r\n          deviceChannels.value = newDeviceChannels;\r\n          lastDeviceChannels.value = JSON.parse(JSON.stringify(newDeviceChannels));\r\n          updateTimestamp.value = Date.now(); // 更新时间戳\r\n        }\r\n\r\n        // 更新配置\r\n        if (response.data.testPlanConfig && !hasUnsavedChanges.value) {\r\n          const newConfig = response.data.testPlanConfig;\r\n          const configChanged = hasConfigChanged(hardwareConfig.value, newConfig);\r\n\r\n          if (configChanged) {\r\n            hardwareConfig.value = newConfig;\r\n            form.value = {\r\n              communicationType: newConfig.communicationType,\r\n              canConfig: {\r\n                ...form.value.canConfig,\r\n                ...newConfig.canConfig\r\n              },\r\n              canFdConfig: {\r\n                ...form.value.canFdConfig,\r\n                ...newConfig.canFdConfig\r\n              }\r\n            };\r\n            originalForm.value = JSON.parse(JSON.stringify(form.value));\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error('Failed to load hardware config:', error);\r\n        ElMessage.error('Failed to load hardware configuration');\r\n      } finally {\r\n        isLoading.value = false;\r\n      }\r\n    };\r\n\r\n    // 比较设备列表是否有变化\r\n    const hasDeviceListChanged = (oldDevices: any[], newDevices: any[]): boolean => {\r\n      if (!oldDevices || !newDevices) return true;\r\n      if (oldDevices.length !== newDevices.length) return true;\r\n\r\n      // 比较名称和连接状态\r\n      const oldMap = new Map(oldDevices.map(d => [d.name, d.isConnected]));\r\n      return newDevices.some(d => oldMap.get(d.name) !== d.isConnected);\r\n    };\r\n\r\n    // 比较配置是否有变化\r\n    const hasConfigChanged = (oldConfig: any, newConfig: any): boolean => {\r\n      if (!oldConfig && newConfig) return true;\r\n      if (oldConfig && !newConfig) return true;\r\n      if (!oldConfig && !newConfig) return false;\r\n\r\n      try {\r\n        // 使用JSON字符串比较进行浅比较\r\n        const oldConfigStr = JSON.stringify(oldConfig);\r\n        const newConfigStr = JSON.stringify(newConfig);\r\n        return oldConfigStr !== newConfigStr;\r\n      } catch (e) {\r\n        // 如果JSON序列化失败，回退到按属性比较\r\n        return oldConfig.communicationType !== newConfig.communicationType ||\r\n          JSON.stringify(oldConfig.canConfig) !== JSON.stringify(newConfig.canConfig) ||\r\n          JSON.stringify(oldConfig.canFdConfig) !== JSON.stringify(newConfig.canFdConfig);\r\n      }\r\n    };\r\n\r\n    // 硬件配置选项 - 使用固定值代替API调用\r\n    const initHardwareOptions = () => {\r\n      // 使用固定的波特率值\r\n      baudRates.value = [125000, 250000, 500000, 1000000];\r\n      fdDataRates.value = [1000000, 2000000, 4000000, 8000000];\r\n    };\r\n\r\n    // 处理总线类型切换\r\n    const handleCommunicationTypeChange = () => {\r\n      // 如果用户从CanFd切换到Can，需要检查并调整当前选择\r\n      if (form.value.communicationType === 'Can') {\r\n        // 如果之前选择的设备不支持普通Can，则清空选择\r\n        const currentDevice = deviceChannels.value.find(\r\n          d => d.name === form.value.canConfig.deviceChannelName\r\n        );\r\n        if (currentDevice && currentDevice.communicationType !== 'Can' && currentDevice.communicationType !== 'CanFd') {\r\n          form.value.canConfig.deviceChannelName = '';\r\n        }\r\n      } else {\r\n        // 如果从Can切换到CanFd，检查设备是否支持CanFd\r\n        const currentDevice = deviceChannels.value.find(\r\n          d => d.name === form.value.canFdConfig.deviceChannelName\r\n        );\r\n        if (currentDevice && currentDevice.communicationType !== 'CanFd') {\r\n          form.value.canFdConfig.deviceChannelName = '';\r\n        }\r\n      }\r\n    };\r\n\r\n    // 将方法改为计算属性\r\n    const getDeviceConnectStatus = computed(() => {\r\n      return (deviceName: string) => {\r\n        const device = deviceChannels.value.find(d => d.name === deviceName);\r\n        return {\r\n          isConnected: device?.isConnected || false\r\n        };\r\n      };\r\n    });\r\n\r\n    // 获取设备标签，显示连接状态\r\n    const getDeviceLabel = (device: any) => {\r\n      return `${device.name} ${device.isConnected ? '(Connected)' : '(Disconnected)'}`;\r\n    };\r\n\r\n    // 刷新设备列表\r\n    const refreshDevices = async () => {\r\n      try {\r\n        isRefreshing.value = true;\r\n        pollingActive = true;\r\n        await loadHardwareConfig();\r\n        ElMessage.success('Device list refreshed');\r\n      } finally {\r\n        isRefreshing.value = false;\r\n      }\r\n    };\r\n\r\n    // 启动设备轮询\r\n    const startDevicePolling = () => {\r\n      pollingTimer = setInterval(() => {\r\n        loadHardwareConfig();\r\n      }, 2000); // 固定2秒轮询\r\n    };\r\n\r\n    // 停止设备轮询\r\n    const stopDevicePolling = () => {\r\n      pollingActive = false; // 标记轮询为非活跃\r\n      if (pollingTimer) {\r\n        clearInterval(pollingTimer);\r\n        pollingTimer = null;\r\n      }\r\n    };\r\n\r\n    // 组件挂载时加载配置\r\n    onMounted(async () => {\r\n      initHardwareOptions();\r\n      await loadHardwareConfig();\r\n\r\n      nextTick(() => {\r\n        originalForm.value = JSON.parse(JSON.stringify(form.value));\r\n      });\r\n\r\n      startDevicePolling();\r\n    });\r\n\r\n    // 组件卸载时停止轮询\r\n    onUnmounted(() => {\r\n      stopDevicePolling();\r\n\r\n      if (saveMessageTimer) {\r\n        clearTimeout(saveMessageTimer);\r\n      }\r\n    });\r\n\r\n    // 当测试计划ID改变时重新加载配置\r\n    watch(() => props.testPlanId, async (newVal) => {\r\n      if (newVal) {\r\n        await loadHardwareConfig();\r\n\r\n        nextTick(() => {\r\n          originalForm.value = JSON.parse(JSON.stringify(form.value));\r\n          hasUnsavedChanges.value = false;\r\n        });\r\n      }\r\n    });\r\n\r\n    return {\r\n      form,\r\n      isLoading,\r\n      isRefreshing,\r\n      isSaving,\r\n      canDevices,\r\n      canFdDevices,\r\n      baudRates,\r\n      fdDataRates,\r\n      handleCommunicationTypeChange,\r\n      handleValueChange,\r\n      getDeviceConnectStatus,\r\n      getDeviceLabel,\r\n      refreshDevices,\r\n      hasUnsavedChanges,\r\n      saveSuccess,\r\n      saveStatusText,\r\n      updateTimestamp,\r\n    };\r\n  }\r\n});\r\n</script>\r\n\r\n<style scoped>\r\n.hardware-config-panel {\r\n  background-color: #fff;\r\n  border-radius: 8px;\r\n  position: relative;\r\n  min-height: 300px;\r\n}\r\n\r\n.select-container {\r\n  width: 100%;\r\n  min-width: 400px;\r\n}\r\n\r\n.full-width-select {\r\n  width: 100%;\r\n}\r\n\r\n.device-channel-container {\r\n  width: 100%;\r\n  min-width: 400px;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.device-status-tag {\r\n  flex-shrink: 0;\r\n  width: 110px;\r\n  display: inline-flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  transition: none !important;  /* 禁用所有过渡动画 */\r\n}\r\n\r\n.status-icon {\r\n  font-size: 14px;\r\n}\r\n\r\n.status-text {\r\n  font-size: 12px;\r\n}\r\n/* Reset button */\r\n.save-status {\r\n  margin-top: 10px;\r\n  text-align: right;\r\n}\r\n.success-icon {\r\n  color: #67c23a;\r\n}\r\n.error-icon {\r\n  color: #f56c6c;\r\n}\r\n\r\n:deep(.el-tag__content) {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n  transition: none !important;  /* 禁用内容的过渡动画 */\r\n}\r\n\r\n</style>\r\n", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\api\\testSuiteApi.ts", ["349"], "import axios, { AxiosResponse } from 'axios'\r\nimport { USE_MOCK, mockApi } from '@/mock/mockApi'\r\n\r\nexport interface SequencePackage {\r\n  name: string;\r\n  sequences: any[];\r\n}\r\n\r\nexport interface TestSuite {\r\n  name: string;\r\n  version: string;\r\n  packages: SequencePackage[];\r\n}\r\n\r\nconst BASE_URL = '/api/testsuite'\r\n\r\nexport const testSuiteApi = {\r\n  getBuiltIn: (): Promise<AxiosResponse<TestSuite[]>> => {\r\n    if (USE_MOCK) {\r\n      return mockApi.testSuite.getBuiltIn();\r\n    }\r\n    return axios.get(`${BASE_URL}/builtin`);\r\n  },\r\n\r\n  getBuiltInXml: (suiteName: string, packageName: string): Promise<AxiosResponse<string>> => {\r\n    if (USE_MOCK) {\r\n      return mockApi.testSuite.getXml(suiteName, packageName);\r\n    }\r\n    return axios.get(`${BASE_URL}/builtin-xml`, { params: { suiteName, packageName } });\r\n  },\r\n\r\n  getXml: (suiteName: string, packageName: string): Promise<AxiosResponse<string>> => {\r\n    if (USE_MOCK) {\r\n      return mockApi.testSuite.getXml(suiteName, packageName);\r\n    }\r\n    return axios.get(`${BASE_URL}/xml`, { params: { suiteName, packageName } });\r\n  }\r\n}\r\n\r\nexport default testSuiteApi\r\n", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\api\\caseApi.ts", ["350"], "import axios, { AxiosResponse } from 'axios';\r\nimport { USE_MOCK, mockApi } from '@/mock/mockApi';\r\nimport { CaseResult } from '@/api/interoperationApi';\r\n\r\n// 修改 WhiteListFrame 接口，添加 transmitter、receivers 和 IsExt\r\nexport interface WhiteListFrame {\r\n  id: number;\r\n  name: string;\r\n  dlc: number;\r\n  isExt: boolean;\r\n  transmitter: string;\r\n  receivers: string[];\r\n}\r\n\r\n// 安全配置信息接口\r\nexport interface SecurityConfigInfo {\r\n  hasDll: boolean;\r\n  dllFileName?: string;\r\n  dllSize?: number;\r\n}\r\n\r\nexport interface CaseConfigDto {\r\n  // 基本属性 - 移除 whiteListIds, dlc, limitValues\r\n  whiteListFrames: WhiteListFrame[]; // 新增：用于替代 whiteListIds 和 dlc\r\n  selectedNodeName?: string;        // 新增：保存选中的目标节点名称\r\n\r\n  // NM唤醒相关配置\r\n  enableNmWakeup: boolean;\r\n  nmWakeupId: number;\r\n  nmWakeupIsExt: boolean;\r\n  nmWakeupDlc: number;\r\n  nmWakeupData: number[];\r\n  nmWakeupCommunicationType: string;\r\n  nmWakeupCycleMs: number;\r\n  nmWakeupDelayMs: number;\r\n  // 诊断通信相关配置\r\n  diagReqId: number;\r\n  diagReqIsExt: boolean;\r\n  diagResId: number;\r\n  diagTimeoutMs: number;\r\n  isDutMtuLessThan4096?: boolean;   // 新增：DUT MTU小于4096的标识\r\n  enableDiagFallbackRequest?: boolean; // 新增：当诊断请求无响应时，是否发送其它诊断请求\r\n  diagFallbackRequestPayload?: number[]; // 新增：备用诊断请求数据\r\n\r\n  // 安全配置相关\r\n  securityInfo?: SecurityConfigInfo;  // 显示用\r\n  securityDllPath?: string;           // 新选择的DLL路径\r\n  removeSecurityDll?: boolean;        // 是否移除现有DLL\r\n}\r\n\r\n// 向后兼容的类型别名，保持接口一致性\r\nexport type CaseConfig = CaseConfigDto;\r\n\r\nexport interface CaseConfigFromDbc {\r\n  whiteListFrames: WhiteListFrame[]; // 帧列表\r\n  nodeNames: string[];               // 新增：节点名称列表\r\n}\r\n\r\nexport interface SecurityDllPathResponse {\r\n  path: string;\r\n}\r\n\r\nconst BASE_URL = '/api/caseconfig';\r\n\r\nexport const caseApi = {\r\n  getCaseConfig(): Promise<AxiosResponse<CaseConfigDto>> {\r\n    if (USE_MOCK) {\r\n      return mockApi.case.getCaseConfig();\r\n    }\r\n    return axios.get(`${BASE_URL}`);\r\n  },\r\n\r\n  updateCaseConfig(config: CaseConfigDto): Promise<AxiosResponse<CaseConfigDto>> {\r\n    if (USE_MOCK) {\r\n      return mockApi.case.updateCaseConfig(config);\r\n    }\r\n    return axios.post(`${BASE_URL}/update`, config);\r\n  },\r\n\r\n  importDbc(): Promise<AxiosResponse<CaseConfigFromDbc>> {\r\n    if (USE_MOCK) {\r\n      return mockApi.case.importDbc();\r\n    }\r\n    return axios.get(`${BASE_URL}/import-dbc`);\r\n  },\r\n\r\n  // 新增：选择安全DLL文件\r\n  selectSecurityDll(): Promise<AxiosResponse<SecurityDllPathResponse>> {\r\n    if (USE_MOCK) {\r\n      return mockApi.case.selectSecurityDll();\r\n    }\r\n    return axios.post(`${BASE_URL}/select-security-dll`);\r\n  }\r\n};\r\n\r\nexport default caseApi;\r\n", "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\components\\hardware\\DeviceChannelSelect.vue", [], "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\components\\XmlEditor\\XmlEditor.vue", [], "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\components\\common\\TestStateTag.vue", [], "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\components\\common\\TimeDisplay.vue", [], "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\api\\hardwareApi.ts", [], "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\utils\\status.ts", [], "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Web\\web\\src\\components\\test\\CaseList.vue", ["351"], "<template>\n  <div class=\"cases-list-container case-list\">\n    <!-- 固定表头 -->\n    <div class=\"cases-header\" ref=\"headerContainer\"></div>\n    <!-- 虚拟滚动内容容器 -->\n    <div ref=\"casesContainer\" class=\"cases-content\"></div>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport { ref, onMounted, onUnmounted, watch, nextTick, defineProps, defineEmits } from 'vue';\nimport { VirtualScroll } from 'js-booster';\nimport { CaseResult, ExecutionState } from '@/api/interoperationApi';\n\nconst props = defineProps<{\n  cases: CaseResult[];\n}>();\n\nconst emit = defineEmits<{\n  (e: 'view-detail', caseResult: CaseResult): void;\n}>();\n\n// 虚拟滚动相关\nconst casesContainer = ref<HTMLElement | null>(null);\nconst headerContainer = ref<HTMLElement | null>(null);\nlet virtualScroll: any = null;\nconst ITEM_HEIGHT = 36; // 行高\nconst BUFFER_SIZE = 20; // 缓冲区大小\nconst HEADER_HEIGHT = 40; // 表头高度\n\n// 渲染固定表头\nconst renderFixedHeader = () => {\n  if (!headerContainer.value) return;\n\n  // 清空表头容器\n  headerContainer.value.innerHTML = '';\n\n  // 创建表头\n  const header = document.createElement('div');\n  header.className = 'header-row';\n  header.style.height = `${HEADER_HEIGHT}px`; // 动态高度\n\n  // ID 列\n  const idHeader = document.createElement('div');\n  idHeader.textContent = 'ID';\n  idHeader.className = 'header-cell-id';\n  header.appendChild(idHeader);\n\n  // 名称列\n  const nameHeader = document.createElement('div');\n  nameHeader.textContent = 'Name';\n  nameHeader.className = 'header-cell-name';\n  header.appendChild(nameHeader);\n\n  // 参数列\n  const paramHeader = document.createElement('div');\n  paramHeader.textContent = 'Parameter';\n  paramHeader.className = 'header-cell-param';\n  header.appendChild(paramHeader);\n\n  // 详情列\n  const detailHeader = document.createElement('div');\n  detailHeader.textContent = 'Detail';\n  detailHeader.className = 'header-cell-detail';\n  header.appendChild(detailHeader);\n\n  // 状态列\n  const statusHeader = document.createElement('div');\n  statusHeader.textContent = 'Status';\n  statusHeader.className = 'header-cell-status';\n  header.appendChild(statusHeader);\n\n  // 添加到表头容器\n  headerContainer.value.appendChild(header);\n};\n\n// 初始化虚拟滚动\nconst initVirtualScroll = () => {\n  if (!casesContainer.value || !props.cases.length) return;\n\n  // 渲染固定表头\n  renderFixedHeader();\n\n  // 如果已经存在虚拟滚动实例，先销毁\n  if (virtualScroll) {\n    virtualScroll.destroy();\n  }\n\n  virtualScroll = new VirtualScroll({\n    container: casesContainer.value,\n    items: props.cases,\n    itemHeight: ITEM_HEIGHT,\n    bufferSize: BUFFER_SIZE,\n    renderItem: (item: CaseResult, index: number) => {\n      // 创建主容器\n      const div = document.createElement('div');\n      div.className = 'case-row';\n      div.onclick = () => emit('view-detail', item);\n\n      // 设置动态样式\n      div.style.height = `${ITEM_HEIGHT}px`;\n      div.style.lineHeight = `${ITEM_HEIGHT}px`;\n      div.style.borderBottom = index === props.cases.length - 1 ? 'none' : '1px solid #ebeef5';\n      div.style.backgroundColor = item.id % 2 === 0 ? '#fff' : '#fafafa';\n\n      // 添加悬停效果\n      div.onmouseover = () => {\n        div.style.backgroundColor = '#f5f7fa';\n      };\n      div.onmouseout = () => {\n        div.style.backgroundColor = item.id % 2 === 0 ? '#fff' : '#fafafa';\n        // 确保鼠标移出时保持最后一个项目没有底部边框\n        div.style.borderBottom = index === props.cases.length - 1 ? 'none' : '1px solid #ebeef5';\n      };\n\n      // 创建行内容\n      const rowDiv = document.createElement('div');\n      rowDiv.className = 'case-row-content';\n\n      // ID\n      const idDiv = document.createElement('div');\n      idDiv.textContent = `#${item.id}`;\n      idDiv.className = 'case-cell-id';\n      rowDiv.appendChild(idDiv);\n\n      // 名称\n      const nameDiv = document.createElement('div');\n      nameDiv.textContent = item.name;\n      nameDiv.className = 'case-cell-name';\n      rowDiv.appendChild(nameDiv);\n\n      // 参数\n      const paramDiv = document.createElement('div');\n      paramDiv.textContent = item.parameter;\n      paramDiv.title = item.parameter;\n      paramDiv.className = 'case-cell-param';\n      rowDiv.appendChild(paramDiv);\n\n      // 详情列\n      const detailDiv = document.createElement('div');\n      detailDiv.textContent = item.detail || '-';\n      detailDiv.title = item.detail || '';\n      detailDiv.className = 'case-cell-detail';\n      rowDiv.appendChild(detailDiv);\n\n      // 状态\n      const statusDiv = document.createElement('div');\n      statusDiv.className = 'case-cell-status';\n\n      // 创建状态标签\n      const tagType = getStatusTagType(item.state);\n      const tagText = getStatusText(item.state);\n\n      const tagEl = document.createElement('span');\n      tagEl.className = `el-tag el-tag--${tagType} el-tag--small case-status-tag`;\n      tagEl.textContent = tagText;\n\n      statusDiv.appendChild(tagEl);\n      rowDiv.appendChild(statusDiv);\n\n      div.appendChild(rowDiv);\n\n      return div;\n    }\n  });\n};\n\n// 更新虚拟滚动数据\nconst updateVirtualScroll = () => {\n  if (virtualScroll) {\n    virtualScroll.updateItems(props.cases);\n  } else {\n    nextTick(() => {\n      initVirtualScroll();\n    });\n  }\n};\n\n// 获取状态对应的标签类型\nconst getStatusTagType = (state: string): string => {\n  switch (state) {\n    case ExecutionState.Success:\n      return 'success';\n    case ExecutionState.Running:\n      return 'warning';\n    case ExecutionState.Failure:\n      return 'danger';\n    case ExecutionState.Pending:\n    default:\n      return 'info';\n  }\n};\n\n// 获取状态的文本描述\nconst getStatusText = (state: string): string => {\n  switch (state) {\n    case ExecutionState.Success:\n      return 'Passed';\n    case ExecutionState.Failure:\n      return 'Failed';\n    case ExecutionState.Running:\n      return 'Running';\n    case ExecutionState.Pending:\n      return 'Pending';\n    default:\n      return 'Unknown';\n  }\n};\n\n// 监听 cases 变化，更新虚拟滚动\nwatch(() => props.cases, () => {\n  nextTick(() => {\n    updateVirtualScroll();\n  });\n}, { deep: true });\n\n// 组件挂载时初始化虚拟滚动\nonMounted(() => {\n  nextTick(() => {\n    initVirtualScroll();\n  });\n});\n\n// 组件卸载时销毁虚拟滚动\nonUnmounted(() => {\n  if (virtualScroll) {\n    virtualScroll.destroy();\n    virtualScroll = null;\n  }\n});\n</script>\n\n<style scoped lang=\"scss\">\n.cases-list-container {\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  position: relative;\n  font-size: 13px;\n}\n\n.cases-header {\n  position: sticky;\n  top: 0;\n  z-index: 10;\n  background-color: #f5f7fa;\n}\n\n.cases-content {\n  flex: 1;\n  overflow: auto;\n  position: relative;\n}\n</style>\n", {"ruleId": "352", "severity": 1, "message": "353", "line": 66, "column": 3, "nodeType": "354", "messageId": "355", "endLine": 66, "endColumn": 16}, {"ruleId": "352", "severity": 1, "message": "353", "line": 77, "column": 5, "nodeType": "354", "messageId": "355", "endLine": 77, "endColumn": 18}, {"ruleId": "352", "severity": 1, "message": "353", "line": 94, "column": 5, "nodeType": "354", "messageId": "355", "endLine": 94, "endColumn": 18}, {"ruleId": "352", "severity": 1, "message": "353", "line": 110, "column": 7, "nodeType": "354", "messageId": "355", "endLine": 110, "endColumn": 20}, {"ruleId": "356", "severity": 1, "message": "357", "line": 38, "column": 18, "nodeType": "358", "messageId": "359", "endLine": 38, "endColumn": 21, "suggestions": "360"}, {"ruleId": "352", "severity": 1, "message": "353", "line": 113, "column": 5, "nodeType": "354", "messageId": "355", "endLine": 113, "endColumn": 18}, {"ruleId": "352", "severity": 1, "message": "353", "line": 146, "column": 9, "nodeType": "354", "messageId": "355", "endLine": 146, "endColumn": 22}, {"ruleId": "352", "severity": 1, "message": "353", "line": 59, "column": 5, "nodeType": "354", "messageId": "355", "endLine": 59, "endColumn": 18}, {"ruleId": "356", "severity": 1, "message": "357", "line": 51, "column": 23, "nodeType": "358", "messageId": "359", "endLine": 51, "endColumn": 26, "suggestions": "361"}, {"ruleId": "356", "severity": 1, "message": "357", "line": 16, "column": 12, "nodeType": "358", "messageId": "359", "endLine": 16, "endColumn": 15, "suggestions": "362"}, {"ruleId": "356", "severity": 1, "message": "357", "line": 17, "column": 18, "nodeType": "358", "messageId": "359", "endLine": 17, "endColumn": 21, "suggestions": "363"}, {"ruleId": "356", "severity": 1, "message": "357", "line": 121, "column": 38, "nodeType": "358", "messageId": "359", "endLine": 121, "endColumn": 41, "suggestions": "364"}, {"ruleId": "356", "severity": 1, "message": "357", "line": 46, "column": 38, "nodeType": "358", "messageId": "359", "endLine": 46, "endColumn": 41, "suggestions": "365"}, {"ruleId": "356", "severity": 1, "message": "357", "line": 83, "column": 21, "nodeType": "358", "messageId": "359", "endLine": 83, "endColumn": 24, "suggestions": "366"}, {"ruleId": "352", "severity": 1, "message": "353", "line": 85, "column": 7, "nodeType": "354", "messageId": "355", "endLine": 85, "endColumn": 20}, {"ruleId": "356", "severity": 1, "message": "357", "line": 101, "column": 21, "nodeType": "358", "messageId": "359", "endLine": 101, "endColumn": 24, "suggestions": "367"}, {"ruleId": "352", "severity": 1, "message": "353", "line": 103, "column": 7, "nodeType": "354", "messageId": "355", "endLine": 103, "endColumn": 20}, {"ruleId": "352", "severity": 1, "message": "353", "line": 114, "column": 7, "nodeType": "354", "messageId": "355", "endLine": 114, "endColumn": 20}, {"ruleId": "352", "severity": 1, "message": "353", "line": 128, "column": 7, "nodeType": "354", "messageId": "355", "endLine": 128, "endColumn": 20}, {"ruleId": "352", "severity": 1, "message": "353", "line": 145, "column": 7, "nodeType": "354", "messageId": "355", "endLine": 145, "endColumn": 20}, {"ruleId": "352", "severity": 1, "message": "353", "line": 164, "column": 7, "nodeType": "354", "messageId": "355", "endLine": 164, "endColumn": 20}, {"ruleId": "356", "severity": 1, "message": "357", "line": 5, "column": 36, "nodeType": "358", "messageId": "359", "endLine": 5, "endColumn": 39, "suggestions": "368"}, {"ruleId": "356", "severity": 1, "message": "357", "line": 37, "column": 35, "nodeType": "358", "messageId": "359", "endLine": 37, "endColumn": 38, "suggestions": "369"}, {"ruleId": "356", "severity": 1, "message": "357", "line": 61, "column": 32, "nodeType": "358", "messageId": "359", "endLine": 61, "endColumn": 35, "suggestions": "370"}, {"ruleId": "356", "severity": 1, "message": "357", "line": 47, "column": 18, "nodeType": "358", "messageId": "359", "endLine": 47, "endColumn": 21, "suggestions": "371"}, {"ruleId": "352", "severity": 1, "message": "353", "line": 83, "column": 5, "nodeType": "354", "messageId": "355", "endLine": 83, "endColumn": 16}, {"ruleId": "372", "severity": 1, "message": "373", "line": 115, "column": 21, "nodeType": "374", "messageId": "375", "endLine": 115, "endColumn": 33}, {"ruleId": "372", "severity": 1, "message": "376", "line": 8, "column": 3, "nodeType": "374", "messageId": "375", "endLine": 8, "endColumn": 20}, {"ruleId": "372", "severity": 1, "message": "377", "line": 11, "column": 3, "nodeType": "374", "messageId": "375", "endLine": 11, "endColumn": 14}, {"ruleId": "352", "severity": 1, "message": "353", "line": 263, "column": 5, "nodeType": "354", "messageId": "355", "endLine": 263, "endColumn": 16}, {"ruleId": "352", "severity": 1, "message": "353", "line": 307, "column": 5, "nodeType": "354", "messageId": "355", "endLine": 307, "endColumn": 16}, {"ruleId": "352", "severity": 1, "message": "353", "line": 333, "column": 5, "nodeType": "354", "messageId": "355", "endLine": 333, "endColumn": 16}, {"ruleId": "352", "severity": 1, "message": "353", "line": 364, "column": 5, "nodeType": "354", "messageId": "355", "endLine": 364, "endColumn": 16}, {"ruleId": "352", "severity": 1, "message": "353", "line": 373, "column": 5, "nodeType": "354", "messageId": "355", "endLine": 373, "endColumn": 16}, {"ruleId": "352", "severity": 1, "message": "353", "line": 381, "column": 5, "nodeType": "354", "messageId": "355", "endLine": 381, "endColumn": 16}, {"ruleId": "352", "severity": 1, "message": "353", "line": 463, "column": 5, "nodeType": "354", "messageId": "355", "endLine": 463, "endColumn": 16}, {"ruleId": "352", "severity": 1, "message": "353", "line": 470, "column": 5, "nodeType": "354", "messageId": "355", "endLine": 470, "endColumn": 16}, {"ruleId": "356", "severity": 1, "message": "357", "line": 17, "column": 34, "nodeType": "358", "messageId": "359", "endLine": 17, "endColumn": 37, "suggestions": "378"}, {"ruleId": "372", "severity": 1, "message": "379", "line": 2, "column": 25, "nodeType": "374", "messageId": "375", "endLine": 2, "endColumn": 39}, {"ruleId": "352", "severity": 1, "message": "353", "line": 57, "column": 5, "nodeType": "354", "messageId": "355", "endLine": 57, "endColumn": 18}, {"ruleId": "352", "severity": 1, "message": "353", "line": 78, "column": 5, "nodeType": "354", "messageId": "355", "endLine": 78, "endColumn": 18}, {"ruleId": "352", "severity": 1, "message": "353", "line": 89, "column": 5, "nodeType": "354", "messageId": "355", "endLine": 89, "endColumn": 18}, {"ruleId": "352", "severity": 1, "message": "353", "line": 98, "column": 5, "nodeType": "354", "messageId": "355", "endLine": 98, "endColumn": 18}, {"ruleId": "352", "severity": 1, "message": "353", "line": 77, "column": 5, "nodeType": "354", "messageId": "355", "endLine": 77, "endColumn": 18}, {"ruleId": "352", "severity": 1, "message": "353", "line": 134, "column": 5, "nodeType": "354", "messageId": "355", "endLine": 134, "endColumn": 18}, {"ruleId": "352", "severity": 1, "message": "353", "line": 169, "column": 5, "nodeType": "354", "messageId": "355", "endLine": 169, "endColumn": 18}, {"ruleId": "352", "severity": 1, "message": "353", "line": 197, "column": 5, "nodeType": "354", "messageId": "355", "endLine": 197, "endColumn": 18}, {"ruleId": "352", "severity": 1, "message": "353", "line": 239, "column": 5, "nodeType": "354", "messageId": "355", "endLine": 239, "endColumn": 18}, {"ruleId": "352", "severity": 1, "message": "353", "line": 256, "column": 5, "nodeType": "354", "messageId": "355", "endLine": 256, "endColumn": 18}, {"ruleId": "372", "severity": 1, "message": "380", "line": 312, "column": 7, "nodeType": "374", "messageId": "375", "endLine": 312, "endColumn": 17}, {"ruleId": "352", "severity": 1, "message": "353", "line": 340, "column": 5, "nodeType": "354", "messageId": "355", "endLine": 340, "endColumn": 18}, {"ruleId": "352", "severity": 1, "message": "353", "line": 95, "column": 5, "nodeType": "354", "messageId": "355", "endLine": 95, "endColumn": 18}, {"ruleId": "352", "severity": 1, "message": "353", "line": 123, "column": 5, "nodeType": "354", "messageId": "355", "endLine": 123, "endColumn": 16}, {"ruleId": "352", "severity": 1, "message": "353", "line": 125, "column": 5, "nodeType": "354", "messageId": "355", "endLine": 125, "endColumn": 18}, {"ruleId": "352", "severity": 1, "message": "353", "line": 147, "column": 5, "nodeType": "354", "messageId": "355", "endLine": 147, "endColumn": 16}, {"ruleId": "352", "severity": 1, "message": "353", "line": 149, "column": 5, "nodeType": "354", "messageId": "355", "endLine": 149, "endColumn": 18}, {"ruleId": "352", "severity": 1, "message": "353", "line": 134, "column": 5, "nodeType": "354", "messageId": "355", "endLine": 134, "endColumn": 18}, {"ruleId": "352", "severity": 1, "message": "353", "line": 151, "column": 5, "nodeType": "354", "messageId": "355", "endLine": 151, "endColumn": 18}, {"ruleId": "352", "severity": 1, "message": "353", "line": 170, "column": 5, "nodeType": "354", "messageId": "355", "endLine": 170, "endColumn": 18}, {"ruleId": "372", "severity": 1, "message": "381", "line": 240, "column": 24, "nodeType": "374", "messageId": "375", "endLine": 240, "endColumn": 33}, {"ruleId": "372", "severity": 1, "message": "382", "line": 240, "column": 35, "nodeType": "374", "messageId": "375", "endLine": 240, "endColumn": 42}, {"ruleId": "372", "severity": 1, "message": "383", "line": 335, "column": 7, "nodeType": "374", "messageId": "375", "endLine": 335, "endColumn": 21}, {"ruleId": "356", "severity": 1, "message": "357", "line": 602, "column": 19, "nodeType": "358", "messageId": "359", "endLine": 602, "endColumn": 22, "suggestions": "384"}, {"ruleId": "356", "severity": 1, "message": "357", "line": 635, "column": 19, "nodeType": "358", "messageId": "359", "endLine": 635, "endColumn": 22, "suggestions": "385"}, {"ruleId": "352", "severity": 1, "message": "353", "line": 178, "column": 9, "nodeType": "354", "messageId": "355", "endLine": 178, "endColumn": 22}, {"ruleId": "352", "severity": 1, "message": "353", "line": 186, "column": 9, "nodeType": "354", "messageId": "355", "endLine": 186, "endColumn": 22}, {"ruleId": "372", "severity": 1, "message": "386", "line": 155, "column": 56, "nodeType": "374", "messageId": "375", "endLine": 155, "endColumn": 72}, {"ruleId": "372", "severity": 1, "message": "387", "line": 174, "column": 7, "nodeType": "374", "messageId": "375", "endLine": 174, "endColumn": 20}, {"ruleId": "352", "severity": 1, "message": "353", "line": 147, "column": 3, "nodeType": "354", "messageId": "355", "endLine": 147, "endColumn": 14}, {"ruleId": "372", "severity": 1, "message": "388", "line": 84, "column": 10, "nodeType": "374", "messageId": "375", "endLine": 84, "endColumn": 17}, {"ruleId": "352", "severity": 1, "message": "353", "line": 133, "column": 5, "nodeType": "354", "messageId": "355", "endLine": 133, "endColumn": 18}, {"ruleId": "372", "severity": 1, "message": "389", "line": 158, "column": 7, "nodeType": "374", "messageId": "375", "endLine": 158, "endColumn": 17}, {"ruleId": "372", "severity": 1, "message": "390", "line": 167, "column": 7, "nodeType": "374", "messageId": "375", "endLine": 167, "endColumn": 22}, {"ruleId": "372", "severity": 1, "message": "391", "line": 114, "column": 10, "nodeType": "374", "messageId": "375", "endLine": 114, "endColumn": 17}, {"ruleId": "372", "severity": 1, "message": "392", "line": 114, "column": 19, "nodeType": "374", "messageId": "375", "endLine": 114, "endColumn": 26}, {"ruleId": "356", "severity": 1, "message": "357", "line": 162, "column": 32, "nodeType": "358", "messageId": "359", "endLine": 162, "endColumn": 35, "suggestions": "393"}, {"ruleId": "356", "severity": 1, "message": "357", "line": 163, "column": 32, "nodeType": "358", "messageId": "359", "endLine": 163, "endColumn": 35, "suggestions": "394"}, {"ruleId": "356", "severity": 1, "message": "357", "line": 164, "column": 36, "nodeType": "358", "messageId": "359", "endLine": 164, "endColumn": 39, "suggestions": "395"}, {"ruleId": "356", "severity": 1, "message": "357", "line": 167, "column": 27, "nodeType": "358", "messageId": "359", "endLine": 167, "endColumn": 30, "suggestions": "396"}, {"ruleId": "356", "severity": 1, "message": "357", "line": 185, "column": 23, "nodeType": "358", "messageId": "359", "endLine": 185, "endColumn": 26, "suggestions": "397"}, {"ruleId": "352", "severity": 1, "message": "353", "line": 218, "column": 9, "nodeType": "354", "messageId": "355", "endLine": 218, "endColumn": 22}, {"ruleId": "352", "severity": 1, "message": "353", "line": 283, "column": 9, "nodeType": "354", "messageId": "355", "endLine": 283, "endColumn": 22}, {"ruleId": "356", "severity": 1, "message": "357", "line": 291, "column": 47, "nodeType": "358", "messageId": "359", "endLine": 291, "endColumn": 50, "suggestions": "398"}, {"ruleId": "356", "severity": 1, "message": "357", "line": 291, "column": 66, "nodeType": "358", "messageId": "359", "endLine": 291, "endColumn": 69, "suggestions": "399"}, {"ruleId": "356", "severity": 1, "message": "357", "line": 301, "column": 42, "nodeType": "358", "messageId": "359", "endLine": 301, "endColumn": 45, "suggestions": "400"}, {"ruleId": "356", "severity": 1, "message": "357", "line": 301, "column": 58, "nodeType": "358", "messageId": "359", "endLine": 301, "endColumn": 61, "suggestions": "401"}, {"ruleId": "356", "severity": 1, "message": "357", "line": 359, "column": 37, "nodeType": "358", "messageId": "359", "endLine": 359, "endColumn": 40, "suggestions": "402"}, {"ruleId": "356", "severity": 1, "message": "357", "line": 6, "column": 14, "nodeType": "358", "messageId": "359", "endLine": 6, "endColumn": 17, "suggestions": "403"}, {"ruleId": "372", "severity": 1, "message": "404", "line": 3, "column": 10, "nodeType": "374", "messageId": "375", "endLine": 3, "endColumn": 20}, {"ruleId": "356", "severity": 1, "message": "357", "line": 26, "column": 20, "nodeType": "358", "messageId": "359", "endLine": 26, "endColumn": 23, "suggestions": "405"}, "no-console", "Unexpected console statement.", "MemberExpression", "unexpected", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["406", "407"], ["408", "409"], ["410", "411"], ["412", "413"], ["414", "415"], ["416", "417"], ["418", "419"], ["420", "421"], ["422", "423"], ["424", "425"], ["426", "427"], ["428", "429"], "@typescript-eslint/no-unused-vars", "'path' is defined but never used.", "Identifier", "unusedVar", "'isTesterCompleted' is defined but never used.", "'PagedResult' is defined but never used.", ["430", "431"], "'WhiteListFrame' is defined but never used.", "'formatDate' is assigned a value but never used.", "'ArrowDown' is defined but never used.", "'ArrowUp' is defined but never used.", "'parseHexString' is assigned a value but never used.", ["432", "433"], ["434", "435"], "'item' is defined but never used.", "'getStatusText' is assigned a value but never used.", "'caseApi' is defined but never used.", "'formatTime' is assigned a value but never used.", "'formatTimestamp' is assigned a value but never used.", "'Refresh' is defined but never used.", "'Loading' is defined but never used.", ["436", "437"], ["438", "439"], ["440", "441"], ["442", "443"], ["444", "445"], ["446", "447"], ["448", "449"], ["450", "451"], ["452", "453"], ["454", "455"], ["456", "457"], "'CaseResult' is defined but never used.", ["458", "459"], {"messageId": "460", "fix": "461", "desc": "462"}, {"messageId": "463", "fix": "464", "desc": "465"}, {"messageId": "460", "fix": "466", "desc": "462"}, {"messageId": "463", "fix": "467", "desc": "465"}, {"messageId": "460", "fix": "468", "desc": "462"}, {"messageId": "463", "fix": "469", "desc": "465"}, {"messageId": "460", "fix": "470", "desc": "462"}, {"messageId": "463", "fix": "471", "desc": "465"}, {"messageId": "460", "fix": "472", "desc": "462"}, {"messageId": "463", "fix": "473", "desc": "465"}, {"messageId": "460", "fix": "474", "desc": "462"}, {"messageId": "463", "fix": "475", "desc": "465"}, {"messageId": "460", "fix": "476", "desc": "462"}, {"messageId": "463", "fix": "477", "desc": "465"}, {"messageId": "460", "fix": "478", "desc": "462"}, {"messageId": "463", "fix": "479", "desc": "465"}, {"messageId": "460", "fix": "480", "desc": "462"}, {"messageId": "463", "fix": "481", "desc": "465"}, {"messageId": "460", "fix": "482", "desc": "462"}, {"messageId": "463", "fix": "483", "desc": "465"}, {"messageId": "460", "fix": "484", "desc": "462"}, {"messageId": "463", "fix": "485", "desc": "465"}, {"messageId": "460", "fix": "486", "desc": "462"}, {"messageId": "463", "fix": "487", "desc": "465"}, {"messageId": "460", "fix": "488", "desc": "462"}, {"messageId": "463", "fix": "489", "desc": "465"}, {"messageId": "460", "fix": "490", "desc": "462"}, {"messageId": "463", "fix": "491", "desc": "465"}, {"messageId": "460", "fix": "492", "desc": "462"}, {"messageId": "463", "fix": "493", "desc": "465"}, {"messageId": "460", "fix": "494", "desc": "462"}, {"messageId": "463", "fix": "495", "desc": "465"}, {"messageId": "460", "fix": "496", "desc": "462"}, {"messageId": "463", "fix": "497", "desc": "465"}, {"messageId": "460", "fix": "498", "desc": "462"}, {"messageId": "463", "fix": "499", "desc": "465"}, {"messageId": "460", "fix": "500", "desc": "462"}, {"messageId": "463", "fix": "501", "desc": "465"}, {"messageId": "460", "fix": "502", "desc": "462"}, {"messageId": "463", "fix": "503", "desc": "465"}, {"messageId": "460", "fix": "504", "desc": "462"}, {"messageId": "463", "fix": "505", "desc": "465"}, {"messageId": "460", "fix": "506", "desc": "462"}, {"messageId": "463", "fix": "507", "desc": "465"}, {"messageId": "460", "fix": "508", "desc": "462"}, {"messageId": "463", "fix": "509", "desc": "465"}, {"messageId": "460", "fix": "510", "desc": "462"}, {"messageId": "463", "fix": "511", "desc": "465"}, {"messageId": "460", "fix": "512", "desc": "462"}, {"messageId": "463", "fix": "513", "desc": "465"}, {"messageId": "460", "fix": "514", "desc": "462"}, {"messageId": "463", "fix": "515", "desc": "465"}, {"messageId": "460", "fix": "516", "desc": "462"}, {"messageId": "463", "fix": "517", "desc": "465"}, "suggestUnknown", {"range": "518", "text": "519"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "518", "text": "520"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "521", "text": "519"}, {"range": "521", "text": "520"}, {"range": "522", "text": "519"}, {"range": "522", "text": "520"}, {"range": "523", "text": "519"}, {"range": "523", "text": "520"}, {"range": "524", "text": "519"}, {"range": "524", "text": "520"}, {"range": "525", "text": "519"}, {"range": "525", "text": "520"}, {"range": "526", "text": "519"}, {"range": "526", "text": "520"}, {"range": "527", "text": "519"}, {"range": "527", "text": "520"}, {"range": "528", "text": "519"}, {"range": "528", "text": "520"}, {"range": "529", "text": "519"}, {"range": "529", "text": "520"}, {"range": "530", "text": "519"}, {"range": "530", "text": "520"}, {"range": "531", "text": "519"}, {"range": "531", "text": "520"}, {"range": "532", "text": "519"}, {"range": "532", "text": "520"}, {"range": "533", "text": "519"}, {"range": "533", "text": "520"}, {"range": "534", "text": "519"}, {"range": "534", "text": "520"}, {"range": "535", "text": "519"}, {"range": "535", "text": "520"}, {"range": "536", "text": "519"}, {"range": "536", "text": "520"}, {"range": "537", "text": "519"}, {"range": "537", "text": "520"}, {"range": "538", "text": "519"}, {"range": "538", "text": "520"}, {"range": "539", "text": "519"}, {"range": "539", "text": "520"}, {"range": "540", "text": "519"}, {"range": "540", "text": "520"}, {"range": "541", "text": "519"}, {"range": "541", "text": "520"}, {"range": "542", "text": "519"}, {"range": "542", "text": "520"}, {"range": "543", "text": "519"}, {"range": "543", "text": "520"}, {"range": "544", "text": "519"}, {"range": "544", "text": "520"}, {"range": "545", "text": "519"}, {"range": "545", "text": "520"}, {"range": "546", "text": "519"}, {"range": "546", "text": "520"}, [1377, 1380], "unknown", "never", [1680, 1683], [391, 394], [414, 417], [3441, 3444], [1231, 1234], [2201, 2204], [2637, 2640], [140, 143], [931, 934], [1479, 1482], [1478, 1481], [492, 495], [22078, 22081], [22955, 22958], [5678, 5681], [5722, 5725], [5770, 5773], [5830, 5833], [6389, 6392], [10085, 10088], [10104, 10107], [10485, 10488], [10501, 10504], [12668, 12671], [166, 169], [754, 757]]