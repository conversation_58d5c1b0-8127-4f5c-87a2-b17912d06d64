using Alsi.Fuzz.Core.Service.CaseFactory.Core;
using Alsi.Fuzz.Core.Service.CaseFactory.Factories.Iso14229.Consts;
using System.Collections.Generic;
using System.Linq;

namespace Alsi.Fuzz.Core.Service.CaseFactory.Factories.Iso14229
{
    public class G341_G342_CaseFactory : CaseFactoryBase
    {
        public override CaseMutation[] Generate(MutationOptions options)
        {
            var caseMutations = new List<CaseMutation>();

            var byteCountArray = new int[] {
                2, 3, 4, 5, 6,
                7, 8, 9, 10, 14,
                15, 16, 17, 18, 30,
                31, 32, 33, 34, 62,
                63, 64, 65, 66, 98,
                99, 100, 101, 102, 126,
                127, 128, 129, 130, 254,
                255, 256, 257, 258, 511,
                512
            };

            var xmlServices = options.XmlServices;
            var supportedXmlServicesWithSubfunction = xmlServices
                // 仅遍历支持的服务
                .Where(x => x.SubfunctionId.HasValue && x.IsSupported)
                // 仅遍历有 Subfunction 的服务
                .Where(x => IsoUdsConsts.Services.FirstOrDefault(service => x.Id == service.Id)?.Subfunctions.Any() == true)
                .ToArray();

            foreach (var xmlServiceWithSubfunction in supportedXmlServicesWithSubfunction)
            {
                var sid = xmlServiceWithSubfunction.Id;
                var subfunctionId = xmlServiceWithSubfunction.SubfunctionId.Value;
                var parameter2k = xmlServiceWithSubfunction.Parameter2k;

                foreach (var byteCount in byteCountArray)
                {
                    var payload = new List<byte>();
                    while (payload.Count < byteCount)
                    {
                        payload.Add(sid);
                        payload.Add(subfunctionId);
                        payload.AddRange(parameter2k);
                    }

                    payload = payload.Take(byteCount).ToList();

                    var caseMutation = CaseMutation.Create($"G341-Sid{sid:X2}-RepeatByByte-{byteCount}")
                        .MutatePayload(payload.ToArray())
                        .MutatePayloadLength(payload.Count);
                    caseMutations.Add(caseMutation);
                }
            }

            var supportedXmlServicesWithoutSubfunction = xmlServices
                // 仅遍历支持的服务
                .Where(x => !x.SubfunctionId.HasValue && x.IsSupported)
                // 仅遍历没有 Subfunction 的服务
                .Where(x => IsoUdsConsts.Services.FirstOrDefault(service => x.Id == service.Id)?.Subfunctions.Any() == false)
                .ToArray();

            foreach (var xmlServiceWithoutSubfunction in supportedXmlServicesWithoutSubfunction)
            {
                var sid = xmlServiceWithoutSubfunction.Id;
                var parameter2k = xmlServiceWithoutSubfunction.Parameter2k;

                foreach (var byteCount in byteCountArray)
                {
                    var payload = new List<byte>();
                    while (payload.Count < byteCount)
                    {
                        payload.Add(sid);
                        payload.AddRange(parameter2k);
                    }

                    payload = payload.Take(byteCount).ToList();

                    var caseMutation = CaseMutation.Create($"G342-Sid{sid:X2}-RepeatByByte-{byteCount}")
                        .MutatePayload(payload.ToArray())
                        .MutatePayloadLength(payload.Count);
                    caseMutations.Add(caseMutation);
                }
            }

            return caseMutations.ToArray();
        }
    }
}
