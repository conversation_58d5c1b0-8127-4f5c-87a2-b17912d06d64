using Alsi.App.Devices.Core;
using System;

namespace Alsi.Fuzz.Web.Dto
{
    public class HardwareConfigDto
    {
        public DeviceChannelDto[] DeviceChannels { get; set; } = Array.Empty<DeviceChannelDto>();
        public TestPlanConfigDto TestPlanConfig { get; set; } = new TestPlanConfigDto();
    }

    public class DeviceChannelDto
    {
        public string Name { get; set; }
        public CommunicationType CommunicationType { get; set; }
        public bool IsConnected { get; set; }
    }

    public class TestPlanConfigDto
    {
        public CommunicationType CommunicationType { get; set; } = CommunicationType.Can;
        public CanConfigDto CanConfig { get; set; }
        public CanFdConfigDto CanFdConfig { get; set; }
    }

    public class CanConfigDto
    {
        public string DeviceChannelName { get; set; } = string.Empty;
        public int DataBitrate { get; set; } = 500_000;
    }

    public class CanFdConfigDto
    {
        public string DeviceChannelName { get; set; } = string.Empty;
        public int ArbitrationBitrate { get; set; } = 500_000;
        public int DataBitrate { get; set; } = 2000_000;
    }
}
