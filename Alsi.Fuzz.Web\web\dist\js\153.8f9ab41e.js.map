{"version": 3, "file": "js/153.8f9ab41e.js", "mappings": "yOAGA,MAAMA,EAAa,CAAEC,MAAO,8BACtBC,EAAa,CAAED,MAAO,cACtBE,EAAa,CAAEF,MAAO,eAS5B,OAA4BG,EAAAA,EAAAA,IAAiB,CAC3CC,OAAQ,kBACRC,KAAAA,CAAMC,GC+BR,MAAMC,GAAUC,EAAAA,EAAAA,KAAI,GACdC,GAAaD,EAAAA,EAAAA,IAAiB,IAC9BE,GAAoBF,EAAAA,EAAAA,IAAY,IAChCG,GAAsBH,EAAAA,EAAAA,IAAY,IAClCI,GAASJ,EAAAA,EAAAA,IAAwB,CACrCK,cAAe,GACfC,oBAAqB,KAGjBC,EAAgB,CACpBF,cAAe,GACfC,oBAAqB,IAGjBE,GAAkBC,EAAAA,EAAAA,KAA4B,KAClD,MAAMC,EAAQT,EAAWU,MAAMC,MAAKC,GAAKA,EAAEC,OAASZ,EAAkBS,QACtE,OAAOD,GAAOK,UAAY,EAAE,IAGxBC,GAAahB,EAAAA,EAAAA,IAAI,IAEjBiB,EAAsBC,UAC1B,GAAKhB,EAAkBS,OAAUR,EAAoBQ,MAErD,IACE,MAAMQ,QAAiBC,EAAAA,EAAaC,OAAOnB,EAAkBS,MAAOR,EAAoBQ,OACxFK,EAAWL,MAAQQ,EAASG,I,CAC5B,MAAOC,GACPC,EAAAA,GAAUD,MAAM,8BAChBE,QAAQF,MAAM,qBAAsBA,E,GAIlCG,EAAoBA,KACxBvB,EAAoBQ,MAAQ,GAC5BK,EAAWL,MAAQ,EAAE,EAGjBgB,EAAiBT,UACrB,IACE,MAAMC,QAAiBC,EAAAA,EAAaQ,aACpC3B,EAAWU,MAAQQ,EAASG,I,CAC5B,MAAOC,GACPC,EAAAA,GAAUD,MAAM,6B,GAIdM,EAAaX,UACjBnB,EAAQY,OAAQ,EAChB,IACE,MAAMQ,QAAiBW,EAAAA,GAAYC,oBACnC3B,EAAOO,MAAQQ,EAASG,KACxBpB,EAAkBS,MAAQP,EAAOO,MAAMN,eAAiB,GACxDF,EAAoBQ,MAAQP,EAAOO,MAAML,qBAAuB,GAG5DJ,EAAkBS,OAASR,EAAoBQ,aAC3CM,G,CAER,MAAOM,GACPC,EAAAA,GAAUD,MAAM,+B,CAChB,QACAxB,EAAQY,OAAQ,C,GAIdqB,EAAad,UACjB,GAAKhB,EAAkBS,OAAUR,EAAoBQ,MAArD,CAKAZ,EAAQY,OAAQ,EAChB,IACE,MAAMsB,EAAgC,CACpC5B,cAAeH,EAAkBS,MACjCL,oBAAqBH,EAAoBQ,MACzCuB,mBAAoBlB,EAAWL,aAE3BmB,EAAAA,GAAYK,qBAAqBF,GACvCT,EAAAA,GAAUY,QAAQ,yBAEZnB,G,CAER,MAAOM,GAELE,QAAQF,MAAM,eAAgBA,E,CAC9B,QACAxB,EAAQY,OAAQ,C,OApBhBa,EAAAA,GAAUa,QAAQ,qD,EAwBhBC,EAAiBA,KACrBpC,EAAkBS,MAAQJ,EAAcF,cACxCF,EAAoBQ,MAAQJ,EAAcD,mBAAmB,EDrB/D,OCwBAiC,EAAAA,EAAAA,KAAUrB,gBACFS,UACAE,GAAY,ID1Bb,CAACW,EAAUC,KAChB,MAAMC,GAAuBC,EAAAA,EAAAA,IAAkB,aACzCC,GAAuBD,EAAAA,EAAAA,IAAkB,aACzCE,GAA0BF,EAAAA,EAAAA,IAAkB,gBAC5CG,GAAuBH,EAAAA,EAAAA,IAAkB,aACzCI,GAAqBJ,EAAAA,EAAAA,IAAkB,WACvCK,GAAsBL,EAAAA,EAAAA,IAAkB,YACxCM,GAAqBC,EAAAA,EAAAA,IAAkB,WAE7C,OAAOC,EAAAA,EAAAA,MAAiBC,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAoB,MAAO9D,EAAY,EAC3E+D,EAAAA,EAAAA,IAAoB,MAAO7D,EAAY,EACrC8D,EAAAA,EAAAA,IAAaR,EAAoB,CAC/BS,MAAOpD,EAAOO,MACd,cAAe,QACf,iBAAkB,OACjB,CACD8C,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBH,EAAAA,EAAAA,IAAaV,EAAyB,CAAEc,MAAO,cAAgB,CAC7DF,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBH,EAAAA,EAAAA,IAAaX,EAAsB,CACjCgB,WAAY1D,EAAkBS,MAC9B,sBAAuB8B,EAAO,KAAOA,EAAO,GAAMoB,GAAkB3D,EAAmBS,MAAQkD,GAC/FC,YAAa,oBACbC,SAAUrC,EACVsC,MAAO,CAAC,MAAQ,SACf,CACDP,SAASC,EAAAA,EAAAA,KAAS,IAAM,GACrBN,EAAAA,EAAAA,KAAW,IAAOC,EAAAA,EAAAA,IAAoBY,EAAAA,GAAW,MAAMC,EAAAA,EAAAA,IAAYjE,EAAWU,OAAQD,KAC7E0C,EAAAA,EAAAA,OAAce,EAAAA,EAAAA,IAAazB,EAAsB,CACvD0B,IAAK1D,EAAMI,KACX6C,MAAO,GAAGjD,EAAMI,SAASJ,EAAM2D,UAC/B1D,MAAOD,EAAMI,MACZ,KAAM,EAAG,CAAC,QAAS,aACpB,SAENwD,EAAG,GACF,EAAG,CAAC,kBAETA,EAAG,KAELf,EAAAA,EAAAA,IAAaV,EAAyB,CAAEc,MAAO,oBAAsB,CACnEF,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBH,EAAAA,EAAAA,IAAaX,EAAsB,CACjCgB,WAAYzD,EAAoBQ,MAChC,sBAAuB8B,EAAO,KAAOA,EAAO,GAAMoB,GAAkB1D,EAAqBQ,MAAQkD,GACjGC,YAAa,0BACbS,UAAWrE,EAAkBS,MAC7BqD,MAAO,CAAC,MAAQ,QAChBD,SAAU9C,GACT,CACDwC,SAASC,EAAAA,EAAAA,KAAS,IAAM,GACrBN,EAAAA,EAAAA,KAAW,IAAOC,EAAAA,EAAAA,IAAoBY,EAAAA,GAAW,MAAMC,EAAAA,EAAAA,IAAY1D,EAAgBG,OAAO,CAAC6D,EAAiBC,MACnGrB,EAAAA,EAAAA,OAAce,EAAAA,EAAAA,IAAazB,EAAsB,CACvD0B,IAAKK,EACLd,MAAOa,EAAgB1D,KACvBH,MAAO6D,EAAgB1D,MACtB,KAAM,EAAG,CAAC,QAAS,aACpB,SAENwD,EAAG,GACF,EAAG,CAAC,aAAc,gBAEvBA,EAAG,KAELf,EAAAA,EAAAA,IAAaV,EAAyB,KAAM,CAC1CY,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBH,EAAAA,EAAAA,IAAaT,EAAsB,CACjC4B,KAAM,UACNC,QAAS3C,GACR,CACDyB,SAASC,EAAAA,EAAAA,KAAS,IAAMjB,EAAO,KAAOA,EAAO,GAAK,EAChDmC,EAAAA,EAAAA,IAAiB,YAEnBN,EAAG,KAELf,EAAAA,EAAAA,IAAaT,EAAsB,CACjC6B,QAASrC,EACT0B,MAAO,CAAC,cAAc,SACrB,CACDP,SAASC,EAAAA,EAAAA,KAAS,IAAMjB,EAAO,KAAOA,EAAO,GAAK,EAChDmC,EAAAA,EAAAA,IAAiB,aAEnBN,EAAG,OAGPA,EAAG,OAGPA,EAAG,GACF,EAAG,CAAC,aAEThB,EAAAA,EAAAA,IAAoB,MAAO5D,EAAY,CACpCQ,EAAkBS,OAASR,EAAoBQ,QAC3CyC,EAAAA,EAAAA,OAAce,EAAAA,EAAAA,IAAaU,EAAAA,EAAW,CACrCT,IAAK,EACLU,MAAO,GAAG5E,EAAkBS,WAAWR,EAAoBQ,QAC3DoE,QAAS/D,EAAWL,MACpB,mBAAoB8B,EAAO,KAAOA,EAAO,GAAMoB,GAAiB7C,EAAWL,MAAQkD,IAClF,KAAM,EAAG,CAAC,QAAS,eACrBT,EAAAA,EAAAA,OAAce,EAAAA,EAAAA,IAAanB,EAAqB,CAC/CoB,IAAK,EACLJ,MAAO,CAAC,WAAa,SACrBgB,YAAa,sBACb,aAAc,YAGnB,CACH,CAAC/B,EAAoBlD,EAAQY,QAC7B,CAEJ,I,UEhOA,MAAMsE,GAA2B,OAAgB,EAAQ,CAAC,CAAC,YAAY,qBAEvE,O", "sources": ["webpack://fuzz-web/./src/views/testplan/SequenceSetting.vue?b7b6", "webpack://fuzz-web/./src/views/testplan/SequenceSetting.vue", "webpack://fuzz-web/./src/views/testplan/SequenceSetting.vue?8ab9"], "sourcesContent": ["import { defineComponent as _defineComponent } from 'vue'\nimport { renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, resolveComponent as _resolveComponent, createBlock as _createBlock, withCtx as _withCtx, createVNode as _createVNode, createTextVNode as _createTextVNode, createElementVNode as _createElementVNode, createCommentVNode as _createCommentVNode, resolveDirective as _resolveDirective, withDirectives as _withDirectives } from \"vue\"\n\nconst _hoisted_1 = { class: \"sequence-setting-container\" }\nconst _hoisted_2 = { class: \"left-panel\" }\nconst _hoisted_3 = { class: \"right-panel\" }\n\nimport { ref, computed, onMounted } from 'vue';\r\nimport { ElMessage } from 'element-plus';\r\nimport { testSuiteApi, type TestSuite, type SequencePackage } from '@/api/testSuiteApi';\r\nimport { sequenceApi, type SequenceConfigData } from '@/api/sequenceApi';\r\nimport XmlViewer from '@/components/TestSuite/XmlViewer.vue';\r\n\r\n\nexport default /*@__PURE__*/_defineComponent({\n  __name: 'SequenceSetting',\n  setup(__props) {\n\r\nconst loading = ref(true);\r\nconst testSuites = ref<TestSuite[]>([]);\r\nconst selectedSuiteName = ref<string>('');\r\nconst selectedPackageName = ref<string>('');\r\nconst config = ref<SequenceConfigData>({\r\n  testSuiteName: '',\r\n  sequencePackageName: '',\r\n});\r\n\r\nconst defaultConfig = {\r\n  testSuiteName: '',\r\n  sequencePackageName: ''\r\n};\r\n\r\nconst currentPackages = computed<SequencePackage[]>(() => {\r\n  const suite = testSuites.value.find(s => s.name === selectedSuiteName.value);\r\n  return suite?.packages || [];\r\n});\r\n\r\nconst xmlContent = ref('');\r\n\r\nconst handlePackageChange = async () => {\r\n  if (!selectedSuiteName.value || !selectedPackageName.value) return;\r\n\r\n  try {\r\n    const response = await testSuiteApi.getXml(selectedSuiteName.value, selectedPackageName.value);\r\n    xmlContent.value = response.data;\r\n  } catch (error) {\r\n    ElMessage.error('Failed to load XML content');\r\n    console.error('Error loading XML:', error);\r\n  }\r\n};\r\n\r\nconst handleSuiteChange = () => {\r\n  selectedPackageName.value = '';\r\n  xmlContent.value = '';\r\n};\r\n\r\nconst loadTestSuites = async () => {\r\n  try {\r\n    const response = await testSuiteApi.getBuiltIn();\r\n    testSuites.value = response.data;\r\n  } catch (error) {\r\n    ElMessage.error('Failed to load test suites');\r\n  }\r\n};\r\n\r\nconst loadConfig = async () => {\r\n  loading.value = true;\r\n  try {\r\n    const response = await sequenceApi.getSequenceConfig();\r\n    config.value = response.data;\r\n    selectedSuiteName.value = config.value.testSuiteName || '';\r\n    selectedPackageName.value = config.value.sequencePackageName || '';\r\n\r\n    // 加载已选择包的XML内容\r\n    if (selectedSuiteName.value && selectedPackageName.value) {\r\n      await handlePackageChange();\r\n    }\r\n  } catch (error) {\r\n    ElMessage.error('Failed to load configuration');\r\n  } finally {\r\n    loading.value = false;\r\n  }\r\n};\r\n\r\nconst handleSave = async () => {\r\n  if (!selectedSuiteName.value || !selectedPackageName.value) {\r\n    ElMessage.warning('Please select both test suite and sequence package');\r\n    return;\r\n  }\r\n\r\n  loading.value = true;\r\n  try {\r\n    const newConfig: SequenceConfigData = {\r\n      testSuiteName: selectedSuiteName.value,\r\n      sequencePackageName: selectedPackageName.value,\r\n      sequencePackageXml: xmlContent.value\r\n    };\r\n    await sequenceApi.updateSequenceConfig(newConfig);\r\n    ElMessage.success('Save successful');\r\n    // 重新加载XML内容\r\n    await handlePackageChange();\r\n  }\r\n  catch (error) {\r\n    // 错误处理已由全局拦截器处理，这里不需要额外显示错误消息\r\n    console.error('Save failed:', error);\r\n  } finally {\r\n    loading.value = false;\r\n  }\r\n};\r\n\r\nconst resetToDefault = () => {\r\n  selectedSuiteName.value = defaultConfig.testSuiteName;\r\n  selectedPackageName.value = defaultConfig.sequencePackageName;\r\n};\r\n\r\nonMounted(async () => {\r\n  await loadTestSuites();\r\n  await loadConfig();\r\n});\r\n\nreturn (_ctx: any,_cache: any) => {\n  const _component_el_option = _resolveComponent(\"el-option\")!\n  const _component_el_select = _resolveComponent(\"el-select\")!\n  const _component_el_form_item = _resolveComponent(\"el-form-item\")!\n  const _component_el_button = _resolveComponent(\"el-button\")!\n  const _component_el_form = _resolveComponent(\"el-form\")!\n  const _component_el_empty = _resolveComponent(\"el-empty\")!\n  const _directive_loading = _resolveDirective(\"loading\")!\n\n  return _withDirectives((_openBlock(), _createElementBlock(\"div\", _hoisted_1, [\n    _createElementVNode(\"div\", _hoisted_2, [\n      _createVNode(_component_el_form, {\n        model: config.value,\n        \"label-width\": \"160px\",\n        \"label-position\": \"top\"\n      }, {\n        default: _withCtx(() => [\n          _createVNode(_component_el_form_item, { label: \"Test Suite\" }, {\n            default: _withCtx(() => [\n              _createVNode(_component_el_select, {\n                modelValue: selectedSuiteName.value,\n                \"onUpdate:modelValue\": _cache[0] || (_cache[0] = ($event: any) => ((selectedSuiteName).value = $event)),\n                placeholder: \"Select Test Suite\",\n                onChange: handleSuiteChange,\n                style: {\"width\":\"100%\"}\n              }, {\n                default: _withCtx(() => [\n                  (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(testSuites.value, (suite) => {\n                    return (_openBlock(), _createBlock(_component_el_option, {\n                      key: suite.name,\n                      label: `${suite.name} v${suite.version}`,\n                      value: suite.name\n                    }, null, 8, [\"label\", \"value\"]))\n                  }), 128))\n                ]),\n                _: 1\n              }, 8, [\"modelValue\"])\n            ]),\n            _: 1\n          }),\n          _createVNode(_component_el_form_item, { label: \"Sequence Package\" }, {\n            default: _withCtx(() => [\n              _createVNode(_component_el_select, {\n                modelValue: selectedPackageName.value,\n                \"onUpdate:modelValue\": _cache[1] || (_cache[1] = ($event: any) => ((selectedPackageName).value = $event)),\n                placeholder: \"Select Sequence Package\",\n                disabled: !selectedSuiteName.value,\n                style: {\"width\":\"100%\"},\n                onChange: handlePackageChange\n              }, {\n                default: _withCtx(() => [\n                  (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(currentPackages.value, (sequencePackage, index) => {\n                    return (_openBlock(), _createBlock(_component_el_option, {\n                      key: index,\n                      label: sequencePackage.name,\n                      value: sequencePackage.name\n                    }, null, 8, [\"label\", \"value\"]))\n                  }), 128))\n                ]),\n                _: 1\n              }, 8, [\"modelValue\", \"disabled\"])\n            ]),\n            _: 1\n          }),\n          _createVNode(_component_el_form_item, null, {\n            default: _withCtx(() => [\n              _createVNode(_component_el_button, {\n                type: \"primary\",\n                onClick: handleSave\n              }, {\n                default: _withCtx(() => _cache[3] || (_cache[3] = [\n                  _createTextVNode(\"Save\")\n                ])),\n                _: 1\n              }),\n              _createVNode(_component_el_button, {\n                onClick: resetToDefault,\n                style: {\"margin-left\":\"10px\"}\n              }, {\n                default: _withCtx(() => _cache[4] || (_cache[4] = [\n                  _createTextVNode(\"Reset\")\n                ])),\n                _: 1\n              })\n            ]),\n            _: 1\n          })\n        ]),\n        _: 1\n      }, 8, [\"model\"])\n    ]),\n    _createElementVNode(\"div\", _hoisted_3, [\n      (selectedSuiteName.value && selectedPackageName.value)\n        ? (_openBlock(), _createBlock(XmlViewer, {\n            key: 0,\n            title: `${selectedSuiteName.value} - ${selectedPackageName.value}`,\n            content: xmlContent.value,\n            \"onUpdate:content\": _cache[2] || (_cache[2] = ($event: any) => (xmlContent.value = $event))\n          }, null, 8, [\"title\", \"content\"]))\n        : (_openBlock(), _createBlock(_component_el_empty, {\n            key: 1,\n            style: {\"background\":\"#DDD2\"},\n            description: \"No sequence content\",\n            \"image-size\": 150\n          }))\n    ])\n  ])), [\n    [_directive_loading, loading.value]\n  ])\n}\n}\n\n})", "<template>\r\n  <div class=\"sequence-setting-container\" v-loading=\"loading\">\r\n    <!-- 左侧配置区域 -->\r\n    <div class=\"left-panel\">\r\n      <el-form :model=\"config\" label-width=\"160px\" label-position=\"top\">\r\n        <el-form-item label=\"Test Suite\">\r\n          <el-select v-model=\"selectedSuiteName\" placeholder=\"Select Test Suite\" @change=\"handleSuiteChange\"\r\n            style=\"width: 100%\">\r\n            <el-option v-for=\"suite in testSuites\" :key=\"suite.name\" :label=\"`${suite.name} v${suite.version}`\"\r\n              :value=\"suite.name\" />\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <el-form-item label=\"Sequence Package\">\r\n          <el-select v-model=\"selectedPackageName\" placeholder=\"Select Sequence Package\" :disabled=\"!selectedSuiteName\"\r\n            style=\"width: 100%\" @change=\"handlePackageChange\">\r\n            <el-option v-for=\"(sequencePackage, index) in currentPackages\" :key=\"index\"\r\n              :label=\"sequencePackage.name\" :value=\"sequencePackage.name\" />\r\n          </el-select>\r\n        </el-form-item>\r\n\r\n        <el-form-item>\r\n          <el-button type=\"primary\" @click=\"handleSave\">Save</el-button>\r\n          <el-button @click=\"resetToDefault\" style=\"margin-left: 10px;\">Reset</el-button>\r\n        </el-form-item>\r\n      </el-form>\r\n    </div>\r\n\r\n    <!-- 右侧XML预览区域 -->\r\n    <div class=\"right-panel\">\r\n      <XmlViewer v-if=\"selectedSuiteName && selectedPackageName\"\r\n        :title=\"`${selectedSuiteName} - ${selectedPackageName}`\"\r\n        :content=\"xmlContent\"\r\n        @update:content=\"xmlContent = $event\" />\r\n\r\n      <el-empty style=\"background: #DDD2;\" v-else description=\"No sequence content\" :image-size=\"150\" />\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { ref, computed, onMounted } from 'vue';\r\nimport { ElMessage } from 'element-plus';\r\nimport { testSuiteApi, type TestSuite, type SequencePackage } from '@/api/testSuiteApi';\r\nimport { sequenceApi, type SequenceConfigData } from '@/api/sequenceApi';\r\nimport XmlViewer from '@/components/TestSuite/XmlViewer.vue';\r\n\r\nconst loading = ref(true);\r\nconst testSuites = ref<TestSuite[]>([]);\r\nconst selectedSuiteName = ref<string>('');\r\nconst selectedPackageName = ref<string>('');\r\nconst config = ref<SequenceConfigData>({\r\n  testSuiteName: '',\r\n  sequencePackageName: '',\r\n});\r\n\r\nconst defaultConfig = {\r\n  testSuiteName: '',\r\n  sequencePackageName: ''\r\n};\r\n\r\nconst currentPackages = computed<SequencePackage[]>(() => {\r\n  const suite = testSuites.value.find(s => s.name === selectedSuiteName.value);\r\n  return suite?.packages || [];\r\n});\r\n\r\nconst xmlContent = ref('');\r\n\r\nconst handlePackageChange = async () => {\r\n  if (!selectedSuiteName.value || !selectedPackageName.value) return;\r\n\r\n  try {\r\n    const response = await testSuiteApi.getXml(selectedSuiteName.value, selectedPackageName.value);\r\n    xmlContent.value = response.data;\r\n  } catch (error) {\r\n    ElMessage.error('Failed to load XML content');\r\n    console.error('Error loading XML:', error);\r\n  }\r\n};\r\n\r\nconst handleSuiteChange = () => {\r\n  selectedPackageName.value = '';\r\n  xmlContent.value = '';\r\n};\r\n\r\nconst loadTestSuites = async () => {\r\n  try {\r\n    const response = await testSuiteApi.getBuiltIn();\r\n    testSuites.value = response.data;\r\n  } catch (error) {\r\n    ElMessage.error('Failed to load test suites');\r\n  }\r\n};\r\n\r\nconst loadConfig = async () => {\r\n  loading.value = true;\r\n  try {\r\n    const response = await sequenceApi.getSequenceConfig();\r\n    config.value = response.data;\r\n    selectedSuiteName.value = config.value.testSuiteName || '';\r\n    selectedPackageName.value = config.value.sequencePackageName || '';\r\n\r\n    // 加载已选择包的XML内容\r\n    if (selectedSuiteName.value && selectedPackageName.value) {\r\n      await handlePackageChange();\r\n    }\r\n  } catch (error) {\r\n    ElMessage.error('Failed to load configuration');\r\n  } finally {\r\n    loading.value = false;\r\n  }\r\n};\r\n\r\nconst handleSave = async () => {\r\n  if (!selectedSuiteName.value || !selectedPackageName.value) {\r\n    ElMessage.warning('Please select both test suite and sequence package');\r\n    return;\r\n  }\r\n\r\n  loading.value = true;\r\n  try {\r\n    const newConfig: SequenceConfigData = {\r\n      testSuiteName: selectedSuiteName.value,\r\n      sequencePackageName: selectedPackageName.value,\r\n      sequencePackageXml: xmlContent.value\r\n    };\r\n    await sequenceApi.updateSequenceConfig(newConfig);\r\n    ElMessage.success('Save successful');\r\n    // 重新加载XML内容\r\n    await handlePackageChange();\r\n  }\r\n  catch (error) {\r\n    // 错误处理已由全局拦截器处理，这里不需要额外显示错误消息\r\n    console.error('Save failed:', error);\r\n  } finally {\r\n    loading.value = false;\r\n  }\r\n};\r\n\r\nconst resetToDefault = () => {\r\n  selectedSuiteName.value = defaultConfig.testSuiteName;\r\n  selectedPackageName.value = defaultConfig.sequencePackageName;\r\n};\r\n\r\nonMounted(async () => {\r\n  await loadTestSuites();\r\n  await loadConfig();\r\n});\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.sequence-setting-container {\r\n  display: flex;\r\n  gap: 20px;\r\n  padding: 20px;\r\n  flex: 1;\r\n}\r\n\r\n.left-panel {\r\n  width: 300px;\r\n  min-width: 300px;\r\n  background-color: #ffffff;\r\n}\r\n\r\n.right-panel {\r\n  flex: 1;\r\n  min-width: 0;\r\n  background-color: #ffffff;\r\n  overflow: hidden;\r\n  display: flex;\r\n  flex-direction: column;\r\n  border-radius: 2px;\r\n\r\n  :deep(.el-empty) {\r\n    flex: 1;\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: center;\r\n    margin: 0;\r\n  }\r\n}\r\n\r\n:deep(.el-form-item:last-child) {\r\n  margin-bottom: 0;\r\n}\r\n\r\n</style>\r\n", "import script from \"./SequenceSetting.vue?vue&type=script&setup=true&lang=ts\"\nexport * from \"./SequenceSetting.vue?vue&type=script&setup=true&lang=ts\"\n\nimport \"./SequenceSetting.vue?vue&type=style&index=0&id=3860eaac&scoped=true&lang=scss\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-3860eaac\"]])\n\nexport default __exports__"], "names": ["_hoisted_1", "class", "_hoisted_2", "_hoisted_3", "_defineComponent", "__name", "setup", "__props", "loading", "ref", "testSuites", "selectedSuiteName", "selectedPackageName", "config", "testSuiteName", "sequencePackageName", "defaultConfig", "currentPackages", "computed", "suite", "value", "find", "s", "name", "packages", "xmlContent", "handlePackageChange", "async", "response", "testSuiteApi", "getXml", "data", "error", "ElMessage", "console", "handleSuiteChange", "loadTestSuites", "getBuiltIn", "loadConfig", "sequenceApi", "getSequenceConfig", "handleSave", "newConfig", "sequencePackageXml", "updateSequenceConfig", "success", "warning", "resetToDefault", "onMounted", "_ctx", "_cache", "_component_el_option", "_resolveComponent", "_component_el_select", "_component_el_form_item", "_component_el_button", "_component_el_form", "_component_el_empty", "_directive_loading", "_resolveDirective", "_withDirectives", "_openBlock", "_createElementBlock", "_createElementVNode", "_createVNode", "model", "default", "_withCtx", "label", "modelValue", "$event", "placeholder", "onChange", "style", "_Fragment", "_renderList", "_createBlock", "key", "version", "_", "disabled", "sequencePackage", "index", "type", "onClick", "_createTextVNode", "XmlViewer", "title", "content", "description", "__exports__"], "sourceRoot": ""}