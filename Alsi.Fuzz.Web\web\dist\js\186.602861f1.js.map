{"version": 3, "file": "js/186.602861f1.js", "mappings": "sXAGA,MAAMA,EAAa,CAAEC,MAAO,gBAM5B,OAA4BC,EAAAA,EAAAA,IAAiB,CAC3CC,OAAQ,cACRC,MAAO,CACLC,MAAO,CAAC,EACRC,IAAK,CAAC,EACNC,aAAc,CAAEC,KAAMC,UAExBC,KAAAA,CAAMC,GCAR,MAAMP,EAAQO,EAMRC,EAAcC,IAClB,MAAMC,EAAOD,EAAKE,cACZC,EAAQC,OAAOJ,EAAKK,WAAa,GAAGC,SAAS,EAAG,KAChDC,EAAMH,OAAOJ,EAAKQ,WAAWF,SAAS,EAAG,KACzCG,EAAQL,OAAOJ,EAAKU,YAAYJ,SAAS,EAAG,KAC5CK,EAAUP,OAAOJ,EAAKY,cAAcN,SAAS,EAAG,KAChDO,EAAUT,OAAOJ,EAAKc,cAAcR,SAAS,EAAG,KAChDS,EAAeX,OAAOJ,EAAKgB,mBAAmBV,SAAS,EAAG,KAEhE,MAAO,GAAGL,KAAQE,KAASI,KAAOE,KAASE,KAAWE,KAAWE,GAAc,EAG3EE,EAAkBjB,IACtB,MAAMS,EAAQL,OAAOJ,EAAKU,YAAYJ,SAAS,EAAG,KAC5CK,EAAUP,OAAOJ,EAAKY,cAAcN,SAAS,EAAG,KAChDO,EAAUT,OAAOJ,EAAKc,cAAcR,SAAS,EAAG,KACtD,MAAO,GAAGG,KAASE,KAAWE,GAAS,EAGnCK,GAAiBC,EAAAA,EAAAA,KAAS,KAC9B,IAAK5B,EAAMC,MAAO,MAAO,GAEzB,MAAM4B,EAAY,IAAIC,KAAK9B,EAAMC,OAC3B8B,EAAYvB,EAAWqB,GAE7B,IAAK7B,EAAME,IAAK,MAAO,UAAU6B,IAEjC,MAAMC,EAAU,IAAIF,KAAK9B,EAAME,KACzB+B,EAAUzB,EAAWwB,GACrBE,EAAaF,EAAQG,UAAYN,EAAUM,UAEjD,IAAIC,EAAe,GACnB,GAAIF,EAAa,IACfE,EAAe,GAAGF,WACb,CACL,MAAMZ,EAAUe,KAAKC,MAAMJ,EAAa,KAClCd,EAAUiB,KAAKC,MAAMhB,EAAU,IAC/BJ,EAAQmB,KAAKC,MAAMlB,EAAU,IAGjCgB,EADElB,EAAQ,EACK,GAAGA,MAAUE,EAAU,OAAOE,EAAU,MAC9CF,EAAU,EACJ,GAAGA,MAAYE,EAAU,MAEzB,GAAGA,I,CAItB,MAAO,aAAac,eAA0BL,aAAqBE,GAAS,IAGxEM,GAAgBX,EAAAA,EAAAA,KAAS,KAC7B,IAAK5B,EAAMC,MAAO,MAAO,IAEzB,MAAM4B,EAAY,IAAIC,KAAK9B,EAAMC,OAC3BuC,EAAWd,EAAeG,GAEhC,IAAK7B,EAAME,MAAQF,EAAMG,aAAc,OAAOqC,EAE9C,MAAMR,EAAU,IAAIF,KAAK9B,EAAME,KACzBgC,EAAaF,EAAQG,UAAYN,EAAUM,UAEjD,GAAID,EAAa,IACf,MAAO,cAAcM,cAAqBN,MACrC,CACL,MAAMZ,EAAUe,KAAKI,MAAMP,EAAa,KACxC,MAAO,cAAcM,cAAqBlB,I,KDE9C,MAAO,CAACoB,EAAUC,IACRD,EAAKzC,QACR2C,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,KAAaC,EAAAA,EAAAA,IAAOC,EAAAA,IAAY,CAC7CC,IAAK,EACLC,QAAStB,EAAeuB,MACxBC,UAAW,MACXC,OAAQ,OACR,cAAe,IACd,CACDC,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBC,EAAAA,EAAAA,IAAoB,OAAQ3D,GAAY4D,EAAAA,EAAAA,IAAiBjB,EAAcW,OAAQ,MAEjFO,EAAG,GACF,EAAG,CAAC,cACPC,EAAAA,EAAAA,IAAoB,IAAI,EAE9B,I,UEpGA,MAAMC,GAA2B,OAAgB,EAAQ,CAAC,CAAC,YAAY,qBAEvE,QCLA,MAAM/D,EAAa,CAAEC,MAAO,gBACtB+D,EAAa,CACjBZ,IAAK,EACLnD,MAAO,qBAEHgE,EAAa,CACjBb,IAAK,EACLnD,MAAO,qBAEHiE,EAAa,CACjBd,IAAK,EACLnD,MAAO,iBAEHkE,EAAa,CACjBf,IAAK,EACLnD,MAAO,gBAEHmE,EAAa,CAAEnE,MAAO,kBACtBoE,EAAa,CAAC,WACdC,EAAa,CAAErE,MAAO,iBACtBsE,EAAa,CAAEtE,MAAO,aACtBuE,EAAc,CAAEvE,MAAO,eACvBwE,EAAc,CAAExE,MAAO,iBAU7B,OAA4BC,EAAAA,EAAAA,IAAiB,CAC3CC,OAAQ,4BACRC,MAAO,CACLsE,QAAS,CAAC,EACVC,QAAS,CAAEnE,KAAMC,SACjBmE,kBAAmB,CAAC,GAEtBC,MAAO,CAAC,4BACRnE,KAAAA,CAAMC,GAAgBmE,KAAMC,IC+C9B,MAAM3E,EAAQO,EAMRmE,EAAOC,EAKPC,GAAgBC,EAAAA,EAAAA,IAAe,IAsBrC,SAASC,IACH9E,EAAMsE,QAAQS,OAAS,IAEzBH,EAAc1B,MAAQ,IAAI8B,MAAMhF,EAAMsE,QAAQS,QAAQE,MAAK,GAC3DC,IAEJ,EAzBAC,EAAAA,EAAAA,KAAU,KACRL,GAAyB,KAI3BM,EAAAA,EAAAA,KAAM,IAAMpF,EAAMsE,UAAS,KACzBQ,GAAyB,GACxB,CAAEO,WAAW,KAGhBD,EAAAA,EAAAA,KAAM,IAAMpF,EAAMwE,oBAAoBc,IAChCA,GAAetF,EAAMsE,QAAQS,OAAS,IACxCH,EAAc1B,MAAQlD,EAAMsE,QAAQiB,KAAIC,GACtCF,EAAYG,SAASD,EAAKE,gB,GAG7B,CAAEC,MAAM,IAYX,MAAMC,GAAgBhE,EAAAA,EAAAA,KAAS,IACtBgD,EAAc1B,MAAM6B,OAAS,GAAKH,EAAc1B,MAAM2C,OAAMC,GAAYA,MAI3EC,GAAkBnE,EAAAA,EAAAA,KAAS,IACxBgD,EAAc1B,MAAM8C,MAAKF,GAAYA,MAAcF,EAAc1C,QAIpE+C,EAAyBC,IAC7BtB,EAAc1B,MAAQ,IAAI8B,MAAMhF,EAAMsE,QAAQS,QAAQE,KAAKiB,GAC3DhB,GAAyB,EAIrBiB,EAAmBC,IACvBxB,EAAc1B,MAAMkD,IAAUxB,EAAc1B,MAAMkD,GAClDlB,GAAyB,EAIrBmB,EAAmBA,CAACH,EAAcE,EAAeZ,KACrDZ,EAAc1B,MAAMkD,GAASF,EAC7BhB,GAAyB,EAIrBA,EAA0BA,KAE9B,MAAMY,EAAW9F,EAAMsE,QACpBgC,QAAO,CAAC7C,EAAG2C,IAAUxB,EAAc1B,MAAMkD,KACzCb,KAAIC,GAAQA,EAAKE,eAGda,EAAiB,IAAI,IAAIC,IAAIV,IAEnCpB,EAAK,2BAA4B6B,EAAe,EAoB5CE,GAAqB7E,EAAAA,EAAAA,KAAS,KAClC,IAAK5B,EAAMsE,SAAoC,IAAzBtE,EAAMsE,QAAQS,OAAc,MAAO,GAEzD,MAAM2B,EAAa,IAAIC,IACjBC,EAAa,IAAIJ,IAavB,OAVAxG,EAAMsE,QAAQuC,SAAQC,IACpB,MAAMC,EAAQL,EAAWM,IAAIF,EAAOpB,eAAiB,EACrDgB,EAAWO,IAAIH,EAAOpB,aAAcqB,EAAQ,GAGxCA,EAAQ,GACVH,EAAWM,IAAIJ,EAAOpB,a,IAInBV,MAAMmC,KAAKP,EAAW,IAIzBQ,EAAeC,GACZZ,EAAmBvD,MAAMuC,SAAS4B,GDhD3C,MAAO,CAAC3E,EAAUC,KAChB,MAAM2E,GAAqBC,EAAAA,EAAAA,IAAkB,WACvCC,GAAyBD,EAAAA,EAAAA,IAAkB,eAC3CE,GAAoBF,EAAAA,EAAAA,IAAkB,UAE5C,OAAQ3E,EAAAA,EAAAA,OAAc8E,EAAAA,EAAAA,IAAoB,MAAO9H,EAAY,CAC3D+C,EAAO,KAAOA,EAAO,IAAKY,EAAAA,EAAAA,IAAoB,MAAO,CAAE1D,MAAO,gBAAkB,EAC9E0D,EAAAA,EAAAA,IAAoB,KAAM,KAAM,2BAChCA,EAAAA,EAAAA,IAAoB,MAAO,CAAE1D,MAAO,kBAAoB,uCACtD,IACH4G,EAAmBvD,MAAM6B,OAAS,IAC9BnC,EAAAA,EAAAA,OAAc8E,EAAAA,EAAAA,IAAoB,MAAO9D,EAAY,EACpD+D,EAAAA,EAAAA,KAAa7E,EAAAA,EAAAA,IAAO8E,EAAAA,IAAU,CAC5BC,MAAO,6CACPzH,KAAM,UACN0H,UAAU,EACV,YAAa,IACZ,CACDzE,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtByE,EAAAA,EAAAA,IAAiB,4BAA6BvE,EAAAA,EAAAA,IAAiBiD,EAAmBvD,MAAM8E,KAAK,OAAS,IAAK,GAC3GrF,EAAO,KAAOA,EAAO,IAAKY,EAAAA,EAAAA,IAAoB,KAAM,KAAM,MAAO,IACjEZ,EAAO,KAAOA,EAAO,IAAKoF,EAAAA,EAAAA,IAAiB,0CAE7CtE,EAAG,QAGPC,EAAAA,EAAAA,IAAoB,IAAI,GAC3BhB,EAAK6B,UACD3B,EAAAA,EAAAA,OAAc8E,EAAAA,EAAAA,IAAoB,MAAO7D,EAAY,EACpD8D,EAAAA,EAAAA,IAAaL,EAAoB,CAAEzH,MAAO,cAAgB,CACxDwD,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBqE,EAAAA,EAAAA,KAAa7E,EAAAA,EAAAA,IAAOmF,EAAAA,aAEtBxE,EAAG,IAELd,EAAO,KAAOA,EAAO,IAAKoF,EAAAA,EAAAA,IAAiB,oBAEpB,IAAxBrF,EAAK4B,QAAQS,SACXnC,EAAAA,EAAAA,OAAc8E,EAAAA,EAAAA,IAAoB,MAAO5D,EAAY,wCACrDlB,EAAAA,EAAAA,OAAc8E,EAAAA,EAAAA,IAAoB,MAAO3D,EAAY,EACpDR,EAAAA,EAAAA,IAAoB,MAAOS,EAAY,EACrC2D,EAAAA,EAAAA,IAAaH,EAAwB,CACnCU,WAAYtC,EAAc1C,MAC1B,sBAAuBP,EAAO,KAAOA,EAAO,GAAMwF,GAAkBvC,EAAe1C,MAAQiF,GAC3FC,cAAerC,EAAgB7C,MAC/BmF,SAAUpC,EACVqC,KAAM,SACL,CACDjF,SAASC,EAAAA,EAAAA,KAAS,IAAMX,EAAO,KAAOA,EAAO,GAAK,EAChDoF,EAAAA,EAAAA,IAAiB,oBAEnBtE,EAAG,GACF,EAAG,CAAC,aAAc,sBAEtBb,EAAAA,EAAAA,KAAW,IAAO8E,EAAAA,EAAAA,IAAoBa,EAAAA,GAAW,MAAMC,EAAAA,EAAAA,IAAY9F,EAAK4B,SAAS,CAACkB,EAAMY,MAC/ExD,EAAAA,EAAAA,OAAc8E,EAAAA,EAAAA,IAAoB,MAAO,CAC/C1E,IAAKwC,EAAKiD,IAAM,QAAQrC,IACxBvG,OAAO6I,EAAAA,EAAAA,IAAgB,CAAC,YAAa,CAAE,QAAWtC,EAAQ,IAAM,EAAG,UAAagB,EAAY5B,EAAKE,iBACjGiD,SAASC,EAAAA,EAAAA,KAAgBT,GAAiBhC,EAAgBC,IAAS,CAAC,UACnE,EACD7C,EAAAA,EAAAA,IAAoB,MAAOW,EAAY,EACrCyD,EAAAA,EAAAA,IAAaH,EAAwB,CACnCU,WAAYtD,EAAc1B,MAAMkD,GAChC,sBAAwB+B,GAAkBvD,EAAc1B,MAAMkD,GAAU+B,EACxEG,KAAM,QACNK,QAAShG,EAAO,KAAOA,EAAO,IAAKiG,EAAAA,EAAAA,KAAe,QAAU,CAAC,UAC7DP,SAAUnC,GAAOG,EAAiBH,EAAKE,EAAOZ,IAC7C,KAAM,EAAG,CAAC,aAAc,sBAAuB,gBAEpDjC,EAAAA,EAAAA,IAAoB,MAAOY,EAAY,EACrC4D,EAAAA,EAAAA,KAAiBvE,EAAAA,EAAAA,IAAiBgC,EAAKE,cAAgB,IAAK,GAC3D0B,EAAY5B,EAAKE,gBACb9C,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAa4E,EAAmB,CAC7CzE,IAAK,EACLsF,KAAM,QACNlI,KAAM,UACNgD,OAAQ,OACRvD,MAAO,iBACN,CACDwD,SAASC,EAAAA,EAAAA,KAAS,IAAMX,EAAO,KAAOA,EAAO,GAAK,EAChDoF,EAAAA,EAAAA,IAAiB,mBAEnBtE,EAAG,MAELC,EAAAA,EAAAA,IAAoB,IAAI,MAE9BH,EAAAA,EAAAA,IAAoB,MAAOa,EAAa,EACtCuD,EAAAA,EAAAA,IAAakB,EAAAA,EAAc,CACzBC,MAAOtD,EAAKsD,OACX,KAAM,EAAG,CAAC,aAEfvF,EAAAA,EAAAA,IAAoB,MAAOc,EAAa,EACtCsD,EAAAA,EAAAA,IAAaoB,EAAa,CACxB9I,MAAOuF,EAAKvF,MACZC,IAAKsF,EAAKtF,IACVC,cAAc,GACb,KAAM,EAAG,CAAC,QAAS,WAEvB,GAAI8D,MACL,UAEZ,CAEJ,IEpQA,MAAM,GAA2B,OAAgB,EAAQ,CAAC,CAAC,YAAY,qBAEvE,QCLA,MAAMrE,EAAa,CAAEC,MAAO,cACtB+D,EAAa,CAAE/D,MAAO,gBACtBgE,EAAa,CACjBb,IAAK,EACLnD,MAAO,aAEHiE,EAAa,CAAEjE,MAAO,cACtBkE,EAAa,CAAElE,MAAO,kBACtBmE,EAAa,CACjBhB,IAAK,EACLnD,MAAO,qBAEHoE,EAAa,CACjBjB,IAAK,EACLnD,MAAO,iBAEHqE,EAAa,CACjBlB,IAAK,EACLnD,MAAO,gBAEHsE,EAAa,CAAC,WACdC,EAAc,CAAEvE,MAAO,cACvBwE,EAAc,CAAExE,MAAO,cACvBmJ,EAAc,CAAEnJ,MAAO,eACvBoJ,EAAc,CAAEpJ,MAAO,cACvBqJ,EAAc,CAClBlG,IAAK,EACLnD,MAAO,iBAEHsJ,EAAc,CAClBnG,IAAK,EACLnD,MAAO,iBAEHuJ,EAAc,CAAEvJ,MAAO,eACvBwJ,EAAc,CAAExJ,MAAO,kBACvByJ,EAAc,CAAEzJ,MAAO,iBACvB0J,EAAc,CAAE1J,MAAO,eACvB2J,EAAc,CAAC,SAOrB,OAA4B1J,EAAAA,EAAAA,IAAiB,CAC3CC,OAAQ,sBACRC,MAAO,CACLyJ,MAAO,CAAC,EACRC,WAAY,CAAEtJ,KAAMC,SACpBsJ,iBAAkB,CAAEvJ,KAAMC,SAC1BuJ,cAAe,CAAC,GAElBtJ,KAAAA,CAAMC,GCcR,MAAMP,EAAQO,EAQRsJ,GAAiBhF,EAAAA,EAAAA,IAA6B,CAAC,GAE/CiF,GAAeC,EAAAA,EAAAA,IAAuC,CAAC,GAEvDC,GAAgBD,EAAAA,EAAAA,IAAkC,CAAC,GAGnDE,GAAerI,EAAAA,EAAAA,KAAS,KAE5B,MAAMsI,EAAiC,CAAC,EAExC,IAAK,MAAM1E,KAAQxF,EAAMyJ,MAAO,CAC9B,MAAMU,GAAa3E,EAAK6B,MAAQ,IAAI+C,MAAM,KACpCC,EAASF,EAAUpF,OAAS,EAAIoF,EAAU,GAAK3E,EAAK6B,KAErD6C,EAAOG,KACVH,EAAOG,GAAU,GAEnBH,EAAOG,I,CAIT,OAAOC,OAAOC,QAAQL,GAAQ3E,KAAI,EAAEiF,EAAazD,MAAW,CAC1DyD,cACAzD,YACE0D,MAAK,CAACC,EAAGC,IAAMD,EAAEF,YAAYI,cAAcD,EAAEH,cAAa,IAI1DK,EAAwBA,CAACL,EAAqBM,KAElD,MAAMC,EAAc/K,EAAM4J,cAAgB5J,EAAMyJ,MAAM1E,OACtD,OAAOgG,EAAcD,CAAU,EAI3BE,EAAiBR,IAErBR,EAAcQ,IAAe,EAG7BS,YAAW,KACT,MAAMC,EAAalL,EAAMyJ,MAAMnD,QAAOd,IACpC,MAAM2E,GAAa3E,EAAK6B,MAAQ,IAAI+C,MAAM,KACpCC,EAASF,EAAUpF,OAAS,EAAIoF,EAAU,GAAK3E,EAAK6B,KAC1D,OAAOgD,IAAWG,CAAW,IAI/BV,EAAaU,GAAeU,EAC5BlB,EAAcQ,IAAe,CAAK,GACjC,IAAI,EAIHW,EAAeX,IACnB,MAAMY,EAAsBvB,EAAe3G,MAAMsH,GAGjDX,EAAe3G,MAAMsH,IAAgBY,EAGhCA,GAAwBtB,EAAaU,IACxCQ,EAAcR,E,GAKlBpF,EAAAA,EAAAA,KAAM,IAAMpF,EAAMyJ,QAAQ4B,IACxBC,QAAQC,IAAI,iBAAkBF,EAAStG,QAEvCuF,OAAOkB,KAAK1B,GAAcjD,SAAQ7D,WACzB8G,EAAa9G,EAAI,IAI1BsH,OAAOkB,KAAKxB,GAAenD,SAAQ7D,WAC1BgH,EAAchH,EAAI,IAI3B6G,EAAe3G,MAAQ,CAAC,CAAC,GACxB,CAAEyC,MAAM,IAGX,MAAM8F,EAAyBC,IAG7B,GAFAA,EAAKrJ,KAAKC,MAAMoJ,GAEZA,EAAK,IACP,MAAO,GAAGA,MACL,GAAIA,EAAK,IAAO,CACrB,MAAMpK,EAAUe,KAAKC,MAAMoJ,EAAK,KAC1BC,EAActJ,KAAKC,MAAMoJ,EAAK,KACpC,MAAO,GAAGpK,MAAYqK,K,CACjB,GAAID,EAAK,KAAS,CACvB,MAAMtK,EAAUiB,KAAKC,MAAMoJ,EAAK,KAC1BpK,EAAUe,KAAKC,MAAOoJ,EAAK,IAAS,KAC1C,MAAO,GAAGtK,MAAYE,I,CACjB,CACL,MAAMJ,EAAQmB,KAAKC,MAAMoJ,EAAK,MACxBtK,EAAUiB,KAAKC,MAAOoJ,EAAK,KAAW,KAC5C,MAAO,GAAGxK,MAAUE,I,GDbxB,MAAO,CAACsB,EAAUC,KAChB,MAAM2E,GAAqBC,EAAAA,EAAAA,IAAkB,WAE7C,OAAQ3E,EAAAA,EAAAA,OAAc8E,EAAAA,EAAAA,IAAoB,MAAO9H,EAAY,EAC3D2D,EAAAA,EAAAA,IAAoB,MAAOK,EAAY,CACrCjB,EAAO,KAAOA,EAAO,IAAKY,EAAAA,EAAAA,IAAoB,MAAO,CAAE1D,MAAO,eAAiB,EAC7E0D,EAAAA,EAAAA,IAAoB,KAAM,KAAM,0BAC9B,IACHb,EAAK+G,MAAM1E,OAAS,IAChBnC,EAAAA,EAAAA,OAAc8E,EAAAA,EAAAA,IAAoB,MAAO7D,EAAY,EACpDN,EAAAA,EAAAA,IAAoB,OAAQO,EAAY,eAAgBN,EAAAA,EAAAA,IAAiBd,EAAK+G,MAAM1E,QAAU,SAAU,IACxGxB,EAAAA,EAAAA,IAAoB,OAAQQ,EAAY,qBAAsBP,EAAAA,EAAAA,IAAiBiI,EAAsB/I,EAAKkH,gBAAiB,OAE7HlG,EAAAA,EAAAA,IAAoB,IAAI,KAE7BhB,EAAKgH,aACD9G,EAAAA,EAAAA,OAAc8E,EAAAA,EAAAA,IAAoB,MAAO1D,EAAY,EACpD2D,EAAAA,EAAAA,IAAaL,EAAoB,CAAEzH,MAAO,cAAgB,CACxDwD,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBqE,EAAAA,EAAAA,KAAa7E,EAAAA,EAAAA,IAAOmF,EAAAA,aAEtBxE,EAAG,IAELd,EAAO,KAAOA,EAAO,IAAKoF,EAAAA,EAAAA,IAAiB,uBAEtB,IAAtBrF,EAAK+G,MAAM1E,QAAgBrC,EAAKiH,mBAC9B/G,EAAAA,EAAAA,OAAc8E,EAAAA,EAAAA,IAAoB,MAAOzD,EAAY,0CACrDvB,EAAK+G,MAAM1E,OAAS,IAClBnC,EAAAA,EAAAA,OAAc8E,EAAAA,EAAAA,IAAoB,MAAOxD,EAAY,GACnDtB,EAAAA,EAAAA,KAAW,IAAO8E,EAAAA,EAAAA,IAAoBa,EAAAA,GAAW,MAAMC,EAAAA,EAAAA,IAAYyB,EAAa/G,OAAQ0I,KAC/EhJ,EAAAA,EAAAA,OAAc8E,EAAAA,EAAAA,IAAoB,MAAO,CAC/C1E,IAAK4I,EAAMpB,YACX3K,MAAO,cACN,EACD0D,EAAAA,EAAAA,IAAoB,MAAO,CACzB1D,MAAO,eACP8I,QAAUR,GAAiBgD,EAAYS,EAAMpB,cAC5C,EACDjH,EAAAA,EAAAA,IAAoB,MAAOa,EAAa,EACtCuD,EAAAA,EAAAA,IAAaL,EAAoB,CAC/BzH,OAAO6I,EAAAA,EAAAA,IAAgB,CAAC,cAAe,CAAE,cAAemB,EAAe3G,MAAM0I,EAAMpB,iBAClF,CACDnH,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBqE,EAAAA,EAAAA,KAAa7E,EAAAA,EAAAA,IAAO+I,EAAAA,gBAEtBpI,EAAG,GACF,KAAM,CAAC,WACVF,EAAAA,EAAAA,IAAoB,OAAQc,GAAab,EAAAA,EAAAA,IAAiBoI,EAAMpB,aAAc,IAC9EjH,EAAAA,EAAAA,IAAoB,OAAQyF,EAAa,KAAMxF,EAAAA,EAAAA,IAAiBoI,EAAM7E,OAAS,UAAW,IAC1FxD,EAAAA,EAAAA,IAAoB,OAAQ0F,EAAa,qBAAsBzF,EAAAA,EAAAA,IAAiBiI,EAAsBZ,EAAsBe,EAAMpB,YAAaoB,EAAM7E,SAAU,MAEhK,EAAG5C,GACL0F,EAAe3G,MAAM0I,EAAMpB,eACvB5H,EAAAA,EAAAA,OAAc8E,EAAAA,EAAAA,IAAoB,MAAOwB,EAAa,CACpDc,EAAc4B,EAAMpB,eAChB5H,EAAAA,EAAAA,OAAc8E,EAAAA,EAAAA,IAAoB,MAAOyB,EAAa,EACrDxB,EAAAA,EAAAA,IAAaL,EAAoB,CAAEzH,MAAO,cAAgB,CACxDwD,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBqE,EAAAA,EAAAA,KAAa7E,EAAAA,EAAAA,IAAOmF,EAAAA,aAEtBxE,EAAG,IAELd,EAAO,KAAOA,EAAO,IAAKoF,EAAAA,EAAAA,IAAiB,oBAE5C+B,EAAa8B,EAAMpB,eACjB5H,EAAAA,EAAAA,KAAW,IAAO8E,EAAAA,EAAAA,IAAoBa,EAAAA,GAAW,CAAEvF,IAAK,IAAKwF,EAAAA,EAAAA,IAAYsB,EAAa8B,EAAMpB,cAAc,CAAChF,EAAMY,MACxGxD,EAAAA,EAAAA,OAAc8E,EAAAA,EAAAA,IAAoB,MAAO,CAC/C1E,IAAKwC,EAAKiD,IAAMrC,EAChBvG,OAAO6I,EAAAA,EAAAA,IAAgB,CAAC,YAAa,CAAE,QAAWtC,EAAQ,IAAM,MAC/D,EACD7C,EAAAA,EAAAA,IAAoB,MAAO6F,EAAa,EACtC7F,EAAAA,EAAAA,IAAoB,MAAO8F,EAAa,EACtC9F,EAAAA,EAAAA,IAAoB,OAAQ+F,GAAa9F,EAAAA,EAAAA,IAAiBgC,EAAKiD,IAAK,IACpElF,EAAAA,EAAAA,IAAoB,OAAQgG,GAAa/F,EAAAA,EAAAA,IAAiBgC,EAAK6B,MAAO,QAG1E9D,EAAAA,EAAAA,IAAoB,MAAO,CACzB1D,MAAO,iBACPgI,MAAO,GAAGrC,EAAKsG,cACdtI,EAAAA,EAAAA,IAAiBgC,EAAKsG,WAAY,EAAGtC,IACvC,MACD,OACJ9F,EAAAA,EAAAA,IAAoB,IAAI,OAEhCA,EAAAA,EAAAA,IAAoB,IAAI,QAE5B,UAENA,EAAAA,EAAAA,IAAoB,IAAI,IAChC,CAEJ,IE1PA,MAAM,GAA2B,OAAgB,EAAQ,CAAC,CAAC,YAAY,qBAEvE,SCLA,MAAM9D,GAAa,CAAEC,MAAO,wBACtB+D,GAAa,CAAE/D,MAAO,WACtBgE,GAAa,CAAEhE,MAAO,oBACtBiE,GAAa,CAAEjE,MAAO,kBACtBkE,GAAa,CAAElE,MAAO,gBAW5B,QAA4BC,EAAAA,EAAAA,IAAiB,CAC3CC,OAAQ,YACRO,KAAAA,CAAMC,GCwCR,MAAMwL,GAAwBlH,EAAAA,EAAAA,IAAkB,IAC1CmH,GAAiBnH,EAAAA,EAAAA,IAAkB,IACnCN,GAAUM,EAAAA,EAAAA,KAAI,GACd6E,GAAa7E,EAAAA,EAAAA,KAAI,GACjBoH,GAASpH,EAAAA,EAAAA,KAAI,GACbqH,GAAmBrH,EAAAA,EAAAA,IAAIsH,EAAAA,GAAaC,QACpCC,GAAsBxH,EAAAA,EAAAA,KAAI,GAC1BL,GAAoBK,EAAAA,EAAAA,IAAc,IAClCyH,GAAoBzH,EAAAA,EAAAA,KAAI,GAGxB0H,GAAoB3K,EAAAA,EAAAA,KAAS,IAC1BmK,EAAsB7I,MAAMoD,QAAOQ,GAAUA,EAAOgC,QAAU0D,EAAAA,GAAeC,YAIhFC,GAAU9K,EAAAA,EAAAA,KAAS,IAChB0K,EAAkBpJ,OAAS8I,EAAe9I,MAAM6B,OAAS,IAAMkH,EAAO/I,SAI/EkC,EAAAA,EAAAA,IAAMmH,GAAoBjI,IACpBA,EAAQS,OAAS,GAAwC,IAAnCP,EAAkBtB,MAAM6B,SAChDP,EAAkBtB,MAAQoB,EAAQiB,KAAIC,GAAQA,EAAKE,e,GAEpD,CAAEL,WAAW,IAGhB,MAAMsH,EAA6BC,UACjCrI,EAAQrB,OAAQ,EAChB,IACE,MAAM2J,QAAiBC,EAAAA,GAAOC,qCAC9BhB,EAAsB7I,MAAQ2J,EAASG,I,CACvC,MAAOC,GACP3B,QAAQ2B,MAAM,YAAaA,GAC3BC,EAAAA,GAAUD,MAAM,yC,CAChB,QACA1I,EAAQrB,OAAQ,C,GAKdiK,EAAoBP,UAExB,GAAuC,IAAnCpI,EAAkBtB,MAAM6B,OAA5B,CAKA2E,EAAWxG,OAAQ,EACnBmJ,EAAoBnJ,OAAQ,EAC5BoJ,EAAkBpJ,OAAQ,EAG1B8I,EAAe9I,MAAQ,GAEvB,IACE,MAAM2J,QAAiBC,EAAAA,GAAOM,cAAclB,EAAiBhJ,MAAOsB,EAAkBtB,OAEtF8I,EAAe9I,MAAQ,IAAI2J,EAASG,MACpCV,EAAkBpJ,OAAQ,EAC1BgK,EAAAA,GAAUG,QAAQ,0BAA0BR,EAASG,KAAKjI,qBAC1DuG,QAAQC,IAAI,mBAAoBS,EAAe9I,MAAM6B,O,CACrD,MAAOkI,GACP3B,QAAQ2B,MAAM,YAAaA,GAC3BC,EAAAA,GAAUD,MAAM,gC,CAChB,QACAvD,EAAWxG,OAAQ,C,OAtBnBgK,EAAAA,GAAUI,QAAQ,sC,EA2BhBC,EAAgBX,UACpB,GAAKN,EAAkBpJ,OAAyC,IAAhC8I,EAAe9I,MAAM6B,OAArD,CAKAkH,EAAO/I,OAAQ,EACf,IACE,MAAM2J,QAAiBC,EAAAA,GAAOU,UAAUtB,EAAiBhJ,MAAOsB,EAAkBtB,OAClFgK,EAAAA,GAAUG,QAAQ,sBAAsBR,EAASG,KAAKjI,qBAItDiH,EAAe9I,MAAQ,IAAI2J,EAASG,MACpC1B,QAAQC,IAAI,eAAgBS,EAAe9I,MAAM6B,O,CACjD,MAAOkI,GACP3B,QAAQ2B,MAAM,YAAaA,GAC3BC,EAAAA,GAAUD,MAAM,4B,CAChB,QACAhB,EAAO/I,OAAQ,C,OAjBfgK,EAAAA,GAAUI,QAAQ,mC,EAsBhBG,GAAyB7L,EAAAA,EAAAA,KAAS,KACtC,IAAKoK,EAAe9I,MAAM6B,SAAWgH,EAAsB7I,MAAM6B,OAC/D,OAAO,EAIT,MAAM2I,EAAkB,IAAI/G,IAG5BoF,EAAsB7I,MAAM2D,SAAQC,IAClC,GAAIA,EAAO7G,OAAS6G,EAAO5G,KAAO4G,EAAOpB,aAAc,CACrD,MAAMiI,EAAgB,IAAI7L,KAAKgF,EAAO5G,KAAKiC,UAAY,IAAIL,KAAKgF,EAAO7G,OAAOkC,UAC9EuL,EAAgBzG,IAAIH,EAAOpB,aAAciI,E,KAK7C,MAAMC,EAAY5B,EAAe9I,MAAM2K,QAAO,CAACC,EAAKC,KAClD,MAAMC,EAAeN,EAAgB1G,IAAI+G,EAASrI,eAAiB,EACnE,OAAOoI,EAAME,CAAY,GACxB,GAEH,OAAOJ,CAAS,ID9BlB,OCkCAzI,EAAAA,EAAAA,KAAU,KACRwH,GAA4B,IDnCvB,CAACjK,EAAUC,KAChB,MAAMsL,GAAsB1G,EAAAA,EAAAA,IAAkB,YACxC2G,GAA4B3G,EAAAA,EAAAA,IAAkB,kBAC9C4G,GAAuB5G,EAAAA,EAAAA,IAAkB,aAE/C,OAAQ3E,EAAAA,EAAAA,OAAc8E,EAAAA,EAAAA,IAAoB,MAAO9H,GAAY,EAC3D2D,EAAAA,EAAAA,IAAoB,MAAOK,GAAY,EACrCL,EAAAA,EAAAA,IAAoB,MAAOM,GAAY,CACrClB,EAAO,KAAOA,EAAO,IAAKY,EAAAA,EAAAA,IAAoB,OAAQ,KAAM,aAAc,KAC1EoE,EAAAA,EAAAA,IAAauG,EAA2B,CACtChG,WAAYgE,EAAiBhJ,MAC7B,sBAAuBP,EAAO,KAAOA,EAAO,GAAMwF,GAAkB+D,EAAkBhJ,MAAQiF,GAC9FG,KAAM,SACL,CACDjF,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBqE,EAAAA,EAAAA,IAAasG,EAAqB,CAChCG,OAAOtL,EAAAA,EAAAA,IAAOqJ,EAAAA,IAAcC,QAC3B,CACD/I,SAASC,EAAAA,EAAAA,KAAS,IAAMX,EAAO,KAAOA,EAAO,GAAK,EAChDoF,EAAAA,EAAAA,IAAiB,cAEnBtE,EAAG,GACF,EAAG,CAAC,WACPkE,EAAAA,EAAAA,IAAasG,EAAqB,CAChCG,OAAOtL,EAAAA,EAAAA,IAAOqJ,EAAAA,IAAckC,MAC3B,CACDhL,SAASC,EAAAA,EAAAA,KAAS,IAAMX,EAAO,KAAOA,EAAO,GAAK,EAChDoF,EAAAA,EAAAA,IAAiB,YAEnBtE,EAAG,GACF,EAAG,CAAC,aAETA,EAAG,GACF,EAAG,CAAC,kBAETF,EAAAA,EAAAA,IAAoB,MAAOO,GAAY,EACrC6D,EAAAA,EAAAA,IAAawG,EAAsB,CACjC/N,KAAM,UACNuI,QAASwE,EACT5I,QAASmF,EAAWxG,MACpBoL,SAA6C,IAAnC9J,EAAkBtB,MAAM6B,OAClCuD,KAAM,SACL,CACDjF,SAASC,EAAAA,EAAAA,KAAS,IAAMX,EAAO,KAAOA,EAAO,GAAK,EAChDoF,EAAAA,EAAAA,IAAiB,kBAEnBtE,EAAG,GACF,EAAG,CAAC,UAAW,cAClBkE,EAAAA,EAAAA,IAAawG,EAAsB,CACjC/N,KAAM,UACNuI,QAAS4E,EACThJ,QAAS0H,EAAO/I,MAChBoL,UAAW5B,EAAQxJ,MACnBoF,KAAM,SACL,CACDjF,SAASC,EAAAA,EAAAA,KAAS,IAAMX,EAAO,KAAOA,EAAO,GAAK,EAChDoF,EAAAA,EAAAA,IAAiB,cAEnBtE,EAAG,GACF,EAAG,CAAC,UAAW,kBAGtBF,EAAAA,EAAAA,IAAoB,MAAOQ,GAAY,EACrC4D,EAAAA,EAAAA,IAAa4G,EAA2B,CACtCjK,QAASiI,EAAkBrJ,MAC3BqB,QAASA,EAAQrB,MACjBsB,kBAAmBA,EAAkBtB,MACrC,6BAA8BP,EAAO,KAAOA,EAAO,GAAMwF,GAAkB3D,EAAmBtB,MAAQiF,IACrG,KAAM,EAAG,CAAC,UAAW,UAAW,uBACnCR,EAAAA,EAAAA,IAAa6G,GAAqB,CAChC/E,MAAOuC,EAAe9I,MACtBwG,WAAYA,EAAWxG,MACvB,qBAAsBmJ,EAAoBnJ,MAC1C,iBAAkBuK,EAAuBvK,OACxC,KAAM,EAAG,CAAC,QAAS,aAAc,qBAAsB,sBAE5D,CAEJ,IE5NA,MAAM,IAA2B,OAAgB,GAAQ,CAAC,CAAC,YAAY,qBAEvE,S,yFCDA,GAA4BpD,EAAAA,EAAAA,IAAiB,CAC3CC,OAAQ,eACRC,MAAO,CACL8I,MAAO,CAAC,GAEVxI,KAAAA,CAAMC,GCFR,MAAMP,EAAQO,EAIRkO,GAAU7M,EAAAA,EAAAA,KAAoD,KAClE,OAAQ5B,EAAM8I,OACZ,KAAK0D,EAAAA,GAAeC,QAClB,MAAO,UACT,KAAKD,EAAAA,GAAekC,QAClB,MAAO,UACT,KAAKlC,EAAAA,GAAemC,QAClB,MAAO,SACT,KAAKnC,EAAAA,GAAeoC,QACpB,QACE,MAAO,O,IAIPC,EAAoB/F,IACxB,OAAQA,GACN,KAAK0D,EAAAA,GAAekC,QAClB,MAAO,UACT,KAAKlC,EAAAA,GAAeoC,QAClB,MAAO,UACT,KAAKpC,EAAAA,GAAeC,QAClB,MAAO,SACT,KAAKD,EAAAA,GAAemC,QAClB,MAAO,SACT,QACE,MAAO,U,EAIPG,GAAYlN,EAAAA,EAAAA,KAAS,IAClBiN,EAAiB7O,EAAM8I,SDKhC,MAAO,CAACpG,EAAUC,KAChB,MAAM8E,GAAoBF,EAAAA,EAAAA,IAAkB,UAE5C,OAAQ3E,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAa4E,EAAmB,CACpDrH,KAAMqO,EAAQvL,MACdoF,KAAM,QACNyG,MAAO,CAAC,YAAY,SACnB,CACD1L,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtByE,EAAAA,EAAAA,KAAiBvE,EAAAA,EAAAA,IAAiBsL,EAAU5L,OAAQ,MAEtDO,EAAG,GACF,EAAG,CAAC,QAAQ,CAEjB,IE5DA,MAAME,EAAc,EAEpB,O", "sources": ["webpack://fuzz-web/./src/components/common/TimeDisplay.vue?10ba", "webpack://fuzz-web/./src/components/common/TimeDisplay.vue", "webpack://fuzz-web/./src/components/common/TimeDisplay.vue?9136", "webpack://fuzz-web/./src/components/TestCases/InteroperationResultPanel.vue?6c02", "webpack://fuzz-web/./src/components/TestCases/InteroperationResultPanel.vue", "webpack://fuzz-web/./src/components/TestCases/InteroperationResultPanel.vue?d74e", "webpack://fuzz-web/./src/components/TestCases/GeneratedCasesPanel.vue?27d5", "webpack://fuzz-web/./src/components/TestCases/GeneratedCasesPanel.vue", "webpack://fuzz-web/./src/components/TestCases/GeneratedCasesPanel.vue?dba8", "webpack://fuzz-web/./src/views/testplan/TestCases.vue?36a3", "webpack://fuzz-web/./src/views/testplan/TestCases.vue", "webpack://fuzz-web/./src/views/testplan/TestCases.vue?b18a", "webpack://fuzz-web/./src/components/common/CaseStateTag.vue?c0c7", "webpack://fuzz-web/./src/components/common/CaseStateTag.vue", "webpack://fuzz-web/./src/components/common/CaseStateTag.vue?4980"], "sourcesContent": ["import { defineComponent as _defineComponent } from 'vue'\nimport { toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, unref as _unref, withCtx as _withCtx, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode } from \"vue\"\n\nconst _hoisted_1 = { class: \"time-display\" }\n\nimport { computed } from 'vue';\r\nimport { ElTooltip } from 'element-plus';\r\n\r\n\nexport default /*@__PURE__*/_defineComponent({\n  __name: 'TimeDisplay',\n  props: {\n    begin: {},\n    end: {},\n    showDuration: { type: <PERSON>olean }\n  },\n  setup(__props: any) {\n\r\nconst props = __props;\r\n\r\nconst formatDate = (date: Date): string => {\r\n  const year = date.getFullYear();\r\n  const month = String(date.getMonth() + 1).padStart(2, '0');\r\n  const day = String(date.getDate()).padStart(2, '0');\r\n  const hours = String(date.getHours()).padStart(2, '0');\r\n  const minutes = String(date.getMinutes()).padStart(2, '0');\r\n  const seconds = String(date.getSeconds()).padStart(2, '0');\r\n  const milliseconds = String(date.getMilliseconds()).padStart(3, '0');\r\n  \r\n  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}.${milliseconds}`;\r\n};\r\n\r\nconst formatTimeOnly = (date: Date): string => {\r\n  const hours = String(date.getHours()).padStart(2, '0');\r\n  const minutes = String(date.getMinutes()).padStart(2, '0');\r\n  const seconds = String(date.getSeconds()).padStart(2, '0');\r\n  return `${hours}:${minutes}:${seconds}`;\r\n};\r\n\r\nconst tooltipContent = computed((): string => {\r\n  if (!props.begin) return '';\r\n  \r\n  const beginDate = new Date(props.begin);\r\n  const beginFull = formatDate(beginDate);\r\n  \r\n  if (!props.end) return `Start: ${beginFull}`;\r\n  \r\n  const endDate = new Date(props.end);\r\n  const endFull = formatDate(endDate);\r\n  const durationMs = endDate.getTime() - beginDate.getTime();\r\n  \r\n  let durationText = '';\r\n  if (durationMs < 1000) {\r\n    durationText = `${durationMs} ms`;\r\n  } else {\r\n    const seconds = Math.floor(durationMs / 1000);\r\n    const minutes = Math.floor(seconds / 60);\r\n    const hours = Math.floor(minutes / 60);\r\n    \r\n    if (hours > 0) {\r\n      durationText = `${hours}h ${minutes % 60}m ${seconds % 60}s`;\r\n    } else if (minutes > 0) {\r\n      durationText = `${minutes}m ${seconds % 60}s`;\r\n    } else {\r\n      durationText = `${seconds}s`;\r\n    }\r\n  }\r\n  \r\n  return `Duration: ${durationText}<br>Start: ${beginFull}<br>End: ${endFull}`;\r\n});\r\n\r\nconst formattedTime = computed((): string => {\r\n  if (!props.begin) return '-';\r\n  \r\n  const beginDate = new Date(props.begin);\r\n  const beginStr = formatTimeOnly(beginDate);\r\n  \r\n  if (!props.end || !props.showDuration) return beginStr;\r\n  \r\n  const endDate = new Date(props.end);\r\n  const durationMs = endDate.getTime() - beginDate.getTime();\r\n  \r\n  if (durationMs < 1000) {\r\n    return `Started at ${beginStr}, elapsed ${durationMs}ms`;\r\n  } else {\r\n    const seconds = Math.round(durationMs / 1000);\r\n    return `Started at ${beginStr}, elapsed ${seconds}s`;\r\n  }\r\n});\r\n\nreturn (_ctx: any,_cache: any) => {\n  return (_ctx.begin)\n    ? (_openBlock(), _createBlock(_unref(ElTooltip), {\n        key: 0,\n        content: tooltipContent.value,\n        placement: \"top\",\n        effect: \"dark\",\n        \"raw-content\": \"\"\n      }, {\n        default: _withCtx(() => [\n          _createElementVNode(\"span\", _hoisted_1, _toDisplayString(formattedTime.value), 1)\n        ]),\n        _: 1\n      }, 8, [\"content\"]))\n    : _createCommentVNode(\"\", true)\n}\n}\n\n})", "<template>\r\n  <el-tooltip \r\n    v-if=\"begin\" \r\n    :content=\"tooltipContent\" \r\n    placement=\"top\" \r\n    effect=\"dark\"\r\n    raw-content\r\n  >\r\n    <span class=\"time-display\">{{ formattedTime }}</span>\r\n  </el-tooltip>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { computed, defineProps } from 'vue';\r\nimport { ElTooltip } from 'element-plus';\r\n\r\nconst props = defineProps<{\r\n  begin: string | null;\r\n  end?: string | null;\r\n  showDuration?: boolean;\r\n}>();\r\n\r\nconst formatDate = (date: Date): string => {\r\n  const year = date.getFullYear();\r\n  const month = String(date.getMonth() + 1).padStart(2, '0');\r\n  const day = String(date.getDate()).padStart(2, '0');\r\n  const hours = String(date.getHours()).padStart(2, '0');\r\n  const minutes = String(date.getMinutes()).padStart(2, '0');\r\n  const seconds = String(date.getSeconds()).padStart(2, '0');\r\n  const milliseconds = String(date.getMilliseconds()).padStart(3, '0');\r\n  \r\n  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}.${milliseconds}`;\r\n};\r\n\r\nconst formatTimeOnly = (date: Date): string => {\r\n  const hours = String(date.getHours()).padStart(2, '0');\r\n  const minutes = String(date.getMinutes()).padStart(2, '0');\r\n  const seconds = String(date.getSeconds()).padStart(2, '0');\r\n  return `${hours}:${minutes}:${seconds}`;\r\n};\r\n\r\nconst tooltipContent = computed((): string => {\r\n  if (!props.begin) return '';\r\n  \r\n  const beginDate = new Date(props.begin);\r\n  const beginFull = formatDate(beginDate);\r\n  \r\n  if (!props.end) return `Start: ${beginFull}`;\r\n  \r\n  const endDate = new Date(props.end);\r\n  const endFull = formatDate(endDate);\r\n  const durationMs = endDate.getTime() - beginDate.getTime();\r\n  \r\n  let durationText = '';\r\n  if (durationMs < 1000) {\r\n    durationText = `${durationMs} ms`;\r\n  } else {\r\n    const seconds = Math.floor(durationMs / 1000);\r\n    const minutes = Math.floor(seconds / 60);\r\n    const hours = Math.floor(minutes / 60);\r\n    \r\n    if (hours > 0) {\r\n      durationText = `${hours}h ${minutes % 60}m ${seconds % 60}s`;\r\n    } else if (minutes > 0) {\r\n      durationText = `${minutes}m ${seconds % 60}s`;\r\n    } else {\r\n      durationText = `${seconds}s`;\r\n    }\r\n  }\r\n  \r\n  return `Duration: ${durationText}<br>Start: ${beginFull}<br>End: ${endFull}`;\r\n});\r\n\r\nconst formattedTime = computed((): string => {\r\n  if (!props.begin) return '-';\r\n  \r\n  const beginDate = new Date(props.begin);\r\n  const beginStr = formatTimeOnly(beginDate);\r\n  \r\n  if (!props.end || !props.showDuration) return beginStr;\r\n  \r\n  const endDate = new Date(props.end);\r\n  const durationMs = endDate.getTime() - beginDate.getTime();\r\n  \r\n  if (durationMs < 1000) {\r\n    return `Started at ${beginStr}, elapsed ${durationMs}ms`;\r\n  } else {\r\n    const seconds = Math.round(durationMs / 1000);\r\n    return `Started at ${beginStr}, elapsed ${seconds}s`;\r\n  }\r\n});\r\n</script>\r\n\r\n<style scoped>\r\n.time-display {\r\n  font-size: 11px;\r\n  color: var(--el-text-color-secondary);\r\n}\r\n</style>\r\n", "import script from \"./TimeDisplay.vue?vue&type=script&setup=true&lang=ts\"\nexport * from \"./TimeDisplay.vue?vue&type=script&setup=true&lang=ts\"\n\nimport \"./TimeDisplay.vue?vue&type=style&index=0&id=9d5dcc2c&scoped=true&lang=css\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-9d5dcc2c\"]])\n\nexport default __exports__", "import { defineComponent as _defineComponent } from 'vue'\nimport { createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, unref as _unref, withCtx as _withCtx, createVNode as _createVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, resolveComponent as _resolveComponent, renderList as _renderList, Fragment as _Fragment, withModifiers as _withModifiers, createBlock as _createBlock, normalizeClass as _normalizeClass } from \"vue\"\n\nconst _hoisted_1 = { class: \"result-panel\" }\nconst _hoisted_2 = {\n  key: 0,\n  class: \"duplicate-warning\"\n}\nconst _hoisted_3 = {\n  key: 1,\n  class: \"loading-indicator\"\n}\nconst _hoisted_4 = {\n  key: 2,\n  class: \"empty-message\"\n}\nconst _hoisted_5 = {\n  key: 3,\n  class: \"results-list\"\n}\nconst _hoisted_6 = { class: \"select-all-row\" }\nconst _hoisted_7 = [\"onClick\"]\nconst _hoisted_8 = { class: \"item-checkbox\" }\nconst _hoisted_9 = { class: \"item-name\" }\nconst _hoisted_10 = { class: \"item-status\" }\nconst _hoisted_11 = { class: \"item-duration\" }\n\nimport { ref, computed, watch, onMounted } from 'vue';\r\nimport { Loading } from '@element-plus/icons-vue';\r\nimport { CaseResult, ExecutionStateString } from '@/api/interoperationApi';\r\nimport { ElAlert } from 'element-plus';\r\nimport CaseStateTag from '@/components/common/CaseStateTag.vue';\r\nimport TimeDisplay from '@/components/common/TimeDisplay.vue';\r\n\r\n\nexport default /*@__PURE__*/_defineComponent({\n  __name: 'InteroperationResultPanel',\n  props: {\n    results: {},\n    loading: { type: Boolean },\n    selectedSequences: {}\n  },\n  emits: [\"update:selectedSequences\"],\n  setup(__props: any, { emit: __emit }) {\n\r\nconst props = __props;\r\n\r\nconst emit = __emit;\r\n\r\n// 选中的项目数组\r\nconst selectedItems = ref<boolean[]>([]);\r\n\r\n// 初始化选中项，在 onMounted 中处理\r\nonMounted(() => {\r\n  initializeSelectedItems();\r\n});\r\n\r\n// 监听结果变化，更新选中状态\r\nwatch(() => props.results, () => {\r\n  initializeSelectedItems();\r\n}, { immediate: false });\r\n\r\n// 监听外部传入的已选中序列\r\nwatch(() => props.selectedSequences, (newSelected) => {\r\n  if (newSelected && props.results.length > 0) {\r\n    selectedItems.value = props.results.map(item => \r\n      newSelected.includes(item.sequenceName)\r\n    );\r\n  }\r\n}, { deep: true });\r\n\r\n// 初始化选中状态函数 - 默认全选\r\nfunction initializeSelectedItems() {\r\n  if (props.results.length > 0) {\r\n    // 默认全选\r\n    selectedItems.value = new Array(props.results.length).fill(true);\r\n    updateSelectedSequences();\r\n  }\r\n}\r\n\r\n// 计算全选状态\r\nconst isAllSelected = computed(() => {\r\n  return selectedItems.value.length > 0 && selectedItems.value.every(selected => selected);\r\n});\r\n\r\n// 计算部分选中状态\r\nconst isIndeterminate = computed(() => {\r\n  return selectedItems.value.some(selected => selected) && !isAllSelected.value;\r\n});\r\n\r\n// 处理全选/取消全选\r\nconst handleSelectAllChange = (val: boolean) => {\r\n  selectedItems.value = new Array(props.results.length).fill(val);\r\n  updateSelectedSequences();\r\n};\r\n\r\n// 新增: 点击项目时切换对应的复选框状态\r\nconst handleItemClick = (index: number) => {\r\n  selectedItems.value[index] = !selectedItems.value[index];\r\n  updateSelectedSequences();\r\n};\r\n\r\n// 处理单个项目选中/取消选中\r\nconst handleItemSelect = (val: boolean, index: number, item: CaseResult) => {\r\n  selectedItems.value[index] = val;\r\n  updateSelectedSequences();\r\n};\r\n\r\n// 更新选中的序列列表并触发事件 - 修正为使用索引跟踪\r\nconst updateSelectedSequences = () => {\r\n  // 根据索引获取选中的结果项，然后提取其序列名称\r\n  const selected = props.results\r\n    .filter((_, index) => selectedItems.value[index])\r\n    .map(item => item.sequenceName);\r\n  \r\n  // 使用Set去重，确保不会因为重复的sequenceName导致问题\r\n  const uniqueSelected = [...new Set(selected)];\r\n  \r\n  emit('update:selectedSequences', uniqueSelected);\r\n};\r\n\r\n// 获取状态文本\r\nconst getStatusText = (state: ExecutionStateString): string => {\r\n  switch (state) {\r\n    case 'Success':\r\n      return 'Passed';\r\n    case 'Failure':\r\n      return 'Failed';\r\n    case 'Running':\r\n      return 'Running';\r\n    case 'Pending':\r\n      return 'Pending';\r\n    default:\r\n      return 'Unknown';\r\n  }\r\n};\r\n\r\n// 检测重复的序列名称\r\nconst duplicateSequences = computed(() => {\r\n  if (!props.results || props.results.length === 0) return [];\r\n\r\n  const nameCounts = new Map<string, number>();\r\n  const duplicates = new Set<string>();\r\n\r\n  // 统计每个序列名称出现的次数\r\n  props.results.forEach(result => {\r\n    const count = nameCounts.get(result.sequenceName) || 0;\r\n    nameCounts.set(result.sequenceName, count + 1);\r\n    \r\n    // 如果出现次数超过1，则是重复的\r\n    if (count > 0) {\r\n      duplicates.add(result.sequenceName);\r\n    }\r\n  });\r\n\r\n  return Array.from(duplicates);\r\n});\r\n\r\n// 检查特定的序列名称是否重复\r\nconst isDuplicate = (name: string) => {\r\n  return duplicateSequences.value.includes(name);\r\n};\r\n\nreturn (_ctx: any,_cache: any) => {\n  const _component_el_icon = _resolveComponent(\"el-icon\")!\n  const _component_el_checkbox = _resolveComponent(\"el-checkbox\")!\n  const _component_el_tag = _resolveComponent(\"el-tag\")!\n\n  return (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [\n    _cache[7] || (_cache[7] = _createElementVNode(\"div\", { class: \"panel-header\" }, [\n      _createElementVNode(\"h3\", null, \"Interoperation Results\"),\n      _createElementVNode(\"div\", { class: \"panel-subtitle\" }, \"Showing only successful sequences\")\n    ], -1)),\n    (duplicateSequences.value.length > 0)\n      ? (_openBlock(), _createElementBlock(\"div\", _hoisted_2, [\n          _createVNode(_unref(ElAlert), {\n            title: \"Warning: Duplicate sequence names detected\",\n            type: \"warning\",\n            closable: false,\n            \"show-icon\": \"\"\n          }, {\n            default: _withCtx(() => [\n              _createTextVNode(\" Found duplicate names: \" + _toDisplayString(duplicateSequences.value.join(', ')) + \".\", 1),\n              _cache[2] || (_cache[2] = _createElementVNode(\"br\", null, null, -1)),\n              _cache[3] || (_cache[3] = _createTextVNode(\" This may cause selection issues. \"))\n            ]),\n            _: 1\n          })\n        ]))\n      : _createCommentVNode(\"\", true),\n    (_ctx.loading)\n      ? (_openBlock(), _createElementBlock(\"div\", _hoisted_3, [\n          _createVNode(_component_el_icon, { class: \"is-loading\" }, {\n            default: _withCtx(() => [\n              _createVNode(_unref(Loading))\n            ]),\n            _: 1\n          }),\n          _cache[4] || (_cache[4] = _createTextVNode(\" Loading... \"))\n        ]))\n      : (_ctx.results.length === 0)\n        ? (_openBlock(), _createElementBlock(\"div\", _hoisted_4, \" No successful results available \"))\n        : (_openBlock(), _createElementBlock(\"div\", _hoisted_5, [\n            _createElementVNode(\"div\", _hoisted_6, [\n              _createVNode(_component_el_checkbox, {\n                modelValue: isAllSelected.value,\n                \"onUpdate:modelValue\": _cache[0] || (_cache[0] = ($event: any) => ((isAllSelected).value = $event)),\n                indeterminate: isIndeterminate.value,\n                onChange: handleSelectAllChange,\n                size: \"small\"\n              }, {\n                default: _withCtx(() => _cache[5] || (_cache[5] = [\n                  _createTextVNode(\" Select All \")\n                ])),\n                _: 1\n              }, 8, [\"modelValue\", \"indeterminate\"])\n            ]),\n            (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.results, (item, index) => {\n              return (_openBlock(), _createElementBlock(\"div\", {\n                key: item.id || `item-${index}`,\n                class: _normalizeClass([\"list-item\", { 'striped': index % 2 === 1, 'duplicate': isDuplicate(item.sequenceName) }]),\n                onClick: _withModifiers(($event: any) => (handleItemClick(index)), [\"stop\"])\n              }, [\n                _createElementVNode(\"div\", _hoisted_8, [\n                  _createVNode(_component_el_checkbox, {\n                    modelValue: selectedItems.value[index],\n                    \"onUpdate:modelValue\": ($event: any) => ((selectedItems.value[index]) = $event),\n                    size: \"small\",\n                    onClick: _cache[1] || (_cache[1] = _withModifiers(() => {}, [\"stop\"])),\n                    onChange: val => handleItemSelect(val, index, item)\n                  }, null, 8, [\"modelValue\", \"onUpdate:modelValue\", \"onChange\"])\n                ]),\n                _createElementVNode(\"div\", _hoisted_9, [\n                  _createTextVNode(_toDisplayString(item.sequenceName) + \" \", 1),\n                  (isDuplicate(item.sequenceName))\n                    ? (_openBlock(), _createBlock(_component_el_tag, {\n                        key: 0,\n                        size: \"small\",\n                        type: \"warning\",\n                        effect: \"dark\",\n                        class: \"duplicate-tag\"\n                      }, {\n                        default: _withCtx(() => _cache[6] || (_cache[6] = [\n                          _createTextVNode(\" Duplicate \")\n                        ])),\n                        _: 1\n                      }))\n                    : _createCommentVNode(\"\", true)\n                ]),\n                _createElementVNode(\"div\", _hoisted_10, [\n                  _createVNode(CaseStateTag, {\n                    state: item.state\n                  }, null, 8, [\"state\"])\n                ]),\n                _createElementVNode(\"div\", _hoisted_11, [\n                  _createVNode(TimeDisplay, {\n                    begin: item.begin,\n                    end: item.end,\n                    showDuration: true\n                  }, null, 8, [\"begin\", \"end\"])\n                ])\n              ], 10, _hoisted_7))\n            }), 128))\n          ]))\n  ]))\n}\n}\n\n})", "<template>\r\n  <div class=\"result-panel\">\r\n    <div class=\"panel-header\">\r\n      <h3>Interoperation Results</h3>\r\n      <div class=\"panel-subtitle\">Showing only successful sequences</div>\r\n    </div>\r\n    \r\n    <!-- 新增: 显示重复序列名称警告 -->\r\n    <div v-if=\"duplicateSequences.length > 0\" class=\"duplicate-warning\">\r\n      <el-alert\r\n        title=\"Warning: Duplicate sequence names detected\"\r\n        type=\"warning\"\r\n        :closable=\"false\"\r\n        show-icon\r\n      >\r\n        <template #default>\r\n          Found duplicate names: {{ duplicateSequences.join(', ') }}.<br />\r\n          This may cause selection issues.\r\n        </template>\r\n      </el-alert>\r\n    </div>\r\n    \r\n    <div v-if=\"loading\" class=\"loading-indicator\">\r\n      <el-icon class=\"is-loading\"><Loading /></el-icon> Loading...\r\n    </div>\r\n    \r\n    <div v-else-if=\"results.length === 0\" class=\"empty-message\">\r\n      No successful results available\r\n    </div>\r\n    \r\n    <div v-else class=\"results-list\">\r\n      <!-- 全选复选框 -->\r\n      <div class=\"select-all-row\">\r\n        <el-checkbox \r\n          v-model=\"isAllSelected\"\r\n          :indeterminate=\"isIndeterminate\"\r\n          @change=\"handleSelectAllChange\"\r\n          size=\"small\"\r\n        >\r\n          Select All\r\n        </el-checkbox>\r\n      </div>\r\n      \r\n      <!-- 优化后的单行项布局 -->\r\n      <div v-for=\"(item, index) in results\" \r\n           :key=\"item.id || `item-${index}`\" \r\n           class=\"list-item\" \r\n           :class=\"{ 'striped': index % 2 === 1, 'duplicate': isDuplicate(item.sequenceName) }\"\r\n           @click.stop=\"handleItemClick(index)\">\r\n        <!-- 使用直接的勾选元素 -->\r\n        <div class=\"item-checkbox\">\r\n          <el-checkbox \r\n            v-model=\"selectedItems[index]\"\r\n            size=\"small\"\r\n            @click.stop\r\n            @change=\"val => handleItemSelect(val, index, item)\"\r\n          />\r\n        </div>\r\n        \r\n        <div class=\"item-name\">\r\n          {{ item.sequenceName }}\r\n          <el-tag v-if=\"isDuplicate(item.sequenceName)\" size=\"small\" type=\"warning\" effect=\"dark\" class=\"duplicate-tag\">\r\n            Duplicate\r\n          </el-tag>\r\n        </div>\r\n        \r\n        <div class=\"item-status\">\r\n          <CaseStateTag :state=\"item.state\" />\r\n        </div>\r\n        \r\n        <div class=\"item-duration\">\r\n          <TimeDisplay \r\n            :begin=\"item.begin\" \r\n            :end=\"item.end\" \r\n            :showDuration=\"true\"\r\n          />\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { defineProps, defineEmits, ref, computed, watch, onMounted } from 'vue';\r\nimport { Loading } from '@element-plus/icons-vue';\r\nimport { CaseResult, ExecutionStateString } from '@/api/interoperationApi';\r\nimport { ElAlert } from 'element-plus';\r\nimport CaseStateTag from '@/components/common/CaseStateTag.vue';\r\nimport TimeDisplay from '@/components/common/TimeDisplay.vue';\r\n\r\nconst props = defineProps<{\r\n  results: CaseResult[];\r\n  loading: boolean;\r\n  selectedSequences?: string[];\r\n}>();\r\n\r\nconst emit = defineEmits<{\r\n  (e: 'update:selectedSequences', value: string[]): void;\r\n}>();\r\n\r\n// 选中的项目数组\r\nconst selectedItems = ref<boolean[]>([]);\r\n\r\n// 初始化选中项，在 onMounted 中处理\r\nonMounted(() => {\r\n  initializeSelectedItems();\r\n});\r\n\r\n// 监听结果变化，更新选中状态\r\nwatch(() => props.results, () => {\r\n  initializeSelectedItems();\r\n}, { immediate: false });\r\n\r\n// 监听外部传入的已选中序列\r\nwatch(() => props.selectedSequences, (newSelected) => {\r\n  if (newSelected && props.results.length > 0) {\r\n    selectedItems.value = props.results.map(item => \r\n      newSelected.includes(item.sequenceName)\r\n    );\r\n  }\r\n}, { deep: true });\r\n\r\n// 初始化选中状态函数 - 默认全选\r\nfunction initializeSelectedItems() {\r\n  if (props.results.length > 0) {\r\n    // 默认全选\r\n    selectedItems.value = new Array(props.results.length).fill(true);\r\n    updateSelectedSequences();\r\n  }\r\n}\r\n\r\n// 计算全选状态\r\nconst isAllSelected = computed(() => {\r\n  return selectedItems.value.length > 0 && selectedItems.value.every(selected => selected);\r\n});\r\n\r\n// 计算部分选中状态\r\nconst isIndeterminate = computed(() => {\r\n  return selectedItems.value.some(selected => selected) && !isAllSelected.value;\r\n});\r\n\r\n// 处理全选/取消全选\r\nconst handleSelectAllChange = (val: boolean) => {\r\n  selectedItems.value = new Array(props.results.length).fill(val);\r\n  updateSelectedSequences();\r\n};\r\n\r\n// 新增: 点击项目时切换对应的复选框状态\r\nconst handleItemClick = (index: number) => {\r\n  selectedItems.value[index] = !selectedItems.value[index];\r\n  updateSelectedSequences();\r\n};\r\n\r\n// 处理单个项目选中/取消选中\r\nconst handleItemSelect = (val: boolean, index: number, item: CaseResult) => {\r\n  selectedItems.value[index] = val;\r\n  updateSelectedSequences();\r\n};\r\n\r\n// 更新选中的序列列表并触发事件 - 修正为使用索引跟踪\r\nconst updateSelectedSequences = () => {\r\n  // 根据索引获取选中的结果项，然后提取其序列名称\r\n  const selected = props.results\r\n    .filter((_, index) => selectedItems.value[index])\r\n    .map(item => item.sequenceName);\r\n  \r\n  // 使用Set去重，确保不会因为重复的sequenceName导致问题\r\n  const uniqueSelected = [...new Set(selected)];\r\n  \r\n  emit('update:selectedSequences', uniqueSelected);\r\n};\r\n\r\n// 获取状态文本\r\nconst getStatusText = (state: ExecutionStateString): string => {\r\n  switch (state) {\r\n    case 'Success':\r\n      return 'Passed';\r\n    case 'Failure':\r\n      return 'Failed';\r\n    case 'Running':\r\n      return 'Running';\r\n    case 'Pending':\r\n      return 'Pending';\r\n    default:\r\n      return 'Unknown';\r\n  }\r\n};\r\n\r\n// 检测重复的序列名称\r\nconst duplicateSequences = computed(() => {\r\n  if (!props.results || props.results.length === 0) return [];\r\n\r\n  const nameCounts = new Map<string, number>();\r\n  const duplicates = new Set<string>();\r\n\r\n  // 统计每个序列名称出现的次数\r\n  props.results.forEach(result => {\r\n    const count = nameCounts.get(result.sequenceName) || 0;\r\n    nameCounts.set(result.sequenceName, count + 1);\r\n    \r\n    // 如果出现次数超过1，则是重复的\r\n    if (count > 0) {\r\n      duplicates.add(result.sequenceName);\r\n    }\r\n  });\r\n\r\n  return Array.from(duplicates);\r\n});\r\n\r\n// 检查特定的序列名称是否重复\r\nconst isDuplicate = (name: string) => {\r\n  return duplicateSequences.value.includes(name);\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.result-panel {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  overflow: hidden;\r\n  border: 1px solid #dcdfe6;\r\n  border-radius: 4px;\r\n}\r\n\r\n.panel-header {\r\n  padding: 8px 12px;\r\n  border-bottom: 1px solid #ebeef5;\r\n  \r\n  h3 {\r\n    margin: 0;\r\n    font-size: 15px;\r\n    font-weight: 500;\r\n    color: #303133;\r\n  }\r\n  \r\n  .panel-subtitle {\r\n    font-size: 12px;\r\n    color: #909399;\r\n    margin-top: 2px;\r\n  }\r\n}\r\n\r\n.select-all-row {\r\n  padding: 8px 12px;\r\n  border-bottom: 1px solid #ebeef5;\r\n  background-color: var(--el-fill-color-light);\r\n}\r\n\r\n.results-list {\r\n  flex: 1;\r\n  overflow-y: auto;\r\n}\r\n\r\n.list-item {\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 8px 12px;\r\n  border-bottom: 1px solid #ebeef5;\r\n  cursor: pointer;\r\n  transition: background-color 0.2s;\r\n  \r\n  &:last-child {\r\n    border-bottom: none;\r\n  }\r\n  \r\n  &.striped {\r\n    background-color: #fafafa;\r\n  }\r\n  \r\n  &:hover {\r\n    background-color: #f5f7fa;\r\n  }\r\n  \r\n  /* 单行布局样式 */\r\n  .item-checkbox {\r\n    flex: 0 0 auto;\r\n    margin-right: 10px;\r\n  }\r\n  \r\n  .item-name {\r\n    flex: 1;\r\n    font-weight: 500;\r\n    color: #303133;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    white-space: nowrap;\r\n  }\r\n  \r\n  .item-status {\r\n    flex: 0 0 80px;\r\n    text-align: right;\r\n    margin-right: 10px;\r\n  }\r\n  \r\n  .item-duration {\r\n    flex: 0 0 50px;\r\n    text-align: right;\r\n    color: #909399;\r\n    font-size: 12px;\r\n    min-width: 120px;\r\n  }\r\n}\r\n\r\n.loading-indicator, .empty-message {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  flex: 1;\r\n  color: #909399;\r\n  font-size: 13px;\r\n  \r\n  .el-icon {\r\n    margin-right: 6px;\r\n  }\r\n}\r\n\r\n.duplicate-warning {\r\n  margin: 0 12px;\r\n  padding-top: 12px;\r\n}\r\n\r\n.duplicate-tag {\r\n  margin-left: 8px;\r\n  font-size: 10px;\r\n  line-height: 1;\r\n  padding: 2px 4px;\r\n  border-radius: 2px;\r\n}\r\n\r\n.list-item {\r\n  // ...existing code...\r\n  \r\n  &.duplicate {\r\n    background-color: rgba(255, 229, 100, 0.1);\r\n    \r\n    &:hover {\r\n      background-color: rgba(255, 229, 100, 0.2);\r\n    }\r\n  }\r\n}\r\n</style>\r\n", "import script from \"./InteroperationResultPanel.vue?vue&type=script&setup=true&lang=ts\"\nexport * from \"./InteroperationResultPanel.vue?vue&type=script&setup=true&lang=ts\"\n\nimport \"./InteroperationResultPanel.vue?vue&type=style&index=0&id=5b656733&scoped=true&lang=scss\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-5b656733\"]])\n\nexport default __exports__", "import { defineComponent as _defineComponent } from 'vue'\nimport { createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, unref as _unref, createVNode as _createVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createTextVNode as _createTextVNode, renderList as _renderList, Fragment as _Fragment, normalizeClass as _normalizeClass } from \"vue\"\n\nconst _hoisted_1 = { class: \"case-panel\" }\nconst _hoisted_2 = { class: \"panel-header\" }\nconst _hoisted_3 = {\n  key: 0,\n  class: \"case-info\"\n}\nconst _hoisted_4 = { class: \"case-count\" }\nconst _hoisted_5 = { class: \"estimated-time\" }\nconst _hoisted_6 = {\n  key: 0,\n  class: \"loading-indicator\"\n}\nconst _hoisted_7 = {\n  key: 1,\n  class: \"empty-message\"\n}\nconst _hoisted_8 = {\n  key: 2,\n  class: \"results-list\"\n}\nconst _hoisted_9 = [\"onClick\"]\nconst _hoisted_10 = { class: \"group-info\" }\nconst _hoisted_11 = { class: \"group-name\" }\nconst _hoisted_12 = { class: \"group-count\" }\nconst _hoisted_13 = { class: \"group-time\" }\nconst _hoisted_14 = {\n  key: 0,\n  class: \"group-content\"\n}\nconst _hoisted_15 = {\n  key: 0,\n  class: \"group-loading\"\n}\nconst _hoisted_16 = { class: \"item-header\" }\nconst _hoisted_17 = { class: \"sequence-group\" }\nconst _hoisted_18 = { class: \"sequence-name\" }\nconst _hoisted_19 = { class: \"group-badge\" }\nconst _hoisted_20 = [\"title\"]\n\nimport { ref, computed, reactive, watch } from 'vue';\r\nimport { Loading, CaretRight } from '@element-plus/icons-vue';\r\nimport { CaseResult } from '@/api/interoperationApi';\r\n\r\n\nexport default /*@__PURE__*/_defineComponent({\n  __name: 'GeneratedCasesPanel',\n  props: {\n    cases: {},\n    generating: { type: Boolean },\n    showEmptyMessage: { type: Boolean },\n    estimatedTime: {}\n  },\n  setup(__props: any) {\n\r\nconst props = __props;\r\n\r\n// 分组展开状态\r\nconst expandedGroups = ref<Record<string, boolean>>({});\r\n// 已加载的分组内容\r\nconst loadedGroups = reactive<Record<string, CaseResult[]>>({});\r\n// 正在加载的分组\r\nconst loadingGroups = reactive<Record<string, boolean>>({});\r\n\r\n// 计算分组摘要信息（不包含具体items）\r\nconst groupSummary = computed(() => {\r\n  // 按前缀统计每个组的数量\r\n  const groups: Record<string, number> = {};\r\n\r\n  for (const item of props.cases) {\r\n    const nameParts = (item.name || '').split('-');\r\n    const prefix = nameParts.length > 0 ? nameParts[0] : item.name;\r\n\r\n    if (!groups[prefix]) {\r\n      groups[prefix] = 0;\r\n    }\r\n    groups[prefix]++;\r\n  }\r\n\r\n  // 转换为数组并排序\r\n  return Object.entries(groups).map(([groupPrefix, count]) => ({\r\n    groupPrefix,\r\n    count\r\n  })).sort((a, b) => a.groupPrefix.localeCompare(b.groupPrefix));\r\n});\r\n\r\n// 获取分组的预估时间\r\nconst getGroupEstimatedTime = (groupPrefix: string, groupCount: number) => {\r\n  // 根据组内用例数量计算时间\r\n  const timePerCase = props.estimatedTime / props.cases.length;\r\n  return timePerCase * groupCount;\r\n};\r\n\r\n// 加载指定分组的数据\r\nconst loadGroupData = (groupPrefix: string) => {\r\n  // 标记为加载中\r\n  loadingGroups[groupPrefix] = true;\r\n\r\n  // 模拟异步加载（实际上是从cases中筛选）\r\n  setTimeout(() => {\r\n    const groupItems = props.cases.filter(item => {\r\n      const nameParts = (item.name || '').split('-');\r\n      const prefix = nameParts.length > 0 ? nameParts[0] : item.name;\r\n      return prefix === groupPrefix;\r\n    });\r\n\r\n    // 保存加载的数据\r\n    loadedGroups[groupPrefix] = groupItems;\r\n    loadingGroups[groupPrefix] = false;\r\n  }, 100); // 添加小延迟以便显示加载状态\r\n};\r\n\r\n// 控制分组展开/收起\r\nconst toggleGroup = (groupPrefix: string) => {\r\n  const isCurrentlyExpanded = expandedGroups.value[groupPrefix];\r\n\r\n  // 切换展开状态\r\n  expandedGroups.value[groupPrefix] = !isCurrentlyExpanded;\r\n\r\n  // 如果是展开且还未加载数据，则加载数据\r\n  if (!isCurrentlyExpanded && !loadedGroups[groupPrefix]) {\r\n    loadGroupData(groupPrefix);\r\n  }\r\n};\r\n\r\n// 监听cases属性变化，重置组件状态\r\nwatch(() => props.cases, (newCases) => {\r\n  console.log('Cases changed:', newCases.length);\r\n  // 清空已加载的分组数据\r\n  Object.keys(loadedGroups).forEach(key => {\r\n    delete loadedGroups[key];\r\n  });\r\n\r\n  // 清空加载状态\r\n  Object.keys(loadingGroups).forEach(key => {\r\n    delete loadingGroups[key];\r\n  });\r\n\r\n  // 重置展开状态\r\n  expandedGroups.value = {};\r\n}, { deep: true });\r\n\r\n// 格式化预估时间(毫秒)，最多显示两个单位\r\nconst formatEstimatedTimeMs = (ms: number) => {\r\n  ms = Math.floor(ms);\r\n\r\n  if (ms < 1000) {\r\n    return `${ms}ms`;\r\n  } else if (ms < 60000) {\r\n    const seconds = Math.floor(ms / 1000);\r\n    const remainingMs = Math.floor(ms % 1000);\r\n    return `${seconds}s ${remainingMs}ms`;\r\n  } else if (ms < 3600000) {\r\n    const minutes = Math.floor(ms / 60000);\r\n    const seconds = Math.floor((ms % 60000) / 1000);\r\n    return `${minutes}m ${seconds}s`;\r\n  } else {\r\n    const hours = Math.floor(ms / 3600000);\r\n    const minutes = Math.floor((ms % 3600000) / 60000);\r\n    return `${hours}h ${minutes}m`;\r\n  }\r\n};\r\n\nreturn (_ctx: any,_cache: any) => {\n  const _component_el_icon = _resolveComponent(\"el-icon\")!\n\n  return (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [\n    _createElementVNode(\"div\", _hoisted_2, [\n      _cache[0] || (_cache[0] = _createElementVNode(\"div\", { class: \"header-main\" }, [\n        _createElementVNode(\"h3\", null, \"Generated Test Cases\")\n      ], -1)),\n      (_ctx.cases.length > 0)\n        ? (_openBlock(), _createElementBlock(\"div\", _hoisted_3, [\n            _createElementVNode(\"span\", _hoisted_4, \"Generated: \" + _toDisplayString(_ctx.cases.length) + \" cases\", 1),\n            _createElementVNode(\"span\", _hoisted_5, \" Estimated Time: \" + _toDisplayString(formatEstimatedTimeMs(_ctx.estimatedTime)), 1)\n          ]))\n        : _createCommentVNode(\"\", true)\n    ]),\n    (_ctx.generating)\n      ? (_openBlock(), _createElementBlock(\"div\", _hoisted_6, [\n          _createVNode(_component_el_icon, { class: \"is-loading\" }, {\n            default: _withCtx(() => [\n              _createVNode(_unref(Loading))\n            ]),\n            _: 1\n          }),\n          _cache[1] || (_cache[1] = _createTextVNode(\" Generating... \"))\n        ]))\n      : (_ctx.cases.length === 0 && _ctx.showEmptyMessage)\n        ? (_openBlock(), _createElementBlock(\"div\", _hoisted_7, \" Click Generate to create test cases \"))\n        : (_ctx.cases.length > 0)\n          ? (_openBlock(), _createElementBlock(\"div\", _hoisted_8, [\n              (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(groupSummary.value, (group) => {\n                return (_openBlock(), _createElementBlock(\"div\", {\n                  key: group.groupPrefix,\n                  class: \"group-item\"\n                }, [\n                  _createElementVNode(\"div\", {\n                    class: \"group-header\",\n                    onClick: ($event: any) => (toggleGroup(group.groupPrefix))\n                  }, [\n                    _createElementVNode(\"div\", _hoisted_10, [\n                      _createVNode(_component_el_icon, {\n                        class: _normalizeClass([\"expand-icon\", { 'is-expanded': expandedGroups.value[group.groupPrefix] }])\n                      }, {\n                        default: _withCtx(() => [\n                          _createVNode(_unref(CaretRight))\n                        ]),\n                        _: 2\n                      }, 1032, [\"class\"]),\n                      _createElementVNode(\"span\", _hoisted_11, _toDisplayString(group.groupPrefix), 1),\n                      _createElementVNode(\"span\", _hoisted_12, \"(\" + _toDisplayString(group.count) + \" cases)\", 1),\n                      _createElementVNode(\"span\", _hoisted_13, \" Estimated Time: \" + _toDisplayString(formatEstimatedTimeMs(getGroupEstimatedTime(group.groupPrefix, group.count))), 1)\n                    ])\n                  ], 8, _hoisted_9),\n                  (expandedGroups.value[group.groupPrefix])\n                    ? (_openBlock(), _createElementBlock(\"div\", _hoisted_14, [\n                        (loadingGroups[group.groupPrefix])\n                          ? (_openBlock(), _createElementBlock(\"div\", _hoisted_15, [\n                              _createVNode(_component_el_icon, { class: \"is-loading\" }, {\n                                default: _withCtx(() => [\n                                  _createVNode(_unref(Loading))\n                                ]),\n                                _: 1\n                              }),\n                              _cache[2] || (_cache[2] = _createTextVNode(\" Loading... \"))\n                            ]))\n                          : (loadedGroups[group.groupPrefix])\n                            ? (_openBlock(true), _createElementBlock(_Fragment, { key: 1 }, _renderList(loadedGroups[group.groupPrefix], (item, index) => {\n                                return (_openBlock(), _createElementBlock(\"div\", {\n                                  key: item.id || index,\n                                  class: _normalizeClass([\"list-item\", { 'striped': index % 2 === 1 }])\n                                }, [\n                                  _createElementVNode(\"div\", _hoisted_16, [\n                                    _createElementVNode(\"div\", _hoisted_17, [\n                                      _createElementVNode(\"span\", _hoisted_18, _toDisplayString(item.id), 1),\n                                      _createElementVNode(\"span\", _hoisted_19, _toDisplayString(item.name), 1)\n                                    ])\n                                  ]),\n                                  _createElementVNode(\"div\", {\n                                    class: \"parameter-text\",\n                                    title: `${item.parameter}`\n                                  }, _toDisplayString(item.parameter), 9, _hoisted_20)\n                                ], 2))\n                              }), 128))\n                            : _createCommentVNode(\"\", true)\n                      ]))\n                    : _createCommentVNode(\"\", true)\n                ]))\n              }), 128))\n            ]))\n          : _createCommentVNode(\"\", true)\n  ]))\n}\n}\n\n})", "<template>\r\n  <div class=\"case-panel\">\r\n    <div class=\"panel-header\">\r\n      <div class=\"header-main\">\r\n        <h3>Generated Test Cases</h3>\r\n      </div>\r\n      <div v-if=\"cases.length > 0\" class=\"case-info\">\r\n        <span class=\"case-count\">Generated: {{ cases.length }} cases</span>\r\n        <span class=\"estimated-time\">\r\n          Estimated Time: {{ formatEstimatedTimeMs(estimatedTime) }}\r\n        </span>\r\n      </div>\r\n    </div>\r\n\r\n    <div v-if=\"generating\" class=\"loading-indicator\">\r\n      <el-icon class=\"is-loading\"><Loading /></el-icon> Generating...\r\n    </div>\r\n\r\n    <div v-else-if=\"cases.length === 0 && showEmptyMessage\" class=\"empty-message\">\r\n      Click Generate to create test cases\r\n    </div>\r\n\r\n    <div v-else-if=\"cases.length > 0\" class=\"results-list\">\r\n      <div v-for=\"group in groupSummary\"\r\n           :key=\"group.groupPrefix\"\r\n           class=\"group-item\">\r\n        <div class=\"group-header\" @click=\"toggleGroup(group.groupPrefix)\">\r\n          <div class=\"group-info\">\r\n            <el-icon class=\"expand-icon\" :class=\"{ 'is-expanded': expandedGroups[group.groupPrefix] }\">\r\n              <CaretRight />\r\n            </el-icon>\r\n            <span class=\"group-name\">{{ group.groupPrefix }}</span>\r\n            <span class=\"group-count\">({{ group.count }} cases)</span>\r\n            <span class=\"group-time\">\r\n              Estimated Time: {{ formatEstimatedTimeMs(getGroupEstimatedTime(group.groupPrefix, group.count)) }}\r\n            </span>\r\n          </div>\r\n        </div>\r\n\r\n        <div v-if=\"expandedGroups[group.groupPrefix]\" class=\"group-content\">\r\n          <div v-if=\"loadingGroups[group.groupPrefix]\" class=\"group-loading\">\r\n            <el-icon class=\"is-loading\"><Loading /></el-icon> Loading...\r\n          </div>\r\n\r\n          <template v-else-if=\"loadedGroups[group.groupPrefix]\">\r\n            <div v-for=\"(item, index) in loadedGroups[group.groupPrefix]\"\r\n                 :key=\"item.id || index\"\r\n                 class=\"list-item\"\r\n                 :class=\"{ 'striped': index % 2 === 1 }\">\r\n              <div class=\"item-header\">\r\n                <div class=\"sequence-group\">\r\n                  <span class=\"sequence-name\">{{ item.id }}</span>\r\n                  <span class=\"group-badge\">{{ item.name }}</span>\r\n                </div>\r\n              </div>\r\n              <div class=\"parameter-text\" :title=\"`${item.parameter}`\">{{ item.parameter }}</div>\r\n            </div>\r\n          </template>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { defineProps, ref, computed, reactive, watch } from 'vue';\r\nimport { Loading, CaretRight } from '@element-plus/icons-vue';\r\nimport { CaseResult } from '@/api/interoperationApi';\r\n\r\nconst props = defineProps<{\r\n  cases: CaseResult[];\r\n  generating: boolean;\r\n  showEmptyMessage: boolean;\r\n  estimatedTime: number; // 单位:毫秒\r\n}>();\r\n\r\n// 分组展开状态\r\nconst expandedGroups = ref<Record<string, boolean>>({});\r\n// 已加载的分组内容\r\nconst loadedGroups = reactive<Record<string, CaseResult[]>>({});\r\n// 正在加载的分组\r\nconst loadingGroups = reactive<Record<string, boolean>>({});\r\n\r\n// 计算分组摘要信息（不包含具体items）\r\nconst groupSummary = computed(() => {\r\n  // 按前缀统计每个组的数量\r\n  const groups: Record<string, number> = {};\r\n\r\n  for (const item of props.cases) {\r\n    const nameParts = (item.name || '').split('-');\r\n    const prefix = nameParts.length > 0 ? nameParts[0] : item.name;\r\n\r\n    if (!groups[prefix]) {\r\n      groups[prefix] = 0;\r\n    }\r\n    groups[prefix]++;\r\n  }\r\n\r\n  // 转换为数组并排序\r\n  return Object.entries(groups).map(([groupPrefix, count]) => ({\r\n    groupPrefix,\r\n    count\r\n  })).sort((a, b) => a.groupPrefix.localeCompare(b.groupPrefix));\r\n});\r\n\r\n// 获取分组的预估时间\r\nconst getGroupEstimatedTime = (groupPrefix: string, groupCount: number) => {\r\n  // 根据组内用例数量计算时间\r\n  const timePerCase = props.estimatedTime / props.cases.length;\r\n  return timePerCase * groupCount;\r\n};\r\n\r\n// 加载指定分组的数据\r\nconst loadGroupData = (groupPrefix: string) => {\r\n  // 标记为加载中\r\n  loadingGroups[groupPrefix] = true;\r\n\r\n  // 模拟异步加载（实际上是从cases中筛选）\r\n  setTimeout(() => {\r\n    const groupItems = props.cases.filter(item => {\r\n      const nameParts = (item.name || '').split('-');\r\n      const prefix = nameParts.length > 0 ? nameParts[0] : item.name;\r\n      return prefix === groupPrefix;\r\n    });\r\n\r\n    // 保存加载的数据\r\n    loadedGroups[groupPrefix] = groupItems;\r\n    loadingGroups[groupPrefix] = false;\r\n  }, 100); // 添加小延迟以便显示加载状态\r\n};\r\n\r\n// 控制分组展开/收起\r\nconst toggleGroup = (groupPrefix: string) => {\r\n  const isCurrentlyExpanded = expandedGroups.value[groupPrefix];\r\n\r\n  // 切换展开状态\r\n  expandedGroups.value[groupPrefix] = !isCurrentlyExpanded;\r\n\r\n  // 如果是展开且还未加载数据，则加载数据\r\n  if (!isCurrentlyExpanded && !loadedGroups[groupPrefix]) {\r\n    loadGroupData(groupPrefix);\r\n  }\r\n};\r\n\r\n// 监听cases属性变化，重置组件状态\r\nwatch(() => props.cases, (newCases) => {\r\n  console.log('Cases changed:', newCases.length);\r\n  // 清空已加载的分组数据\r\n  Object.keys(loadedGroups).forEach(key => {\r\n    delete loadedGroups[key];\r\n  });\r\n\r\n  // 清空加载状态\r\n  Object.keys(loadingGroups).forEach(key => {\r\n    delete loadingGroups[key];\r\n  });\r\n\r\n  // 重置展开状态\r\n  expandedGroups.value = {};\r\n}, { deep: true });\r\n\r\n// 格式化预估时间(毫秒)，最多显示两个单位\r\nconst formatEstimatedTimeMs = (ms: number) => {\r\n  ms = Math.floor(ms);\r\n\r\n  if (ms < 1000) {\r\n    return `${ms}ms`;\r\n  } else if (ms < 60000) {\r\n    const seconds = Math.floor(ms / 1000);\r\n    const remainingMs = Math.floor(ms % 1000);\r\n    return `${seconds}s ${remainingMs}ms`;\r\n  } else if (ms < 3600000) {\r\n    const minutes = Math.floor(ms / 60000);\r\n    const seconds = Math.floor((ms % 60000) / 1000);\r\n    return `${minutes}m ${seconds}s`;\r\n  } else {\r\n    const hours = Math.floor(ms / 3600000);\r\n    const minutes = Math.floor((ms % 3600000) / 60000);\r\n    return `${hours}h ${minutes}m`;\r\n  }\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.case-panel {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  overflow: hidden;\r\n  border: 1px solid #dcdfe6;\r\n  border-radius: 4px;\r\n}\r\n\r\n.panel-header {\r\n  padding: 8px 12px;\r\n  border-bottom: 1px solid #ebeef5;\r\n\r\n  h3 {\r\n    margin: 0;\r\n    font-size: 15px;\r\n    font-weight: 500;\r\n    color: #303133;\r\n  }\r\n\r\n  .case-info {\r\n    margin-top: 2px;\r\n    font-size: 12px;\r\n    color: #909399;\r\n    display: flex;\r\n    gap: 12px;\r\n  }\r\n}\r\n\r\n.results-list {\r\n  flex: 1;\r\n  overflow-y: auto;\r\n\r\n  .list-item {\r\n    padding: 8px 12px;\r\n    border-bottom: 1px solid #ebeef5;\r\n    font-size: 13px;\r\n\r\n    &:last-child {\r\n      border-bottom: none;\r\n    }\r\n\r\n    &.striped {\r\n      background-color: #fafafa;\r\n    }\r\n\r\n    &:hover {\r\n      background-color: #f5f7fa;\r\n    }\r\n  }\r\n}\r\n\r\n.item-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.sequence-group {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n\r\n  .sequence-name {\r\n    font-weight: 500;\r\n    color: #303133;\r\n  }\r\n\r\n  .group-badge {\r\n    font-size: 12px;\r\n    padding: 1px 5px;\r\n    background-color: #ecf5ff;\r\n    color: #409EFF;\r\n    border-radius: 3px;\r\n  }\r\n}\r\n\r\n.parameter-text {\r\n  margin-top: 4px;\r\n  font-size: 12px;\r\n  color: #606266;\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  max-width: 30vw;\r\n}\r\n\r\n.loading-indicator, .empty-message {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  flex: 1;\r\n  color: #909399;\r\n  font-size: 13px;\r\n\r\n  .el-icon {\r\n    margin-right: 6px;\r\n  }\r\n}\r\n\r\n.header-main {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n}\r\n\r\n.group-item {\r\n  border-bottom: 1px solid #ebeef5;\r\n\r\n  &:last-child {\r\n    border-bottom: none;\r\n  }\r\n}\r\n\r\n.group-header {\r\n  padding: 8px 12px;\r\n  background-color: #f5f7fa;\r\n  cursor: pointer;\r\n  user-select: none;\r\n\r\n  &:hover {\r\n    background-color: #ecf5ff;\r\n  }\r\n\r\n  .group-info {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n\r\n    .expand-icon {\r\n      transition: transform 0.2s;\r\n      font-size: 16px;\r\n\r\n      &.is-expanded {\r\n        transform: rotate(90deg);\r\n      }\r\n    }\r\n\r\n    .group-name {\r\n      font-weight: 500;\r\n      color: #303133;\r\n    }\r\n\r\n    .group-count {\r\n      color: #909399;\r\n      font-size: 12px;\r\n    }\r\n\r\n    .group-time {\r\n      color: #909399;\r\n      font-size: 12px;\r\n      margin-left: auto;\r\n    }\r\n  }\r\n}\r\n\r\n.group-content {\r\n  .list-item {\r\n    padding-left: 32px;\r\n  }\r\n}\r\n\r\n.group-loading {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  padding: 16px;\r\n  color: #909399;\r\n  font-size: 13px;\r\n\r\n  .el-icon {\r\n    margin-right: 6px;\r\n  }\r\n}\r\n</style>\r\n", "import script from \"./GeneratedCasesPanel.vue?vue&type=script&setup=true&lang=ts\"\nexport * from \"./GeneratedCasesPanel.vue?vue&type=script&setup=true&lang=ts\"\n\nimport \"./GeneratedCasesPanel.vue?vue&type=style&index=0&id=7dd937b5&scoped=true&lang=scss\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-7dd937b5\"]])\n\nexport default __exports__", "import { defineComponent as _defineComponent } from 'vue'\nimport { createElementVNode as _createElementVNode, unref as _unref, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\"\n\nconst _hoisted_1 = { class: \"test-cases-container\" }\nconst _hoisted_2 = { class: \"toolbar\" }\nconst _hoisted_3 = { class: \"coverage-options\" }\nconst _hoisted_4 = { class: \"action-buttons\" }\nconst _hoisted_5 = { class: \"content-area\" }\n\nimport { ref, computed, onMounted, watch } from 'vue';\r\nimport { ElMessage } from 'element-plus';\r\nimport { appApi, CoverageType, ExecutionState } from '@/api/appApi';\r\nimport { CaseResult } from '@/api/interoperationApi';\r\nimport InteroperationResultPanel from '@/components/TestCases/InteroperationResultPanel.vue';\r\nimport GeneratedCasesPanel from '@/components/TestCases/GeneratedCasesPanel.vue';\r\n\r\n// 状态变量\r\n\nexport default /*@__PURE__*/_defineComponent({\n  __name: 'TestCases',\n  setup(__props) {\n\r\nconst interoperationResults = ref<CaseResult[]>([]);\r\nconst generatedCases = ref<CaseResult[]>([]);\r\nconst loading = ref(true);\r\nconst generating = ref(false);\r\nconst saving = ref(false);\r\nconst selectedCoverage = ref(CoverageType.Normal);\r\nconst showGenerationEmpty = ref(false);\r\nconst selectedSequences = ref<string[]>([]);\r\nconst hasGeneratedCases = ref(false);\r\n\r\n// 只显示成功的测试结果 - 移除副作用\r\nconst successfulResults = computed(() => {\r\n  return interoperationResults.value.filter(result => result.state === ExecutionState.Success);\r\n});\r\n\r\n// 是否可以保存测试用例\r\nconst canSave = computed(() => {\r\n  return hasGeneratedCases.value && generatedCases.value.length > 0 && !saving.value;\r\n});\r\n\r\n// 监听成功的测试结果变化，初始化选中状态\r\nwatch(successfulResults, (results) => {\r\n  if (results.length > 0 && selectedSequences.value.length === 0) {\r\n    selectedSequences.value = results.map(item => item.sequenceName);\r\n  }\r\n}, { immediate: true });\r\n\r\n// 获取最新互操作测试结果\r\nconst fetchInteroperationResults = async () => {\r\n  loading.value = true;\r\n  try {\r\n    const response = await appApi.getLatestInteroperationCaseResults();\r\n    interoperationResults.value = response.data;\r\n  } catch (error) {\r\n    console.error('获取测试结果失败:', error);\r\n    ElMessage.error('Failed to fetch interoperation results');\r\n  } finally {\r\n    loading.value = false;\r\n  }\r\n};\r\n\r\n// 生成测试用例\r\nconst generateTestCases = async () => {\r\n  // 验证至少选中一个序列\r\n  if (selectedSequences.value.length === 0) {\r\n    ElMessage.warning('Please select at least one sequence');\r\n    return;\r\n  }\r\n\r\n  generating.value = true;\r\n  showGenerationEmpty.value = true;\r\n  hasGeneratedCases.value = false;\r\n\r\n  // 先清空现有的用例列表，确保视图更新\r\n  generatedCases.value = [];\r\n\r\n  try {\r\n    const response = await appApi.generateCases(selectedCoverage.value, selectedSequences.value);\r\n    // 使用新数组替换而不是修改现有数组，确保响应式更新\r\n    generatedCases.value = [...response.data];\r\n    hasGeneratedCases.value = true;\r\n    ElMessage.success(`Successfully generated ${response.data.length} test cases`);\r\n    console.log('Generated cases:', generatedCases.value.length);\r\n  } catch (error) {\r\n    console.error('生成测试用例失败:', error);\r\n    ElMessage.error('Failed to generate test cases');\r\n  } finally {\r\n    generating.value = false;\r\n  }\r\n};\r\n\r\n// 保存测试用例\r\nconst saveTestCases = async () => {\r\n  if (!hasGeneratedCases.value || generatedCases.value.length === 0) {\r\n    ElMessage.warning('Please generate test cases first');\r\n    return;\r\n  }\r\n\r\n  saving.value = true;\r\n  try {\r\n    const response = await appApi.saveCases(selectedCoverage.value, selectedSequences.value);\r\n    ElMessage.success(`Successfully saved ${response.data.length} test cases`);\r\n\r\n    // 更新UI中的测试用例数据，使其包含保存后可能更新的信息（如测试结果ID）\r\n    // 使用新数组替换而不是修改现有数组，确保响应式更新\r\n    generatedCases.value = [...response.data];\r\n    console.log('Saved cases:', generatedCases.value.length);\r\n  } catch (error) {\r\n    console.error('保存测试用例失败:', error);\r\n    ElMessage.error('Failed to save test cases');\r\n  } finally {\r\n    saving.value = false;\r\n  }\r\n};\r\n\r\n// 计算预估执行时间(毫秒)\r\nconst estimatedExecutionTime = computed(() => {\r\n  if (!generatedCases.value.length || !interoperationResults.value.length) {\r\n    return 0;\r\n  }\r\n\r\n  // 创建一个Map来存储每个sequence的执行时间\r\n  const sequenceTimeMap = new Map<string, number>();\r\n\r\n  // 从互操作结果中计算每个sequence的执行时间\r\n  interoperationResults.value.forEach(result => {\r\n    if (result.begin && result.end && result.sequenceName) {\r\n      const executionTime = new Date(result.end).getTime() - new Date(result.begin).getTime();\r\n      sequenceTimeMap.set(result.sequenceName, executionTime);\r\n    }\r\n  });\r\n\r\n  // 累加生成用例中每个sequence对应的执行时间\r\n  const totalTime = generatedCases.value.reduce((sum, testCase) => {\r\n    const sequenceTime = sequenceTimeMap.get(testCase.sequenceName) || 0;\r\n    return sum + sequenceTime;\r\n  }, 0);\r\n\r\n  return totalTime;\r\n});\r\n\r\n// 组件挂载时获取最新互操作测试结果\r\nonMounted(() => {\r\n  fetchInteroperationResults();\r\n});\r\n\nreturn (_ctx: any,_cache: any) => {\n  const _component_el_radio = _resolveComponent(\"el-radio\")!\n  const _component_el_radio_group = _resolveComponent(\"el-radio-group\")!\n  const _component_el_button = _resolveComponent(\"el-button\")!\n\n  return (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [\n    _createElementVNode(\"div\", _hoisted_2, [\n      _createElementVNode(\"div\", _hoisted_3, [\n        _cache[4] || (_cache[4] = _createElementVNode(\"span\", null, \"Coverage:\", -1)),\n        _createVNode(_component_el_radio_group, {\n          modelValue: selectedCoverage.value,\n          \"onUpdate:modelValue\": _cache[0] || (_cache[0] = ($event: any) => ((selectedCoverage).value = $event)),\n          size: \"small\"\n        }, {\n          default: _withCtx(() => [\n            _createVNode(_component_el_radio, {\n              label: _unref(CoverageType).Normal\n            }, {\n              default: _withCtx(() => _cache[2] || (_cache[2] = [\n                _createTextVNode(\"Normal\")\n              ])),\n              _: 1\n            }, 8, [\"label\"]),\n            _createVNode(_component_el_radio, {\n              label: _unref(CoverageType).High\n            }, {\n              default: _withCtx(() => _cache[3] || (_cache[3] = [\n                _createTextVNode(\"High\")\n              ])),\n              _: 1\n            }, 8, [\"label\"])\n          ]),\n          _: 1\n        }, 8, [\"modelValue\"])\n      ]),\n      _createElementVNode(\"div\", _hoisted_4, [\n        _createVNode(_component_el_button, {\n          type: \"primary\",\n          onClick: generateTestCases,\n          loading: generating.value,\n          disabled: selectedSequences.value.length === 0,\n          size: \"small\"\n        }, {\n          default: _withCtx(() => _cache[5] || (_cache[5] = [\n            _createTextVNode(\" Generate \")\n          ])),\n          _: 1\n        }, 8, [\"loading\", \"disabled\"]),\n        _createVNode(_component_el_button, {\n          type: \"success\",\n          onClick: saveTestCases,\n          loading: saving.value,\n          disabled: !canSave.value,\n          size: \"small\"\n        }, {\n          default: _withCtx(() => _cache[6] || (_cache[6] = [\n            _createTextVNode(\" Save \")\n          ])),\n          _: 1\n        }, 8, [\"loading\", \"disabled\"])\n      ])\n    ]),\n    _createElementVNode(\"div\", _hoisted_5, [\n      _createVNode(InteroperationResultPanel, {\n        results: successfulResults.value,\n        loading: loading.value,\n        selectedSequences: selectedSequences.value,\n        \"onUpdate:selectedSequences\": _cache[1] || (_cache[1] = ($event: any) => ((selectedSequences).value = $event))\n      }, null, 8, [\"results\", \"loading\", \"selectedSequences\"]),\n      _createVNode(GeneratedCasesPanel, {\n        cases: generatedCases.value,\n        generating: generating.value,\n        \"show-empty-message\": showGenerationEmpty.value,\n        \"estimated-time\": estimatedExecutionTime.value\n      }, null, 8, [\"cases\", \"generating\", \"show-empty-message\", \"estimated-time\"])\n    ])\n  ]))\n}\n}\n\n})", "<template>\r\n  <div class=\"test-cases-container\">\r\n    <!-- 顶部工具栏 -->\r\n    <div class=\"toolbar\">\r\n      <div class=\"coverage-options\">\r\n        <span>Coverage:</span>\r\n        <el-radio-group v-model=\"selectedCoverage\" size=\"small\">\r\n          <el-radio :label=\"CoverageType.Normal\">Normal</el-radio>\r\n          <el-radio :label=\"CoverageType.High\">High</el-radio>\r\n        </el-radio-group>\r\n      </div>\r\n      <div class=\"action-buttons\">\r\n        <el-button\r\n          type=\"primary\"\r\n          @click=\"generateTestCases\"\r\n          :loading=\"generating\"\r\n          :disabled=\"selectedSequences.length === 0\"\r\n          size=\"small\">\r\n          Generate\r\n        </el-button>\r\n        <el-button\r\n          type=\"success\"\r\n          @click=\"saveTestCases\"\r\n          :loading=\"saving\"\r\n          :disabled=\"!canSave\"\r\n          size=\"small\">\r\n          Save\r\n        </el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 下方内容区域 -->\r\n    <div class=\"content-area\">\r\n      <!-- 左侧：互操作测试结果 -->\r\n      <InteroperationResultPanel\r\n        :results=\"successfulResults\"\r\n        :loading=\"loading\"\r\n        v-model:selectedSequences=\"selectedSequences\"\r\n      />\r\n\r\n      <!-- 右侧：生成的测试用例 -->\r\n      <GeneratedCasesPanel\r\n        :cases=\"generatedCases\"\r\n        :generating=\"generating\"\r\n        :show-empty-message=\"showGenerationEmpty\"\r\n        :estimated-time=\"estimatedExecutionTime\"\r\n      />\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { ref, computed, onMounted, watch } from 'vue';\r\nimport { ElMessage } from 'element-plus';\r\nimport { appApi, CoverageType, ExecutionState } from '@/api/appApi';\r\nimport { CaseResult } from '@/api/interoperationApi';\r\nimport InteroperationResultPanel from '@/components/TestCases/InteroperationResultPanel.vue';\r\nimport GeneratedCasesPanel from '@/components/TestCases/GeneratedCasesPanel.vue';\r\n\r\n// 状态变量\r\nconst interoperationResults = ref<CaseResult[]>([]);\r\nconst generatedCases = ref<CaseResult[]>([]);\r\nconst loading = ref(true);\r\nconst generating = ref(false);\r\nconst saving = ref(false);\r\nconst selectedCoverage = ref(CoverageType.Normal);\r\nconst showGenerationEmpty = ref(false);\r\nconst selectedSequences = ref<string[]>([]);\r\nconst hasGeneratedCases = ref(false);\r\n\r\n// 只显示成功的测试结果 - 移除副作用\r\nconst successfulResults = computed(() => {\r\n  return interoperationResults.value.filter(result => result.state === ExecutionState.Success);\r\n});\r\n\r\n// 是否可以保存测试用例\r\nconst canSave = computed(() => {\r\n  return hasGeneratedCases.value && generatedCases.value.length > 0 && !saving.value;\r\n});\r\n\r\n// 监听成功的测试结果变化，初始化选中状态\r\nwatch(successfulResults, (results) => {\r\n  if (results.length > 0 && selectedSequences.value.length === 0) {\r\n    selectedSequences.value = results.map(item => item.sequenceName);\r\n  }\r\n}, { immediate: true });\r\n\r\n// 获取最新互操作测试结果\r\nconst fetchInteroperationResults = async () => {\r\n  loading.value = true;\r\n  try {\r\n    const response = await appApi.getLatestInteroperationCaseResults();\r\n    interoperationResults.value = response.data;\r\n  } catch (error) {\r\n    console.error('获取测试结果失败:', error);\r\n    ElMessage.error('Failed to fetch interoperation results');\r\n  } finally {\r\n    loading.value = false;\r\n  }\r\n};\r\n\r\n// 生成测试用例\r\nconst generateTestCases = async () => {\r\n  // 验证至少选中一个序列\r\n  if (selectedSequences.value.length === 0) {\r\n    ElMessage.warning('Please select at least one sequence');\r\n    return;\r\n  }\r\n\r\n  generating.value = true;\r\n  showGenerationEmpty.value = true;\r\n  hasGeneratedCases.value = false;\r\n\r\n  // 先清空现有的用例列表，确保视图更新\r\n  generatedCases.value = [];\r\n\r\n  try {\r\n    const response = await appApi.generateCases(selectedCoverage.value, selectedSequences.value);\r\n    // 使用新数组替换而不是修改现有数组，确保响应式更新\r\n    generatedCases.value = [...response.data];\r\n    hasGeneratedCases.value = true;\r\n    ElMessage.success(`Successfully generated ${response.data.length} test cases`);\r\n    console.log('Generated cases:', generatedCases.value.length);\r\n  } catch (error) {\r\n    console.error('生成测试用例失败:', error);\r\n    ElMessage.error('Failed to generate test cases');\r\n  } finally {\r\n    generating.value = false;\r\n  }\r\n};\r\n\r\n// 保存测试用例\r\nconst saveTestCases = async () => {\r\n  if (!hasGeneratedCases.value || generatedCases.value.length === 0) {\r\n    ElMessage.warning('Please generate test cases first');\r\n    return;\r\n  }\r\n\r\n  saving.value = true;\r\n  try {\r\n    const response = await appApi.saveCases(selectedCoverage.value, selectedSequences.value);\r\n    ElMessage.success(`Successfully saved ${response.data.length} test cases`);\r\n\r\n    // 更新UI中的测试用例数据，使其包含保存后可能更新的信息（如测试结果ID）\r\n    // 使用新数组替换而不是修改现有数组，确保响应式更新\r\n    generatedCases.value = [...response.data];\r\n    console.log('Saved cases:', generatedCases.value.length);\r\n  } catch (error) {\r\n    console.error('保存测试用例失败:', error);\r\n    ElMessage.error('Failed to save test cases');\r\n  } finally {\r\n    saving.value = false;\r\n  }\r\n};\r\n\r\n// 计算预估执行时间(毫秒)\r\nconst estimatedExecutionTime = computed(() => {\r\n  if (!generatedCases.value.length || !interoperationResults.value.length) {\r\n    return 0;\r\n  }\r\n\r\n  // 创建一个Map来存储每个sequence的执行时间\r\n  const sequenceTimeMap = new Map<string, number>();\r\n\r\n  // 从互操作结果中计算每个sequence的执行时间\r\n  interoperationResults.value.forEach(result => {\r\n    if (result.begin && result.end && result.sequenceName) {\r\n      const executionTime = new Date(result.end).getTime() - new Date(result.begin).getTime();\r\n      sequenceTimeMap.set(result.sequenceName, executionTime);\r\n    }\r\n  });\r\n\r\n  // 累加生成用例中每个sequence对应的执行时间\r\n  const totalTime = generatedCases.value.reduce((sum, testCase) => {\r\n    const sequenceTime = sequenceTimeMap.get(testCase.sequenceName) || 0;\r\n    return sum + sequenceTime;\r\n  }, 0);\r\n\r\n  return totalTime;\r\n});\r\n\r\n// 组件挂载时获取最新互操作测试结果\r\nonMounted(() => {\r\n  fetchInteroperationResults();\r\n});\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.test-cases-container {\r\n  flex: 1;\r\n  display: flex;\r\n  flex-direction: column;\r\n  padding: 20px;\r\n}\r\n\r\n/* 顶部工具栏样式 */\r\n.toolbar {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 8px 12px;\r\n  border-bottom: 1px solid #dcdfe6;\r\n  margin-bottom: 12px;\r\n\r\n  .coverage-options {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 10px;\r\n\r\n    span {\r\n      font-size: 13px;\r\n      color: #606266;\r\n    }\r\n  }\r\n\r\n  .action-buttons {\r\n    display: flex;\r\n    gap: 8px;\r\n  }\r\n}\r\n\r\n/* 下方内容区域样式 */\r\n.content-area {\r\n  display: flex;\r\n  flex: 1;\r\n  min-height: 0; /* 确保内容能正确滚动 */\r\n  gap: 12px;\r\n}\r\n\r\n/* 响应式布局 */\r\n@media (max-width: 768px) {\r\n  .content-area {\r\n    flex-direction: column;\r\n  }\r\n}\r\n</style>\r\n", "import script from \"./TestCases.vue?vue&type=script&setup=true&lang=ts\"\nexport * from \"./TestCases.vue?vue&type=script&setup=true&lang=ts\"\n\nimport \"./TestCases.vue?vue&type=style&index=0&id=6afd28e8&scoped=true&lang=scss\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-6afd28e8\"]])\n\nexport default __exports__", "import { defineComponent as _defineComponent } from 'vue'\nimport { toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, openBlock as _openBlock, createBlock as _createBlock } from \"vue\"\n\nimport { computed } from 'vue';\r\nimport { ExecutionState } from '@/api/appApi';\r\n\r\n\nexport default /*@__PURE__*/_defineComponent({\n  __name: 'CaseStateTag',\n  props: {\n    state: {}\n  },\n  setup(__props: any) {\n\r\nconst props = __props;\r\n\r\nconst tagType = computed<'success' | 'warning' | 'danger' | 'info'>(() => {\r\n  switch (props.state) {\r\n    case ExecutionState.Success:\r\n      return 'success';\r\n    case ExecutionState.Running:\r\n      return 'warning';\r\n    case ExecutionState.Failure:\r\n      return 'danger';\r\n    case ExecutionState.Pending:\r\n    default:\r\n      return 'info';\r\n  }\r\n});\r\n\r\nconst getCaseStateName = (state: string): 'Not Run' | 'Running' | 'Passed' | 'Failed' | 'Unknown' => {\r\n  switch (state) {\r\n    case ExecutionState.Running:\r\n      return 'Running';\r\n    case ExecutionState.Pending:\r\n      return 'Not Run';\r\n    case ExecutionState.Success:\r\n      return 'Passed';\r\n    case ExecutionState.Failure:\r\n      return 'Failed';\r\n    default:\r\n      return 'Unknown';\r\n  }\r\n};\r\n\r\nconst stateName = computed(() => {\r\n  return getCaseStateName(props.state);\r\n});\r\n\nreturn (_ctx: any,_cache: any) => {\n  const _component_el_tag = _resolveComponent(\"el-tag\")!\n\n  return (_openBlock(), _createBlock(_component_el_tag, {\n    type: tagType.value,\n    size: \"small\",\n    style: {\"min-width\":\"60px\"}\n  }, {\n    default: _withCtx(() => [\n      _createTextVNode(_toDisplayString(stateName.value), 1)\n    ]),\n    _: 1\n  }, 8, [\"type\"]))\n}\n}\n\n})", "<template>\r\n  <el-tag :type=\"tagType\" size=\"small\" style=\"min-width: 60px;\">\r\n    {{ stateName }}\r\n  </el-tag>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { computed, defineProps } from 'vue';\r\nimport { ExecutionState } from '@/api/appApi';\r\n\r\nconst props = defineProps<{\r\n  state: string;\r\n}>();\r\n\r\nconst tagType = computed<'success' | 'warning' | 'danger' | 'info'>(() => {\r\n  switch (props.state) {\r\n    case ExecutionState.Success:\r\n      return 'success';\r\n    case ExecutionState.Running:\r\n      return 'warning';\r\n    case ExecutionState.Failure:\r\n      return 'danger';\r\n    case ExecutionState.Pending:\r\n    default:\r\n      return 'info';\r\n  }\r\n});\r\n\r\nconst getCaseStateName = (state: string): 'Not Run' | 'Running' | 'Passed' | 'Failed' | 'Unknown' => {\r\n  switch (state) {\r\n    case ExecutionState.Running:\r\n      return 'Running';\r\n    case ExecutionState.Pending:\r\n      return 'Not Run';\r\n    case ExecutionState.Success:\r\n      return 'Passed';\r\n    case ExecutionState.Failure:\r\n      return 'Failed';\r\n    default:\r\n      return 'Unknown';\r\n  }\r\n};\r\n\r\nconst stateName = computed(() => {\r\n  return getCaseStateName(props.state);\r\n});\r\n</script>\r\n", "import script from \"./CaseStateTag.vue?vue&type=script&setup=true&lang=ts\"\nexport * from \"./CaseStateTag.vue?vue&type=script&setup=true&lang=ts\"\n\nconst __exports__ = script;\n\nexport default __exports__"], "names": ["_hoisted_1", "class", "_defineComponent", "__name", "props", "begin", "end", "showDuration", "type", "Boolean", "setup", "__props", "formatDate", "date", "year", "getFullYear", "month", "String", "getMonth", "padStart", "day", "getDate", "hours", "getHours", "minutes", "getMinutes", "seconds", "getSeconds", "milliseconds", "getMilliseconds", "formatTimeOnly", "tooltipContent", "computed", "beginDate", "Date", "beginFull", "endDate", "endFull", "durationMs", "getTime", "durationText", "Math", "floor", "formattedTime", "beginStr", "round", "_ctx", "_cache", "_openBlock", "_createBlock", "_unref", "ElTooltip", "key", "content", "value", "placement", "effect", "default", "_withCtx", "_createElementVNode", "_toDisplayString", "_", "_createCommentVNode", "__exports__", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_hoisted_5", "_hoisted_6", "_hoisted_7", "_hoisted_8", "_hoisted_9", "_hoisted_10", "_hoisted_11", "results", "loading", "selectedSequences", "emits", "emit", "__emit", "selectedItems", "ref", "initializeSelectedItems", "length", "Array", "fill", "updateSelectedSequences", "onMounted", "watch", "immediate", "newSelected", "map", "item", "includes", "sequenceName", "deep", "isAllSelected", "every", "selected", "isIndeterminate", "some", "handleSelectAllChange", "val", "handleItemClick", "index", "handleItemSelect", "filter", "uniqueSelected", "Set", "duplicateSequences", "nameCounts", "Map", "duplicates", "for<PERSON>ach", "result", "count", "get", "set", "add", "from", "isDuplicate", "name", "_component_el_icon", "_resolveComponent", "_component_el_checkbox", "_component_el_tag", "_createElementBlock", "_createVNode", "<PERSON><PERSON><PERSON><PERSON>", "title", "closable", "_createTextVNode", "join", "Loading", "modelValue", "$event", "indeterminate", "onChange", "size", "_Fragment", "_renderList", "id", "_normalizeClass", "onClick", "_withModifiers", "CaseStateTag", "state", "TimeDisplay", "_hoisted_12", "_hoisted_13", "_hoisted_14", "_hoisted_15", "_hoisted_16", "_hoisted_17", "_hoisted_18", "_hoisted_19", "_hoisted_20", "cases", "generating", "showEmptyMessage", "estimatedTime", "expandedGroups", "loadedGroups", "reactive", "loadingGroups", "groupSummary", "groups", "nameParts", "split", "prefix", "Object", "entries", "groupPrefix", "sort", "a", "b", "localeCompare", "getGroupEstimatedTime", "groupCount", "timePerCase", "loadGroupData", "setTimeout", "groupItems", "toggleGroup", "isCurrentlyExpanded", "newCases", "console", "log", "keys", "formatEstimatedTimeMs", "ms", "remainingMs", "group", "CaretRight", "parameter", "interoperationResults", "generatedCases", "saving", "selectedCoverage", "CoverageType", "Normal", "showGenerationEmpty", "hasGeneratedCases", "successfulResults", "ExecutionState", "Success", "canSave", "fetchInteroperationResults", "async", "response", "appApi", "getLatestInteroperationCaseResults", "data", "error", "ElMessage", "generateTestCases", "generateCases", "success", "warning", "saveTestCases", "saveCases", "estimatedExecutionTime", "sequenceTimeMap", "executionTime", "totalTime", "reduce", "sum", "testCase", "sequenceTime", "_component_el_radio", "_component_el_radio_group", "_component_el_button", "label", "High", "disabled", "InteroperationResultPanel", "GeneratedCasesPanel", "tagType", "Running", "Failure", "Pending", "getCaseStateName", "stateName", "style"], "sourceRoot": ""}