<template>
  <div class="cases-list-container case-list">
    <!-- 固定表头 -->
    <div class="cases-header" ref="headerContainer"></div>
    <!-- 虚拟滚动内容容器 -->
    <div ref="casesContainer" class="cases-content"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick, defineProps, defineEmits } from 'vue';
import { VirtualScroll } from 'js-booster';
import { CaseResult, ExecutionState } from '@/api/interoperationApi';

const props = defineProps<{
  cases: CaseResult[];
}>();

const emit = defineEmits<{
  (e: 'view-detail', caseResult: CaseResult): void;
}>();

// 虚拟滚动相关
const casesContainer = ref<HTMLElement | null>(null);
const headerContainer = ref<HTMLElement | null>(null);
let virtualScroll: any = null;
const ITEM_HEIGHT = 36; // 行高
const BUFFER_SIZE = 20; // 缓冲区大小
const HEADER_HEIGHT = 40; // 表头高度

// 渲染固定表头
const renderFixedHeader = () => {
  if (!headerContainer.value) return;

  // 清空表头容器
  headerContainer.value.innerHTML = '';

  // 创建表头
  const header = document.createElement('div');
  header.className = 'header-row';
  header.style.height = `${HEADER_HEIGHT}px`; // 动态高度

  // ID 列
  const idHeader = document.createElement('div');
  idHeader.textContent = 'ID';
  idHeader.className = 'header-cell-id';
  header.appendChild(idHeader);

  // 名称列
  const nameHeader = document.createElement('div');
  nameHeader.textContent = 'Name';
  nameHeader.className = 'header-cell-name';
  header.appendChild(nameHeader);

  // 参数列
  const paramHeader = document.createElement('div');
  paramHeader.textContent = 'Parameter';
  paramHeader.className = 'header-cell-param';
  header.appendChild(paramHeader);

  // 详情列
  const detailHeader = document.createElement('div');
  detailHeader.textContent = 'Detail';
  detailHeader.className = 'header-cell-detail';
  header.appendChild(detailHeader);

  // 状态列
  const statusHeader = document.createElement('div');
  statusHeader.textContent = 'Status';
  statusHeader.className = 'header-cell-status';
  header.appendChild(statusHeader);

  // 添加到表头容器
  headerContainer.value.appendChild(header);
};

// 初始化虚拟滚动
const initVirtualScroll = () => {
  if (!casesContainer.value || !props.cases.length) return;

  // 渲染固定表头
  renderFixedHeader();

  // 如果已经存在虚拟滚动实例，先销毁
  if (virtualScroll) {
    virtualScroll.destroy();
  }

  virtualScroll = new VirtualScroll({
    container: casesContainer.value,
    items: props.cases,
    itemHeight: ITEM_HEIGHT,
    bufferSize: BUFFER_SIZE,
    renderItem: (item: CaseResult, index: number) => {
      // 创建主容器
      const div = document.createElement('div');
      div.className = 'case-row';
      div.onclick = () => emit('view-detail', item);

      // 设置动态样式
      div.style.height = `${ITEM_HEIGHT}px`;
      div.style.lineHeight = `${ITEM_HEIGHT}px`;
      div.style.borderBottom = index === props.cases.length - 1 ? 'none' : '1px solid #ebeef5';
      div.style.backgroundColor = item.id % 2 === 0 ? '#fff' : '#fafafa';

      // 添加悬停效果
      div.onmouseover = () => {
        div.style.backgroundColor = '#f5f7fa';
      };
      div.onmouseout = () => {
        div.style.backgroundColor = item.id % 2 === 0 ? '#fff' : '#fafafa';
        // 确保鼠标移出时保持最后一个项目没有底部边框
        div.style.borderBottom = index === props.cases.length - 1 ? 'none' : '1px solid #ebeef5';
      };

      // 创建行内容
      const rowDiv = document.createElement('div');
      rowDiv.className = 'case-row-content';

      // ID
      const idDiv = document.createElement('div');
      idDiv.textContent = `#${item.id}`;
      idDiv.className = 'case-cell-id';
      rowDiv.appendChild(idDiv);

      // 名称
      const nameDiv = document.createElement('div');
      nameDiv.textContent = item.name;
      nameDiv.className = 'case-cell-name';
      rowDiv.appendChild(nameDiv);

      // 参数
      const paramDiv = document.createElement('div');
      paramDiv.textContent = item.parameter;
      paramDiv.title = item.parameter;
      paramDiv.className = 'case-cell-param';
      rowDiv.appendChild(paramDiv);

      // 详情列
      const detailDiv = document.createElement('div');
      detailDiv.textContent = item.detail || '-';
      detailDiv.title = item.detail || '';
      detailDiv.className = 'case-cell-detail';
      rowDiv.appendChild(detailDiv);

      // 状态
      const statusDiv = document.createElement('div');
      statusDiv.className = 'case-cell-status';

      // 创建状态标签
      const tagType = getStatusTagType(item.state);
      const tagText = getStatusText(item.state);

      const tagEl = document.createElement('span');
      tagEl.className = `el-tag el-tag--${tagType} el-tag--small case-status-tag`;
      tagEl.textContent = tagText;

      statusDiv.appendChild(tagEl);
      rowDiv.appendChild(statusDiv);

      div.appendChild(rowDiv);

      return div;
    }
  });
};

// 更新虚拟滚动数据
const updateVirtualScroll = () => {
  if (virtualScroll) {
    virtualScroll.updateItems(props.cases);
  } else {
    nextTick(() => {
      initVirtualScroll();
    });
  }
};

// 获取状态对应的标签类型
const getStatusTagType = (state: string): string => {
  switch (state) {
    case ExecutionState.Success:
      return 'success';
    case ExecutionState.Running:
      return 'warning';
    case ExecutionState.Failure:
      return 'danger';
    case ExecutionState.Pending:
    default:
      return 'info';
  }
};

// 获取状态的文本描述
const getStatusText = (state: string): string => {
  switch (state) {
    case ExecutionState.Success:
      return 'Passed';
    case ExecutionState.Failure:
      return 'Failed';
    case ExecutionState.Running:
      return 'Running';
    case ExecutionState.Pending:
      return 'Pending';
    default:
      return 'Unknown';
  }
};

// 监听 cases 变化，更新虚拟滚动 - 移除深度监听提升性能
watch(() => props.cases, (newCases, oldCases) => {
  // 只有在数组引用变化或长度变化时才更新
  if (newCases !== oldCases || newCases?.length !== oldCases?.length) {
    nextTick(() => {
      updateVirtualScroll();
    });
  }
}, { flush: 'post' });

// 组件挂载时初始化虚拟滚动
onMounted(() => {
  nextTick(() => {
    initVirtualScroll();
  });
});

// 组件卸载时销毁虚拟滚动
onUnmounted(() => {
  if (virtualScroll) {
    virtualScroll.destroy();
    virtualScroll = null;
  }
});
</script>

<style scoped lang="scss">
.cases-list-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  font-size: 13px;
}

.cases-header {
  position: sticky;
  top: 0;
  z-index: 10;
  background-color: #f5f7fa;
}

.cases-content {
  flex: 1;
  overflow: auto;
  position: relative;
}
</style>
