using Alsi.App;
using Alsi.Common.Utils;
using Alsi.Fuzz.Core.Storage;
using System;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;

namespace Alsi.Fuzz.Core.Utils
{
    public static class SecurityAccessUtils
    {
        public static void PrepareDll(byte[] securityAccessDllBytes)
        {
            if (securityAccessDllBytes.Any())
            {
                var dllFolder = GetSecurityAccessDllFolder();
                Directory.CreateDirectory(dllFolder);

                var dllFilePath = GetSecurityAccessDllFillePath();
                if (File.Exists(dllFilePath))
                {
                    File.Delete(dllFilePath);
                }
                File.WriteAllBytes(dllFilePath, securityAccessDllBytes);
            }
        }

        public static byte[] CalculateKey(byte[] seedBytes, uint securityLevel)
        {
            var dllFolder = GetSecurityAccessDllFolder();
            NativeDllUtils.AddSearchPath(dllFolder);

            var dllFilePath = GetSecurityAccessDllFillePath();
            if (!File.Exists(dllFilePath))
            {
                throw new Exception("Can't find the security access dll file.");
            }

            try
            {
                var keyBytes = new byte[10240];
                uint actualSize = 0;

                var result = KeyGenAlgo.GenerateKeyEx(
                    seedBytes, (byte)seedBytes.Length,
                    securityLevel, "",
                    keyBytes, (uint)keyBytes.Length, ref actualSize);

                if (result != VKeyGenResultEx.KGRE_Ok)
                {
                    throw new Exception($"Failed to calculate security access key: error code is {result}");
                }

                return keyBytes.Take((int)actualSize).ToArray();
            }
            catch (BadImageFormatException)
            {
                // 尝试使用 32 位程序加载 dll
                return RunX86Process(dllFilePath, seedBytes, securityLevel);
            }
        }

        private static byte[] RunX86Process(string dllFilePath, byte[] seedBytes, uint securityLevel)
        {
            string x86AppPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "SecurityAccess.x86", "Alsi.App.SecurityAccess.x86.exe");
            
            if (!File.Exists(x86AppPath))
            {
                throw new Exception($"Cannot find the 32-bit helper application at: {x86AppPath}");
            }

            // 将种子字节数组转换为十六进制字符串
            string seedHex = BitConverter.ToString(seedBytes).Replace("-", "");

            // 准备进程启动信息
            ProcessStartInfo startInfo = new ProcessStartInfo
            {
                FileName = x86AppPath,
                Arguments = $"\"{dllFilePath}\" \"{seedHex}\" \"{securityLevel}\"",
                UseShellExecute = false,
                RedirectStandardOutput = true,
                RedirectStandardError = true,
                CreateNoWindow = true,
                StandardOutputEncoding = Encoding.UTF8,
                StandardErrorEncoding = Encoding.UTF8
            };

            try
            {
                using (Process process = Process.Start(startInfo))
                {
                    // 读取输出和错误信息
                    string output = process.StandardOutput.ReadToEnd();
                    string error = process.StandardError.ReadToEnd();
                    
                    // 等待进程结束
                    process.WaitForExit();

                    // 检查进程执行结果
                    if (process.ExitCode != 0)
                    {
                        if (!string.IsNullOrEmpty(error) && error.StartsWith("ERROR:"))
                        {
                            throw new Exception($"32-bit helper application error: {error.Substring(6)}");
                        }
                        throw new Exception($"32-bit helper application failed with exit code: {process.ExitCode}");
                    }

                    // 分析输出结果
                    string[] lines = output.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);
                    string keyLine = lines.FirstOrDefault(l => l.StartsWith("KEY:"));
                    
                    if (keyLine == null)
                    {
                        throw new Exception("32-bit helper application did not return a valid key");
                    }

                    // 提取十六进制格式的密钥并转换为字节数组
                    string hexKey = keyLine.Substring(4);
                    return HexStringToByteArray(hexKey);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"Failed to execute 32-bit helper application: {ex.Message}", ex);
            }
        }

        // 将十六进制字符串转换为字节数组的辅助方法
        private static byte[] HexStringToByteArray(string hex)
        {
            if (string.IsNullOrEmpty(hex))
                return new byte[0];
                
            if (hex.Length % 2 != 0)
            {
                throw new ArgumentException("十六进制字符串必须具有偶数长度");
            }

            byte[] bytes = new byte[hex.Length / 2];
            for (int i = 0; i < hex.Length; i += 2)
            {
                bytes[i / 2] = Convert.ToByte(hex.Substring(i, 2), 16);
            }
            return bytes;
        }

        private static string GetSecurityAccessDllFillePath()
        {
            return Path.Combine(GetSecurityAccessDllFolder(), TestPlanStorage.SecurityAccessFile);
        }

        private static string GetSecurityAccessDllFolder()
        {
            return Path.Combine(AppEnv.WebHostApp.DataFolder, "temp", "security_access");
        }

        private enum VKeyGenResultEx
        {
            KGRE_Ok = 0,
            KGRE_BufferToSmall = 1,
            KGRE_SecurityLevelInvalid = 2,
            KGRE_VariantInvalid = 3,
            KGRE_UnspecifiedError = 4
        }

        private class KeyGenAlgo
        {
            [DllImport(TestPlanStorage.SecurityAccessFile, CallingConvention = CallingConvention.Cdecl)]
            public static extern VKeyGenResultEx GenerateKeyEx(
                byte[] iSeedArray, uint iSeedArraySize, uint iSecurityLevel,
                string iVariant,
                byte[] ioKeyArray, uint iKeyArraySize, ref uint oSize
            );
        }
    }
}
