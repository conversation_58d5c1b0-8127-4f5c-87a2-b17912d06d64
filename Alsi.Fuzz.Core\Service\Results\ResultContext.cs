using Alsi.App;
using System;
using System.IO;

namespace Alsi.Fuzz.Core.Service.Results
{
    public class ResultContext
    {
        public ResultContext(TestType testType, string resultFolderName)
        {
            // 验证文件夹名称
            if (string.IsNullOrEmpty(resultFolderName))
            {
                throw new ArgumentException("Result folder name cannot be empty", nameof(resultFolderName));
            }

            if (resultFolderName.Length > 50)
            {
                throw new ArgumentException($"Result folder name exceeds maximum length of 50 characters (current: {resultFolderName.Length})", nameof(resultFolderName));
            }

            // 验证是否只是文件夹名称（不包含路径分隔符和上级目录引用）
            if (resultFolderName.IndexOfAny(Path.GetInvalidFileNameChars()) >= 0 ||
                resultFolderName.Contains(Path.DirectorySeparatorChar.ToString()) ||
                resultFolderName.Contains(Path.AltDirectorySeparatorChar.ToString()) ||
                resultFolderName.Contains(".."))
            {
                throw new ArgumentException("Result folder name contains invalid characters, path separators, or directory traversal sequences", nameof(resultFolderName));
            }

            TestType = testType;
            _resultFolderName = resultFolderName;
            ResultFolder = GetResultFolder(testType, resultFolderName);
            ResultDbPath = Path.Combine(ResultFolder, "result.sqlite");
            DataLogPath = Path.Combine(ResultFolder, "result.blf");
        }

        public TestType TestType { get; }
        private string _resultFolderName { get; }
        public string ResultFolder { get; }
        public string ResultDbPath { get; }
        public string DataLogPath { get; }

        private static string GetResultFolder(TestType testType, string resultFolderName)
        {
            var folderName = string.Empty;
            if (testType == TestType.Interoperation)
            {
                folderName = "interoperation";
            }
            else if (testType == TestType.Case)
            {
                folderName = "case_results";
            }
            else
            {
                throw new NotImplementedException($"Unsupported test type: {testType}");
            }

            return Path.Combine(AppEnv.WebHostApp.DataFolder, folderName, resultFolderName);
        }
    }
}
