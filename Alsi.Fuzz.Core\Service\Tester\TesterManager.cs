using Alsi.App;
using Alsi.App.Utils;
using System;
using System.Diagnostics;
using System.IO;
using System.Threading.Tasks;

namespace Alsi.Fuzz.Core.Service.Tester
{
    /// <summary>
    /// Tester进程管理器实现
    /// </summary>
    public class TesterManager : ITesterManager
    {
        // 单例实现
        private static readonly Lazy<TesterManager> _instance =
            new Lazy<TesterManager>(() => new TesterManager());

        /// <summary>
        /// 获取TesterManager的单例实例
        /// </summary>
        public static TesterManager Instance => _instance.Value;

        private Process _testerProcess;
        private string _testerPath;
        private bool _disposed = false;
        private ITesterApiClient _apiClient;

        /// <summary>
        /// 初始化Tester管理器
        /// </summary>
        /// <param name="testerPath">Tester可执行文件路径，如果为null则自动在当前目录中查找</param>
        private TesterManager(string testerPath = null)
        {
            if (string.IsNullOrEmpty(testerPath))
            {
                // 由于输出目录已设置为统一目录，直接在应用程序基础目录查找
                _testerPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Alsi.Fuzz.Tester.exe");

                if (!File.Exists(_testerPath))
                {
                    throw new FileNotFoundException($"无法在当前目录找到Tester可执行文件: {_testerPath}");
                }
            }
            else
            {
                if (!File.Exists(testerPath))
                {
                    throw new FileNotFoundException($"指定的Tester可执行文件不存在：{testerPath}");
                }
                _testerPath = testerPath;
            }
        }

        /// <summary>
        /// 获取Tester进程是否正在运行
        /// </summary>
        public bool IsRunning => _testerProcess != null && !_testerProcess.HasExited;

        /// <summary>
        /// 获取Tester API是否可访问
        /// </summary>
        public async Task<bool> IsApiAccessibleAsync()
        {
            if (!IsRunning)
            {
                return false;
            }
            var apiResponse = await _apiClient.PingAsync();
            return apiResponse.Success;
        }

        /// <summary>
        /// 通过API获取Tester是否正在运行
        /// </summary>
        public async Task<bool> IsTesterRunningAsync()
        {
            if (!IsRunning) return false;
            return await _apiClient.IsRunningAsync();
        }

        /// <summary>
        /// 获取API客户端
        /// </summary>
        public ITesterApiClient GetApiClient()
        {
            return _apiClient;
        }

        /// <summary>
        /// Tester进程退出事件
        /// </summary>
        public event EventHandler<int> ProcessExited;

        /// <summary>
        /// 启动Tester进程
        /// </summary>
        /// <param name="arguments">启动参数</param>
        /// <returns>启动是否成功</returns>
        public async Task<bool> StartAsync()
        {
            if (IsRunning)
            {
                return true; // 已经在运行中
            }

            try
            {
                // 创建API客户端
                var port = PortUtils.FindAvailablePort();

                _apiClient = new TesterApiClient(port);

                var arguments = $"--port={port}";

                if (TesterConsts.QuickDebug && !Debugger.IsAttached)
                {
                    arguments += " --quick-debug=true";
                }

                arguments += $" --parent-process-mutex={FuzzConsts.ProcessMutexName}";

                var startInfo = new ProcessStartInfo
                {
                    FileName = _testerPath,
                    Arguments = arguments,
                    UseShellExecute = true,
                    CreateNoWindow = false
                };

                _testerProcess = new Process
                {
                    StartInfo = startInfo,
                    EnableRaisingEvents = true
                };

                _testerProcess.Exited += OnProcessExited;
                // 移除了OutputDataReceived和ErrorDataReceived事件处理

                bool result = _testerProcess.Start();

                // 等待进程启动完成
                await Task.Delay(500);
                return result && !_testerProcess.HasExited;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"启动Tester进程失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 停止Tester进程
        /// </summary>
        /// <returns>停止是否成功</returns>
        public async Task<bool> StopAsync()
        {
            if (!IsRunning)
            {
                return true; // 已经停止
            }

            try
            {
                // 尝试正常关闭进程
                if (!_testerProcess.CloseMainWindow())
                {
                    // 如果无法正常关闭，则强制终止
                    _testerProcess.Kill();
                }

                // 等待进程退出
                await Task.Delay(1000);
                if (!_testerProcess.HasExited)
                {
                    // 如果1秒后进程仍未退出，再次尝试强制终止
                    _testerProcess.Kill();
                    await Task.Delay(500);
                }

                return _testerProcess.HasExited;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"停止Tester进程失败: {ex.Message}");
                return false;
            }
        }

        private void OnProcessExited(object sender, EventArgs e)
        {
            int exitCode = 0;
            try
            {
                exitCode = _testerProcess.ExitCode;
            }
            catch
            {
                // 忽略获取退出码时可能发生的异常
            }

            ProcessExited?.Invoke(this, exitCode);
        }

        public async Task WaitTesterAsync(bool expectIsRunning)
        {
            var apiClient = GetApiClient();
            if (apiClient == null)
            {
                throw new Exception("The API client is null");
            }

            bool isRunning = false;
            int attempts = 1;
            const int maxAttempts = 20;
            const int delayMs = 500;

            while (attempts <= maxAttempts)
            {
                isRunning = await apiClient.IsRunningAsync();
                var status = isRunning ? "running" : "not running";
                AppEnv.Logger.Debug($"Tester is {status} ({attempts}/{maxAttempts})");

                if (isRunning == expectIsRunning)
                {
                    return;
                }

                attempts++;
                await Task.Delay(delayMs);
            }

            var finalStatus = isRunning ? "running" : "not running";
            throw new Exception($"Tester is {finalStatus} in {attempts} attampts");
        }

        #region IDisposable Support
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    // 清理托管资源
                    if (_testerProcess != null)
                    {
                        try
                        {
                            if (!_testerProcess.HasExited)
                            {
                                _testerProcess.Kill();
                            }
                            _testerProcess.Dispose();
                        }
                        catch
                        {
                            // 忽略清理过程中可能发生的异常
                        }
                    }

                    _apiClient?.Dispose();
                }

                _testerProcess = null;
                _apiClient = null;
                _disposed = true;
            }
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }
        #endregion
    }
}
