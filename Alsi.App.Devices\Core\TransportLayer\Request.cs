using System;

namespace Alsi.App.Devices.Core.TransportLayer
{
    public class Request
    {
        public int RequestId { get; set; }
        public bool RequestIsExt { get; set; }
        public int FlowControlId { get; set; }
        public int ResponseId { get; set; }
        public byte[] Payload { get; set; } = Array.Empty<byte>();
        public int PayloadLength { get; set; }
        public bool IsCanfd { get; set; } = false;
    }
}
