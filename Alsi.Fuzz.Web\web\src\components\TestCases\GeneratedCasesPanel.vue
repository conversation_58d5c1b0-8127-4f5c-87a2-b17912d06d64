<template>
  <div class="case-panel">
    <div class="panel-header">
      <div class="header-main">
        <h3>Generated Test Cases</h3>
      </div>
      <div v-if="cases.length > 0" class="case-info">
        <span class="case-count">Generated: {{ cases.length }} cases</span>
        <span class="estimated-time">
          Estimated Time: {{ formatEstimatedTimeMs(estimatedTime) }}
        </span>
      </div>
    </div>

    <div v-if="generating" class="loading-indicator">
      <el-icon class="is-loading"><Loading /></el-icon> Generating...
    </div>

    <div v-else-if="cases.length === 0 && showEmptyMessage" class="empty-message">
      Click Generate to create test cases
    </div>

    <div v-else-if="cases.length > 0" class="results-list">
      <div v-for="group in groupSummary"
           :key="group.groupPrefix"
           class="group-item">
        <div class="group-header" @click="toggleGroup(group.groupPrefix)">
          <div class="group-info">
            <el-icon class="expand-icon" :class="{ 'is-expanded': expandedGroups[group.groupPrefix] }">
              <CaretRight />
            </el-icon>
            <span class="group-name">{{ group.groupPrefix }}</span>
            <span class="group-count">({{ group.count }} cases)</span>
            <span class="group-time">
              Estimated Time: {{ formatEstimatedTimeMs(getGroupEstimatedTime(group.groupPrefix, group.count)) }}
            </span>
          </div>
        </div>

        <div v-if="expandedGroups[group.groupPrefix]" class="group-content">
          <div v-if="loadingGroups[group.groupPrefix]" class="group-loading">
            <el-icon class="is-loading"><Loading /></el-icon> Loading...
          </div>

          <template v-else-if="loadedGroups[group.groupPrefix]">
            <div v-for="(item, index) in loadedGroups[group.groupPrefix]"
                 :key="item.id || index"
                 class="list-item"
                 :class="{ 'striped': index % 2 === 1 }">
              <div class="item-header">
                <div class="sequence-group">
                  <span class="sequence-name">{{ item.id }}</span>
                  <span class="group-badge">{{ item.name }}</span>
                </div>
              </div>
              <div class="parameter-text" :title="`${item.parameter}`">{{ item.parameter }}</div>
            </div>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, ref, computed, reactive, watch } from 'vue';
import { Loading, CaretRight } from '@element-plus/icons-vue';
import { CaseResult } from '@/api/interoperationApi';

const props = defineProps<{
  cases: CaseResult[];
  generating: boolean;
  showEmptyMessage: boolean;
  estimatedTime: number; // 单位:毫秒
}>();

// 分组展开状态
const expandedGroups = ref<Record<string, boolean>>({});
// 已加载的分组内容
const loadedGroups = reactive<Record<string, CaseResult[]>>({});
// 正在加载的分组
const loadingGroups = reactive<Record<string, boolean>>({});

// 计算分组摘要信息（不包含具体items）
const groupSummary = computed(() => {
  // 按前缀统计每个组的数量
  const groups: Record<string, number> = {};

  for (const item of props.cases) {
    const nameParts = (item.name || '').split('-');
    const prefix = nameParts.length > 0 ? nameParts[0] : item.name;

    if (!groups[prefix]) {
      groups[prefix] = 0;
    }
    groups[prefix]++;
  }

  // 转换为数组并排序
  return Object.entries(groups).map(([groupPrefix, count]) => ({
    groupPrefix,
    count
  })).sort((a, b) => a.groupPrefix.localeCompare(b.groupPrefix));
});

// 获取分组的预估时间
const getGroupEstimatedTime = (groupPrefix: string, groupCount: number) => {
  // 根据组内用例数量计算时间
  const timePerCase = props.estimatedTime / props.cases.length;
  return timePerCase * groupCount;
};

// 加载指定分组的数据
const loadGroupData = (groupPrefix: string) => {
  // 标记为加载中
  loadingGroups[groupPrefix] = true;

  // 模拟异步加载（实际上是从cases中筛选）
  setTimeout(() => {
    const groupItems = props.cases.filter(item => {
      const nameParts = (item.name || '').split('-');
      const prefix = nameParts.length > 0 ? nameParts[0] : item.name;
      return prefix === groupPrefix;
    });

    // 保存加载的数据
    loadedGroups[groupPrefix] = groupItems;
    loadingGroups[groupPrefix] = false;
  }, 100); // 添加小延迟以便显示加载状态
};

// 控制分组展开/收起
const toggleGroup = (groupPrefix: string) => {
  const isCurrentlyExpanded = expandedGroups.value[groupPrefix];

  // 切换展开状态
  expandedGroups.value[groupPrefix] = !isCurrentlyExpanded;

  // 如果是展开且还未加载数据，则加载数据
  if (!isCurrentlyExpanded && !loadedGroups[groupPrefix]) {
    loadGroupData(groupPrefix);
  }
};

// 监听cases属性变化，重置组件状态
watch(() => props.cases, (newCases) => {
  console.log('Cases changed:', newCases.length);
  // 清空已加载的分组数据
  Object.keys(loadedGroups).forEach(key => {
    delete loadedGroups[key];
  });

  // 清空加载状态
  Object.keys(loadingGroups).forEach(key => {
    delete loadingGroups[key];
  });

  // 重置展开状态
  expandedGroups.value = {};
}, { deep: true });

// 格式化预估时间(毫秒)，最多显示两个单位
const formatEstimatedTimeMs = (ms: number) => {
  ms = Math.floor(ms);

  if (ms < 1000) {
    return `${ms}ms`;
  } else if (ms < 60000) {
    const seconds = Math.floor(ms / 1000);
    const remainingMs = Math.floor(ms % 1000);
    return `${seconds}s ${remainingMs}ms`;
  } else if (ms < 3600000) {
    const minutes = Math.floor(ms / 60000);
    const seconds = Math.floor((ms % 60000) / 1000);
    return `${minutes}m ${seconds}s`;
  } else {
    const hours = Math.floor(ms / 3600000);
    const minutes = Math.floor((ms % 3600000) / 60000);
    return `${hours}h ${minutes}m`;
  }
};
</script>

<style scoped lang="scss">
.case-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
}

.panel-header {
  padding: 8px 12px;
  border-bottom: 1px solid #ebeef5;

  h3 {
    margin: 0;
    font-size: 15px;
    font-weight: 500;
    color: #303133;
  }

  .case-info {
    margin-top: 2px;
    font-size: 12px;
    color: #909399;
    display: flex;
    gap: 12px;
  }
}

.results-list {
  flex: 1;
  overflow-y: auto;

  .list-item {
    padding: 8px 12px;
    border-bottom: 1px solid #ebeef5;
    font-size: 13px;

    &:last-child {
      border-bottom: none;
    }

    &.striped {
      background-color: #fafafa;
    }

    &:hover {
      background-color: #f5f7fa;
    }
  }
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.sequence-group {
  display: flex;
  align-items: center;
  gap: 8px;

  .sequence-name {
    font-weight: 500;
    color: #303133;
  }

  .group-badge {
    font-size: 12px;
    padding: 1px 5px;
    background-color: #ecf5ff;
    color: #409EFF;
    border-radius: 3px;
  }
}

.parameter-text {
  margin-top: 4px;
  font-size: 12px;
  color: #606266;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 30vw;
}

.loading-indicator, .empty-message {
  display: flex;
  justify-content: center;
  align-items: center;
  flex: 1;
  color: #909399;
  font-size: 13px;

  .el-icon {
    margin-right: 6px;
  }
}

.header-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.group-item {
  border-bottom: 1px solid #ebeef5;

  &:last-child {
    border-bottom: none;
  }
}

.group-header {
  padding: 8px 12px;
  background-color: #f5f7fa;
  cursor: pointer;
  user-select: none;

  &:hover {
    background-color: #ecf5ff;
  }

  .group-info {
    display: flex;
    align-items: center;
    gap: 8px;

    .expand-icon {
      transition: transform 0.2s;
      font-size: 16px;

      &.is-expanded {
        transform: rotate(90deg);
      }
    }

    .group-name {
      font-weight: 500;
      color: #303133;
    }

    .group-count {
      color: #909399;
      font-size: 12px;
    }

    .group-time {
      color: #909399;
      font-size: 12px;
      margin-left: auto;
    }
  }
}

.group-content {
  .list-item {
    padding-left: 32px;
  }
}

.group-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 16px;
  color: #909399;
  font-size: 13px;

  .el-icon {
    margin-right: 6px;
  }
}
</style>
