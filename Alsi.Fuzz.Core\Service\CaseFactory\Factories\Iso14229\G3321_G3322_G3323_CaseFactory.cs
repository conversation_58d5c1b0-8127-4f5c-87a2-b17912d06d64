using Alsi.Fuzz.Core.Service.CaseFactory.Core;
using Alsi.Fuzz.Core.Service.CaseFactory.Factories.Iso14229.Consts;
using System.Collections.Generic;
using System.Linq;

namespace Alsi.Fuzz.Core.Service.CaseFactory.Factories.Iso14229
{
    public class G3321_G3322_G3323_CaseFactory : CaseFactoryBase
    {
        public override CaseMutation[] Generate(MutationOptions options)
        {
            var xmlServices = options.XmlServices;

            var supportedXmlServicesWithoutSubfunction = xmlServices
                // 仅遍历支持的服务
                .Where(x => !x.SubfunctionId.HasValue && x.IsSupported)
                // 仅遍历没有 Subfunction 的服务
                .Where(x => IsoUdsConsts.Services.FirstOrDefault(service => x.Id == service.Id)?.Subfunctions.Any() == false)
                .ToArray();

            var caseMutations = new List<CaseMutation>();

            var repeatCountArray = new int[] {
                2, 3, 4, 5, 6,
                7, 8, 9, 10, 14,
                15, 16, 17, 18, 30,
                31, 32, 33, 34, 62,
                63, 64, 65, 66, 98,
                99, 100, 101, 102, 126,
                127, 128, 129, 130, 254,
                255, 256, 257, 258, 511,
                512
            };

            foreach (var xmlServiceWithoutSubfunction in supportedXmlServicesWithoutSubfunction)
            {
                var sid = xmlServiceWithoutSubfunction.Id;
                var parameter2k = xmlServiceWithoutSubfunction.Parameter2k;

                foreach (var repeatCount in repeatCountArray)
                {
                    var payload = new List<byte>();
                    for (var i = 0; i < repeatCount; i++)
                    {
                        payload.Add(sid);
                    }
                    payload.AddRange(parameter2k);

                    var caseMutation = CaseMutation.Create($"G3321-Sid{sid:X2}-RepeatSid-{repeatCount:D2}")
                        .MutatePayload(payload.ToArray())
                        .MutatePayloadLength(payload.Count);
                    caseMutations.Add(caseMutation);
                }

                foreach (var repeatCount in repeatCountArray)
                {
                    var payload = new List<byte> { sid };
                    for (var i = 0; i < repeatCount; i++)
                    {
                        payload.AddRange(parameter2k);
                    }

                    var caseMutation = CaseMutation.Create($"G3322-Sid{sid:X2}-RepeatParameter1k-{repeatCount:D2}")
                        .MutatePayload(payload.ToArray())
                        .MutatePayloadLength(payload.Count);
                    caseMutations.Add(caseMutation);
                }

                foreach (var repeatCount in repeatCountArray)
                {
                    var payload = new List<byte>();
                    for (var i = 0; i < repeatCount; i++)
                    {
                        payload.Add(sid);
                        payload.AddRange(parameter2k);
                    }

                    var caseMutation = CaseMutation.Create($"G3323-Sid{sid:X2}-RepeatAll-{repeatCount:D2}")
                        .MutatePayload(payload.ToArray())
                        .MutatePayloadLength(payload.Count);
                    caseMutations.Add(caseMutation);
                }

                foreach (var repeatCount in repeatCountArray)
                {
                    var payload = new List<byte> { sid };
                    payload.AddRange(parameter2k);

                    var randomByte = (byte)options.Random.Next(0, 0x100);
                    for (var i = 0; i < repeatCount; i++)
                    {
                        payload.Add(randomByte);
                    }

                    var caseMutation = CaseMutation.Create($"G3324-Sid{sid:X2}-RepeatAll-{repeatCount:D2}")
                        .MutatePayload(payload.ToArray())
                        .MutatePayloadLength(payload.Count);
                    caseMutations.Add(caseMutation);
                }
            }

            return caseMutations.ToArray();
        }
    }
}
