using Alsi.App.Devices.Core;
using Alsi.Common.Utils;
using Alsi.Common.Utils.Autosar;
using Alsi.Fuzz.Core.Service.CaseFactory.Core;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Alsi.Fuzz.Core.Service.CaseFactory.Factories
{
    public class HsDlcCaseFactory : DlcCaseFactory
    {
        public override CommunicationType CommunicationType => CommunicationType.Can;
        public override CaseMutation[] Generate(MutationOptions options)
        {
            var list = new List<CaseMutation>();
            list.AddRange(base.Generate(options));

            foreach (var id in options.WhiteListIds)
            {
                // Abn2
                if (options.Coverage == CoverageType.Normal)
                {
                    // Abn2: 策略-通常
                    if (options.Dlc > 7)
                    {
                        // DLC = {1, 7}，使用 {00, FF, 单随机} 作为数据
                        AddAllDataPatternMutations(list, id, 1, 1);
                        AddAllDataPatternMutations(list, id, 7, 7);
                    }
                    else if (options.Dlc < 2)
                    {
                        // DLC = {2, 8}，使用 {00, FF, 单随机} 作为数据
                        AddAllDataPatternMutations(list, id, 2, 2);
                        AddAllDataPatternMutations(list, id, 8, 8);
                    }
                    else
                    {
                        // DLC = {1, 8}，使用 {00, FF, 单随机} 作为数据
                        AddAllDataPatternMutations(list, id, 1, 1);
                        AddAllDataPatternMutations(list, id, 8, 8);
                    }
                }
                else if (options.Coverage == CoverageType.High)
                {
                    // Abn2: 策略-强: DLC=1~8 除了给定的 DLC 值
                    var allDlcValues = new int[] { 1, 2, 3, 4, 5, 6, 7, 8 };
                    var dlcValuesToUse = allDlcValues.Where(dlc => dlc != options.Dlc).ToArray();

                    foreach (var dlc in dlcValuesToUse)
                    {
                        AddAllDataPatternMutations(list, id, dlc, dlc);
                    }
                }

                // Abn3
                if (options.Coverage == CoverageType.Normal)
                {
                    // Abn3: DLC > 8 (通常策略: DLC={9,F})
                    AddAllDataPatternMutations(list, id, 9, 12);
                    AddAllDataPatternMutations(list, id, 15, 64);
                }
                else if (options.Coverage == CoverageType.High)
                {
                    // Abn3: 策略-强: DLC={9,A,B,C,D,E,F}
                    int[] extendedDlcValues = new int[] { 9, 10, 11, 12, 13, 14, 15 };

                    foreach (var dlc in extendedDlcValues)
                    {
                        var dataLength = DlcUtils.GetDataLength(dlc);
                        AddAllDataPatternMutations(list, id, dlc, dataLength);
                    }
                }
            }

            return list.ToArray();
        }


        // 辅助方法：生成CaseMutation对象
        private CaseMutation GenerateMutation(int id, int dlc, byte[] data, string dataType)
        {
            var name = $"G{Group}-ID{id.ToHex()}-DLC{dlc}-{dataType}";
            return CaseMutation.Create(name).MutateId(id).MutateDlc(dlc).MutateData(data);
        }

        // 辅助方法：生成全0数据
        private byte[] CreateAllZeroData(int length)
        {
            return new byte[length];
        }

        // 辅助方法：生成全1数据
        private byte[] CreateAllOnesData(int length)
        {
            var data = new byte[length];
            for (int i = 0; i < length; i++)
            {
                data[i] = 0xFF;
            }
            return data;
        }

        // 辅助方法：生成随机数据
        private byte[] CreateRandomData(int length)
        {
            var data = new byte[length];
            var random = new Random();
            random.NextBytes(data);
            return data;
        }

        // 辅助方法：添加所有数据模式的变异
        private void AddAllDataPatternMutations(List<CaseMutation> list, int id, int dlc, int dataLength)
        {
            list.Add(GenerateMutation(id, dlc, CreateAllZeroData(dataLength), "00"));
            list.Add(GenerateMutation(id, dlc, CreateAllOnesData(dataLength), "FF"));
            list.Add(GenerateMutation(id, dlc, CreateRandomData(dataLength), "RND"));
        }
    }

    public class FdDlcCaseFactory : DlcCaseFactory
    {
        public override CommunicationType CommunicationType => CommunicationType.CanFd;
        public override CaseMutation[] Generate(MutationOptions options)
        {
            var list = new List<CaseMutation>();
            list.AddRange(base.Generate(options));
            foreach (var id in options.WhiteListIds)
            {
                if (options.Dlc <= 8)
                {
                    #region Abn2: DLC <= 8 时的规则
                    if (options.Coverage == CoverageType.Normal)
                    {
                        // Abn2: 策略-通常
                        if (options.Dlc > 7)
                        {
                            // DLC = {1, 7}，使用 {00, FF, 单随机} 作为数据
                            AddAllDataPatternMutations(list, id, 1, 1);
                            AddAllDataPatternMutations(list, id, 7, 7);
                        }
                        else if (options.Dlc < 2)
                        {
                            // DLC = {2, 8}，使用 {00, FF, 单随机} 作为数据
                            AddAllDataPatternMutations(list, id, 2, 2);
                            AddAllDataPatternMutations(list, id, 8, 8);
                        }
                        else
                        {
                            // DLC = {1, 8}，使用 {00, FF, 单随机} 作为数据
                            AddAllDataPatternMutations(list, id, 1, 1);
                            AddAllDataPatternMutations(list, id, 8, 8);
                        }
                    }
                    else if (options.Coverage == CoverageType.High)
                    {
                        // Abn2: 策略-强: DLC=1~8 除了给定的 DLC 值
                        int[] allDlcValues = new int[] { 1, 2, 3, 4, 5, 6, 7, 8 };
                        int[] dlcValuesToUse = allDlcValues.Where(dlc => dlc != options.Dlc).ToArray();

                        foreach (var dlc in dlcValuesToUse)
                        {
                            AddAllDataPatternMutations(list, id, dlc, dlc);
                        }
                    }
                    #endregion
                }
                else
                {
                    #region Abn2: DLC > 8 时的规则
                    if (options.Coverage == CoverageType.Normal)
                    {
                        if (options.Dlc == 0xF) // DLC == F
                        {
                            // DLC = {1, 8, 9, E}
                            int[] dlcValues = new int[] { 1, 8, 9, 14 };
                            foreach (var dlc in dlcValues)
                            {
                                var dataLength = dlc <= 8 ? dlc : DlcUtils.GetDataLength(dlc);
                                AddAllDataPatternMutations(list, id, dlc, dataLength);
                            }
                        }
                        else if (options.Dlc == 9)
                        {
                            // DLC = {1, 8, A, F}
                            int[] dlcValues = new int[] { 1, 8, 10, 15 };
                            foreach (var dlc in dlcValues)
                            {
                                var dataLength = dlc <= 8 ? dlc : DlcUtils.GetDataLength(dlc);
                                AddAllDataPatternMutations(list, id, dlc, dataLength);
                            }
                        }
                        else if (options.Dlc == 14) // DLC == E
                        {
                            // DLC = {1, 8, 9, F}
                            int[] dlcValues = new int[] { 1, 8, 9, 15 };
                            foreach (var dlc in dlcValues)
                            {
                                var dataLength = dlc <= 8 ? dlc : DlcUtils.GetDataLength(dlc);
                                AddAllDataPatternMutations(list, id, dlc, dataLength);
                            }
                        }
                        else
                        {
                            // DLC = {1, 8, given-1, given+1, F}
                            int[] dlcValues = new int[] { 1, 8, options.Dlc - 1, options.Dlc + 1, 15 };
                            foreach (var dlc in dlcValues)
                            {
                                var dataLength = dlc <= 8 ? dlc : DlcUtils.GetDataLength(dlc);
                                AddAllDataPatternMutations(list, id, dlc, dataLength);
                            }
                        }
                    }
                    else if (options.Coverage == CoverageType.High)
                    {
                        // 策略-强: DLC=1~F except given dlc
                        int[] allDlcValues = new int[] { 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15 };
                        int[] dlcValuesToUse = allDlcValues.Where(dlc => dlc != options.Dlc).ToArray();
                        foreach (var dlc in dlcValuesToUse)
                        {
                            var dataLength = dlc <= 8 ? dlc : DlcUtils.GetDataLength(dlc);
                            AddAllDataPatternMutations(list, id, dlc, dataLength);
                        }
                    }
                    #endregion
                }

                #region Abn3: 当 【DLC <= 8】 和【DLC > 8】 时的规则相同
                if (options.Coverage == CoverageType.Normal)
                {
                    // 策略-通常: DLC={9,F}
                    int[] extendedDlcValues = new int[] { 9, 15 };
                    foreach (var dlc in extendedDlcValues)
                    {
                        var dataLength = DlcUtils.GetDataLength(dlc);
                        AddAllDataPatternMutations(list, id, dlc, dataLength);
                    }
                }
                else if (options.Coverage == CoverageType.High)
                {
                    // 策略-强: DLC={9,A,B,C,D,E,F}
                    int[] extendedDlcValues = new int[] { 9, 10, 11, 12, 13, 14, 15 };
                    foreach (var dlc in extendedDlcValues)
                    {
                        var dataLength = DlcUtils.GetDataLength(dlc);
                        AddAllDataPatternMutations(list, id, dlc, dataLength);
                    }
                }
                #endregion
            }

            return list.ToArray();
        }

        // 辅助方法：生成CaseMutation对象
        private CaseMutation GenerateMutation(int id, int dlc, byte[] data, string dataType)
        {
            var name = $"G{Group}-ID{id.ToHex()}-DLC{dlc}-{dataType}";
            return CaseMutation.Create(name).MutateId(id).MutateDlc(dlc).MutateData(data);
        }

        // 辅助方法：生成全0数据
        private byte[] CreateAllZeroData(int length)
        {
            return new byte[length];
        }

        // 辅助方法：生成全1数据
        private byte[] CreateAllOnesData(int length)
        {
            var data = new byte[length];
            for (int i = 0; i < length; i++)
            {
                data[i] = 0xFF;
            }
            return data;
        }

        // 辅助方法：生成随机数据
        private byte[] CreateRandomData(int length)
        {
            var data = new byte[length];
            var random = new Random();
            random.NextBytes(data);
            return data;
        }

        // 辅助方法：添加所有数据模式的变异
        private void AddAllDataPatternMutations(List<CaseMutation> list, int id, int dlc, int dataLength)
        {
            list.Add(GenerateMutation(id, dlc, CreateAllZeroData(dataLength), "00"));
            list.Add(GenerateMutation(id, dlc, CreateAllOnesData(dataLength), "FF"));
            list.Add(GenerateMutation(id, dlc, CreateRandomData(dataLength), "RND"));
        }
    }

    public abstract class DlcCaseFactory : ICaseFactory
    {
        public int Group => 3;
        public abstract CommunicationType CommunicationType { get; }
        public virtual CaseMutation[] Generate(MutationOptions options)
        {
            // abn1
            var list = new List<CaseMutation>();
            foreach (var id in options.WhiteListIds)
            {
                var name = $"G{Group}-ID{id.ToHex()}-DLC0";
                var caseMutation = CaseMutation.Create(name).MutateId(id).MutateDlc(0).MutateData(Array.Empty<byte>());
                list.Add(caseMutation);
            }

            return list.ToArray();
        }
    }
}
