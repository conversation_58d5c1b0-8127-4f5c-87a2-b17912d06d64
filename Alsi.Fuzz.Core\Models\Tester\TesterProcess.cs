using System;
using System.Diagnostics;

namespace Alsi.Fuzz.Core.Models.Tester
{
    /// <summary>
    /// Tester进程信息
    /// </summary>
    public class TesterProcess
    {
        /// <summary>
        /// 进程ID
        /// </summary>
        public int ProcessId { get; set; }

        /// <summary>
        /// 启动时间
        /// </summary>
        public DateTime StartTime { get; set; }

        /// <summary>
        /// 退出时间（如果已退出）
        /// </summary>
        public DateTime? ExitTime { get; set; }

        /// <summary>
        /// 退出代码（如果已退出）
        /// </summary>
        public int? ExitCode { get; set; }

        /// <summary>
        /// 运行状态
        /// </summary>
        public TesterProcessStatus Status { get; set; }

        /// <summary>
        /// 启动命令行参数
        /// </summary>
        public string Arguments { get; set; }

        /// <summary>
        /// 创建一个新的Tester进程信息实例
        /// </summary>
        /// <param name="process">进程对象</param>
        /// <param name="arguments">启动参数</param>
        /// <returns>Tester进程信息</returns>
        public static TesterProcess FromProcess(Process process, string arguments = null)
        {
            if (process == null)
            {
                return null;
            }

            return new TesterProcess
            {
                ProcessId = process.Id,
                StartTime = process.StartTime,
                Status = TesterProcessStatus.Running,
                Arguments = arguments
            };
        }

        /// <summary>
        /// 更新进程退出信息
        /// </summary>
        /// <param name="exitCode">退出代码</param>
        public void SetExited(int exitCode)
        {
            ExitTime = DateTime.Now;
            ExitCode = exitCode;
            Status = TesterProcessStatus.Exited;
        }
    }

    /// <summary>
    /// Tester进程状态
    /// </summary>
    public enum TesterProcessStatus
    {
        /// <summary>
        /// 未知状态
        /// </summary>
        Unknown = 0,

        /// <summary>
        /// 正在运行
        /// </summary>
        Running = 1,

        /// <summary>
        /// 已退出
        /// </summary>
        Exited = 2
    }
}
