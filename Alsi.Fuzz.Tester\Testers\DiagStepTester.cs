using Alsi.App.Devices.Core;
using Alsi.App.Devices.Core.TransportLayer;
using Alsi.App.Devices.Core.TransportLayer.Frames;
using Alsi.Common.Utils;
using Alsi.Fuzz.Core.Models.Tester;
using Alsi.Fuzz.Core.Models.TestSuites.Steps;
using Alsi.Fuzz.Core.Models.TestSuites.Steps.Diagnostic;
using Alsi.Fuzz.Core.Service.CaseFactory.Core;
using Alsi.Fuzz.Core.Service.Results;
using Alsi.Fuzz.Core.Utils;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace Alsi.Fuzz.Tester.Testers
{
    public class DiagStepTester
    {
        public async Task<bool> TryExecuteAsync(StepBase step, CaseStep caseStep, CaseContext caseContext)
        {
            switch (step)
            {
                case SendDiagStep sendDiagStep:
                    await SendDiagAsync(sendDiagStep, caseStep, caseContext);
                    return true;

                case RecvDiagStep recvDiagStep:
                    await RecvDiagAsync(recvDiagStep, caseStep, caseContext);
                    return true;
            }

            return false;
        }

        public Response LatestUdsResponse { get; set; }

        private Task SendDiagAsync(SendDiagStep sendDiagStep, CaseStep caseStep, CaseContext caseContext)
        {
            var payload = sendDiagStep.HexPayload?.Trim() ?? string.Empty;
            caseContext.EnvVars.Eval(ref payload);

            var payloadBytes = SequenceUtils.ParseBytes(payload);

            var tpService = new TpService(new DiagParams());

            var diagReqId = caseContext.CaseConfig.DiagReqId;
            var diagResId = caseContext.CaseConfig.DiagResId;
            var diagReqIsExt = caseContext.CaseConfig.DiagReqIsExt;
            var diagTimeoutMs = caseContext.CaseConfig.DiagTimeoutMs;
            var isCanFd = caseContext.HardwareConfig.CommunicationType == CommunicationType.CanFd;

            var payloadLength = payloadBytes.Length;

            var caseMutation = CaseMutation.Deserialize(caseContext.CaseResult.Parameter);
            caseMutation.ApplyToDiagFrame(ref payloadBytes, ref payloadLength);

            var request = new Request
            {
                IsCanfd = isCanFd,
                Payload = payloadBytes,
                PayloadLength = payloadLength,
                RequestId = diagReqId,
                RequestIsExt = diagReqIsExt,
                FlowControlId = diagReqId,
                ResponseId = diagResId
            };

            LatestUdsResponse = null;

            caseStep.Detail = $"Payload={payloadBytes.ToHex()}, PayloadLength={payloadLength}";

            var response = tpService.Request(request, TimeSpan.FromMilliseconds(diagTimeoutMs), acceptNoResponse: true);
            if (response?.Payload != null)
            {
                LatestUdsResponse = response;
            }

            if (LatestUdsResponse == null || LatestUdsResponse.Payload.Length == 0)
            {
                DiagFallbackTester.Run(caseContext);
            }

            // 检测发送的请求中，是否包含多帧请求
            if (MultipleFrame.TryBuild(request.Payload, request.PayloadLength, request.IsCanfd, out var multipleFrame))
            {
                var resultProps = new ResultProps();
                if (!string.IsNullOrWhiteSpace(caseContext.CaseResult.ResultProps))
                {
                    resultProps = JsonUtils.Deserialize<ResultProps>(caseContext.CaseResult.ResultProps);
                }
                resultProps.HasMultipleFrameRequest = true;
                caseContext.CaseResult.ResultProps = JsonUtils.Serialize(resultProps);
            }

            return Task.CompletedTask;
        }

        private DiagFallbackTester DiagFallbackTester { get; set; } = new DiagFallbackTester();

        private Task RecvDiagAsync(RecvDiagStep recvDiagStep, CaseStep caseStep, CaseContext caseContext)
        {
            var message = string.Empty;

            var actualPayload = LatestUdsResponse?.Payload ?? Array.Empty<byte>();
            var payloadText = string.Empty;
            if (actualPayload.Length > 10)
            {
                payloadText = actualPayload.ToHex().Substring(0, 10) + "...";
            }
            else
            {
                payloadText = actualPayload.ToHex();
            }

            var actualPayloadText = $"Actual payload: {payloadText}";

            if (caseContext.TestResult.TestType == TestType.Interoperation)
            {
                // 未定义任何数据匹配
                if (!recvDiagStep.MatchDiags.Any())
                {
                    // TODO: get timestamp
                    //caseStep.FrameTimestamp = rxFrame.TimeUS;
                    caseStep.State = ExecutionState.Success;
                    caseStep.Detail = actualPayloadText;
                    return Task.CompletedTask;
                }

                var expectText = string.Empty;
                // 定义了数据匹配
                foreach (var matchDiag in recvDiagStep.MatchDiags)
                {
                    var expectedPayload = matchDiag.Payload;
                    caseContext.EnvVars.Eval(ref expectedPayload);
                    if (matchDiag.IsMatch(expectedPayload, actualPayload))
                    {
                        expectText = $"Expect payload: {expectedPayload}, ";

                        // TODO: get timestamp
                        //caseStep.FrameTimestamp = rxFrame.TimeUS;
                        caseStep.State = ExecutionState.Success;
                        caseStep.Detail = expectText + actualPayloadText;

                        caseContext.EnvVars.SetByCommand(matchDiag.SetVars, actualPayload);

                        return Task.CompletedTask;
                    }
                }

                message = $"Expect payload: {recvDiagStep.MatchDiags.First()?.Payload}, Actual payload: {payloadText}";
            }
            else if (caseContext.TestResult.TestType == TestType.Case)
            {
                if (actualPayload.Length == 0)
                {
                    // 当诊断没响应时，根据用户配置，发送其它诊断请求，并收到了诊断响应
                    var fallbackResponse = DiagFallbackTester.FallbackResponse;
                    if (fallbackResponse != null)
                    {
                        caseStep.State = ExecutionState.Success;
                        caseStep.Detail = $"No response, use [Fallback] request and received: {fallbackResponse.Payload.ToHex()}";

                        return Task.CompletedTask;
                    }
                }
                else
                {
                    // 收到了诊断响应
                    caseStep.State = ExecutionState.Success;
                    caseStep.Detail = actualPayloadText;
                    return Task.CompletedTask;
                }

                message = $"No response";
            }
            else
            {
                message = $"Unknown test result type: {caseContext.TestResult.TestType}";
            }

            caseStep.State = ExecutionState.Failure;
            caseStep.Detail = message;
            throw new Exception(message);
        }
    }
}
