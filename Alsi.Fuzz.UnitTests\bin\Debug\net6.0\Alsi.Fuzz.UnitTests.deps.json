{"runtimeTarget": {"name": ".NETCoreApp,Version=v6.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v6.0": {"Alsi.Fuzz.UnitTests/1.0.0": {"dependencies": {"Alsi.App": "1.0.0", "Alsi.App.Database": "1.0.0", "Alsi.App.Devices": "1.0.0", "Alsi.Common.Utils": "1.0.0", "Alsi.Fuzz.Core": "1.0.0", "FreeSql.Provider.Sqlite": "3.5.106", "Microsoft.NET.Test.Sdk": "17.5.0", "Shouldly": "4.3.0", "Xunit.SkippableFact": "1.5.23", "coverlet.collector": "3.2.0", "xunit": "2.9.3", "xunit.runner.visualstudio": "3.0.2"}, "runtime": {"Alsi.Fuzz.UnitTests.dll": {}}}, "coverlet.collector/3.2.0": {}, "DiffEngine/11.3.0": {"dependencies": {"EmptyFiles": "4.4.0", "System.Management": "6.0.1"}, "runtime": {"lib/net6.0/DiffEngine.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "EmptyFiles/4.4.0": {"runtime": {"lib/net6.0/EmptyFiles.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "FreeSql/3.5.106": {"runtime": {"lib/netstandard2.1/FreeSql.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "FreeSql.Provider.Sqlite/3.5.106": {"dependencies": {"FreeSql": "3.5.106", "System.Data.SQLite.Core": "*********"}, "runtime": {"lib/netstandard2.0/FreeSql.Provider.Sqlite.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}}, "Microsoft.CodeCoverage/17.5.0": {"runtime": {"lib/netcoreapp3.1/Microsoft.VisualStudio.CodeCoverage.Shim.dll": {"assemblyVersion": "********", "fileVersion": "17.500.222.62001"}}}, "Microsoft.CSharp/4.7.0": {}, "Microsoft.NET.Test.Sdk/17.5.0": {"dependencies": {"Microsoft.CodeCoverage": "17.5.0", "Microsoft.TestPlatform.TestHost": "17.5.0"}}, "Microsoft.TestPlatform.ObjectModel/17.5.0": {"dependencies": {"NuGet.Frameworks": "5.11.0", "System.Reflection.Metadata": "1.6.0"}, "runtime": {"lib/netcoreapp3.1/Microsoft.TestPlatform.CoreUtilities.dll": {"assemblyVersion": "********", "fileVersion": "********"}, "lib/netcoreapp3.1/Microsoft.TestPlatform.PlatformAbstractions.dll": {"assemblyVersion": "********", "fileVersion": "********"}, "lib/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.ObjectModel.dll": {"assemblyVersion": "********", "fileVersion": "********"}}, "resources": {"lib/netcoreapp3.1/cs/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/cs/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/de/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/es/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/fr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/it/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ja/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/ko/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pl/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/pt-BR/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/ru/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/tr/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.TestPlatform.CoreUtilities.resources.dll": {"locale": "zh-Han<PERSON>"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.VisualStudio.TestPlatform.ObjectModel.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.TestPlatform.TestHost/17.5.0": {"dependencies": {"Microsoft.TestPlatform.ObjectModel": "17.5.0", "Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/netcoreapp3.1/Microsoft.TestPlatform.CommunicationUtilities.dll": {"assemblyVersion": "********", "fileVersion": "********"}, "lib/netcoreapp3.1/Microsoft.TestPlatform.CrossPlatEngine.dll": {"assemblyVersion": "********", "fileVersion": "********"}, "lib/netcoreapp3.1/Microsoft.TestPlatform.Utilities.dll": {"assemblyVersion": "********", "fileVersion": "********"}, "lib/netcoreapp3.1/Microsoft.VisualStudio.TestPlatform.Common.dll": {"assemblyVersion": "********", "fileVersion": "********"}, "lib/netcoreapp3.1/testhost.dll": {"assemblyVersion": "********", "fileVersion": "********"}}, "resources": {"lib/netcoreapp3.1/cs/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/cs/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/cs/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "cs"}, "lib/netcoreapp3.1/de/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/de/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/de/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "de"}, "lib/netcoreapp3.1/es/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/es/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/es/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "es"}, "lib/netcoreapp3.1/fr/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/fr/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/fr/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "fr"}, "lib/netcoreapp3.1/it/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/it/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/it/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "it"}, "lib/netcoreapp3.1/ja/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ja/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ja/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "ja"}, "lib/netcoreapp3.1/ko/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/ko/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/ko/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "ko"}, "lib/netcoreapp3.1/pl/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pl/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pl/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "pl"}, "lib/netcoreapp3.1/pt-BR/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/pt-BR/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/pt-BR/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "pt-BR"}, "lib/netcoreapp3.1/ru/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/ru/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/ru/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "ru"}, "lib/netcoreapp3.1/tr/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/tr/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/tr/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "tr"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hans/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "zh-Hans"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.TestPlatform.CommunicationUtilities.resources.dll": {"locale": "zh-Han<PERSON>"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.TestPlatform.CrossPlatEngine.resources.dll": {"locale": "zh-Han<PERSON>"}, "lib/netcoreapp3.1/zh-Hant/Microsoft.VisualStudio.TestPlatform.Common.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.3.27908"}}}, "NuGet.Frameworks/5.11.0": {"runtime": {"lib/netstandard2.0/NuGet.Frameworks.dll": {"assemblyVersion": "5.11.0.10", "fileVersion": "5.11.0.10"}}}, "Shouldly/4.3.0": {"dependencies": {"DiffEngine": "11.3.0", "EmptyFiles": "4.4.0", "Microsoft.CSharp": "4.7.0"}, "runtime": {"lib/netstandard2.0/Shouldly.dll": {"assemblyVersion": "4.3.0.0", "fileVersion": "4.3.0.0"}}}, "Stub.System.Data.SQLite.Core.NetStandard/*********": {"runtime": {"lib/netstandard2.1/System.Data.SQLite.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}}, "runtimeTargets": {"runtimes/linux-x64/native/SQLite.Interop.dll": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/SQLite.Interop.dll": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/SQLite.Interop.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "*********"}, "runtimes/win-x86/native/SQLite.Interop.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "*********"}}}, "System.CodeDom/6.0.0": {"runtime": {"lib/net6.0/System.CodeDom.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "System.Data.SQLite.Core/*********": {"dependencies": {"Stub.System.Data.SQLite.Core.NetStandard": "*********"}}, "System.Management/6.0.1": {"dependencies": {"System.CodeDom": "6.0.0"}, "runtime": {"lib/net6.0/System.Management.dll": {"assemblyVersion": "6.0.0.1", "fileVersion": "6.0.1623.17311"}}, "runtimeTargets": {"runtimes/win/lib/net6.0/System.Management.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "6.0.0.1", "fileVersion": "6.0.1623.17311"}}}, "System.Reflection.Metadata/1.6.0": {}, "Validation/2.5.51": {"runtime": {"lib/netstandard2.0/Validation.dll": {"assemblyVersion": "2.5.0.0", "fileVersion": "2.5.51.3930"}}}, "xunit/2.9.3": {"dependencies": {"xunit.analyzers": "1.18.0", "xunit.assert": "2.9.3", "xunit.core": "2.9.3"}}, "xunit.abstractions/2.0.3": {"runtime": {"lib/netstandard2.0/xunit.abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "xunit.analyzers/1.18.0": {}, "xunit.assert/2.9.3": {"runtime": {"lib/net6.0/xunit.assert.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "xunit.core/2.9.3": {"dependencies": {"xunit.extensibility.core": "2.9.3", "xunit.extensibility.execution": "2.9.3"}}, "xunit.extensibility.core/2.9.3": {"dependencies": {"xunit.abstractions": "2.0.3"}, "runtime": {"lib/netstandard1.1/xunit.core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "xunit.extensibility.execution/2.9.3": {"dependencies": {"xunit.extensibility.core": "2.9.3"}, "runtime": {"lib/netstandard1.1/xunit.execution.dotnet.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "xunit.runner.visualstudio/3.0.2": {}, "Xunit.SkippableFact/1.5.23": {"dependencies": {"Validation": "2.5.51", "xunit.extensibility.execution": "2.9.3"}, "runtime": {"lib/netstandard2.0/Xunit.SkippableFact.dll": {"assemblyVersion": "*******", "fileVersion": "1.5.23.18339"}}}, "Alsi.App/1.0.0": {"runtime": {"Alsi.App.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Alsi.App.Database/1.0.0": {"dependencies": {"Alsi.App": "1.0.0"}, "runtime": {"Alsi.App.Database.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Alsi.App.Devices/1.0.0": {"dependencies": {"Alsi.App": "1.0.0"}, "runtime": {"Alsi.App.Devices.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Alsi.Common.Utils/1.0.0": {"dependencies": {"Newtonsoft.Json": "13.0.3"}, "runtime": {"Alsi.Common.Utils.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Alsi.Fuzz.Core/1.0.0": {"dependencies": {"Alsi.App": "1.0.0", "Alsi.App.Database": "1.0.0", "Alsi.App.Devices": "1.0.0", "Alsi.Common.Utils": "1.0.0"}, "runtime": {"Alsi.Fuzz.Core.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog/*******": {"runtime": {"Serilog.dll": {"assemblyVersion": "*******", "fileVersion": "********"}}}, "ICSharpCode.SharpZipLib/********": {"runtime": {"ICSharpCode.SharpZipLib.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Owin/*******": {"runtime": {"Owin.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "System.Web.Http/*******": {"runtime": {"System.Web.Http.dll": {"assemblyVersion": "*******", "fileVersion": "5.3.61130.707"}}}, "Microsoft.Owin/*******": {"runtime": {"Microsoft.Owin.dll": {"assemblyVersion": "*******", "fileVersion": "4.200.222.26002"}}}, "Serilog.Sinks.Console/*******": {"runtime": {"Serilog.Sinks.Console.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Serilog.Sinks.File/*******": {"runtime": {"Serilog.Sinks.File.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Owin.Host.HttpListener/*******": {"runtime": {"Microsoft.Owin.Host.HttpListener.dll": {"assemblyVersion": "*******", "fileVersion": "4.200.222.26002"}}}, "Microsoft.Owin.Hosting/*******": {"runtime": {"Microsoft.Owin.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "4.200.222.26002"}}}, "System.Net.Http.Formatting/*******": {"runtime": {"System.Net.Http.Formatting.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.61130.707"}}}, "System.Web.Http.Owin/*******": {"runtime": {"System.Web.Http.Owin.dll": {"assemblyVersion": "*******", "fileVersion": "5.3.61130.707"}}}, "vxlapi_NET/**********": {"runtime": {"vxlapi_NET.dll": {"assemblyVersion": "**********", "fileVersion": "**********"}}}, "Interop.TsCANApi/*******": {"runtime": {"Interop.TsCANApi.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Newtonsoft.Json.Bson/*******": {"runtime": {"Newtonsoft.Json.Bson.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.2.22727"}}}}}, "libraries": {"Alsi.Fuzz.UnitTests/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "coverlet.collector/3.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-xjY8xBigSeWIYs4I7DgUHqSNoGqnHi7Fv7/7RZD02rvZyG3hlsjnQKiVKVWKgr9kRKgmV+dEfu8KScvysiC0Wg==", "path": "coverlet.collector/3.2.0", "hashPath": "coverlet.collector.3.2.0.nupkg.sha512"}, "DiffEngine/11.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-k0ZgZqd09jLZQjR8FyQbSQE86Q7QZnjEzq1LPHtj1R2AoWO8sjV5x+jlSisL7NZAbUOI4y+7Bog8gkr9WIRBGw==", "path": "diffengine/11.3.0", "hashPath": "diffengine.11.3.0.nupkg.sha512"}, "EmptyFiles/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-gwJEfIGS7FhykvtZoscwXj/XwW+mJY6UbAZk+qtLKFUGWC95kfKXnj8VkxsZQnWBxJemM/q664rGLN5nf+OHZw==", "path": "emptyfiles/4.4.0", "hashPath": "emptyfiles.4.4.0.nupkg.sha512"}, "FreeSql/3.5.106": {"type": "package", "serviceable": true, "sha512": "sha512-PgQuwtsjz3CH3M92ViEyRCej7og0X0xM+Of707zW2SW9GGIQqmHOkCrdfNTnmUb9vY1W+juOjp92WwS3j/8/rg==", "path": "freesql/3.5.106", "hashPath": "freesql.3.5.106.nupkg.sha512"}, "FreeSql.Provider.Sqlite/3.5.106": {"type": "package", "serviceable": true, "sha512": "sha512-tcLM85BNndoPl0ZTiBbn5yzz/ysYn+6YsVh7n4kHxTtDTTfUbSu5H1C4ed+fbjmknS+A2e2WwdM8iKq1lqRhBg==", "path": "freesql.provider.sqlite/3.5.106", "hashPath": "freesql.provider.sqlite.3.5.106.nupkg.sha512"}, "Microsoft.CodeCoverage/17.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-6FQo0O6LKDqbCiIgVQhJAf810HSjFlOj7FunWaeOGDKxy8DAbpHzPk4SfBTXz9ytaaceuIIeR6hZgplt09m+ig==", "path": "microsoft.codecoverage/17.5.0", "hashPath": "microsoft.codecoverage.17.5.0.nupkg.sha512"}, "Microsoft.CSharp/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA==", "path": "microsoft.csharp/4.7.0", "hashPath": "microsoft.csharp.4.7.0.nupkg.sha512"}, "Microsoft.NET.Test.Sdk/17.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-IJ4eSPcsRbwbAZehh1M9KgejSy0u3d0wAdkJytfCh67zOaCl5U3ltruUEe15MqirdRqGmm/ngbjeaVeGapSZxg==", "path": "microsoft.net.test.sdk/17.5.0", "hashPath": "microsoft.net.test.sdk.17.5.0.nupkg.sha512"}, "Microsoft.TestPlatform.ObjectModel/17.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QwiBJcC/oEA1kojOaB0uPWOIo4i6BYuTBBYJVhUvmXkyYqZ2Ut/VZfgi+enf8LF8J4sjO98oRRFt39MiRorcIw==", "path": "microsoft.testplatform.objectmodel/17.5.0", "hashPath": "microsoft.testplatform.objectmodel.17.5.0.nupkg.sha512"}, "Microsoft.TestPlatform.TestHost/17.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-X86aikwp9d4SDcBChwzQYZihTPGEtMdDk+9t64emAl7N0Tq+OmlLAoW+Rs+2FB2k6QdUicSlT4QLO2xABRokaw==", "path": "microsoft.testplatform.testhost/17.5.0", "hashPath": "microsoft.testplatform.testhost.17.5.0.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "NuGet.Frameworks/5.11.0": {"type": "package", "serviceable": true, "sha512": "sha512-eaiXkUjC4NPcquGWzAGMXjuxvLwc6XGKMptSyOGQeT0X70BUZObuybJFZLA0OfTdueLd3US23NBPTBb6iF3V1Q==", "path": "nuget.frameworks/5.11.0", "hashPath": "nuget.frameworks.5.11.0.nupkg.sha512"}, "Shouldly/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-sDetrWXrl6YXZ4HeLsdBoNk3uIa7K+V4uvIJ+cqdRa5DrFxeTED7VkjoxCuU1kJWpUuBDZz2QXFzSxBtVXLwRQ==", "path": "shouldly/4.3.0", "hashPath": "shouldly.4.3.0.nupkg.sha512"}, "Stub.System.Data.SQLite.Core.NetStandard/*********": {"type": "package", "serviceable": true, "sha512": "sha512-WfrqQg6WL+r4H1sVKTenNj6ERLXUukUxqcjH1rqPqXadeIWccTVpydESieD7cZ/NWQVSKLYIHuoBX5du+BFhIQ==", "path": "stub.system.data.sqlite.core.netstandard/*********", "hashPath": "stub.system.data.sqlite.core.netstandard.*********.nupkg.sha512"}, "System.CodeDom/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-CPc6tWO1LAer3IzfZufDBRL+UZQcj5uS207NHALQzP84Vp/z6wF0Aa0YZImOQY8iStY0A2zI/e3ihKNPfUm8XA==", "path": "system.codedom/6.0.0", "hashPath": "system.codedom.6.0.0.nupkg.sha512"}, "System.Data.SQLite.Core/*********": {"type": "package", "serviceable": true, "sha512": "sha512-vADIqqgpxaC5xR6qOV8/KMZkQeSDCfmmWpVOtQx0oEr3Yjq2XdTxX7+jfE4+oO2xPovAbYiz6Q5cLRbSsDkq6Q==", "path": "system.data.sqlite.core/*********", "hashPath": "system.data.sqlite.core.*********.nupkg.sha512"}, "System.Management/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-10J1D0h/lioojphfJ4Fuh5ZUThT/xOVHdV9roGBittKKNP2PMjrvibEdbVTGZcPra1399Ja3tqIJLyQrc5Wmhg==", "path": "system.management/6.0.1", "hashPath": "system.management.6.0.1.nupkg.sha512"}, "System.Reflection.Metadata/1.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-COC1aiAJjCoA5GBF+QKL2uLqEBew4JsCkQmoHKbN3TlOZKa2fKLz5CpiRQKDz0RsAOEGsVKqOD5bomsXq/4STQ==", "path": "system.reflection.metadata/1.6.0", "hashPath": "system.reflection.metadata.1.6.0.nupkg.sha512"}, "Validation/2.5.51": {"type": "package", "serviceable": true, "sha512": "sha512-g/Aug7PVWaenlJ0QUyt/mEetngkQNsMCuNeRVXbcJED1nZS7JcK+GTU4kz3jcQ7bFuKfi8PF4ExXH7XSFNuSLQ==", "path": "validation/2.5.51", "hashPath": "validation.2.5.51.nupkg.sha512"}, "xunit/2.9.3": {"type": "package", "serviceable": true, "sha512": "sha512-TlXQBinK35LpOPKHAqbLY4xlEen9TBafjs0V5KnA4wZsoQLQJiirCR4CbIXvOH8NzkW4YeJKP5P/Bnrodm0h9Q==", "path": "xunit/2.9.3", "hashPath": "xunit.2.9.3.nupkg.sha512"}, "xunit.abstractions/2.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-pot1I4YOxlWjIb5jmwvvQNbTrZ3lJQ+jUGkGjWE3hEFM0l5gOnBWS+H3qsex68s5cO52g+44vpGzhAt+42vwKg==", "path": "xunit.abstractions/2.0.3", "hashPath": "xunit.abstractions.2.0.3.nupkg.sha512"}, "xunit.analyzers/1.18.0": {"type": "package", "serviceable": true, "sha512": "sha512-OtFMHN8yqIcYP9wcVIgJrq01AfTxijjAqVDy/WeQVSyrDC1RzBWeQPztL49DN2syXRah8TYnfvk035s7L95EZQ==", "path": "xunit.analyzers/1.18.0", "hashPath": "xunit.analyzers.1.18.0.nupkg.sha512"}, "xunit.assert/2.9.3": {"type": "package", "serviceable": true, "sha512": "sha512-/Kq28fCE7MjOV42YLVRAJzRF0WmEqsmflm0cfpMjGtzQ2lR5mYVj1/i0Y8uDAOLczkL3/jArrwehfMD0YogMAA==", "path": "xunit.assert/2.9.3", "hashPath": "xunit.assert.2.9.3.nupkg.sha512"}, "xunit.core/2.9.3": {"type": "package", "serviceable": true, "sha512": "sha512-BiAEvqGvyme19wE0wTKdADH+NloYqikiU0mcnmiNyXaF9HyHmE6sr/3DC5vnBkgsWaE6yPyWszKSPSApWdRVeQ==", "path": "xunit.core/2.9.3", "hashPath": "xunit.core.2.9.3.nupkg.sha512"}, "xunit.extensibility.core/2.9.3": {"type": "package", "serviceable": true, "sha512": "sha512-kf3si0YTn2a8J8eZNb+zFpwfoyvIrQ7ivNk5ZYA5yuYk1bEtMe4DxJ2CF/qsRgmEnDr7MnW1mxylBaHTZ4qErA==", "path": "xunit.extensibility.core/2.9.3", "hashPath": "xunit.extensibility.core.2.9.3.nupkg.sha512"}, "xunit.extensibility.execution/2.9.3": {"type": "package", "serviceable": true, "sha512": "sha512-yMb6vMESlSrE3Wfj7V6cjQ3S4TXdXpRqYeNEI3zsX31uTsGMJjEw6oD5F5u1cHnMptjhEECnmZSsPxB6ChZHDQ==", "path": "xunit.extensibility.execution/2.9.3", "hashPath": "xunit.extensibility.execution.2.9.3.nupkg.sha512"}, "xunit.runner.visualstudio/3.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-oXbusR6iPq0xlqoikjdLvzh+wQDkMv9If58myz9MEzldS4nIcp442Btgs2sWbYWV+caEluMe2pQCZ0hUZgPiow==", "path": "xunit.runner.visualstudio/3.0.2", "hashPath": "xunit.runner.visualstudio.3.0.2.nupkg.sha512"}, "Xunit.SkippableFact/1.5.23": {"type": "package", "serviceable": true, "sha512": "sha512-JlKobLTlsGcuJ8OtoodxL63bUagHSVBnF+oQ2GgnkwNqK+XYjeYyhQasULi5Ebx1MNDGNbOMplQYr89mR+nItQ==", "path": "xunit.skippablefact/1.5.23", "hashPath": "xunit.skippablefact.1.5.23.nupkg.sha512"}, "Alsi.App/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Alsi.App.Database/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Alsi.App.Devices/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Alsi.Common.Utils/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Alsi.Fuzz.Core/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Serilog/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "ICSharpCode.SharpZipLib/********": {"type": "reference", "serviceable": false, "sha512": ""}, "Owin/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "System.Web.Http/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Microsoft.Owin/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Serilog.Sinks.Console/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Serilog.Sinks.File/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Microsoft.Owin.Host.HttpListener/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Microsoft.Owin.Hosting/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "System.Net.Http.Formatting/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "System.Web.Http.Owin/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "vxlapi_NET/**********": {"type": "reference", "serviceable": false, "sha512": ""}, "Interop.TsCANApi/*******": {"type": "reference", "serviceable": false, "sha512": ""}, "Newtonsoft.Json.Bson/*******": {"type": "reference", "serviceable": false, "sha512": ""}}}