import axios, { AxiosResponse } from 'axios';
import { USE_MOCK, mockApi } from '@/mock/mockApi';
import { CaseResult } from '@/api/interoperationApi';

// 修改 WhiteListFrame 接口，添加 transmitter、receivers 和 IsExt
export interface WhiteListFrame {
  id: number;
  name: string;
  dlc: number;
  isExt: boolean;
  transmitter: string;
  receivers: string[];
}

// 安全配置信息接口
export interface SecurityConfigInfo {
  hasDll: boolean;
  dllFileName?: string;
  dllSize?: number;
}

export interface CaseConfigDto {
  // 基本属性 - 移除 whiteListIds, dlc, limitValues
  whiteListFrames: WhiteListFrame[]; // 新增：用于替代 whiteListIds 和 dlc
  selectedNodeName?: string;        // 新增：保存选中的目标节点名称

  // NM唤醒相关配置
  enableNmWakeup: boolean;
  nmWakeupId: number;
  nmWakeupIsExt: boolean;
  nmWakeupDlc: number;
  nmWakeupData: number[];
  nmWakeupCommunicationType: string;
  nmWakeupCycleMs: number;
  nmWakeupDelayMs: number;
  // 诊断通信相关配置
  diagReqId: number;
  diagReqIsExt: boolean;
  diagResId: number;
  diagTimeoutMs: number;
  isDutMtuLessThan4096?: boolean;   // 新增：DUT MTU小于4096的标识
  enableDiagFallbackRequest?: boolean; // 新增：当诊断请求无响应时，是否发送其它诊断请求
  diagFallbackRequestPayload?: number[]; // 新增：备用诊断请求数据

  // 安全配置相关
  securityInfo?: SecurityConfigInfo;  // 显示用
  securityDllPath?: string;           // 新选择的DLL路径
  removeSecurityDll?: boolean;        // 是否移除现有DLL
}

// 向后兼容的类型别名，保持接口一致性
export type CaseConfig = CaseConfigDto;

export interface CaseConfigFromDbc {
  whiteListFrames: WhiteListFrame[]; // 帧列表
  nodeNames: string[];               // 新增：节点名称列表
}

export interface SecurityDllPathResponse {
  path: string;
}

const BASE_URL = '/api/caseconfig';

export const caseApi = {
  getCaseConfig(): Promise<AxiosResponse<CaseConfigDto>> {
    if (USE_MOCK) {
      return mockApi.case.getCaseConfig();
    }
    return axios.get(`${BASE_URL}`);
  },

  updateCaseConfig(config: CaseConfigDto): Promise<AxiosResponse<CaseConfigDto>> {
    if (USE_MOCK) {
      return mockApi.case.updateCaseConfig(config);
    }
    return axios.post(`${BASE_URL}/update`, config);
  },

  importDbc(): Promise<AxiosResponse<CaseConfigFromDbc>> {
    if (USE_MOCK) {
      return mockApi.case.importDbc();
    }
    return axios.get(`${BASE_URL}/import-dbc`);
  },

  // 新增：选择安全DLL文件
  selectSecurityDll(): Promise<AxiosResponse<SecurityDllPathResponse>> {
    if (USE_MOCK) {
      return mockApi.case.selectSecurityDll();
    }
    return axios.post(`${BASE_URL}/select-security-dll`);
  }
};

export default caseApi;
