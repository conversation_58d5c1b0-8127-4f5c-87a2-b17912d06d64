import axios, { AxiosResponse } from 'axios'
import { USE_MOCK, mockApi } from '@/mock/mockApi'
import { TestPlanHistory } from '@/api/testPlanHistoryApi';

// 定义测试计划相关的类型
export interface TestPlanManifest {
  name: string;
  description: string;
  created?: string;
  modified?: string;
}

export interface TestPlan {
  path: string;
  manifest: TestPlanManifest;
  config?: any;
  [key: string]: any;
}

// 调整TestPlanHistoryItem类型，与TestPlanHistory保持一致
export interface TestPlanHistoryItem {
  id: string;
  filePath: string; // 与path同义
  planName: string;
  lastAccessTime: string; // 与lastAccessed同义
  lastModified: string;
  isDeleted: boolean;
}

const BASE_URL = '/api/testPlan'

// 类型转换辅助函数，将TestPlanHistory转换为TestPlanHistoryItem
const convertToHistoryItems = (histories: TestPlanHistory[]): TestPlanHistoryItem[] => {
  return histories.map(history => ({
    id: history.id,
    filePath: history.filePath,
    planName: history.planName,
    lastAccessTime: history.lastAccessTime,
    lastModified: history.lastModified,
    isDeleted: history.isDeleted
  }));
};

export const testPlanApi = {
  // 在文件浏览器中打开测试计划
  openInExplorer: (): Promise<AxiosResponse<TestPlan>> => {
    if (USE_MOCK) {
      return mockApi.testPlan.openInExplorer();
    }
    return axios.get(`${BASE_URL}/OpenInExplorer`);
  },

  // 从指定路径打开测试计划
  open: (path: string): Promise<AxiosResponse<TestPlan>> => {
    if (USE_MOCK) {
      return mockApi.testPlan.open(path);
    }
    return axios.post(`${BASE_URL}/open`, { path });
  },

  // 创建测试计划
  create: (testPlanData: { name: string; description: string; folder: string }): Promise<AxiosResponse<TestPlan>> => {
    if (USE_MOCK) {
      return mockApi.testPlan.create(testPlanData);
    }
    return axios.post(`${BASE_URL}/create`, testPlanData);
  },

  // 获取测试计划历史记录 - 修改调用到历史API
  getHistory: (): Promise<AxiosResponse<TestPlanHistoryItem[]>> => {
    if (USE_MOCK) {
      // 改为调用历史API而不是测试计划API
      return mockApi.testPlanHistory.get().then(response => {
        const convertedData = convertToHistoryItems(response.data);
        return {
          ...response,
          data: convertedData
        };
      });
    }
    return axios.get(`${BASE_URL}/history`);
  },

  // 获取当前测试计划
  getCurrentPlan: (): Promise<AxiosResponse<TestPlan>> => {
    if (USE_MOCK) {
      return mockApi.testPlan.getCurrentPlan();
    }
    return axios.get(`${BASE_URL}/current`);
  },

  // 关闭测试计划
  close: (): Promise<AxiosResponse<void>> => {
    if (USE_MOCK) {
      return mockApi.testPlan.close();
    }
    return axios.post(`${BASE_URL}/close`);
  },

  // 更新测试计划基本信息
  updateBasicInfo: (description: string): Promise<AxiosResponse<TestPlan>> => {
    if (USE_MOCK) {
      return mockApi.testPlan.updateBasicInfo(description);
    }
    return axios.post(`${BASE_URL}/updateBasicInfo`, { description });
  },

  // 检查文件是否存在
  checkFileExists: (path: string): Promise<AxiosResponse<{exists: boolean}>> => {
    if (USE_MOCK) {
      // 模拟实现，可以根据需要调整
      if (mockApi.testPlan.checkFileExists) {
        return mockApi.testPlan.checkFileExists(path);
      } else {
        // 创建一个符合AxiosResponse类型的对象
        return Promise.resolve({
          data: { exists: true },
          status: 200,
          statusText: 'OK',
          headers: {},
          config: { headers: {} } as any
        });
      }
    }
    return axios.post(`${BASE_URL}/checkFileExists`, { path });
  }
}

export default testPlanApi
