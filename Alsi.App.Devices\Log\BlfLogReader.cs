using Alsi.App.Devices.Core;
using System.Collections.Generic;
using System.Runtime.InteropServices;

namespace Alsi.Common.Log
{
    public class BlfLogReader
    {
        // 定义与 C++ 回调匹配的委托类型  
        public delegate void CanMessageCallBack(VBLCANMessage canMessage);
        public delegate void CanfdMessageCallBack(VBLCANFDMessage64 canfdMessage);
        public delegate void LinMessageCallBack(VBLLINMessage linMessage);
        public delegate void CanErrorFrameCallBack(VBLCANErrorFrame canErrorFrame);
        public delegate void CanfdErrorFrameCallBack(VBLCANFDErrorFrame64 canfdErrorFrame);

        [DllImport("OperateBlf.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern void RegisterCanCallback(CanMessageCallBack callback);

        [DllImport("OperateBlf.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern void RegisterCanfdCallback(CanfdMessageCallBack canfdCallback);

        [DllImport("OperateBlf.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern void RegisterLinCallback(LinMessageCallBack linCallback);

        [DllImport("OperateBlf.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern void RegisterCanErrorFrameCallback(CanErrorFrameCallBack canErrorFrameCallback);

        [DllImport("OperateBlf.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern void RegisterCanfdErrorFrameCallback(CanfdErrorFrameCallBack canfdErrorFrameCallback);

        [DllImport("OperateBlf.dll", CallingConvention = CallingConvention.Cdecl)]
        public static extern int ReadBlf(string blfPath);

        public static List<CanFrame> Frames { get; set; } = new List<CanFrame>();

        public static GCHandle readCanHandle;
        public static GCHandle readCanfdHandle;
        public static GCHandle readCanErrorHandle;
        public static GCHandle readCanfdErrorHandle;

        public static void ReceiveCanMessage(VBLCANMessage canMessage)
        {
            var id = canMessage.mID;
            var isExt = false;
            if ((id & 0x8000_0000) != 0)
            {
                id &= 0x7FFF_FFFF;
                isExt = true;
            }

            var canFrame = new CanFrame
            {
                Channel = (byte)canMessage.mChannel,
                Data = canMessage.mData,
                Dlc = canMessage.mDLC,
                Id = (int)id,
                TimeUS = canMessage.mHeader.mObjectTimeStamp / 1000,
                IsTx = canMessage.mFlags == 1,
                IsExt = isExt
            };
            Frames.Add(canFrame);
        }

        public static void ReceiveCanfdMsg(VBLCANFDMessage64 canfdMessage)
        {
            var id = canfdMessage.mID;
            var isExt = false;
            if ((id & 0x8000_0000) != 0)
            {
                id &= 0x7FFF_FFFF;
                isExt = true;
            }

            var canfdFrame = new CanFrame
            {
                TimeUS = (canfdMessage.mHeader.mObjectTimeStamp / 1000),
                Channel = canfdMessage.mChannel,
                Data = canfdMessage.mData,
                Id = (int)id,
                IsTx = canfdMessage.mDir == 1,
                Dlc = canfdMessage.mDLC,
                IsExt = isExt
            };
            if (canfdMessage.mFlags == 0x7000)
            {
                canfdFrame.IsBrs = true;
                canfdFrame.IsEsi = true;
            }
            else if (canfdMessage.mFlags == 0x5000)
            {
                canfdFrame.IsBrs = false;
                canfdFrame.IsEsi = true;
            }
            else if (canfdMessage.mFlags == 0x3000)
            {
                canfdFrame.IsBrs = true;
                canfdFrame.IsEsi = false;
            }
            Frames.Add(canfdFrame);
        }

        public static void ReceiveCanErrorFrame(VBLCANErrorFrame errorFrame)
        {
            var canFrame = new CanFrame
            {
                Channel = (byte)errorFrame.mChannel,
                Id = -1,
                TimeUS = errorFrame.mHeader.mObjectTimeStamp / 1000,
                IsTx = false
            };
            Frames.Add(canFrame);
        }

        public static void ReceiveCanfdErrorFrame(VBLCANFDErrorFrame64 errorFrame)
        {
            var canFrame = new CanFrame
            {
                Channel = errorFrame.mChannel,
                Id = -1,
                TimeUS = errorFrame.mHeader.mObjectTimeStamp / 1000,
                IsTx = false
            };
            Frames.Add(canFrame);
        }


        public static CanFrame[] ReadBlfFile(string blfFilePath)
        {
            var canMessageDelegate = new CanMessageCallBack(ReceiveCanMessage);
            RegisterCanCallback(canMessageDelegate);

            var canfdMessageDelegate = new CanfdMessageCallBack(ReceiveCanfdMsg);
            RegisterCanfdCallback(canfdMessageDelegate);

            var canErrorFrameDelegate = new CanErrorFrameCallBack(ReceiveCanErrorFrame);
            RegisterCanErrorFrameCallback(canErrorFrameDelegate);

            var canfdErrorFrameDelegate = new CanfdErrorFrameCallBack(ReceiveCanfdErrorFrame);
            RegisterCanfdErrorFrameCallback(canfdErrorFrameDelegate);
            readCanHandle = GCHandle.Alloc(canMessageDelegate);
            readCanfdHandle = GCHandle.Alloc(canfdMessageDelegate);
            readCanErrorHandle = GCHandle.Alloc(canErrorFrameDelegate);
            readCanfdErrorHandle = GCHandle.Alloc(canfdErrorFrameDelegate);
            Frames.Clear();
            var result = ReadBlf(blfFilePath);
            return Frames.ToArray();
        }

        public static void CleanUp()
        {
            if (readCanHandle.IsAllocated)
            {
                readCanHandle.Free();
            }
            if (readCanfdHandle.IsAllocated)
            {
                readCanfdHandle.Free();
            }
            if (readCanErrorHandle.IsAllocated)
            {
                readCanErrorHandle.Free();
            }
            if (readCanfdErrorHandle.IsAllocated)
            {
                readCanfdErrorHandle.Free();
            }
        }
    }
}
