﻿using System.Xml.Serialization;

namespace Alsi.App.Devices.Vector
{
    public class VectorInfo
    {
        [XmlAttribute]
        public int PortHandle { get; set; }

        [XmlAttribute]
        public ulong AccessMask { get; set; }

        [XmlAttribute]
        public ulong PermissionMask { get; set; }

        [XmlAttribute]
        public int HWIndex { get; set; }

        [XmlAttribute]
        public ulong ChanelMask { get; set; }

        [XmlAttribute]
        public byte HWChanel { get; set; }

        [XmlAttribute]
        public byte ChanelIndex { get; set; }

        [XmlAttribute]
        public int VectorDeviceType { get; set; }
    }
}
