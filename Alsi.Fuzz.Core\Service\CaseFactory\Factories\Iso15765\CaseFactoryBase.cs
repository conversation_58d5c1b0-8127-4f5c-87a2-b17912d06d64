using Alsi.Fuzz.Core.Service.CaseFactory.Core;
using Alsi.Fuzz.Core.Utils;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Alsi.Fuzz.Core.Service.CaseFactory.Factories.Iso15765
{
    public abstract class CaseFactoryBase : ICaseFactory
    {
        public IsoType IsoType => IsoType.Iso15765;

        public abstract CaseMutation[] Generate(MutationOptions options);

        protected static Random random = new Random();

        public static byte[] UniformSampleBytes(byte min, byte max, int sampleCount)
        {
            return new UniformSampler(random).UniformSampleBytes(min, max, sampleCount);
        }

        public static int[] UniformSample(int min, int max, int sampleCount)
        {
            return new UniformSampler(random).UniformSample(min, max, sampleCount);
        }

        public static T[] UniformSample<T>(T[] source, int sampleCount)
        {
            return new UniformSampler(random).UniformSample(source, sampleCount);
        }

        protected static int[] RandomUniqueInts(int min, int max, int length)
        {
            var uniqueNumbers = new HashSet<int>();

            while (uniqueNumbers.Count < length)
            {
                var number = random.Next(min, max + 1);
                uniqueNumbers.Add(number);
            }

            return uniqueNumbers.OrderBy(x => x).ToArray();
        }

        protected static byte[] RandomUniqueBytes(byte min, byte max, int length)
        {
            var uniqueNumbers = new HashSet<byte>();

            while (uniqueNumbers.Count < length)
            {
                var number = (byte)random.Next(min, max + 1);
                uniqueNumbers.Add(number);
            }

            return uniqueNumbers.OrderBy(x => x).ToArray();
        }

        protected static byte[] GetTpParameters(byte[] tpParameters, int sf_dl)
        {
            if (tpParameters.Length >= sf_dl)
            {
                return tpParameters.Take(sf_dl).ToArray();
            }
            else
            {
                var list = new List<byte>();
                while (tpParameters.Length > 0)
                {
                    foreach (var tpParameter in tpParameters)
                    {
                        if (list.Count >= sf_dl)
                        {
                            return list.ToArray();
                        }

                        list.Add(tpParameter);
                    }
                }
            }
            return Array.Empty<byte>();
        }
    }
}
