using Alsi.App.Devices.Core;
using Alsi.App.Devices.Core.TransportLayer;
using System;
using System.Linq;

namespace Alsi.Fuzz.Tester.Testers
{
    public class DiagFallbackTester
    {
        public Response FallbackResponse { get; set; }

        public void Run(CaseContext caseContext)
        {
            if (!caseContext.CaseConfig.EnableDiagFallbackRequest)
            {
                return;
            }

            var payloadBytes = caseContext.CaseConfig.DiagFallbackRequestPayload
                .Select(x => (byte)x)
                .ToArray();

            var request = new Request
            {
                IsCanfd = caseContext.HardwareConfig.CommunicationType == CommunicationType.CanFd,
                Payload = payloadBytes,
                PayloadLength = payloadBytes.Length,
                RequestId = caseContext.CaseConfig.DiagReqId,
                RequestIsExt = caseContext.CaseConfig.DiagReqIsExt,
                FlowControlId = caseContext.CaseConfig.DiagReqId,
                ResponseId = caseContext.CaseConfig.DiagResId
            };

            FallbackResponse = null;

            var tpService = new TpService(new DiagParams());
            var response = tpService.Request(request, TimeSpan.FromMilliseconds(caseContext.CaseConfig.DiagTimeoutMs));
            if (response?.Payload != null)
            {
                FallbackResponse = response;
            }
        }
    }
}
