"use strict";(self["webpackChunkfuzz_web"]=self["webpackChunkfuzz_web"]||[]).push([[112],{5112:function(e,a,l){l.r(a),l.d(a,{default:function(){return E}});l(8111),l(2489),l(7588),l(1701),l(3579),l(7642),l(8004),l(3853),l(5876),l(2475),l(5024),l(1698);var t=l(6768),u=l(4232),d=l(144),n=l(1219),s=l(7477),i=l(4373),o=l(3144);const r="/api/caseconfig",c={getCaseConfig(){return o.Xo?o.Z0.case.getCaseConfig():i.A.get(`${r}`)},updateCaseConfig(e){return o.Xo?o.Z0.case.updateCaseConfig(e):i.A.post(`${r}/update`,e)},importDbc(){return o.Xo?o.Z0.case.importDbc():i.A.get(`${r}/import-dbc`)},selectSecurityDll(){return o.Xo?o.Z0.case.selectSecurityDll():i.A.post(`${r}/select-security-dll`)}};const m={class:"case-setting-container"},p={class:"toolbar top-toolbar"},v={class:"toolbar-left"},b={class:"content-area"},k={class:"card-container"},g={key:0,class:"import-note"},F={key:1,class:"node-selection-required"},f={class:"card-container"},y={class:"nm-wakeup-row"},C={class:"card-container"},h={class:"uds-row"},D={class:"uds-row"},x={class:"uds-row"},W={class:"card-container"},I={class:"security-input-group"},w={class:"security-buttons"},R={key:0,class:"selected-dll-info"},V={key:1,class:"selected-dll-info"},N={class:"toolbar bottom-toolbar"};var _=(0,t.pM)({__name:"CaseSetting",setup(e){const a=(0,d.KR)(!1),l=(0,d.KR)({whiteListFrames:[],enableNmWakeup:!0,nmWakeupId:1343,nmWakeupIsExt:!1,nmWakeupDlc:8,nmWakeupData:[63,80,255,255,255,255,255,255],nmWakeupCommunicationType:"Can",nmWakeupCycleMs:100,nmWakeupDelayMs:2e3,diagReqId:1841,diagReqIsExt:!1,diagResId:1585,diagTimeoutMs:500,isDutMtuLessThan4096:!1,enableDiagFallbackRequest:!1,diagFallbackRequestPayload:[16,1],securityInfo:{hasDll:!1,dllFileName:void 0,dllSize:0}}),i=(0,d.KR)([]),o=(0,d.KR)(""),r=(0,d.KR)([]),_=(0,t.EW)((()=>o.value?r.value.filter((e=>e.transmitter===o.value||e.receivers.includes(o.value))):[])),L=(0,d.KR)(["case","nmWakeup","uds","security"]),U=(0,d.KR)(""),E=(0,d.KR)(""),S=(0,d.KR)(""),q=(0,d.KR)(""),M=(0,d.KR)(""),A=(0,d.KR)(""),T=(0,t.EW)({get:()=>"Can"===l.value.nmWakeupCommunicationType?"CAN Frame":"CANFD Frame",set:e=>{l.value.nmWakeupCommunicationType="CAN Frame"===e?"Can":"Canfd"}}),K=(0,d.KR)(null),P=(0,d.KR)(null),X=(0,d.KR)(!1),z={whiteListFrames:[],enableNmWakeup:!0,nmWakeupId:1343,nmWakeupIsExt:!1,nmWakeupDlc:8,nmWakeupData:[63,80,255,255,255,255,255,255],nmWakeupCommunicationType:"Can",nmWakeupCycleMs:100,nmWakeupDelayMs:2e3,diagReqId:1841,diagReqIsExt:!1,diagResId:1585,diagTimeoutMs:500,isDutMtuLessThan4096:!1,enableDiagFallbackRequest:!1,diagFallbackRequestPayload:[16,1],securityInfo:{hasDll:!1,dllFileName:void 0,dllSize:0}},B=e=>e.map((e=>"0x"+e.toString(16).toUpperCase())).join(","),$=e=>{try{const a=e.replace(/^0x/i,""),t=parseInt(a,16);if(isNaN(t))return void n.nk.error("Invalid ID format");l.value.nmWakeupId=t,U.value="0x"+t.toString(16).toUpperCase()}catch(a){n.nk.error("Invalid input value")}},Q=e=>{try{const a=e.replace(/^0x/i,""),t=parseInt(a,16);if(isNaN(t))return void n.nk.error("Invalid DLC format");if(t<0||t>64)return void n.nk.warning("DLC should be between 0 and 64");l.value.nmWakeupDlc=t,E.value="0x"+t.toString(16).toUpperCase()}catch(a){n.nk.error("Invalid input value")}},Z=e=>{try{const a=e.split(",").map((e=>e.trim())).filter((e=>e)).map((e=>parseInt(e,16)));if(a.length>8)return void n.nk.warning("Maximum 8 bytes allowed");if(a.some((e=>isNaN(e)||e<0||e>255)))return void n.nk.warning("Each byte must be between 0x00 and 0xFF");l.value.nmWakeupData=a}catch(a){n.nk.error("Invalid input format")}},j=e=>{try{const a=e.replace(/^0x/i,""),t=parseInt(a,16);if(isNaN(t))return void n.nk.error("Invalid ID format");l.value.diagReqId=t,q.value="0x"+t.toString(16).toUpperCase()}catch(a){n.nk.error("Invalid input value")}},J=e=>{try{const a=e.replace(/^0x/i,""),t=parseInt(a,16);if(isNaN(t))return void n.nk.error("Invalid ID format");l.value.diagResId=t,M.value="0x"+t.toString(16).toUpperCase()}catch(a){n.nk.error("Invalid input value")}},O=e=>{try{const a=e.split(",").map((e=>e.trim())).filter((e=>e)).map((e=>parseInt(e,16)));if(a.some((e=>isNaN(e)||e<0||e>255)))return void n.nk.warning("Each byte must be between 0x00 and 0xFF");l.value.diagFallbackRequestPayload=a}catch(a){n.nk.error("Invalid input format")}},G=()=>{l.value=JSON.parse(JSON.stringify(z)),U.value="0x"+z.nmWakeupId.toString(16).toUpperCase(),E.value="0x"+z.nmWakeupDlc.toString(16).toUpperCase(),S.value=B(z.nmWakeupData),q.value="0x"+z.diagReqId.toString(16).toUpperCase(),M.value="0x"+z.diagResId.toString(16).toUpperCase(),A.value=B(z.diagFallbackRequestPayload),K.value=null,P.value=null,X.value=!1,i.value=[],o.value="",r.value=[]},H=async()=>{a.value=!0;try{const e=await c.getCaseConfig();if(l.value=e.data,U.value="0x"+e.data.nmWakeupId.toString(16).toUpperCase(),S.value=B(e.data.nmWakeupData),q.value="0x"+e.data.diagReqId.toString(16).toUpperCase(),M.value="0x"+e.data.diagResId.toString(16).toUpperCase(),e.data.diagFallbackRequestPayload?A.value=B(e.data.diagFallbackRequestPayload):A.value="0x10,0x01",void 0===e.data.nmWakeupIsExt&&(l.value.nmWakeupIsExt=!1),void 0===e.data.nmWakeupDlc&&(l.value.nmWakeupDlc=8),void 0===e.data.diagReqIsExt&&(l.value.diagReqIsExt=!1),void 0===e.data.enableDiagFallbackRequest&&(l.value.enableDiagFallbackRequest=!1),void 0===e.data.diagFallbackRequestPayload&&(l.value.diagFallbackRequestPayload=[16,1]),E.value="0x"+l.value.nmWakeupDlc.toString(16).toUpperCase(),e.data.whiteListFrames.length>0){const a=e.data.whiteListFrames;r.value=a;const l=new Set;a.forEach((e=>{e.transmitter&&l.add(e.transmitter),e.receivers?.forEach((e=>l.add(e)))})),i.value=Array.from(l),e.data.selectedNodeName&&i.value.includes(e.data.selectedNodeName)?o.value=e.data.selectedNodeName:1===i.value.length&&(o.value=i.value[0])}}catch(e){n.nk.error("Failed to load configuration")}finally{a.value=!1}},Y=e=>{e||n.nk.warning("Please select a target ECU")},ee=async()=>{a.value=!0;try{const e={...l.value};e.whiteListFrames=r.value,e.selectedNodeName=o.value,K.value&&(e.securityDllPath=K.value),X.value&&(e.removeSecurityDll=!0);const a=await c.updateCaseConfig(e);l.value=a.data,K.value=null,P.value=null,X.value=!1,n.nk.success("Save successful")}catch(e){n.nk.error("Save failed")}finally{a.value=!1}},ae=(0,d.KR)(!1),le=async()=>{ae.value=!0;try{const e=await c.importDbc();r.value=e.data.whiteListFrames,i.value=e.data.nodeNames,o.value="",l.value.whiteListFrames=[],n.nk.success("DBC file imported successfully. Please select a target ECU.")}catch(e){if("UserCanceled"===e.response?.data)return;n.nk.error("InvalidFileFormat"===e.response?.data?"Invalid DBC file format":"Failed to import DBC file")}finally{ae.value=!1}},te=e=>{if(!e)return"0 B";const a=["B","KB","MB","GB"];let l=e,t=0;while(l>=1024&&t<a.length-1)l/=1024,t++;return`${l.toFixed(2)} ${a[t]}`},ue=async()=>{try{const e=await c.selectSecurityDll(),a=e.data.path;K.value=a,P.value=a.split("\\").pop()||a}catch(e){if("UserCanceled"===e.response?.data)return;n.nk.error("InvalidFileFormat"===e.response?.data?"Invalid DLL file format":"Failed to select DLL file")}},de=()=>{X.value=!0},ne=(0,t.EW)((()=>P.value?P.value:X.value?"DLL will be removed after save":l.value.securityInfo?.hasDll?`${l.value.securityInfo.dllFileName} (${te(l.value.securityInfo.dllSize)})`:"")),se=()=>{L.value=["case","nmWakeup","uds","security"]},ie=()=>{L.value=[]};return(0,t.sV)((()=>{H()})),(e,n)=>{const c=(0,t.g2)("font-awesome-icon"),K=(0,t.g2)("el-button"),z=(0,t.g2)("el-form-item"),B=(0,t.g2)("el-option"),H=(0,t.g2)("el-select"),te=(0,t.g2)("el-table-column"),oe=(0,t.g2)("el-table"),re=(0,t.g2)("el-form"),ce=(0,t.g2)("el-collapse-item"),me=(0,t.g2)("el-switch"),pe=(0,t.g2)("el-input"),ve=(0,t.g2)("el-input-number"),be=(0,t.g2)("el-collapse"),ke=(0,t.gN)("loading");return(0,t.bo)(((0,t.uX)(),(0,t.CE)("div",m,[(0,t.Lk)("div",p,[(0,t.Lk)("div",v,[(0,t.bF)(K,{onClick:se,type:"primary",size:"small",class:"expand-button"},{default:(0,t.k6)((()=>[(0,t.bF)(c,{icon:"up-right-and-down-left-from-center"}),n[18]||(n[18]=(0,t.Lk)("span",{class:"button-text"},"Expand All",-1))])),_:1}),(0,t.bF)(K,{onClick:ie,type:"primary",size:"small",class:"collapse-button"},{default:(0,t.k6)((()=>[(0,t.bF)(c,{icon:"down-left-and-up-right-to-center"}),n[19]||(n[19]=(0,t.Lk)("span",{class:"button-text"},"Collapse All",-1))])),_:1})])]),(0,t.Lk)("div",b,[(0,t.bF)(be,{modelValue:L.value,"onUpdate:modelValue":n[17]||(n[17]=e=>L.value=e)},{default:(0,t.k6)((()=>[(0,t.Lk)("div",k,[(0,t.bF)(ce,{title:"Case Setting",name:"case",class:"custom-card"},{default:(0,t.k6)((()=>[(0,t.bF)(re,{model:l.value,"label-width":"160px","label-position":"top"},{default:(0,t.k6)((()=>[(0,t.bF)(z,null,{default:(0,t.k6)((()=>[(0,t.bF)(K,{onClick:le,loading:ae.value,type:"primary",size:"small",style:{"margin-bottom":"15px"}},{default:(0,t.k6)((()=>n[20]||(n[20]=[(0,t.eW)(" Import DBC ")]))),_:1},8,["loading"])])),_:1}),i.value.length>0?((0,t.uX)(),(0,t.Wv)(z,{key:0,label:"Target ECU (DUT)"},{default:(0,t.k6)((()=>[(0,t.bF)(H,{modelValue:o.value,"onUpdate:modelValue":n[0]||(n[0]=e=>o.value=e),placeholder:"Select target ECU",style:{width:"100%"},onChange:Y},{default:(0,t.k6)((()=>[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(i.value,(e=>((0,t.uX)(),(0,t.Wv)(B,{key:e,label:e,value:e},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1})):(0,t.Q3)("",!0),(0,t.bF)(z,{label:`White List Frames (${_.value.length})`},{default:(0,t.k6)((()=>[0===r.value.length?((0,t.uX)(),(0,t.CE)("div",g,' No frames imported. Please use "Import DBC" button above to add frames. ')):o.value?((0,t.uX)(),(0,t.Wv)(oe,{key:2,data:_.value,style:{width:"100%"},"max-height":400,border:""},{default:(0,t.k6)((()=>[(0,t.bF)(te,{label:"ID","min-width":"120"},{default:(0,t.k6)((({row:e})=>[(0,t.eW)(" 0x"+(0,u.v_)(e.id.toString(16).toUpperCase().padStart(3,"0")),1)])),_:1}),(0,t.bF)(te,{prop:"name",label:"Name","min-width":"180"}),(0,t.bF)(te,{label:"DLC","min-width":"80"},{default:(0,t.k6)((({row:e})=>[(0,t.eW)(" 0x"+(0,u.v_)(e.dlc.toString(16).toUpperCase()),1)])),_:1}),(0,t.bF)(te,{label:"Ext Flag","min-width":"120"},{default:(0,t.k6)((({row:e})=>[(0,t.eW)((0,u.v_)(e.isExt?"CAN Extended":"CAN Standard"),1)])),_:1}),(0,t.bF)(te,{prop:"transmitter",label:"Transmitter","min-width":"120"}),(0,t.bF)(te,{label:"Receivers","min-width":"180"},{default:(0,t.k6)((({row:e})=>[(0,t.eW)((0,u.v_)(e.receivers.join(", ")),1)])),_:1})])),_:1},8,["data"])):((0,t.uX)(),(0,t.CE)("div",F," Please select a target ECU to view related frames "))])),_:1},8,["label"])])),_:1},8,["model"])])),_:1})]),(0,t.Lk)("div",f,[(0,t.bF)(ce,{title:"NM Wake Up Setting",name:"nmWakeup",class:"custom-card"},{default:(0,t.k6)((()=>[(0,t.bF)(re,{model:l.value,"label-width":"160px","label-position":"top"},{default:(0,t.k6)((()=>[(0,t.bF)(z,{label:"Enable NM Wake Up"},{default:(0,t.k6)((()=>[(0,t.bF)(me,{modelValue:l.value.enableNmWakeup,"onUpdate:modelValue":n[1]||(n[1]=e=>l.value.enableNmWakeup=e)},null,8,["modelValue"])])),_:1}),(0,t.Lk)("div",{class:(0,u.C4)({"disabled-form-content":!l.value.enableNmWakeup})},[(0,t.bF)(oe,{data:[{}],border:"",style:{width:"100%","margin-bottom":"15px"},"show-header":!0,class:"nm-table"},{default:(0,t.k6)((()=>[(0,t.bF)(te,{label:"ID",width:"180"},{default:(0,t.k6)((()=>[(0,t.bF)(pe,{modelValue:U.value,"onUpdate:modelValue":n[2]||(n[2]=e=>U.value=e),placeholder:"Enter ID in hex (e.g.: 53F)",onChange:$,disabled:!l.value.enableNmWakeup},null,8,["modelValue","disabled"])])),_:1}),(0,t.bF)(te,{label:"Ext Flag",width:"100"},{default:(0,t.k6)((()=>[(0,t.bF)(me,{modelValue:l.value.nmWakeupIsExt,"onUpdate:modelValue":n[3]||(n[3]=e=>l.value.nmWakeupIsExt=e),disabled:!l.value.enableNmWakeup},null,8,["modelValue","disabled"])])),_:1}),(0,t.bF)(te,{label:"Frame Type",width:"200"},{default:(0,t.k6)((()=>[(0,t.bF)(H,{modelValue:T.value,"onUpdate:modelValue":n[4]||(n[4]=e=>T.value=e),disabled:!l.value.enableNmWakeup},{default:(0,t.k6)((()=>[(0,t.bF)(B,{value:"CAN Frame",label:"CAN Frame"}),(0,t.bF)(B,{value:"CANFD Frame",label:"CANFD Frame"})])),_:1},8,["modelValue","disabled"])])),_:1}),(0,t.bF)(te,{label:"DLC",width:"150"},{default:(0,t.k6)((()=>[(0,t.bF)(pe,{modelValue:E.value,"onUpdate:modelValue":n[5]||(n[5]=e=>E.value=e),placeholder:"Enter DLC in hex (e.g.: 0x8)",onChange:Q,disabled:!l.value.enableNmWakeup},null,8,["modelValue","disabled"])])),_:1}),(0,t.bF)(te,{label:"Data"},{default:(0,t.k6)((()=>[(0,t.bF)(pe,{modelValue:S.value,"onUpdate:modelValue":n[6]||(n[6]=e=>S.value=e),placeholder:"Enter data bytes (e.g.: 0x3F,0x50,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF)",onChange:Z,disabled:!l.value.enableNmWakeup},null,8,["modelValue","disabled"])])),_:1})])),_:1}),(0,t.Lk)("div",y,[(0,t.bF)(z,{label:"Cycle (ms)",class:"nm-wakeup-item"},{default:(0,t.k6)((()=>[(0,t.bF)(ve,{modelValue:l.value.nmWakeupCycleMs,"onUpdate:modelValue":n[7]||(n[7]=e=>l.value.nmWakeupCycleMs=e),min:1,max:6e4,style:{width:"200px"},disabled:!l.value.enableNmWakeup},null,8,["modelValue","disabled"])])),_:1}),(0,t.bF)(z,{label:"Wake Up Delay (ms)",class:"nm-wakeup-item"},{default:(0,t.k6)((()=>[(0,t.bF)(ve,{modelValue:l.value.nmWakeupDelayMs,"onUpdate:modelValue":n[8]||(n[8]=e=>l.value.nmWakeupDelayMs=e),min:0,max:6e4,style:{width:"200px"},disabled:!l.value.enableNmWakeup},null,8,["modelValue","disabled"])])),_:1})])],2)])),_:1},8,["model"])])),_:1})]),(0,t.Lk)("div",C,[(0,t.bF)(ce,{title:"UDS Setting",name:"uds",class:"custom-card"},{default:(0,t.k6)((()=>[(0,t.bF)(re,{model:l.value,"label-width":"160px","label-position":"top"},{default:(0,t.k6)((()=>[(0,t.Lk)("div",h,[(0,t.bF)(z,{label:"Diagnostic Request ID",class:"uds-item"},{default:(0,t.k6)((()=>[(0,t.bF)(pe,{modelValue:q.value,"onUpdate:modelValue":n[9]||(n[9]=e=>q.value=e),placeholder:"Enter ID in hex (e.g.: 731)",onChange:j},null,8,["modelValue"])])),_:1}),(0,t.bF)(z,{label:"Ext Flag",class:"uds-item"},{default:(0,t.k6)((()=>[(0,t.bF)(me,{modelValue:l.value.diagReqIsExt,"onUpdate:modelValue":n[10]||(n[10]=e=>l.value.diagReqIsExt=e)},null,8,["modelValue"])])),_:1}),(0,t.bF)(z,{label:"Diagnostic Response ID",class:"uds-item"},{default:(0,t.k6)((()=>[(0,t.bF)(pe,{modelValue:M.value,"onUpdate:modelValue":n[11]||(n[11]=e=>M.value=e),placeholder:"Enter ID in hex (e.g.: 631)",onChange:J},null,8,["modelValue"])])),_:1})]),(0,t.Lk)("div",D,[(0,t.bF)(z,{label:"Diagnostic Timeout (ms)",class:"uds-item"},{default:(0,t.k6)((()=>[(0,t.bF)(ve,{modelValue:l.value.diagTimeoutMs,"onUpdate:modelValue":n[12]||(n[12]=e=>l.value.diagTimeoutMs=e),min:1,max:6e4,style:{width:"200px"}},null,8,["modelValue"])])),_:1}),(0,t.bF)(z,{label:"DUT MTU < 4096 bytes",class:"uds-item"},{default:(0,t.k6)((()=>[(0,t.bF)(me,{modelValue:l.value.isDutMtuLessThan4096,"onUpdate:modelValue":n[13]||(n[13]=e=>l.value.isDutMtuLessThan4096=e)},null,8,["modelValue"])])),_:1})]),(0,t.Lk)("div",x,[(0,t.bF)(z,{label:"Enable Fallback Request",class:"uds-item"},{default:(0,t.k6)((()=>[(0,t.bF)(me,{modelValue:l.value.enableDiagFallbackRequest,"onUpdate:modelValue":n[14]||(n[14]=e=>l.value.enableDiagFallbackRequest=e)},null,8,["modelValue"])])),_:1}),(0,t.bF)(z,{label:"Fallback Request Payload",class:"uds-item"},{default:(0,t.k6)((()=>[(0,t.bF)(pe,{modelValue:A.value,"onUpdate:modelValue":n[15]||(n[15]=e=>A.value=e),placeholder:"Enter data bytes (e.g.: 0x10,0x01)",onChange:O,disabled:!l.value.enableDiagFallbackRequest},null,8,["modelValue","disabled"])])),_:1})])])),_:1},8,["model"])])),_:1})]),(0,t.Lk)("div",W,[(0,t.bF)(ce,{title:"Security Access Setting",name:"security",class:"custom-card"},{default:(0,t.k6)((()=>[(0,t.bF)(re,{model:l.value,"label-width":"160px","label-position":"top"},{default:(0,t.k6)((()=>[(0,t.bF)(z,{label:"Security Access Dll"},{default:(0,t.k6)((()=>[(0,t.Lk)("div",I,[(0,t.bF)(pe,{modelValue:ne.value,"onUpdate:modelValue":n[16]||(n[16]=e=>ne.value=e),placeholder:"No Dll selected",readonly:"",class:"security-dll-input"},null,8,["modelValue"]),(0,t.Lk)("div",w,[l.value.securityInfo?.hasDll?(0,t.Q3)("",!0):((0,t.uX)(),(0,t.Wv)(K,{key:0,onClick:ue,type:"primary",icon:(0,d.R1)(s.Plus),title:"Select DLL"},null,8,["icon"])),l.value.securityInfo?.hasDll?((0,t.uX)(),(0,t.Wv)(K,{key:1,onClick:de,type:"danger",icon:(0,d.R1)(s.Delete),title:"Remove DLL"},null,8,["icon"])):(0,t.Q3)("",!0)])]),P.value?((0,t.uX)(),(0,t.CE)("div",R," DLL selected. Click Save to apply. ")):(0,t.Q3)("",!0),X.value?((0,t.uX)(),(0,t.CE)("div",V," DLL marked for removal. Click Save to apply. ")):(0,t.Q3)("",!0)])),_:1})])),_:1},8,["model"])])),_:1})])])),_:1},8,["modelValue"])]),(0,t.Lk)("div",N,[(0,t.bF)(K,{type:"primary",onClick:ee},{default:(0,t.k6)((()=>n[21]||(n[21]=[(0,t.eW)("Save")]))),_:1}),(0,t.bF)(K,{onClick:G,style:{"margin-left":"10px"}},{default:(0,t.k6)((()=>n[22]||(n[22]=[(0,t.eW)("Reset")]))),_:1})])])),[[ke,a.value]])}}}),L=l(1241);const U=(0,L.A)(_,[["__scopeId","data-v-58369462"]]);var E=U}}]);
//# sourceMappingURL=112.dc1abed5.js.map