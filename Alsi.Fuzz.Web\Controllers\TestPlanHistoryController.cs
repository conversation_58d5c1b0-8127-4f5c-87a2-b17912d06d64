using Alsi.Fuzz.Core.Service;
using Alsi.Fuzz.Web.Dto;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Web.Http;

namespace Alsi.Fuzz.Web.Controllers
{
    public class TestPlanHistoryController : WebControllerBase
    {
        private readonly ITestPlanHistoryService _historyService;

        public TestPlanHistoryController()
        {
            _historyService = new TestPlanHistoryService();
        }

        [HttpGet]
        public async Task<IHttpActionResult> Get(int count = 10)
        {
            var history = await _historyService.GetRecentHistoryAsync(count);
            var historyDtos = _mapper.Map<IEnumerable<TestPlanHistoryDto>>(history);
            return Ok(historyDtos);
        }

        [HttpDelete]
        public async Task<IHttpActionResult> Clear()
        {
            await _historyService.ClearHistoryAsync();
            return Ok();
        }

        [HttpDelete]
        [ActionName("deleteRecord")]
        public async Task<IHttpActionResult> DeleteRecord([FromBody] DeleteRecordRequest request)
        {
            if (string.IsNullOrEmpty(request.FilePath))
            {
                return BadRequest("FilePath cannot be empty");
            }

            await _historyService.DeleteRecordAsync(request.FilePath);
            return Ok();
        }
    }

    public class DeleteRecordRequest
    {
        public string FilePath { get; set; }
    }
}
