{"version": 3, "file": "js/766.95a4e0c1.js", "mappings": "uSA+DA,MAAMA,GAAYC,EAAAA,EAAAA,KAAI,GAChBC,GAAQC,EAAAA,EAAAA,MAERC,EAAgBA,KACpBJ,EAAUK,OAASL,EAAUK,KAAK,E,yqEC7DpC,MAAMC,GAA2B,OAAgB,EAAQ,CAAC,CAAC,YAAY,qBAEvE,Q,UCLA,MAAMC,EAAa,CAAEC,MAAO,sBACtBC,EAAa,CAAED,MAAO,gBACtBE,EAAa,CAAEF,MAAO,iBACtBG,EAAa,CAAEH,MAAO,gBACtBI,EAAa,CAAEJ,MAAO,aACtBK,EAAa,CAAEL,MAAO,gBAS5B,OAA4BM,EAAAA,EAAAA,IAAiB,CAC3CC,OAAQ,eACRC,KAAAA,CAAMC,GCWR,MAAMC,GAASC,EAAAA,EAAAA,MACfC,EAAAA,EAAgBC,UAAUH,GAE1B,MAAMI,EAAQF,EAAAA,EAAgBG,WACxBC,GAAWC,EAAAA,EAAAA,KAAS,IAAMH,EAAMI,cAEhCC,EAAeC,IACnBC,SAASC,MAAQF,EAAO,UAAUA,IAAS,MAAM,EDWnD,OCRAG,EAAAA,EAAAA,KAAUC,UACR,IACE,MAAMN,QAAoBN,EAAAA,EAAgBa,iBACtCP,EACFC,EAAYD,EAAYQ,SAASN,OAEjCO,EAAAA,GAAUC,MAAM,kCAChBlB,EAAOmB,KAAK,K,CAEd,MAAOD,GACPD,EAAAA,GAAUC,MAAM,4BAChBlB,EAAOmB,KAAK,I,KDHT,CAACC,EAAUC,KAChB,MAAMC,GAAyBC,EAAAA,EAAAA,IAAkB,eAC3CC,GAAqBD,EAAAA,EAAAA,IAAkB,WAE7C,OAAQE,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAoB,MAAOrC,EAAY,EAC3DsC,EAAAA,EAAAA,IAAaH,EAAoB,CAAElC,MAAO,iBAAmB,CAC3DsC,QAAQC,EAAAA,EAAAA,KAAS,IAAM,EACrBC,EAAAA,EAAAA,IAAoB,MAAOvC,EAAY,EACrCuC,EAAAA,EAAAA,IAAoB,MAAOtC,EAAY,EACrCsC,EAAAA,EAAAA,IAAoB,KAAM,MAAMC,EAAAA,EAAAA,IAAiBzB,EAASnB,OAAO6B,SAASN,MAAO,UAIvFsB,SAASH,EAAAA,EAAAA,KAAS,IAAM,EACtBC,EAAAA,EAAAA,IAAoB,MAAOrC,EAAY,EACrCqC,EAAAA,EAAAA,IAAoB,MAAOpC,EAAY,EACrCiC,EAAAA,EAAAA,IAAaM,MAEfH,EAAAA,EAAAA,IAAoB,MAAOnC,EAAY,EACrCgC,EAAAA,EAAAA,IAAaL,UAInBY,EAAG,KAEL,CAEJ,IErEA,MAAM,GAA2B,OAAgB,EAAQ,CAAC,CAAC,YAAY,qBAEvE,O", "sources": ["webpack://fuzz-web/./src/components/layout/SideNav.vue", "webpack://fuzz-web/./src/components/layout/SideNav.vue?1e1f", "webpack://fuzz-web/./src/views/TestPlanView.vue?9ba5", "webpack://fuzz-web/./src/views/TestPlanView.vue", "webpack://fuzz-web/./src/views/TestPlanView.vue?94d8"], "sourcesContent": ["<template>\r\n  <div class=\"side-nav-wrapper\">\r\n    <el-menu\r\n      :collapse=\"collapsed\"\r\n      :collapse-transition=\"false\"\r\n      class=\"side-nav\"\r\n      :class=\"{'side-nav-collapsed': collapsed}\"\r\n      router\r\n      :default-active=\"route.path\"\r\n    >\r\n      <el-menu-item index=\"/test-plan\">\r\n        <el-icon><Document /></el-icon>\r\n        <span>Basic Setting</span>\r\n      </el-menu-item>\r\n\r\n      <el-menu-item index=\"/test-plan/hardware\">\r\n        <el-icon><Monitor /></el-icon>\r\n        <span>Hardware Setting</span>\r\n      </el-menu-item>\r\n\r\n      <el-menu-item index=\"/test-plan/case-setting\">\r\n        <el-icon><Setting /></el-icon>\r\n        <span>Case Setting</span>\r\n      </el-menu-item>\r\n\r\n      <el-menu-item index=\"/test-plan/sequence-setting\">\r\n        <el-icon><Setting /></el-icon>\r\n        <span>Sequence Setting</span>\r\n      </el-menu-item>\r\n\r\n      <el-menu-item index=\"/test-plan/interoperation\">\r\n        <el-icon><Connection /></el-icon>\r\n        <span>Interoperation</span>\r\n      </el-menu-item>\r\n\r\n      <el-menu-item index=\"/test-plan/test-cases\">\r\n        <el-icon><List /></el-icon>\r\n        <span>Test Cases</span>\r\n      </el-menu-item>\r\n      \r\n      <el-menu-item index=\"/test-plan/test-run\">\r\n        <el-icon><VideoPlay /></el-icon>\r\n        <span>Test Run</span>\r\n      </el-menu-item>\r\n      \r\n      <el-menu-item index=\"/test-plan/test-results\">\r\n        <el-icon><DataAnalysis /></el-icon>\r\n        <span>Results</span>\r\n      </el-menu-item>\r\n    </el-menu>\r\n    <div class=\"sidebar-toggle\" @click=\"toggleSidebar\">\r\n      <el-icon :size=\"16\">\r\n        <component :is=\"collapsed ? 'ArrowRight' : 'ArrowLeft'\" />\r\n      </el-icon>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script setup>\r\nimport { ref } from 'vue'\r\nimport { Document, List, DataAnalysis, Connection, Monitor, VideoPlay, Setting } from '@element-plus/icons-vue'\r\nimport { useRoute } from 'vue-router'\r\n\r\nconst collapsed = ref(false)\r\nconst route = useRoute()\r\n\r\nconst toggleSidebar = () => {\r\n  collapsed.value = !collapsed.value\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.side-nav-wrapper {\r\n  position: relative;\r\n  height: 100%;\r\n}\r\n\r\n.side-nav {\r\n  height: 100%;\r\n  border-right: none;\r\n  transition: width 0.3s;\r\n  width: 200px;\r\n  border-right: 1px solid var(--el-border-color-light);\r\n}\r\n\r\n.side-nav-collapsed {\r\n  width: 64px;\r\n}\r\n\r\n.sidebar-toggle {\r\n  position: absolute;\r\n  right: 0;\r\n  top: 0;\r\n  height: 100%;\r\n  width: 12px;\r\n  background-color: transparent;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  cursor: pointer;\r\n  z-index: 100;\r\n  color: var(--el-text-color-secondary);\r\n  transition: background-color 0.3s;\r\n  \r\n  &:hover {\r\n    background-color: #f1f1f1;\r\n    color: #777;\r\n  }\r\n  \r\n  .el-icon {\r\n    font-size: 12px;\r\n  }\r\n}\r\n</style>\r\n", "import script from \"./SideNav.vue?vue&type=script&setup=true&lang=js\"\nexport * from \"./SideNav.vue?vue&type=script&setup=true&lang=js\"\n\nimport \"./SideNav.vue?vue&type=style&index=0&id=70c75d22&scoped=true&lang=css\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-70c75d22\"]])\n\nexport default __exports__", "import { defineComponent as _defineComponent } from 'vue'\nimport { toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, createVNode as _createVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\"\n\nconst _hoisted_1 = { class: \"testplan-container\" }\nconst _hoisted_2 = { class: \"panel-header\" }\nconst _hoisted_3 = { class: \"title-section\" }\nconst _hoisted_4 = { class: \"plan-content\" }\nconst _hoisted_5 = { class: \"side-menu\" }\nconst _hoisted_6 = { class: \"content-area\" }\n\nimport { onMounted, computed } from 'vue';\r\nimport { useRouter } from 'vue-router';\r\nimport { ElMessage } from 'element-plus';\r\nimport SideNav from '@/components/layout/SideNav.vue';\r\nimport { testPlanService } from '@/services/testPlanService';\r\n\r\n\nexport default /*@__PURE__*/_defineComponent({\n  __name: 'TestPlanView',\n  setup(__props) {\n\r\nconst router = useRouter();\r\ntestPlanService.setRouter(router);\r\n\r\nconst state = testPlanService.getState();\r\nconst testPlan = computed(() => state.currentPlan);\r\n\r\nconst updateTitle = (name?: string) => {\r\n  document.title = name ? `FUZZ - ${name}` : 'FUZZ';\r\n};\r\n\r\nonMounted(async () => {\r\n  try {\r\n    const currentPlan = await testPlanService.getCurrentPlan();\r\n    if (currentPlan) {\r\n      updateTitle(currentPlan.manifest.name);\r\n    } else {\r\n      ElMessage.error(\"No test plan is currently open\");\r\n      router.push('/');\r\n    }\r\n  } catch (error) {\r\n    ElMessage.error(\"Failed to load test plan\");\r\n    router.push('/');\r\n  }\r\n});\r\n\r\n\r\n\nreturn (_ctx: any,_cache: any) => {\n  const _component_router_view = _resolveComponent(\"router-view\")!\n  const _component_el_card = _resolveComponent(\"el-card\")!\n\n  return (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [\n    _createVNode(_component_el_card, { class: \"testplan-card\" }, {\n      header: _withCtx(() => [\n        _createElementVNode(\"div\", _hoisted_2, [\n          _createElementVNode(\"div\", _hoisted_3, [\n            _createElementVNode(\"h2\", null, _toDisplayString(testPlan.value?.manifest.name), 1)\n          ])\n        ])\n      ]),\n      default: _withCtx(() => [\n        _createElementVNode(\"div\", _hoisted_4, [\n          _createElementVNode(\"div\", _hoisted_5, [\n            _createVNode(SideNav)\n          ]),\n          _createElementVNode(\"div\", _hoisted_6, [\n            _createVNode(_component_router_view)\n          ])\n        ])\n      ]),\n      _: 1\n    })\n  ]))\n}\n}\n\n})", "<template>\r\n  <div class=\"testplan-container\">\r\n    <el-card class=\"testplan-card\">\r\n      <template #header>\r\n        <div class=\"panel-header\">\r\n          <div class=\"title-section\">\r\n            <h2>{{ testPlan?.manifest.name }}</h2>\r\n          </div>\r\n        </div>\r\n      </template>\r\n\r\n      <div class=\"plan-content\">\r\n        <div class=\"side-menu\">\r\n          <SideNav />\r\n        </div>\r\n        <div class=\"content-area\">\r\n          <router-view></router-view>\r\n        </div>\r\n      </div>\r\n    </el-card>\r\n  </div>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { onMounted, computed } from 'vue';\r\nimport { useRouter } from 'vue-router';\r\nimport { ElMessage } from 'element-plus';\r\nimport SideNav from '@/components/layout/SideNav.vue';\r\nimport { testPlanService } from '@/services/testPlanService';\r\n\r\nconst router = useRouter();\r\ntestPlanService.setRouter(router);\r\n\r\nconst state = testPlanService.getState();\r\nconst testPlan = computed(() => state.currentPlan);\r\n\r\nconst updateTitle = (name?: string) => {\r\n  document.title = name ? `FUZZ - ${name}` : 'FUZZ';\r\n};\r\n\r\nonMounted(async () => {\r\n  try {\r\n    const currentPlan = await testPlanService.getCurrentPlan();\r\n    if (currentPlan) {\r\n      updateTitle(currentPlan.manifest.name);\r\n    } else {\r\n      ElMessage.error(\"No test plan is currently open\");\r\n      router.push('/');\r\n    }\r\n  } catch (error) {\r\n    ElMessage.error(\"Failed to load test plan\");\r\n    router.push('/');\r\n  }\r\n});\r\n\r\n\r\n</script>\r\n\r\n<style scoped>\r\n.testplan-container {\r\n  height: 100%;\r\n  padding: 20px;\r\n  box-sizing: border-box;\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n.testplan-card {\r\n  height: 100%;\r\n  border-radius: 8px;\r\n  display: flex;\r\n  flex-direction: column;\r\n  overflow: hidden;\r\n}\r\n\r\n:deep(.el-card__body){\r\n  height: 100%;\r\n  padding: 0;\r\n}\r\n\r\n.panel-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  min-height: 32px;\r\n}\r\n\r\n.title-section h2 {\r\n  margin: 0;\r\n  color: var(--el-text-color-primary);\r\n  font-size: 20px;\r\n}\r\n\r\n.plan-content {\r\n  display: flex;\r\n  flex: 1;\r\n  overflow: hidden;\r\n  height: 100%;\r\n}\r\n\r\n.side-menu {\r\n  border-right: 1px solid var(--el-border-color-light);\r\n  user-select: none;\r\n}\r\n\r\n.content-area {\r\n  display: flex;\r\n  flex: 1;\r\n  overflow-y: auto;\r\n}\r\n\r\n.plan-content::-webkit-scrollbar {\r\n  width: 6px;\r\n}\r\n\r\n.plan-content::-webkit-scrollbar-thumb {\r\n  background-color: var(--el-border-color-light);\r\n  border-radius: 3px;\r\n}\r\n\r\n:deep(.el-card__header) {\r\n  padding: 15px 20px;\r\n  border-bottom: 1px solid var(--el-border-color-light);\r\n}\r\n\r\n.config-section {\r\n  padding: 15px;\r\n  min-height: 200px;\r\n}\r\n</style>\r\n", "import script from \"./TestPlanView.vue?vue&type=script&setup=true&lang=ts\"\nexport * from \"./TestPlanView.vue?vue&type=script&setup=true&lang=ts\"\n\nimport \"./TestPlanView.vue?vue&type=style&index=0&id=f1364f7e&scoped=true&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-f1364f7e\"]])\n\nexport default __exports__"], "names": ["collapsed", "ref", "route", "useRoute", "toggleSidebar", "value", "__exports__", "_hoisted_1", "class", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_hoisted_5", "_hoisted_6", "_defineComponent", "__name", "setup", "__props", "router", "useRouter", "testPlanService", "setRouter", "state", "getState", "testPlan", "computed", "currentPlan", "updateTitle", "name", "document", "title", "onMounted", "async", "getCurrentPlan", "manifest", "ElMessage", "error", "push", "_ctx", "_cache", "_component_router_view", "_resolveComponent", "_component_el_card", "_openBlock", "_createElementBlock", "_createVNode", "header", "_withCtx", "_createElementVNode", "_toDisplayString", "default", "SideNav", "_"], "sourceRoot": ""}