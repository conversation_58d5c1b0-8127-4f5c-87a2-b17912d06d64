<template>
  <div class="xml-viewer">
    <div class="editor-container">
        <XmlEditor
          :value="content"
          language="xml"
          @update:value="handleContentChange"
        />
      </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue'
import XmlEditor from '@/components/XmlEditor/XmlEditor.vue'

defineProps<{
  title: string;
  content: string;
}>()

const emit = defineEmits(['update:content']);

const handleContentChange = (value: string) => {
  emit('update:content', value);
}
</script>

<style scoped lang="scss">
.xml-viewer {
  flex: 1;
  overflow: hidden;
  height: 100%;
  
  .viewer-card {
    height: 100%;
  }
  
  .viewer-header {
    h3 {
      margin: 0;
    }
  }
  
  .editor-container {
    height: 100%;
  }
}

:deep(.el-card__body){
  height: 96%;
  padding: 0;
}
</style>
