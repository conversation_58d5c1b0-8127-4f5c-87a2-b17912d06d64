using Alsi.Fuzz.Core.Service.CaseFactory.Core;
using Alsi.Fuzz.Core.Service.CaseFactory.Factories.Iso14229.Consts;
using System.Collections.Generic;
using System.Linq;

namespace Alsi.Fuzz.Core.Service.CaseFactory.Factories.Iso14229
{
    public class G3121_G3122_G3123_CaseFactory : CaseFactoryBase
    {
        public override CaseMutation[] Generate(MutationOptions options)
        {
            var xmlServices = options.XmlServices;
            var supportedXmlServicesWithoutSubfunction = xmlServices
                // 仅遍历支持的服务
                .Where(x => !x.SubfunctionId.HasValue && x.IsSupported)
                // 仅遍历没有 Subfunction 的服务
                .Where(x => IsoUdsConsts.Services.FirstOrDefault(service => x.Id == service.Id)?.Subfunctions.Any() == false)
                .ToArray();

            var caseMutations = new List<CaseMutation>();

            foreach (var xmlService in supportedXmlServicesWithoutSubfunction)
            {
                var sid = xmlService.Id;

                var payload = new List<byte> { sid, };
                payload.AddRange(xmlService.Parameter2k);

                var caseMutation = CaseMutation.Create($"G3121-Sid{sid:X2}")
                    .MutatePayload(payload.ToArray())
                    .MutatePayloadLength(payload.Count);
                caseMutations.Add(caseMutation);

                for (var i = 1; i < payload.Count - 1; i++)
                {
                    caseMutation = CaseMutation.Create($"G3122-Sid{sid:X2}")
                        .MutatePayload(payload.ToArray())
                        .MutatePayloadLength(i);
                    caseMutations.Add(caseMutation);
                }

                var randomByte = new byte[] { 0, 0x55, 0xAA, 0xCC, 0xFF };
                {
                    var payloadBytes = payload.ToList();
                    payloadBytes.Insert(0, randomByte[options.Random.Next(0, 5)]);
                    caseMutation = CaseMutation.Create($"G3123-Sid{sid:X2}")
                        .MutatePayload(payloadBytes.ToArray())
                        .MutatePayloadLength(payloadBytes.Count + 1);
                    caseMutations.Add(caseMutation);
                }

                {
                    var payloadBytes = payload.ToList();
                    payloadBytes.Add(randomByte[options.Random.Next(0, 5)]);
                    caseMutation = CaseMutation.Create($"G3123-Sid{sid:X2}")
                        .MutatePayload(payloadBytes.ToArray())
                        .MutatePayloadLength(payloadBytes.Count + 1);
                    caseMutations.Add(caseMutation);
                }
            }

            return caseMutations.ToArray();
        }
    }
}
