using System;
using System.Collections.Generic;
using System.Linq;

namespace Alsi.App.Devices.Core.TransportLayer.Frames
{
    public class MultipleFrame
    {
        private const int maxPayloadLength = 0xFFF;
        private const int ffPayloadLength = 6;
        private const int cfPayloadLength = 7;
        private const int canfdffPayloadLength = 62;
        private const int canfdcfPayloadLength = 63;

        public FirstFrame FirstFrame { get; private set; }
        public ConsecutiveFrame[] ConsecutiveFrames { get; private set; }

        public static bool TryBuild(byte[] payload, int payloadLength, bool isCanfd, out MultipleFrame multipleFrame)
        {
            multipleFrame = null;

            if (isCanfd)
            {
                if (payloadLength < 63)
                {
                    return false;
                }
            }
            else
            {
                if (payloadLength < 7)
                {
                    return false;
                }
            }

            if (payloadLength > maxPayloadLength)
            {
                throw new Exception($"The payload length is greater then {maxPayloadLength}");
            }

            var data = new List<byte>();
            data.Add((byte)(0x10 | payloadLength / 0x100 % 0x10));
            data.Add((byte)(payloadLength % 0x100));
            if (isCanfd)
            {
                data.AddRange(payload.Take(canfdffPayloadLength));
            }
            else
            {
                data.AddRange(payload.Take(ffPayloadLength));
            }

            var ff = new FirstFrame(data.ToArray(), true);

            int cfCount;
            if (isCanfd && payloadLength > 7)
            {
                cfCount = (int)Math.Ceiling((double)(payloadLength - canfdffPayloadLength) / canfdcfPayloadLength);
            }
            else
            {
                cfCount = (int)Math.Ceiling((double)(payloadLength - ffPayloadLength) / cfPayloadLength);
            }

            var cfs = new ConsecutiveFrame[cfCount];
            var sequenceNumber = 0x1;
            for (var i = 0; i < cfCount; i++)
            {
                var cfData = new List<byte>();
                cfData.Add((byte)(0x20 | sequenceNumber));
                if (isCanfd && payloadLength > 7)
                {
                    cfData.AddRange(payload.Skip(canfdffPayloadLength + i * canfdcfPayloadLength).Take(canfdcfPayloadLength));

                }
                else
                {
                    cfData.AddRange(payload.Skip(6 + i * cfPayloadLength).Take(cfPayloadLength));

                }
                cfs[i] = new ConsecutiveFrame(cfData.ToArray(), true);

                if (sequenceNumber == 0xF)
                {
                    sequenceNumber = 0x0;
                }
                else
                {
                    sequenceNumber++;
                }
            }

            multipleFrame = new MultipleFrame
            {
                FirstFrame = ff,
                ConsecutiveFrames = cfs
            };

            return true;
        }
    }
}
