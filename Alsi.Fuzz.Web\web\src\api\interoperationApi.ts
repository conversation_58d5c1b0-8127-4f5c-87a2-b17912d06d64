import axios, { AxiosResponse } from 'axios'
import { USE_MOCK, mockApi } from '@/mock/mockApi'
import { TesterSnapshot } from './appApi';

// 基础响应类型
export interface BaseResponse {
  success: boolean;
  message: string;
}

// 执行状态枚举 - 与C#后端ExecutionState保持一致
export enum ExecutionState {
  Pending = 'Pending',
  Running = 'Running',
  Success = 'Success',
  Failure = 'Failure'
}

// 执行状态字符串类型 - 后端API实际返回的字符串值
export type ExecutionStateString = 'Pending' | 'Running' | 'Success' | 'Failure';

// 状态字符串与枚举值的映射函数
export const mapStateStringToEnum = (stateStr: ExecutionStateString): ExecutionState => {
  switch (stateStr) {
    case 'Pending': return ExecutionState.Pending;
    case 'Running': return ExecutionState.Running;
    case 'Success': return ExecutionState.Success;
    case 'Failure': return ExecutionState.Failure;
    default: return ExecutionState.Pending;
  }
};

// 用例结果类型
export interface CaseResult {
  id: number;
  testResultId: string; // Guid in C#
  sequenceId: string; // Guid in C#
  sequenceName: string;
  name: string;
  parameter: string;
  state: ExecutionStateString; // 使用字符串类型，与后端API一致
  begin: string | null;
  end: string | null;
  detail: string;
}

// 互操作测试状态类型
export interface InteroperationStatus {
  isRunning: boolean;
  progress: number;
  currentOperation: string;
  beginTime: string | null;
  endTime: string | null;
  summary: {
    passed: number;
    failed: number;
    skipped: number;
  };
  caseResults: CaseResult[] | null;
}

export function isTesterCompleted(tester: TesterSnapshot): boolean {
  return tester.processState === ExecutionState.Success || 
         tester.processState === ExecutionState.Failure;
}

const BASE_URL = '/api/interoperation'

export const interoperationApi = {
  // 启动互操作测试
  startTest: (): Promise<AxiosResponse<BaseResponse>> => {
    if (USE_MOCK) {
      return mockApi.interoperation.startTest();
    }
    return axios.post(`${BASE_URL}/start`);
  },

  // 停止互操作测试
  stopTest: (): Promise<AxiosResponse<BaseResponse>> => {
    if (USE_MOCK) {
      return mockApi.interoperation.stopTest();
    }
    return axios.post(`${BASE_URL}/stop`);
  },

  // 获取测试状态
  getStatus: (): Promise<AxiosResponse<TesterSnapshot>> => {
    if (USE_MOCK) {
      return mockApi.interoperation.getStatus();
    }
    return axios.get(`${BASE_URL}/status`);
  }
}

export default interoperationApi;
