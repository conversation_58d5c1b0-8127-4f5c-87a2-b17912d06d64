namespace Alsi.Fuzz.Core.Service.CaseFactory.Core
{
    public class MutationField
    {
        public MutationField(MutationFieldType fieldType, string value)
        {
            FieldType = fieldType;
            Value = value;
        }

        public MutationFieldType FieldType { get; set; }
        public string Value { get; set; }

        public string Serialize()
        {
            return $"{FieldType}={Value}";
        }
    }
}
