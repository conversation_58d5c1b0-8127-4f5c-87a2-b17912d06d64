{"files": [{"time": "2025-03-04T08:56:38.5583685+08:00", "path": ".browserslistrc"}, {"time": "2025-03-04T12:24:29.0514891+08:00", "path": ".eslintrc.js"}, {"time": "2025-03-04T08:56:38.5603717+08:00", "path": ".giti<PERSON>re"}, {"time": "2025-03-04T08:56:53.9228363+08:00", "path": "babel.config.js"}, {"time": "2025-05-03T12:35:09.1543728+08:00", "path": "package.json"}, {"time": "2025-03-04T08:56:54.0238532+08:00", "path": "README.md"}, {"time": "2025-03-04T13:58:26.5523706+08:00", "path": "tsconfig.json"}, {"time": "2025-03-12T13:46:16.8793871+08:00", "path": "vue.config.js"}, {"time": "2025-05-03T12:35:09.1443718+08:00", "path": "yarn.lock"}, {"time": "2025-03-05T12:53:06.3626117+08:00", "path": "public\\favicon.ico"}, {"time": "2025-03-10T15:58:01.6450768+08:00", "path": "public\\index.html"}, {"time": "2025-03-07T17:55:47.1152555+08:00", "path": "src\\App.vue"}, {"time": "2025-04-28T13:24:10.3482026+08:00", "path": "src\\main.ts"}, {"time": "2025-03-04T08:56:38.5643692+08:00", "path": "src\\shims-vue.d.ts"}, {"time": "2025-05-26T13:43:04.6198681+08:00", "path": "src\\api\\appApi.ts"}, {"time": "2025-04-27T13:29:42.6783650+08:00", "path": "src\\api\\caseApi.ts"}, {"time": "2025-03-20T08:55:15.2285638+08:00", "path": "src\\api\\explorerApi.ts"}, {"time": "2025-04-28T14:46:26.4551916+08:00", "path": "src\\api\\hardwareApi.ts"}, {"time": "2025-03-07T13:37:27.3326822+08:00", "path": "src\\api\\index.ts"}, {"time": "2025-03-27T14:34:25.1400694+08:00", "path": "src\\api\\interoperationApi.ts"}, {"time": "2025-03-31T12:26:02.8957982+08:00", "path": "src\\api\\sequenceApi.ts"}, {"time": "2025-04-28T14:35:05.7433546+08:00", "path": "src\\api\\testPlanApi.ts"}, {"time": "2025-04-28T14:35:28.3110135+08:00", "path": "src\\api\\testPlanHistoryApi.ts"}, {"time": "2025-03-31T12:39:55.0083684+08:00", "path": "src\\api\\testSuiteApi.ts"}, {"time": "2025-04-11T11:18:17.2266243+08:00", "path": "src\\components\\common\\CaseStateTag.vue"}, {"time": "2025-03-28T14:42:40.4544851+08:00", "path": "src\\components\\common\\TestStateTag.vue"}, {"time": "2025-03-24T12:39:30.4884039+08:00", "path": "src\\components\\common\\TimeDisplay.vue"}, {"time": "2025-03-28T10:07:09.6224904+08:00", "path": "src\\components\\Guide\\TestPlanGuide.vue"}, {"time": "2025-03-13T17:47:34.4231736+08:00", "path": "src\\components\\hardware\\DeviceChannelSelect.vue"}, {"time": "2025-04-28T14:47:57.6879315+08:00", "path": "src\\components\\hardware\\HardwareConfigPanel.vue"}, {"time": "2025-03-24T11:05:18.9724768+08:00", "path": "src\\components\\Interoperation\\CaseResultList.vue"}, {"time": "2025-03-19T14:16:17.2420847+08:00", "path": "src\\components\\Interoperation\\StatusCheckbox.vue"}, {"time": "2025-03-19T13:38:59.8541672+08:00", "path": "src\\components\\Interoperation\\TestControlPanel.vue"}, {"time": "2025-04-02T10:32:27.8466395+08:00", "path": "src\\components\\layout\\SideNav.vue"}, {"time": "2025-04-28T15:31:27.2110076+08:00", "path": "src\\components\\layout\\TestPlanStatusIndicator.vue"}, {"time": "2025-04-28T15:36:48.1324693+08:00", "path": "src\\components\\layout\\TopMenuBar.vue"}, {"time": "2025-04-27T15:11:27.9194717+08:00", "path": "src\\components\\test\\CaseDetailDialog.vue"}, {"time": "2025-05-03T15:26:05.3937180+08:00", "path": "src\\components\\test\\CaseList.vue"}, {"time": "2025-05-03T16:08:00.8041950+08:00", "path": "src\\components\\test\\TestMonitor.vue"}, {"time": "2025-04-24T14:09:24.0546841+08:00", "path": "src\\components\\TestCases\\GeneratedCasesPanel.vue"}, {"time": "2025-03-24T15:52:11.5268638+08:00", "path": "src\\components\\TestCases\\InteroperationResultPanel.vue"}, {"time": "2025-04-28T13:56:02.3098733+08:00", "path": "src\\components\\TestPlan\\CreateTestPlan.vue"}, {"time": "2025-04-28T15:34:17.0181971+08:00", "path": "src\\components\\TestPlan\\TestPlanHistory.vue"}, {"time": "2025-03-06T17:23:45.1717149+08:00", "path": "src\\components\\TestPlan\\types.ts"}, {"time": "2025-03-31T12:29:09.6550945+08:00", "path": "src\\components\\TestSuite\\XmlViewer.vue"}, {"time": "2025-03-12T15:09:38.6174746+08:00", "path": "src\\components\\XmlEditor\\XmlEditor.vue"}, {"time": "2025-03-28T15:24:52.9104329+08:00", "path": "src\\mock\\mockApi.ts"}, {"time": "2025-03-12T11:37:47.7776997+08:00", "path": "src\\mock\\mockData.ts"}, {"time": "2025-05-26T13:45:40.9594049+08:00", "path": "src\\mock\\modules\\mockAppApi.ts"}, {"time": "2025-05-03T16:01:18.4979225+08:00", "path": "src\\mock\\modules\\mockCaseApi.ts"}, {"time": "2025-03-20T10:03:55.9069879+08:00", "path": "src\\mock\\modules\\mockExplorerApi.ts"}, {"time": "2025-04-28T14:49:53.6245751+08:00", "path": "src\\mock\\modules\\mockHardwareApi.ts"}, {"time": "2025-03-27T14:30:39.8357471+08:00", "path": "src\\mock\\modules\\mockInteroperationApi.ts"}, {"time": "2025-03-31T12:26:01.6422690+08:00", "path": "src\\mock\\modules\\mockSequenceApi.ts"}, {"time": "2025-04-28T14:30:44.0669913+08:00", "path": "src\\mock\\modules\\mockTestPlanApi.ts"}, {"time": "2025-04-28T14:30:58.8841808+08:00", "path": "src\\mock\\modules\\mockTestPlanHistoryApi.ts"}, {"time": "2025-03-28T12:37:33.0474180+08:00", "path": "src\\mock\\modules\\mockTestSuiteApi.ts"}, {"time": "2025-03-24T15:06:43.9048389+08:00", "path": "src\\router\\index.ts"}, {"time": "2025-04-28T13:54:38.2291197+08:00", "path": "src\\services\\testPlanService.ts"}, {"time": "2025-03-06T15:05:40.1863753+08:00", "path": "src\\store\\index.ts"}, {"time": "2025-05-03T15:30:08.7723179+08:00", "path": "src\\styles\\element-variables.css"}, {"time": "2025-03-04T13:58:05.7893803+08:00", "path": "src\\types\\element-plus.d.ts"}, {"time": "2025-04-28T13:59:10.8388682+08:00", "path": "src\\utils\\errorHandler.ts"}, {"time": "2025-03-24T10:49:12.5935236+08:00", "path": "src\\utils\\status.ts"}, {"time": "2025-04-28T13:32:04.1698927+08:00", "path": "src\\views\\AboutView.vue"}, {"time": "2025-03-12T15:09:08.9426968+08:00", "path": "src\\views\\HomeView.vue"}, {"time": "2025-04-28T15:21:04.0058750+08:00", "path": "src\\views\\TestPlanView.vue"}, {"time": "2025-03-31T12:39:34.2472390+08:00", "path": "src\\views\\TestSuiteView.vue"}, {"time": "2025-04-02T10:09:06.8081249+08:00", "path": "src\\views\\testplan\\BasicSetting.vue"}, {"time": "2025-05-03T16:02:18.0587611+08:00", "path": "src\\views\\testplan\\CaseSetting.vue"}, {"time": "2025-04-02T10:09:24.5449425+08:00", "path": "src\\views\\testplan\\HardwareSetting.vue"}, {"time": "2025-04-11T10:58:56.5359369+08:00", "path": "src\\views\\testplan\\Interoperation.vue"}, {"time": "2025-04-28T13:24:28.4373821+08:00", "path": "src\\views\\testplan\\SequenceSetting.vue"}, {"time": "2025-04-24T14:10:21.9951309+08:00", "path": "src\\views\\testplan\\TestCases.vue"}, {"time": "2025-05-03T16:08:19.8624393+08:00", "path": "src\\views\\testplan\\TestResults.vue"}, {"time": "2025-05-26T14:56:44.2668350+08:00", "path": "src\\views\\testplan\\TestRun.vue"}], "buildTime": "2025-05-26T14:58:18.1594831+08:00"}