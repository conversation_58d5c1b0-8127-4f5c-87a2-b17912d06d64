<template>
  <div class="case-result-list">
    <!-- 添加状态筛选工具栏 -->
    <div class="filter-toolbar" v-if="caseResults && caseResults.length > 0">
      <div class="filter-left">
        <div class="filter-label">Filter</div>
        <div class="filter-checkboxes">
          <StatusCheckbox 
            v-for="(state, index) in stateTypes"
            :key="index"
            v-model="filters[state.value.toLowerCase()]"
            :status-type="state.value"
            :id="`filter-${state.value}`"
            :disabled="getStateCount(state.value) === 0"
            @update:modelValue="applyFilters"
          >
            {{ state.label }} ({{ getStateCount(state.value) }})
          </StatusCheckbox>
        </div>
      </div>
      
      <div class="filter-summary" v-if="caseResults && caseResults.length > 0">
        Showing {{ filteredResults.length }} of {{ caseResults.length }}
      </div>
    </div>
    
    <div class="list-content" v-if="filteredResults.length > 0">
      <div class="result-list">
        <div v-for="(item, index) in filteredResults" 
             :key="item.id || index" 
             class="result-item"
             :class="{ 'is-striped': index % 2 === 1 }">
          <div class="item-header">
            <div class="sequence-name">
              <el-tooltip 
                effect="dark" 
                :content="item.sequenceId" 
                placement="top" 
                :show-after="1000"
              >
                <span>{{ item.sequenceName }}</span>
              </el-tooltip>
              <span class="parameter" v-if="item.parameter">/ {{ item.parameter }}</span>
            </div>
            <div class="item-status">
              <span class="status-tag" :class="getStatusClass(item.state)">
                {{ getStatusText(item.state) }}
              </span>
            </div>
          </div>
          <div class="item-details">
            <span class="detail-text" v-if="item.detail">{{ item.detail }}</span>
            <TimeDisplay 
              :begin="item.begin" 
              :end="item.end" 
              :showDuration="true" 
            />
          </div>
        </div>
      </div>
    </div>
    
    <div class="empty-list" v-else>
      <el-empty description="No results" :image-size="80" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, ref, computed, watch } from 'vue';
import { CaseResult, ExecutionStateString } from '@/api/interoperationApi';
import { ElTooltip, ElEmpty } from 'element-plus';
import StatusCheckbox from './StatusCheckbox.vue';
import TimeDisplay from '@/components/common/TimeDisplay.vue';

const props = defineProps<{
  caseResults: CaseResult[] | null;
}>();

// 所有可能的状态类型
const stateTypes = [
  { value: 'Pending', label: 'Pending' },
  { value: 'Running', label: 'Running' },
  { value: 'Success', label: 'Success' },
  { value: 'Failure', label: 'Failure' }
];

// 筛选状态 - 默认全部不勾选，但显示全部结果
const filters = ref({
  pending: false,
  running: false,
  success: false,
  failure: false
});

// 是否有任何筛选被激活
const hasActiveFilters = computed(() => {
  return filters.value.pending || filters.value.running || 
         filters.value.success || filters.value.failure;
});

// 筛选后的结果
const filteredResults = computed(() => {
  if (!props.caseResults) return [];
  
  // 如果没有任何筛选被激活，显示所有结果
  if (!hasActiveFilters.value) {
    return props.caseResults;
  }
  
  return props.caseResults.filter(result => {
    switch (result.state) {
      case 'Pending': return filters.value.pending;
      case 'Running': return filters.value.running;
      case 'Success': return filters.value.success;
      case 'Failure': return filters.value.failure;
      default: return true;
    }
  });
});

// 获取特定状态的用例数量
const getStateCount = (state: ExecutionStateString): number => {
  if (!props.caseResults) return 0;
  return props.caseResults.filter(result => result.state === state).length;
};

// 应用筛选
const applyFilters = () => {
  // 筛选逻辑已通过计算属性自动应用
  console.log('Filters applied:', filters.value);
};

// 当caseResults变化时，保持筛选设置不变，因为默认已经是不勾选但显示全部
watch(() => props.caseResults, () => {
  // 不重置筛选条件，保持用户的筛选选择
}, { deep: true });

// 获取状态对应的Class
const getStatusClass = (state: ExecutionStateString): string => {
  switch (state) {
    case 'Success':
      return 'status-success';
    case 'Failure':
      return 'status-failure';
    case 'Running':
      return 'status-running';
    case 'Pending':
    default:
      return 'status-pending';
  }
};

// 获取状态的文本描述
const getStatusText = (state: ExecutionStateString): string => {
  switch (state) {
    case 'Success':
      return 'Passed';
    case 'Failure':
      return 'Failed';
    case 'Running':
      return 'Running';
    case 'Pending':
      return 'Pending';
    default:
      return 'Unknown';
  }
};

// 不再需要formatTime方法，由组件内部处理
// const formatTime = (begin: string | null, end: string | null): string => {
//   if (!begin) return '-';
//   
//   const beginDate = new Date(begin);
//   const beginStr = beginDate.toLocaleTimeString();
//   
//   if (!end) return beginStr;
//   
//   const endDate = new Date(end);
//   const duration = Math.round((endDate.getTime() - beginDate.getTime()) / 1000);
//   
//   return `${beginStr} (${duration}s)`;
// };

// 不再需要toggleFilter方法，由组件内部处理
// const toggleFilter = (stateType: string) => {
//   const key = stateType.toLowerCase() as keyof typeof filters.value;
//   filters.value[key] = !filters.value[key];
//   applyFilters();
// };
</script>

<style scoped lang="scss">
.case-result-list {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.list-content {
  flex: 1;
  overflow-y: auto;
}

.result-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.result-item {
  padding: 8px 10px;
  border-radius: 4px;
  font-size: 12px;
  background-color: var(--el-bg-color);
  border: 1px solid var(--el-border-color-lighter);
  
  &:hover {
    background-color: var(--el-fill-color-light);
  }
  
  &.is-striped {
    background-color: var(--el-fill-color-lighter);
  }
  
  .item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
    
    .sequence-name {
      font-weight: 500;
      color: var(--el-color-primary);
      cursor: default;
      
      .parameter {
        color: var(--el-text-color-secondary);
        margin-left: 2px;
        font-weight: normal;
      }
    }
  }
  
  .item-details {
    display: flex;
    justify-content: space-between;
    color: var(--el-text-color-secondary);
    font-size: 11px;
    
    .detail-text {
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

.status-tag {
  display: inline-block;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 11px;
  line-height: 1.2;
  
  &.status-success {
    background-color: rgba(var(--el-color-success-rgb), 0.1);
    color: var(--el-color-success);
  }
  
  &.status-failure {
    background-color: rgba(var(--el-color-danger-rgb), 0.1);
    color: var(--el-color-danger);
  }
  
  &.status-running {
    background-color: rgba(var(--el-color-warning-rgb), 0.1);
    color: var(--el-color-warning);
  }
  
  &.status-pending {
    background-color: rgba(var(--el-color-primary-rgb), 0.1); // 从info改为primary
    color: var(--el-color-primary); // 从info改为primary
  }
}

// 筛选工具栏样式
.filter-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding: 8px 10px;
  background-color: var(--el-fill-color-lighter);
  border-radius: 4px;
  
  .filter-left {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
  }
  
  .filter-label {
    font-size: 12px;
    font-weight: 500;
    color: var(--el-text-color-secondary);
    margin-right: 10px;
  }
  
  .filter-checkboxes {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
  }
  
  .filter-summary {
    font-size: 12px;
    color: var(--el-text-color-secondary);
    white-space: nowrap;
  }
}

// 移除自定义复选框相关的CSS代码，因为已经移到组件中

.empty-list {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  flex: 1;
}

// 响应式调整
@media screen and (max-width: 768px) {
  .item-details {
    flex-direction: column;
    gap: 2px;
    
    .time-text {
      margin-left: 0;
    }
  }

  .filter-toolbar {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
    
    .filter-summary {
      align-self: flex-end;
    }
  }
}
</style>
