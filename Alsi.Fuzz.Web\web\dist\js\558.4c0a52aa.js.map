{"version": 3, "file": "js/558.4c0a52aa.js", "mappings": "uQAGA,MAAMA,EAAa,CAAEC,MAAO,sBACtBC,EAAa,CAAED,MAAO,WACtBE,EAAa,CAAEF,MAAO,kBACtBG,EAAa,CAAEH,MAAO,gBACtBI,EAAa,CAAEJ,MAAO,oBACtBK,EAAa,CAAEL,MAAO,eACtBM,EAAa,CACjBC,IAAK,EACLP,MAAO,cAEHQ,EAAa,CACjBD,IAAK,EACLP,MAAO,qBAEHS,EAAa,CACjBF,IAAK,EACLP,MAAO,mBAEHU,EAAc,CAClBH,IAAK,EACLP,MAAO,qBAaT,OAA4BW,EAAAA,EAAAA,IAAiB,CAC3CC,OAAQ,UACRC,KAAAA,CAAMC,GC2DR,MAAMC,GAAiBC,EAAAA,EAAAA,IAAkB,IACnCC,GAAUD,EAAAA,EAAAA,KAAI,GACdE,GAAWF,EAAAA,EAAAA,KAAI,GACfG,GAAWH,EAAAA,EAAAA,KAAI,GACfI,GAAUJ,EAAAA,EAAAA,KAAI,GACdK,GAAWL,EAAAA,EAAAA,KAAI,GACfM,GAAiBN,EAAAA,EAAAA,KAAI,GACrBO,GAAYP,EAAAA,EAAAA,IAAoB,CACpCQ,aAAcC,EAAAA,GAAeC,QAC7BC,iBAAkB,GAClBC,WAAY,CACVC,GAAI,GACJC,iBAAkB,GAClBC,SAAU,GACVC,aAAc,GACdC,WAAY,EACZC,aAAc,EACdC,aAAc,GAEhBC,YAAa,KAETC,GAAcrB,EAAAA,EAAAA,IAAI,IAGlBsB,GAAsBtB,EAAAA,EAAAA,KAAI,GAC1BuB,GAAiBvB,EAAAA,EAAAA,IAAmB,MACpCwB,GAAexB,EAAAA,EAAAA,IAAmB,MAGlCyB,GAAezB,EAAAA,EAAAA,KAAI,GACnB0B,GAAW1B,EAAAA,EAAAA,IAAkB,IACnC,IAAI2B,EAAmB,EAGvB,MAAMC,GAAe5B,EAAAA,EAAAA,IAAyB,IAAI6B,KAGlD,IAAIC,EAAoC,KAGxC,MAAMC,GAAYC,EAAAA,EAAAA,KAAS,IAClBzB,EAAU0B,MAAMzB,eAAiBC,EAAAA,GAAeyB,UAGnDC,GAAWH,EAAAA,EAAAA,KAAS,IACjBzB,EAAU0B,MAAMzB,eAAiBC,EAAAA,GAAe2B,SAGnDC,GAAiBL,EAAAA,EAAAA,KAAS,KAC9BM,QAAQC,IAAI,2CAEPR,EAAUE,OAASE,EAASF,QAAUP,EAASO,OAAOO,OAAS,GAClEF,QAAQC,IAAI,wDACLb,EAASO,QAGlBK,QAAQC,IAAI,sDACLxC,EAAekC,UAIlBQ,EAAsBC,UAC1BzC,EAAQgC,OAAQ,EAChB,IACE,MAAMU,QAAiBC,EAAAA,GAAOC,gBAC9B9C,EAAekC,MAAQU,EAASG,I,CAIhC,MAAOC,GACPT,QAAQS,MAAM,eAAgBA,GAC9BC,EAAAA,GAAUD,MAAM,mC,CAChB,QACA9C,EAAQgC,OAAQ,C,GAKdgB,EAAmBA,KACvBR,GAAqB,EAIjBS,EAAqBR,UACzB,GAAoC,IAAhC3C,EAAekC,MAAMO,OAAzB,CAKAtC,EAAS+B,OAAQ,EACjB,UACQW,EAAAA,GAAOO,YACb7C,EAAe2B,OAAQ,EACvBe,EAAAA,GAAUI,QAAQ,gCAGZC,IACNC,G,CACA,MAAOP,GACPT,QAAQS,MAAM,UAAWA,GACzBC,EAAAA,GAAUD,MAAM,iC,CAChB,QACA7C,EAAS+B,OAAQ,C,OAjBjBe,EAAAA,GAAUO,QAAQ,qC,EAsBhBC,EAAqBd,UACzBtC,EAAQ6B,OAAQ,EAChB,UACQW,EAAAA,GAAOa,YACbT,EAAAA,GAAUI,QAAQ,+BAGZC,G,CACN,MAAON,GACPT,QAAQS,MAAM,UAAWA,GACzBC,EAAAA,GAAUD,MAAM,iC,CAChB,QACA3C,EAAQ6B,OAAQ,C,GAKdyB,EAAsBhB,UAC1BrC,EAAS4B,OAAQ,EACjB,UACQW,EAAAA,GAAOe,aACbX,EAAAA,GAAUI,QAAQ,gCAGZC,G,CACN,MAAON,GACPT,QAAQS,MAAM,UAAWA,GACzBC,EAAAA,GAAUD,MAAM,kC,CAChB,QACA1C,EAAS4B,OAAQ,C,GAKf2B,EAAoBlB,UACxBvC,EAAS8B,OAAQ,EACjB,UACQW,EAAAA,GAAOiB,WACbb,EAAAA,GAAUI,QAAQ,gCAGZC,G,CACN,MAAON,GACPT,QAAQS,MAAM,UAAWA,GACzBC,EAAAA,GAAUD,MAAM,gC,CAChB,QACA5C,EAAS8B,OAAQ,C,GAKfoB,EAAkBX,UACtB,IACE,IAAIC,EAEJ,GAAIlB,EAAaQ,MAEfU,QAAiBC,EAAAA,GAAOkB,gBACxBrC,EAAaQ,OAAQ,EAErB1B,EAAU0B,MAAQU,EAASG,KAC3BpB,EAASO,MAAQU,EAASG,KAAK1B,aAAe,GAG9C2C,IAGApC,EAAmBqC,EAAsBtC,EAASO,WAC7C,CAEL,MAAMgC,EAAaC,KAAKC,MAAMxC,EAAmB,KAAO,EACxDgB,QAAiBC,EAAAA,GAAOwB,mBAAmB,CACzCH,aACAI,SAAU,MAIZ9D,EAAU0B,MAAMzB,aAAemC,EAASG,KAAKtC,aAC7CD,EAAU0B,MAAMtB,iBAAmBgC,EAASG,KAAKnC,iBAI7CgC,EAASG,KAAKwB,iBAAiBC,QACjCC,EAAkB7B,EAASG,KAAKwB,gBAAgBC,OAChD5C,EAAmBqC,EAAsBtC,EAASO,O,CAOtDZ,EAAYY,OAAQ,IAAIwC,MAAOC,kBAG3BC,EAAAA,EAAAA,IAAkBpE,EAAU0B,QAAUH,IACxCmB,IACA2B,I,CAGF,MAAO7B,GACPT,QAAQS,MAAM,YAAaA,E,GAKzBiB,EAAyBa,IAC7BvC,QAAQC,IAAI,+BACZ,IAAK,IAAIuC,EAAI,EAAGA,EAAID,EAAMrC,OAAQsC,IAChC,GAAID,EAAMC,GAAGC,QAAUtE,EAAAA,GAAeC,QAEpC,OADA4B,QAAQC,IAAI,6BACLuC,EAIX,OADAxC,QAAQC,IAAI,6BACLsC,EAAMrC,MAAM,EAIfuB,EAAkBA,KACtBzB,QAAQC,IAAI,yBACZX,EAAaK,MAAM+C,QACnBtD,EAASO,MAAMgD,SAAQ,CAACC,EAAUC,KAChCvD,EAAaK,MAAMmD,IAAIF,EAASrE,GAAIsE,EAAM,IAE5C7C,QAAQC,IAAI,iCAAkCX,EAAaK,MAAMoD,KAAK,EAIlEb,EAAqBc,IAGzB,GAFAhD,QAAQC,IAAI,oCAAqC+C,EAAa9C,OAAQ,UAEjEd,EAASO,OAAmC,IAA1BP,EAASO,MAAMO,OAIpC,OAHAd,EAASO,MAAQqD,EACjBvB,SACAzB,QAAQC,IAAI,wCAKd,MAAMgD,EAAoD,GAC1D,IAAIC,EAAU,EACVC,EAAY,EAEhB,IAAK,MAAMC,KAAWJ,EAAc,CAClC,MAAMH,EAAQvD,EAAaK,MAAM0D,IAAID,EAAQ7E,IAC7C,QAAc+E,IAAVT,GAAuBA,EAAQzD,EAASO,MAAMO,OAEhD,GAAId,EAASO,MAAMkD,GAAOtE,KAAO6E,EAAQ7E,GACvC0E,EAAQM,KAAK,CAACV,QAAOW,KAAMJ,IAC3BF,QACK,CAELlD,QAAQyD,KAAK,mDACbhC,IACA,MAAMiC,EAAWpE,EAAaK,MAAM0D,IAAID,EAAQ7E,SAC/B+E,IAAbI,GAA0BA,EAAWtE,EAASO,MAAMO,QACtD+C,EAAQM,KAAK,CAACV,MAAOa,EAAUF,KAAMJ,IACrCF,KAEAC,G,MAIJA,G,CAKJF,EAAQN,SAAQ,EAAEE,QAAOW,KAAMG,MAC7BvE,EAASO,MAAMkD,GAASc,CAAW,IAGrC3D,QAAQC,IAAI,oCAAqCiD,EAAS,UAAWC,EAAU,EAI3EnC,EAAqBA,KAEzBsB,IAGAnD,EAAaQ,OAAQ,EAGrBL,EAAaK,MAAM+C,QAEnBlD,EAAqBoE,OAAOC,YAAY9C,EAAiB,IAAI,EAIzDuB,EAAoBA,KACpB9C,IACFsE,cAActE,GACdA,EAAqB,K,EAKnBuE,EAAkBC,IACtB9E,EAAaS,MAAQqE,EAAW9E,aAChCD,EAAeU,MAAQqE,EAAWzF,GAClCS,EAAoBW,OAAQ,CAAI,EAI5BsE,EAAoBA,KACxBjF,EAAoBW,OAAQ,EAC5BV,EAAeU,MAAQ,IAAI,EDlC7B,OCwCAuE,EAAAA,EAAAA,KAAU,KACR/D,IACAY,IAAkBoD,MAAK,KAEjB1E,EAAUE,QACZ3B,EAAe2B,OAAQ,EACvBqB,I,GAEF,KAIJoD,EAAAA,EAAAA,KAAY,KACV9B,GAAmB,IDrDd,CAAC+B,EAAUC,KAChB,MAAMC,GAAuBC,EAAAA,EAAAA,IAAkB,aACzCC,GAAyBD,EAAAA,EAAAA,IAAkB,eAC3CE,GAAsBF,EAAAA,EAAAA,IAAkB,YACxCG,GAAqBH,EAAAA,EAAAA,IAAkB,WAE7C,OAAQI,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAoB,MAAOpI,EAAY,EAC3DqI,EAAAA,EAAAA,IAAoB,MAAOnI,EAAY,CACrC2H,EAAO,KAAOA,EAAO,IAAKQ,EAAAA,EAAAA,IAAoB,KAAM,KAAM,kBAAmB,KAC7EA,EAAAA,EAAAA,IAAoB,MAAOlI,EAAY,CACnC6C,EAAUE,OAAUE,EAASF,OAc3BoF,EAAAA,EAAAA,IAAoB,IAAI,KAbvBH,EAAAA,EAAAA,OAAcI,EAAAA,EAAAA,IAAaT,EAAsB,CAChDtH,IAAK,EACLgI,KAAM,UACNlC,KAAM,QACNpF,QAASC,EAAS+B,MAClBuF,QAAStE,EACTuE,SAA0C,IAAhC1H,EAAekC,MAAMO,QAC9B,CACDkF,SAASC,EAAAA,EAAAA,KAAS,IAAMf,EAAO,KAAOA,EAAO,GAAK,EAChDgB,EAAAA,EAAAA,IAAiB,eAEnBC,EAAG,GACF,EAAG,CAAC,UAAW,cAErB9F,EAAUE,QAAUE,EAASF,QACzBiF,EAAAA,EAAAA,OAAcI,EAAAA,EAAAA,IAAaT,EAAsB,CAChDtH,IAAK,EACLgI,KAAM,UACNlC,KAAM,QACNpF,QAASG,EAAQ6B,MACjBuF,QAAShE,GACR,CACDkE,SAASC,EAAAA,EAAAA,KAAS,IAAMf,EAAO,KAAOA,EAAO,GAAK,EAChDgB,EAAAA,EAAAA,IAAiB,eAEnBC,EAAG,GACF,EAAG,CAAC,cACPR,EAAAA,EAAAA,IAAoB,IAAI,GAC3BlF,EAASF,QACLiF,EAAAA,EAAAA,OAAcI,EAAAA,EAAAA,IAAaT,EAAsB,CAChDtH,IAAK,EACLgI,KAAM,OACNlC,KAAM,QACNpF,QAASI,EAAS4B,MAClBuF,QAAS9D,GACR,CACDgE,SAASC,EAAAA,EAAAA,KAAS,IAAMf,EAAO,KAAOA,EAAO,GAAK,EAChDgB,EAAAA,EAAAA,IAAiB,gBAEnBC,EAAG,GACF,EAAG,CAAC,cACPR,EAAAA,EAAAA,IAAoB,IAAI,GAC3BtF,EAAUE,OAASE,EAASF,QACxBiF,EAAAA,EAAAA,OAAcI,EAAAA,EAAAA,IAAaT,EAAsB,CAChDtH,IAAK,EACLgI,KAAM,SACNlC,KAAM,QACNpF,QAASE,EAAS8B,MAClBuF,QAAS5D,GACR,CACD8D,SAASC,EAAAA,EAAAA,KAAS,IAAMf,EAAO,KAAOA,EAAO,GAAK,EAChDgB,EAAAA,EAAAA,IAAiB,cAEnBC,EAAG,GACF,EAAG,CAAC,cACPR,EAAAA,EAAAA,IAAoB,IAAI,QAGhCS,EAAAA,EAAAA,IAAaC,EAAAA,EAAa,CACxB,aAAcxH,EAAU0B,MACxB+F,QAAS1H,EAAe2B,OACvB,KAAM,EAAG,CAAC,aAAc,aAC3BmF,EAAAA,EAAAA,IAAoB,MAAOjI,EAAY,EACrCiI,EAAAA,EAAAA,IAAoB,MAAOhI,EAAY,EACrC0I,EAAAA,EAAAA,IAAab,EAAoB,CAC/BgB,OAAQ,QACRjJ,MAAO,mBACN,CACDkJ,QAAQP,EAAAA,EAAAA,KAAS,IAAM,EACrBP,EAAAA,EAAAA,IAAoB,MAAO/H,EAAY,CACrCuH,EAAO,KAAOA,EAAO,IAAKQ,EAAAA,EAAAA,IAAoB,OAAQ,KAAM,cAAe,IAC1E/E,EAAeJ,MAAMO,OAAS,IAC1B0E,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAoB,OAAQ7H,GAAY6I,EAAAA,EAAAA,IAAiB9F,EAAeJ,MAAMO,QAAU,SAAU,KACjH6E,EAAAA,EAAAA,IAAoB,IAAI,QAGhCK,SAASC,EAAAA,EAAAA,KAAS,IAAM,CACrB1H,EAAQgC,QACJiF,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAoB,MAAO3H,EAAY,EACpDsI,EAAAA,EAAAA,IAAaf,EAAwB,CACnCqB,KAAM,GACNC,SAAU,QAGmB,IAAhChG,EAAeJ,MAAMO,SACnB0E,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAoB,MAAO1H,EAAY,EACpDqI,EAAAA,EAAAA,IAAad,EAAqB,CAAEsB,YAAa,6BAElDpB,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAoB,MAAOzH,EAAa,EACrDoI,EAAAA,EAAAA,IAAaS,EAAAA,EAAU,CACrB1D,MAAOxC,EAAeJ,MACtBuG,aAAcnC,GACb,KAAM,EAAG,CAAC,gBAGvBwB,EAAG,SAITC,EAAAA,EAAAA,IAAaW,EAAAA,EAAkB,CAC7BT,QAAS1G,EAAoBW,MAC7B,mBAAoB2E,EAAO,KAAOA,EAAO,GAAM8B,GAAkBpH,EAAqBW,MAAQyG,GAC9FlH,aAAcA,EAAaS,MAC3B0G,aAAcpH,EAAeU,MAC7B2G,QAASrC,GACR,KAAM,EAAG,CAAC,UAAW,eAAgB,kBACxC,CAEJ,I,UE1eA,MAAMsC,GAA2B,OAAgB,EAAQ,CAAC,CAAC,YAAY,qBAEvE,O,4GCDA,GAA4BlJ,EAAAA,EAAAA,IAAiB,CAC3CC,OAAQ,eACRkJ,MAAO,CACL/D,MAAO,CAAC,GAEVlF,KAAAA,CAAMC,GCFR,MAAMgJ,EAAQhJ,EAIRiJ,GAAU/G,EAAAA,EAAAA,KAAoD,KAClE,OAAQ8G,EAAM/D,OACZ,KAAKtE,EAAAA,GAAeuI,QAClB,MAAO,UACT,KAAKvI,EAAAA,GAAeyB,QAClB,MAAO,UACT,KAAKzB,EAAAA,GAAewI,QAClB,MAAO,SACT,KAAKxI,EAAAA,GAAeC,QACpB,QACE,MAAO,O,IAIPwI,EAAoBnE,IACxB,OAAQA,GACN,KAAKtE,EAAAA,GAAeyB,QAClB,MAAO,UACT,KAAKzB,EAAAA,GAAeC,QAClB,MAAO,UACT,KAAKD,EAAAA,GAAeuI,QAClB,MAAO,YACT,KAAKvI,EAAAA,GAAewI,QAClB,MAAO,UACT,KAAKxI,EAAAA,GAAe2B,OAClB,MAAO,SACT,QACE,MAAO,U,EAIP+G,GAAYnH,EAAAA,EAAAA,KAAS,IAClBkH,EAAiBJ,EAAM/D,SDKhC,MAAO,CAAC4B,EAAUC,KAChB,MAAMwC,GAAoBtC,EAAAA,EAAAA,IAAkB,UAE5C,OAAQI,EAAAA,EAAAA,OAAcI,EAAAA,EAAAA,IAAa8B,EAAmB,CACpD7B,KAAMwB,EAAQ9G,MACdoD,KAAM,SACL,CACDqC,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBC,EAAAA,EAAAA,KAAiBO,EAAAA,EAAAA,IAAiBgB,EAAUlH,OAAQ,MAEtD4F,EAAG,GACF,EAAG,CAAC,QAAQ,CAEjB,IE7DA,MAAMgB,EAAc,EAEpB,QCFA,MAAM9J,EAAa,CACjBQ,IAAK,EACLP,MAAO,gBAEHC,EAAa,CAAED,MAAO,eACtBE,EAAa,CAAEF,MAAO,kBACtBG,EAAa,CAAEH,MAAO,wBACtBI,EAAa,CACjBG,IAAK,EACLP,MAAO,aAEHK,EAAa,CAAEL,MAAO,qBACtBM,EAAa,CAAEN,MAAO,qBACtBQ,EAAa,CAAER,MAAO,mBAS5B,OAA4BW,EAAAA,EAAAA,IAAiB,CAC3CC,OAAQ,cACRkJ,MAAO,CACLvI,UAAW,CAAC,EACZyH,QAAS,CAAET,KAAM8B,UAEnBxJ,KAAAA,CAAMC,GCgBR,MAAMgJ,EAAQhJ,EAKRmB,GAAae,EAAAA,EAAAA,KAAS,IACnB8G,EAAMvI,UAAUK,YAAYK,YAAc,IAG7CqI,GAAiBtH,EAAAA,EAAAA,KAAS,KACtB8G,EAAMvI,UAAUK,YAAYM,cAAgB,IAAM4H,EAAMvI,UAAUK,YAAYO,cAAgB,KDdxG,MAAO,CAACwF,EAAUC,KAChB,MAAM2C,GAAqBzC,EAAAA,EAAAA,IAAkB,WACvC0C,GAAyB1C,EAAAA,EAAAA,IAAkB,eAEjD,OAAQH,EAAKqB,UACRd,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAoB,MAAOpI,EAAY,EACpDqI,EAAAA,EAAAA,IAAoB,MAAOnI,EAAY,EACrCmI,EAAAA,EAAAA,IAAoB,MAAOlI,EAAY,EACrCkI,EAAAA,EAAAA,IAAoB,MAAOjI,EAAY,CACpC8B,EAAWgB,MAAQ,IACfiF,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAoB,MAAO/H,EAAY,EACpDgI,EAAAA,EAAAA,IAAoB,MAAO/H,EAAY,EACrCyI,EAAAA,EAAAA,IAAayB,EAAoB,KAAM,CACrC7B,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBG,EAAAA,EAAAA,KAAa2B,EAAAA,EAAAA,IAAOC,EAAAA,uBAEtB7B,EAAG,KAELT,EAAAA,EAAAA,IAAoB,OAAQ,MAAMe,EAAAA,EAAAA,IAAiBxB,EAAKpG,UAAUK,YAAYM,cAAgB,GAAI,MAEpGkG,EAAAA,EAAAA,IAAoB,MAAO9H,EAAY,EACrCwI,EAAAA,EAAAA,IAAayB,EAAoB,KAAM,CACrC7B,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBG,EAAAA,EAAAA,KAAa2B,EAAAA,EAAAA,IAAOE,EAAAA,uBAEtB9B,EAAG,KAELT,EAAAA,EAAAA,IAAoB,OAAQ,MAAMe,EAAAA,EAAAA,IAAiBxB,EAAKpG,UAAUK,YAAYO,cAAgB,GAAI,MAEpGiG,EAAAA,EAAAA,IAAoB,MAAO5H,EAAY,EACrCsI,EAAAA,EAAAA,IAAayB,EAAoB,KAAM,CACrC7B,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBG,EAAAA,EAAAA,KAAa2B,EAAAA,EAAAA,IAAOG,EAAAA,gBAEtB/B,EAAG,KAELT,EAAAA,EAAAA,IAAoB,OAAQ,MAAMe,EAAAA,EAAAA,IAAiBlH,EAAWgB,OAAS,GAAI,SAG/EoF,EAAAA,EAAAA,IAAoB,IAAI,IAC5BS,EAAAA,EAAAA,IAAa+B,EAAc,CACzB9E,MAAO4B,EAAKpG,UAAUC,cACrB,KAAM,EAAG,CAAC,aAEfsH,EAAAA,EAAAA,IAAa0B,EAAwB,CACnCM,WAAY7I,EAAWgB,MAAQ,EAAIiC,KAAK6F,MAAMT,EAAerH,MAAQhB,EAAWgB,MAAQ,KAAO,EAC/F,eAAgB,GACf,KAAM,EAAG,CAAC,uBAInBoF,EAAAA,EAAAA,IAAoB,IAAI,EAAK,CAEnC,I,UE1FA,MAAM,GAA2B,OAAgB,EAAQ,CAAC,CAAC,YAAY,qBAEvE,O", "sources": ["webpack://fuzz-web/./src/views/testplan/TestRun.vue?0daa", "webpack://fuzz-web/./src/views/testplan/TestRun.vue", "webpack://fuzz-web/./src/views/testplan/TestRun.vue?f1b4", "webpack://fuzz-web/./src/components/common/TestStateTag.vue?fb59", "webpack://fuzz-web/./src/components/common/TestStateTag.vue", "webpack://fuzz-web/./src/components/common/TestStateTag.vue?d49a", "webpack://fuzz-web/./src/components/test/TestMonitor.vue?60d6", "webpack://fuzz-web/./src/components/test/TestMonitor.vue", "webpack://fuzz-web/./src/components/test/TestMonitor.vue?602e"], "sourcesContent": ["import { defineComponent as _defineComponent } from 'vue'\nimport { createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, createVNode as _createVNode, toDisplayString as _toDisplayString, createElementBlock as _createElementBlock } from \"vue\"\n\nconst _hoisted_1 = { class: \"test-run-container\" }\nconst _hoisted_2 = { class: \"toolbar\" }\nconst _hoisted_3 = { class: \"action-buttons\" }\nconst _hoisted_4 = { class: \"content-area\" }\nconst _hoisted_5 = { class: \"test-cases-panel\" }\nconst _hoisted_6 = { class: \"card-header\" }\nconst _hoisted_7 = {\n  key: 0,\n  class: \"case-count\"\n}\nconst _hoisted_8 = {\n  key: 0,\n  class: \"loading-container\"\n}\nconst _hoisted_9 = {\n  key: 1,\n  class: \"empty-container\"\n}\nconst _hoisted_10 = {\n  key: 2,\n  class: \"case-list-wrapper\"\n}\n\nimport { ref, onMounted, onUnmounted, computed } from 'vue';\r\nimport { ElMessage } from 'element-plus';\r\nimport { appApi, TesterSnapshot, ExecutionState, isTesterCompleted } from '@/api/appApi';\r\nimport { CaseResult } from '@/api/interoperationApi';\r\nimport TestMonitor from '@/components/test/TestMonitor.vue';\r\nimport CaseDetailDialog from '@/components/test/CaseDetailDialog.vue';\r\nimport CaseList from '@/components/test/CaseList.vue';\r\n\r\n// 状态变量\r\n\nexport default /*@__PURE__*/_defineComponent({\n  __name: 'TestRun',\n  setup(__props) {\n\r\n/* eslint-disable */\r\nconst savedTestCases = ref<CaseResult[]>([]);\r\nconst loading = ref(true);\r\nconst starting = ref(false);\r\nconst stopping = ref(false);\r\nconst pausing = ref(false);\r\nconst resuming = ref(false);\r\nconst hasEverStarted = ref(false);\r\nconst runStatus = ref<TesterSnapshot>({\r\n  processState: ExecutionState.Pending,\r\n  currentOperation: '',\r\n  testResult: {\r\n    id: '',\r\n    resultFolderName: '',\r\n    testType: '',\r\n    creationTime: '',\r\n    totalCount: 0,\r\n    successCount: 0,\r\n    failureCount: 0\r\n  },\r\n  caseResults: []\r\n});\r\nconst lastUpdated = ref('');\r\n\r\n// 用例详情对话框相关\r\nconst detailDialogVisible = ref(false);\r\nconst selectedCaseId = ref<number | null>(null);\r\nconst testResultId = ref<string | null>(null);\r\n\r\n// 无需虚拟滚动相关变量，已移至 CaseList 组件\r\nconst isFirstFetch = ref(true);\r\nconst allCases = ref<CaseResult[]>([]);\r\nlet lastPendingIndex = 0;\r\n\r\n// 性能优化：维护一个ID到索引的映射表\r\nconst caseIndexMap = ref<Map<number, number>>(new Map());\r\n\r\n// 状态轮询定时器\r\nlet statusPollingTimer: number | null = null;\r\n\r\n// 计算属性\r\nconst isRunning = computed(() => {\r\n  return runStatus.value.processState === ExecutionState.Running;\r\n});\r\n\r\nconst isPaused = computed(() => {\r\n  return runStatus.value.processState === ExecutionState.Paused;\r\n});\r\n\r\nconst displayedCases = computed(() => {\r\n  console.log('displayedCases = computed(() => begin.');\r\n  // 如果测试正在运行，显示优化后的allCases中的用例\r\n  if ((isRunning.value || isPaused.value) && allCases.value?.length > 0) {\r\n    console.log('displayedCases = computed(() => end - running cases.');\r\n    return allCases.value;\r\n  }\r\n  // 否则显示保存的测试用例\r\n  console.log('displayedCases = computed(() => end - saved cases.');\r\n  return savedTestCases.value;\r\n});\r\n\r\n// 获取保存的测试用例\r\nconst fetchSavedTestCases = async () => {\r\n  loading.value = true;\r\n  try {\r\n    const response = await appApi.getSavedCases();\r\n    savedTestCases.value = response.data;\r\n\r\n    // 数据加载完成，无需额外操作\r\n    // 虚拟滚动已移至 CaseList 组件\r\n  } catch (error) {\r\n    console.error('获取保存的测试用例失败:', error);\r\n    ElMessage.error('Failed to fetch saved test cases');\r\n  } finally {\r\n    loading.value = false;\r\n  }\r\n};\r\n\r\n// 刷新测试用例列表\r\nconst refreshTestCases = () => {\r\n  fetchSavedTestCases();\r\n};\r\n\r\n// 开始测试执行\r\nconst startTestExecution = async () => {\r\n  if (savedTestCases.value.length === 0) {\r\n    ElMessage.warning('No test cases available to execute');\r\n    return;\r\n  }\r\n\r\n  starting.value = true;\r\n  try {\r\n    await appApi.startTest();\r\n    hasEverStarted.value = true;\r\n    ElMessage.success('Test execution started');\r\n\r\n    // 立即获取状态并开始轮询\r\n    await fetchTestStatus();\r\n    startStatusPolling();\r\n  } catch (error) {\r\n    console.error('启动测试失败:', error);\r\n    ElMessage.error('Failed to start test execution');\r\n  } finally {\r\n    starting.value = false;\r\n  }\r\n};\r\n\r\n// 暂停测试执行\r\nconst pauseTestExecution = async () => {\r\n  pausing.value = true;\r\n  try {\r\n    await appApi.pauseTest();\r\n    ElMessage.success('Test execution paused');\r\n\r\n    // 立即更新状态\r\n    await fetchTestStatus();\r\n  } catch (error) {\r\n    console.error('暂停测试失败:', error);\r\n    ElMessage.error('Failed to pause test execution');\r\n  } finally {\r\n    pausing.value = false;\r\n  }\r\n};\r\n\r\n// 恢复测试执行\r\nconst resumeTestExecution = async () => {\r\n  resuming.value = true;\r\n  try {\r\n    await appApi.resumeTest();\r\n    ElMessage.success('Test execution resumed');\r\n\r\n    // 立即更新状态\r\n    await fetchTestStatus();\r\n  } catch (error) {\r\n    console.error('恢复测试失败:', error);\r\n    ElMessage.error('Failed to resume test execution');\r\n  } finally {\r\n    resuming.value = false;\r\n  }\r\n};\r\n\r\n// 停止测试执行\r\nconst stopTestExecution = async () => {\r\n  stopping.value = true;\r\n  try {\r\n    await appApi.stopTest();\r\n    ElMessage.success('Test execution stopped');\r\n\r\n    // 立即更新状态\r\n    await fetchTestStatus();\r\n  } catch (error) {\r\n    console.error('停止测试失败:', error);\r\n    ElMessage.error('Failed to stop test execution');\r\n  } finally {\r\n    stopping.value = false;\r\n  }\r\n};\r\n\r\n// 获取测试状态\r\nconst fetchTestStatus = async () => {\r\n  try {\r\n    let response;\r\n\r\n    if (isFirstFetch.value) {\r\n      // 第一次获取全部数据\r\n      response = await appApi.getTestStatus();\r\n      isFirstFetch.value = false;\r\n\r\n      runStatus.value = response.data;\r\n      allCases.value = response.data.caseResults || [];\r\n\r\n      // 性能优化：建立索引映射\r\n      rebuildIndexMap();\r\n\r\n      // 找到第一个Pending状态的索引\r\n      lastPendingIndex = findFirstPendingIndex(allCases.value);\r\n    } else {\r\n      // 后续只获取状态可能变化的用例\r\n      const pageNumber = Math.floor(lastPendingIndex / 100) + 1;\r\n      response = await appApi.getTestStatusPaged({\r\n        pageNumber,\r\n        pageSize: 100\r\n      });\r\n\r\n      // 更新基本状态\r\n      runStatus.value.processState = response.data.processState;\r\n      runStatus.value.currentOperation = response.data.currentOperation;\r\n      // runStatus.value.testResult = response.data.testResult;\r\n\r\n      // 更新用例状态\r\n      if (response.data.pagedCaseResult?.items) {\r\n        updateCaseResults(response.data.pagedCaseResult.items);\r\n        lastPendingIndex = findFirstPendingIndex(allCases.value);\r\n      }\r\n\r\n      // 将更新后的所有用例赋值给runStatus\r\n      // runStatus.value.caseResults = allCases.value;\r\n    }\r\n\r\n    lastUpdated.value = new Date().toLocaleString();\r\n\r\n    // 测试完成时停止轮询\r\n    if (isTesterCompleted(runStatus.value) && statusPollingTimer) {\r\n      refreshTestCases();\r\n      stopStatusPolling();\r\n    }\r\n\r\n  } catch (error) {\r\n    console.error('获取测试状态失败:', error);\r\n  }\r\n};\r\n\r\n// 查找第一个Pending状态的用例索引\r\nconst findFirstPendingIndex = (cases: CaseResult[]) => {\r\n  console.log('findFirstPendingIndex begin');\r\n  for (let i = 0; i < cases.length; i++) {\r\n    if (cases[i].state === ExecutionState.Pending) {\r\n      console.log('findFirstPendingIndex end');\r\n      return i;\r\n    }\r\n  }\r\n  console.log('findFirstPendingIndex end');\r\n  return cases.length;\r\n};\r\n\r\n// 性能优化：重建索引映射表\r\nconst rebuildIndexMap = () => {\r\n  console.log('rebuildIndexMap begin');\r\n  caseIndexMap.value.clear();\r\n  allCases.value.forEach((caseItem, index) => {\r\n    caseIndexMap.value.set(caseItem.id, index);\r\n  });\r\n  console.log('rebuildIndexMap end, map size:', caseIndexMap.value.size);\r\n};\r\n\r\n// 更新用例结果 - 性能优化版本\r\nconst updateCaseResults = (updatedCases: CaseResult[]) => {\r\n  console.log('updateCaseResults begin, updating', updatedCases.length, 'cases');\r\n\r\n  if (!allCases.value || allCases.value.length === 0) {\r\n    allCases.value = updatedCases;\r\n    rebuildIndexMap();\r\n    console.log('updateCaseResults end - initial load');\r\n    return;\r\n  }\r\n\r\n  // 批量更新：收集所有需要更新的索引和数据\r\n  const updates: Array<{index: number, case: CaseResult}> = [];\r\n  let mapHits = 0;\r\n  let mapMisses = 0;\r\n\r\n  for (const updated of updatedCases) {\r\n    const index = caseIndexMap.value.get(updated.id);\r\n    if (index !== undefined && index < allCases.value.length) {\r\n      // 验证索引是否仍然有效（防止数组结构变化导致的不一致）\r\n      if (allCases.value[index].id === updated.id) {\r\n        updates.push({index, case: updated});\r\n        mapHits++;\r\n      } else {\r\n        // 索引映射失效，需要重建\r\n        console.warn('Index map inconsistency detected, rebuilding...');\r\n        rebuildIndexMap();\r\n        const newIndex = caseIndexMap.value.get(updated.id);\r\n        if (newIndex !== undefined && newIndex < allCases.value.length) {\r\n          updates.push({index: newIndex, case: updated});\r\n          mapHits++;\r\n        } else {\r\n          mapMisses++;\r\n        }\r\n      }\r\n    } else {\r\n      mapMisses++;\r\n    }\r\n  }\r\n\r\n  // 一次性应用所有更新，减少响应式触发次数\r\n  updates.forEach(({index, case: updatedCase}) => {\r\n    allCases.value[index] = updatedCase;\r\n  });\r\n\r\n  console.log('updateCaseResults end - map hits:', mapHits, 'misses:', mapMisses);\r\n};\r\n\r\n// 开始状态轮询 - 保持300毫秒间隔\r\nconst startStatusPolling = () => {\r\n  // 清除可能存在的轮询定时器\r\n  stopStatusPolling();\r\n\r\n  // 重置首次获取标志\r\n  isFirstFetch.value = true;\r\n\r\n  // 清空索引映射，将在首次获取时重建\r\n  caseIndexMap.value.clear();\r\n\r\n  statusPollingTimer = window.setInterval(fetchTestStatus, 300);\r\n};\r\n\r\n// 停止状态轮询\r\nconst stopStatusPolling = () => {\r\n  if (statusPollingTimer) {\r\n    clearInterval(statusPollingTimer);\r\n    statusPollingTimer = null;\r\n  }\r\n};\r\n\r\n// 查看用例详情\r\nconst viewCaseDetail = (caseResult: CaseResult) => {\r\n  testResultId.value = caseResult.testResultId;\r\n  selectedCaseId.value = caseResult.id;\r\n  detailDialogVisible.value = true;\r\n};\r\n\r\n// 关闭详情对话框\r\nconst closeDetailDialog = () => {\r\n  detailDialogVisible.value = false;\r\n  selectedCaseId.value = null;\r\n};\r\n\r\n// 虚拟滚动相关代码已移至 CaseList 组件\r\n\r\n// 组件挂载时获取保存的测试用例和测试状态\r\nonMounted(() => {\r\n  fetchSavedTestCases();\r\n  fetchTestStatus().then(() => {\r\n    // 如果测试正在运行，开始轮询\r\n    if (isRunning.value) {\r\n      hasEverStarted.value = true;\r\n      startStatusPolling();\r\n    }\r\n  });\r\n});\r\n\r\n// 组件卸载时停止轮询\r\nonUnmounted(() => {\r\n  stopStatusPolling();\r\n});\r\n\nreturn (_ctx: any,_cache: any) => {\n  const _component_el_button = _resolveComponent(\"el-button\")!\n  const _component_el_skeleton = _resolveComponent(\"el-skeleton\")!\n  const _component_el_empty = _resolveComponent(\"el-empty\")!\n  const _component_el_card = _resolveComponent(\"el-card\")!\n\n  return (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [\n    _createElementVNode(\"div\", _hoisted_2, [\n      _cache[5] || (_cache[5] = _createElementVNode(\"h3\", null, \"Test Execution\", -1)),\n      _createElementVNode(\"div\", _hoisted_3, [\n        (!isRunning.value && !isPaused.value)\n          ? (_openBlock(), _createBlock(_component_el_button, {\n              key: 0,\n              type: \"success\",\n              size: \"small\",\n              loading: starting.value,\n              onClick: startTestExecution,\n              disabled: savedTestCases.value.length === 0\n            }, {\n              default: _withCtx(() => _cache[1] || (_cache[1] = [\n                _createTextVNode(\" Start \")\n              ])),\n              _: 1\n            }, 8, [\"loading\", \"disabled\"]))\n          : _createCommentVNode(\"\", true),\n        (isRunning.value && !isPaused.value)\n          ? (_openBlock(), _createBlock(_component_el_button, {\n              key: 1,\n              type: \"warning\",\n              size: \"small\",\n              loading: pausing.value,\n              onClick: pauseTestExecution\n            }, {\n              default: _withCtx(() => _cache[2] || (_cache[2] = [\n                _createTextVNode(\" Pause \")\n              ])),\n              _: 1\n            }, 8, [\"loading\"]))\n          : _createCommentVNode(\"\", true),\n        (isPaused.value)\n          ? (_openBlock(), _createBlock(_component_el_button, {\n              key: 2,\n              type: \"info\",\n              size: \"small\",\n              loading: resuming.value,\n              onClick: resumeTestExecution\n            }, {\n              default: _withCtx(() => _cache[3] || (_cache[3] = [\n                _createTextVNode(\" Resume \")\n              ])),\n              _: 1\n            }, 8, [\"loading\"]))\n          : _createCommentVNode(\"\", true),\n        (isRunning.value || isPaused.value)\n          ? (_openBlock(), _createBlock(_component_el_button, {\n              key: 3,\n              type: \"danger\",\n              size: \"small\",\n              loading: stopping.value,\n              onClick: stopTestExecution\n            }, {\n              default: _withCtx(() => _cache[4] || (_cache[4] = [\n                _createTextVNode(\" Stop \")\n              ])),\n              _: 1\n            }, 8, [\"loading\"]))\n          : _createCommentVNode(\"\", true)\n      ])\n    ]),\n    _createVNode(TestMonitor, {\n      \"run-status\": runStatus.value,\n      visible: hasEverStarted.value\n    }, null, 8, [\"run-status\", \"visible\"]),\n    _createElementVNode(\"div\", _hoisted_4, [\n      _createElementVNode(\"div\", _hoisted_5, [\n        _createVNode(_component_el_card, {\n          shadow: \"never\",\n          class: \"test-cases-card\"\n        }, {\n          header: _withCtx(() => [\n            _createElementVNode(\"div\", _hoisted_6, [\n              _cache[6] || (_cache[6] = _createElementVNode(\"span\", null, \"Test Cases\", -1)),\n              (displayedCases.value.length > 0)\n                ? (_openBlock(), _createElementBlock(\"span\", _hoisted_7, _toDisplayString(displayedCases.value.length) + \" cases\", 1))\n                : _createCommentVNode(\"\", true)\n            ])\n          ]),\n          default: _withCtx(() => [\n            (loading.value)\n              ? (_openBlock(), _createElementBlock(\"div\", _hoisted_8, [\n                  _createVNode(_component_el_skeleton, {\n                    rows: 10,\n                    animated: \"\"\n                  })\n                ]))\n              : (displayedCases.value.length === 0)\n                ? (_openBlock(), _createElementBlock(\"div\", _hoisted_9, [\n                    _createVNode(_component_el_empty, { description: \"No test cases found\" })\n                  ]))\n                : (_openBlock(), _createElementBlock(\"div\", _hoisted_10, [\n                    _createVNode(CaseList, {\n                      cases: displayedCases.value,\n                      onViewDetail: viewCaseDetail\n                    }, null, 8, [\"cases\"])\n                  ]))\n          ]),\n          _: 1\n        })\n      ])\n    ]),\n    _createVNode(CaseDetailDialog, {\n      visible: detailDialogVisible.value,\n      \"onUpdate:visible\": _cache[0] || (_cache[0] = ($event: any) => ((detailDialogVisible).value = $event)),\n      testResultId: testResultId.value,\n      caseResultId: selectedCaseId.value,\n      onClose: closeDetailDialog\n    }, null, 8, [\"visible\", \"testResultId\", \"caseResultId\"])\n  ]))\n}\n}\n\n})", "<template>\r\n  <div class=\"test-run-container\">\r\n    <div class=\"toolbar\">\r\n      <h3>Test Execution</h3>\r\n      <div class=\"action-buttons\">\r\n        <!-- 开始按钮：在测试未运行且未暂停时显示 -->\r\n        <el-button\r\n          v-if=\"!isRunning && !isPaused\"\r\n          type=\"success\"\r\n          size=\"small\"\r\n          :loading=\"starting\"\r\n          @click=\"startTestExecution\"\r\n          :disabled=\"savedTestCases.length === 0\">\r\n          Start\r\n        </el-button>\r\n\r\n        <!-- 暂停按钮：只在测试运行且未暂停时显示 -->\r\n        <el-button\r\n          v-if=\"isRunning && !isPaused\"\r\n          type=\"warning\"\r\n          size=\"small\"\r\n          :loading=\"pausing\"\r\n          @click=\"pauseTestExecution\">\r\n          Pause\r\n        </el-button>\r\n\r\n        <!-- 恢复按钮：只在测试已暂停时显示 -->\r\n        <el-button\r\n          v-if=\"isPaused\"\r\n          type=\"info\"\r\n          size=\"small\"\r\n          :loading=\"resuming\"\r\n          @click=\"resumeTestExecution\">\r\n          Resume\r\n        </el-button>\r\n\r\n        <!-- 停止按钮：在测试运行或暂停时都显示 -->\r\n        <el-button\r\n          v-if=\"isRunning || isPaused\"\r\n          type=\"danger\"\r\n          size=\"small\"\r\n          :loading=\"stopping\"\r\n          @click=\"stopTestExecution\">\r\n          Stop\r\n        </el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 使用新的TestMonitor组件 -->\r\n    <TestMonitor :run-status=\"runStatus\" :visible=\"hasEverStarted\" />\r\n\r\n    <div class=\"content-area\">\r\n      <div class=\"test-cases-panel\">\r\n        <el-card shadow=\"never\" class=\"test-cases-card\">\r\n          <template #header>\r\n            <div class=\"card-header\">\r\n              <span>Test Cases</span>\r\n              <span v-if=\"displayedCases.length > 0\" class=\"case-count\">{{ displayedCases.length }} cases</span>\r\n            </div>\r\n          </template>\r\n          <div v-if=\"loading\" class=\"loading-container\">\r\n            <el-skeleton :rows=\"10\" animated />\r\n          </div>\r\n          <div v-else-if=\"displayedCases.length === 0\" class=\"empty-container\">\r\n            <el-empty description=\"No test cases found\" />\r\n          </div>\r\n          <div v-else class=\"case-list-wrapper\">\r\n            <CaseList\r\n              :cases=\"displayedCases\"\r\n              @view-detail=\"viewCaseDetail\"\r\n            />\r\n          </div>\r\n        </el-card>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 添加用例详情对话框组件 -->\r\n    <CaseDetailDialog\r\n      v-model:visible=\"detailDialogVisible\"\r\n      :testResultId=\"testResultId\"\r\n      :caseResultId=\"selectedCaseId\"\r\n      @close=\"closeDetailDialog\"\r\n    />\r\n  </div>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\n/* eslint-disable */\r\nimport { ref, onMounted, onUnmounted, computed } from 'vue';\r\nimport { ElMessage } from 'element-plus';\r\nimport { appApi, TesterSnapshot, ExecutionState, isTesterCompleted } from '@/api/appApi';\r\nimport { CaseResult } from '@/api/interoperationApi';\r\nimport TestMonitor from '@/components/test/TestMonitor.vue';\r\nimport CaseDetailDialog from '@/components/test/CaseDetailDialog.vue';\r\nimport CaseList from '@/components/test/CaseList.vue';\r\n\r\n// 状态变量\r\nconst savedTestCases = ref<CaseResult[]>([]);\r\nconst loading = ref(true);\r\nconst starting = ref(false);\r\nconst stopping = ref(false);\r\nconst pausing = ref(false);\r\nconst resuming = ref(false);\r\nconst hasEverStarted = ref(false);\r\nconst runStatus = ref<TesterSnapshot>({\r\n  processState: ExecutionState.Pending,\r\n  currentOperation: '',\r\n  testResult: {\r\n    id: '',\r\n    resultFolderName: '',\r\n    testType: '',\r\n    creationTime: '',\r\n    totalCount: 0,\r\n    successCount: 0,\r\n    failureCount: 0\r\n  },\r\n  caseResults: []\r\n});\r\nconst lastUpdated = ref('');\r\n\r\n// 用例详情对话框相关\r\nconst detailDialogVisible = ref(false);\r\nconst selectedCaseId = ref<number | null>(null);\r\nconst testResultId = ref<string | null>(null);\r\n\r\n// 无需虚拟滚动相关变量，已移至 CaseList 组件\r\nconst isFirstFetch = ref(true);\r\nconst allCases = ref<CaseResult[]>([]);\r\nlet lastPendingIndex = 0;\r\n\r\n// 性能优化：维护一个ID到索引的映射表\r\nconst caseIndexMap = ref<Map<number, number>>(new Map());\r\n\r\n// 状态轮询定时器\r\nlet statusPollingTimer: number | null = null;\r\n\r\n// 计算属性\r\nconst isRunning = computed(() => {\r\n  return runStatus.value.processState === ExecutionState.Running;\r\n});\r\n\r\nconst isPaused = computed(() => {\r\n  return runStatus.value.processState === ExecutionState.Paused;\r\n});\r\n\r\nconst displayedCases = computed(() => {\r\n  console.log('displayedCases = computed(() => begin.');\r\n  // 如果测试正在运行，显示优化后的allCases中的用例\r\n  if ((isRunning.value || isPaused.value) && allCases.value?.length > 0) {\r\n    console.log('displayedCases = computed(() => end - running cases.');\r\n    return allCases.value;\r\n  }\r\n  // 否则显示保存的测试用例\r\n  console.log('displayedCases = computed(() => end - saved cases.');\r\n  return savedTestCases.value;\r\n});\r\n\r\n// 获取保存的测试用例\r\nconst fetchSavedTestCases = async () => {\r\n  loading.value = true;\r\n  try {\r\n    const response = await appApi.getSavedCases();\r\n    savedTestCases.value = response.data;\r\n\r\n    // 数据加载完成，无需额外操作\r\n    // 虚拟滚动已移至 CaseList 组件\r\n  } catch (error) {\r\n    console.error('获取保存的测试用例失败:', error);\r\n    ElMessage.error('Failed to fetch saved test cases');\r\n  } finally {\r\n    loading.value = false;\r\n  }\r\n};\r\n\r\n// 刷新测试用例列表\r\nconst refreshTestCases = () => {\r\n  fetchSavedTestCases();\r\n};\r\n\r\n// 开始测试执行\r\nconst startTestExecution = async () => {\r\n  if (savedTestCases.value.length === 0) {\r\n    ElMessage.warning('No test cases available to execute');\r\n    return;\r\n  }\r\n\r\n  starting.value = true;\r\n  try {\r\n    await appApi.startTest();\r\n    hasEverStarted.value = true;\r\n    ElMessage.success('Test execution started');\r\n\r\n    // 立即获取状态并开始轮询\r\n    await fetchTestStatus();\r\n    startStatusPolling();\r\n  } catch (error) {\r\n    console.error('启动测试失败:', error);\r\n    ElMessage.error('Failed to start test execution');\r\n  } finally {\r\n    starting.value = false;\r\n  }\r\n};\r\n\r\n// 暂停测试执行\r\nconst pauseTestExecution = async () => {\r\n  pausing.value = true;\r\n  try {\r\n    await appApi.pauseTest();\r\n    ElMessage.success('Test execution paused');\r\n\r\n    // 立即更新状态\r\n    await fetchTestStatus();\r\n  } catch (error) {\r\n    console.error('暂停测试失败:', error);\r\n    ElMessage.error('Failed to pause test execution');\r\n  } finally {\r\n    pausing.value = false;\r\n  }\r\n};\r\n\r\n// 恢复测试执行\r\nconst resumeTestExecution = async () => {\r\n  resuming.value = true;\r\n  try {\r\n    await appApi.resumeTest();\r\n    ElMessage.success('Test execution resumed');\r\n\r\n    // 立即更新状态\r\n    await fetchTestStatus();\r\n  } catch (error) {\r\n    console.error('恢复测试失败:', error);\r\n    ElMessage.error('Failed to resume test execution');\r\n  } finally {\r\n    resuming.value = false;\r\n  }\r\n};\r\n\r\n// 停止测试执行\r\nconst stopTestExecution = async () => {\r\n  stopping.value = true;\r\n  try {\r\n    await appApi.stopTest();\r\n    ElMessage.success('Test execution stopped');\r\n\r\n    // 立即更新状态\r\n    await fetchTestStatus();\r\n  } catch (error) {\r\n    console.error('停止测试失败:', error);\r\n    ElMessage.error('Failed to stop test execution');\r\n  } finally {\r\n    stopping.value = false;\r\n  }\r\n};\r\n\r\n// 获取测试状态\r\nconst fetchTestStatus = async () => {\r\n  try {\r\n    let response;\r\n\r\n    if (isFirstFetch.value) {\r\n      // 第一次获取全部数据\r\n      response = await appApi.getTestStatus();\r\n      isFirstFetch.value = false;\r\n\r\n      runStatus.value = response.data;\r\n      allCases.value = response.data.caseResults || [];\r\n\r\n      // 性能优化：建立索引映射\r\n      rebuildIndexMap();\r\n\r\n      // 找到第一个Pending状态的索引\r\n      lastPendingIndex = findFirstPendingIndex(allCases.value);\r\n    } else {\r\n      // 后续只获取状态可能变化的用例\r\n      const pageNumber = Math.floor(lastPendingIndex / 100) + 1;\r\n      response = await appApi.getTestStatusPaged({\r\n        pageNumber,\r\n        pageSize: 100\r\n      });\r\n\r\n      // 更新基本状态\r\n      runStatus.value.processState = response.data.processState;\r\n      runStatus.value.currentOperation = response.data.currentOperation;\r\n      // runStatus.value.testResult = response.data.testResult;\r\n\r\n      // 更新用例状态\r\n      if (response.data.pagedCaseResult?.items) {\r\n        updateCaseResults(response.data.pagedCaseResult.items);\r\n        lastPendingIndex = findFirstPendingIndex(allCases.value);\r\n      }\r\n\r\n      // 将更新后的所有用例赋值给runStatus\r\n      // runStatus.value.caseResults = allCases.value;\r\n    }\r\n\r\n    lastUpdated.value = new Date().toLocaleString();\r\n\r\n    // 测试完成时停止轮询\r\n    if (isTesterCompleted(runStatus.value) && statusPollingTimer) {\r\n      refreshTestCases();\r\n      stopStatusPolling();\r\n    }\r\n\r\n  } catch (error) {\r\n    console.error('获取测试状态失败:', error);\r\n  }\r\n};\r\n\r\n// 查找第一个Pending状态的用例索引\r\nconst findFirstPendingIndex = (cases: CaseResult[]) => {\r\n  console.log('findFirstPendingIndex begin');\r\n  for (let i = 0; i < cases.length; i++) {\r\n    if (cases[i].state === ExecutionState.Pending) {\r\n      console.log('findFirstPendingIndex end');\r\n      return i;\r\n    }\r\n  }\r\n  console.log('findFirstPendingIndex end');\r\n  return cases.length;\r\n};\r\n\r\n// 性能优化：重建索引映射表\r\nconst rebuildIndexMap = () => {\r\n  console.log('rebuildIndexMap begin');\r\n  caseIndexMap.value.clear();\r\n  allCases.value.forEach((caseItem, index) => {\r\n    caseIndexMap.value.set(caseItem.id, index);\r\n  });\r\n  console.log('rebuildIndexMap end, map size:', caseIndexMap.value.size);\r\n};\r\n\r\n// 更新用例结果 - 性能优化版本\r\nconst updateCaseResults = (updatedCases: CaseResult[]) => {\r\n  console.log('updateCaseResults begin, updating', updatedCases.length, 'cases');\r\n\r\n  if (!allCases.value || allCases.value.length === 0) {\r\n    allCases.value = updatedCases;\r\n    rebuildIndexMap();\r\n    console.log('updateCaseResults end - initial load');\r\n    return;\r\n  }\r\n\r\n  // 批量更新：收集所有需要更新的索引和数据\r\n  const updates: Array<{index: number, case: CaseResult}> = [];\r\n  let mapHits = 0;\r\n  let mapMisses = 0;\r\n\r\n  for (const updated of updatedCases) {\r\n    const index = caseIndexMap.value.get(updated.id);\r\n    if (index !== undefined && index < allCases.value.length) {\r\n      // 验证索引是否仍然有效（防止数组结构变化导致的不一致）\r\n      if (allCases.value[index].id === updated.id) {\r\n        updates.push({index, case: updated});\r\n        mapHits++;\r\n      } else {\r\n        // 索引映射失效，需要重建\r\n        console.warn('Index map inconsistency detected, rebuilding...');\r\n        rebuildIndexMap();\r\n        const newIndex = caseIndexMap.value.get(updated.id);\r\n        if (newIndex !== undefined && newIndex < allCases.value.length) {\r\n          updates.push({index: newIndex, case: updated});\r\n          mapHits++;\r\n        } else {\r\n          mapMisses++;\r\n        }\r\n      }\r\n    } else {\r\n      mapMisses++;\r\n    }\r\n  }\r\n\r\n  // 一次性应用所有更新，减少响应式触发次数\r\n  updates.forEach(({index, case: updatedCase}) => {\r\n    allCases.value[index] = updatedCase;\r\n  });\r\n\r\n  console.log('updateCaseResults end - map hits:', mapHits, 'misses:', mapMisses);\r\n};\r\n\r\n// 开始状态轮询 - 保持300毫秒间隔\r\nconst startStatusPolling = () => {\r\n  // 清除可能存在的轮询定时器\r\n  stopStatusPolling();\r\n\r\n  // 重置首次获取标志\r\n  isFirstFetch.value = true;\r\n\r\n  // 清空索引映射，将在首次获取时重建\r\n  caseIndexMap.value.clear();\r\n\r\n  statusPollingTimer = window.setInterval(fetchTestStatus, 300);\r\n};\r\n\r\n// 停止状态轮询\r\nconst stopStatusPolling = () => {\r\n  if (statusPollingTimer) {\r\n    clearInterval(statusPollingTimer);\r\n    statusPollingTimer = null;\r\n  }\r\n};\r\n\r\n// 查看用例详情\r\nconst viewCaseDetail = (caseResult: CaseResult) => {\r\n  testResultId.value = caseResult.testResultId;\r\n  selectedCaseId.value = caseResult.id;\r\n  detailDialogVisible.value = true;\r\n};\r\n\r\n// 关闭详情对话框\r\nconst closeDetailDialog = () => {\r\n  detailDialogVisible.value = false;\r\n  selectedCaseId.value = null;\r\n};\r\n\r\n// 虚拟滚动相关代码已移至 CaseList 组件\r\n\r\n// 组件挂载时获取保存的测试用例和测试状态\r\nonMounted(() => {\r\n  fetchSavedTestCases();\r\n  fetchTestStatus().then(() => {\r\n    // 如果测试正在运行，开始轮询\r\n    if (isRunning.value) {\r\n      hasEverStarted.value = true;\r\n      startStatusPolling();\r\n    }\r\n  });\r\n});\r\n\r\n// 组件卸载时停止轮询\r\nonUnmounted(() => {\r\n  stopStatusPolling();\r\n});\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.test-run-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  flex: 1;\r\n  padding: 20px;\r\n}\r\n\r\n/* 顶部工具栏样式 */\r\n.toolbar {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 8px 12px;\r\n  border-bottom: 1px solid #dcdfe6;\r\n  margin-bottom: 12px;\r\n\r\n  h3 {\r\n    margin: 0;\r\n    font-size: 16px;\r\n    font-weight: 500;\r\n    color: #303133;\r\n  }\r\n\r\n  .action-buttons {\r\n    display: flex;\r\n    gap: 8px;\r\n  }\r\n}\r\n\r\n/* 内容区域样式 */\r\n.content-area {\r\n  flex: 1;\r\n  min-height: 0;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.test-cases-panel {\r\n  flex: 1;\r\n  min-height: 0;\r\n}\r\n\r\n.test-cases-card {\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n  box-shadow: none;\r\n\r\n  :deep(.el-card__header) {\r\n    padding: 12px 16px;\r\n    border-bottom: 2px solid #f0f0f0;\r\n  }\r\n\r\n  :deep(.el-card__body) {\r\n    flex: 1;\r\n    overflow: hidden;\r\n    padding: 0;\r\n  }\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n\r\n  .case-count {\r\n    font-size: 12px;\r\n    color: #909399;\r\n  }\r\n}\r\n\r\n.loading-container,\r\n.empty-container {\r\n  height: 100%;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  padding: 20px;\r\n}\r\n\r\n.case-list-wrapper {\r\n  height: 100%;\r\n}\r\n</style>\r\n", "import script from \"./TestRun.vue?vue&type=script&setup=true&lang=ts\"\nexport * from \"./TestRun.vue?vue&type=script&setup=true&lang=ts\"\n\nimport \"./TestRun.vue?vue&type=style&index=0&id=55a32910&scoped=true&lang=scss\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-55a32910\"]])\n\nexport default __exports__", "import { defineComponent as _defineComponent } from 'vue'\nimport { toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, openBlock as _openBlock, createBlock as _createBlock } from \"vue\"\n\nimport { computed } from 'vue';\r\nimport { ExecutionState } from '@/api/appApi';\r\n\r\n\nexport default /*@__PURE__*/_defineComponent({\n  __name: 'TestStateTag',\n  props: {\n    state: {}\n  },\n  setup(__props: any) {\n\r\nconst props = __props;\r\n\r\nconst tagType = computed<'success' | 'warning' | 'danger' | 'info'>(() => {\r\n  switch (props.state) {\r\n    case ExecutionState.Success:\r\n      return 'success';\r\n    case ExecutionState.Running:\r\n      return 'warning';\r\n    case ExecutionState.Failure:\r\n      return 'danger';\r\n    case ExecutionState.Pending:\r\n    default:\r\n      return 'info';\r\n  }\r\n});\r\n\r\nconst getTestStateName = (state: string): 'Not Run' | 'Running' | 'Completed' | 'Faulted' | 'Paused' | 'Unknown' => {\r\n  switch (state) {\r\n    case ExecutionState.Running:\r\n      return 'Running';\r\n    case ExecutionState.Pending:\r\n      return 'Not Run';\r\n    case ExecutionState.Success:\r\n      return 'Completed';\r\n    case ExecutionState.Failure:\r\n      return 'Faulted';\r\n    case ExecutionState.Paused:\r\n      return 'Paused';\r\n    default:\r\n      return 'Unknown';\r\n  }\r\n};\r\n\r\nconst stateName = computed(() => {\r\n  return getTestStateName(props.state);\r\n});\r\n\nreturn (_ctx: any,_cache: any) => {\n  const _component_el_tag = _resolveComponent(\"el-tag\")!\n\n  return (_openBlock(), _createBlock(_component_el_tag, {\n    type: tagType.value,\n    size: \"small\"\n  }, {\n    default: _withCtx(() => [\n      _createTextVNode(_toDisplayString(stateName.value), 1)\n    ]),\n    _: 1\n  }, 8, [\"type\"]))\n}\n}\n\n})", "<template>\r\n  <el-tag :type=\"tagType\" size=\"small\">\r\n    {{ stateName }}\r\n  </el-tag>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { computed, defineProps } from 'vue';\r\nimport { ExecutionState } from '@/api/appApi';\r\n\r\nconst props = defineProps<{\r\n  state: string;\r\n}>();\r\n\r\nconst tagType = computed<'success' | 'warning' | 'danger' | 'info'>(() => {\r\n  switch (props.state) {\r\n    case ExecutionState.Success:\r\n      return 'success';\r\n    case ExecutionState.Running:\r\n      return 'warning';\r\n    case ExecutionState.Failure:\r\n      return 'danger';\r\n    case ExecutionState.Pending:\r\n    default:\r\n      return 'info';\r\n  }\r\n});\r\n\r\nconst getTestStateName = (state: string): 'Not Run' | 'Running' | 'Completed' | 'Faulted' | 'Paused' | 'Unknown' => {\r\n  switch (state) {\r\n    case ExecutionState.Running:\r\n      return 'Running';\r\n    case ExecutionState.Pending:\r\n      return 'Not Run';\r\n    case ExecutionState.Success:\r\n      return 'Completed';\r\n    case ExecutionState.Failure:\r\n      return 'Faulted';\r\n    case ExecutionState.Paused:\r\n      return 'Paused';\r\n    default:\r\n      return 'Unknown';\r\n  }\r\n};\r\n\r\nconst stateName = computed(() => {\r\n  return getTestStateName(props.state);\r\n});\r\n</script>\r\n", "import script from \"./TestStateTag.vue?vue&type=script&setup=true&lang=ts\"\nexport * from \"./TestStateTag.vue?vue&type=script&setup=true&lang=ts\"\n\nconst __exports__ = script;\n\nexport default __exports__", "import { defineComponent as _defineComponent } from 'vue'\nimport { unref as _unref, createVNode as _createVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode } from \"vue\"\n\nconst _hoisted_1 = {\n  key: 0,\n  class: \"test-monitor\"\n}\nconst _hoisted_2 = { class: \"status-area\" }\nconst _hoisted_3 = { class: \"compact-status\" }\nconst _hoisted_4 = { class: \"status-header-inline\" }\nconst _hoisted_5 = {\n  key: 0,\n  class: \"stats-row\"\n}\nconst _hoisted_6 = { class: \"stat-item success\" }\nconst _hoisted_7 = { class: \"stat-item failure\" }\nconst _hoisted_8 = { class: \"stat-item total\" }\n\nimport { computed } from 'vue';\r\nimport { CircleCheckFilled, CircleCloseFilled, InfoFilled } from '@element-plus/icons-vue';\r\nimport { TesterSnapshot } from '@/api/appApi';\r\nimport TestStateTag from '@/components/common/TestStateTag.vue';\r\n\r\n// 组件属性定义\r\n\nexport default /*@__PURE__*/_defineComponent({\n  __name: 'TestMonitor',\n  props: {\n    runStatus: {},\n    visible: { type: Boolean }\n  },\n  setup(__props: any) {\n\r\nconst props = __props;\r\n\r\nconst totalCount = computed(() => {\r\n  return props.runStatus.testResult?.totalCount || 0;\r\n});\r\n\r\nconst completedCount = computed(() => {\r\n  return (props.runStatus.testResult?.successCount || 0) + (props.runStatus.testResult?.failureCount || 0);\r\n});\r\n\nreturn (_ctx: any,_cache: any) => {\n  const _component_el_icon = _resolveComponent(\"el-icon\")!\n  const _component_el_progress = _resolveComponent(\"el-progress\")!\n\n  return (_ctx.visible)\n    ? (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [\n        _createElementVNode(\"div\", _hoisted_2, [\n          _createElementVNode(\"div\", _hoisted_3, [\n            _createElementVNode(\"div\", _hoisted_4, [\n              (totalCount.value > 0)\n                ? (_openBlock(), _createElementBlock(\"div\", _hoisted_5, [\n                    _createElementVNode(\"div\", _hoisted_6, [\n                      _createVNode(_component_el_icon, null, {\n                        default: _withCtx(() => [\n                          _createVNode(_unref(CircleCheckFilled))\n                        ]),\n                        _: 1\n                      }),\n                      _createElementVNode(\"span\", null, _toDisplayString(_ctx.runStatus.testResult?.successCount || 0), 1)\n                    ]),\n                    _createElementVNode(\"div\", _hoisted_7, [\n                      _createVNode(_component_el_icon, null, {\n                        default: _withCtx(() => [\n                          _createVNode(_unref(CircleCloseFilled))\n                        ]),\n                        _: 1\n                      }),\n                      _createElementVNode(\"span\", null, _toDisplayString(_ctx.runStatus.testResult?.failureCount || 0), 1)\n                    ]),\n                    _createElementVNode(\"div\", _hoisted_8, [\n                      _createVNode(_component_el_icon, null, {\n                        default: _withCtx(() => [\n                          _createVNode(_unref(InfoFilled))\n                        ]),\n                        _: 1\n                      }),\n                      _createElementVNode(\"span\", null, _toDisplayString(totalCount.value || 0), 1)\n                    ])\n                  ]))\n                : _createCommentVNode(\"\", true),\n              _createVNode(TestStateTag, {\n                state: _ctx.runStatus.processState\n              }, null, 8, [\"state\"])\n            ]),\n            _createVNode(_component_el_progress, {\n              percentage: totalCount.value > 0 ? Math.round(completedCount.value / totalCount.value * 100) : 0,\n              \"stroke-width\": 8\n            }, null, 8, [\"percentage\"])\n          ])\n        ])\n      ]))\n    : _createCommentVNode(\"\", true)\n}\n}\n\n})", "<template>\r\n  <div class=\"test-monitor\" v-if=\"visible\">\r\n    <!-- 优化后的紧凑测试状态区域 -->\r\n    <div class=\"status-area\">\r\n      <div class=\"compact-status\">\r\n        <!-- 内联状态头部 -->\r\n        <div class=\"status-header-inline\">\r\n          <div class=\"stats-row\" v-if=\"totalCount > 0\">\r\n            <div class=\"stat-item success\">\r\n              <el-icon>\r\n                <CircleCheckFilled />\r\n              </el-icon>\r\n              <span>{{ runStatus.testResult?.successCount || 0 }}</span>\r\n            </div>\r\n            <div class=\"stat-item failure\">\r\n              <el-icon>\r\n                <CircleCloseFilled />\r\n              </el-icon>\r\n              <span>{{ runStatus.testResult?.failureCount || 0 }}</span>\r\n            </div>\r\n\r\n            <div class=\"stat-item total\">\r\n              <el-icon>\r\n                <InfoFilled />\r\n              </el-icon>\r\n              <span>{{ totalCount || 0 }}</span>\r\n            </div>\r\n          </div>\r\n\r\n          <TestStateTag :state=\"runStatus.processState\" />\r\n        </div>\r\n\r\n        <!-- 进度条 -->\r\n        <el-progress :percentage=\"totalCount > 0 ? Math.round(completedCount / totalCount * 100) : 0\" :stroke-width=\"8\">\r\n        </el-progress>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { defineProps, computed } from 'vue';\r\nimport { CircleCheckFilled, CircleCloseFilled, InfoFilled } from '@element-plus/icons-vue';\r\nimport { TesterSnapshot } from '@/api/appApi';\r\nimport TestStateTag from '@/components/common/TestStateTag.vue';\r\n\r\n// 组件属性定义\r\nconst props = defineProps<{\r\n  runStatus: TesterSnapshot;\r\n  visible: boolean;\r\n}>();\r\n\r\nconst totalCount = computed(() => {\r\n  return props.runStatus.testResult?.totalCount || 0;\r\n});\r\n\r\nconst completedCount = computed(() => {\r\n  return (props.runStatus.testResult?.successCount || 0) + (props.runStatus.testResult?.failureCount || 0);\r\n});\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.test-monitor {\r\n  width: 100%;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n/* 状态区域样式 */\r\n.status-area {\r\n  padding: 0 12px;\r\n\r\n  .compact-status {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 8px;\r\n  }\r\n\r\n  .status-header-inline {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n  }\r\n\r\n  .progress-numbers {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 4px;\r\n  }\r\n\r\n  .stats-row {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 10px;\r\n  }\r\n\r\n  .stat-item {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 4px;\r\n\r\n    &.success {\r\n      color: var(--el-color-success);\r\n    }\r\n\r\n    &.failure {\r\n      color: var(--el-color-danger);\r\n    }\r\n\r\n    &.total {\r\n      color: var(--el-color-primary);\r\n    }\r\n  }\r\n}\r\n</style>\r\n", "import script from \"./TestMonitor.vue?vue&type=script&setup=true&lang=ts\"\nexport * from \"./TestMonitor.vue?vue&type=script&setup=true&lang=ts\"\n\nimport \"./TestMonitor.vue?vue&type=style&index=0&id=ebdce83c&scoped=true&lang=scss\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-ebdce83c\"]])\n\nexport default __exports__"], "names": ["_hoisted_1", "class", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_hoisted_5", "_hoisted_6", "_hoisted_7", "key", "_hoisted_8", "_hoisted_9", "_hoisted_10", "_defineComponent", "__name", "setup", "__props", "savedTestCases", "ref", "loading", "starting", "stopping", "pausing", "resuming", "hasEverStarted", "runStatus", "processState", "ExecutionState", "Pending", "currentOperation", "testResult", "id", "resultFolderName", "testType", "creationTime", "totalCount", "successCount", "failureCount", "caseResults", "lastUpdated", "detailDialogVisible", "selectedCaseId", "testResultId", "isFirstFetch", "allCases", "lastPendingIndex", "caseIndexMap", "Map", "statusPollingTimer", "isRunning", "computed", "value", "Running", "isPaused", "Paused", "displayedCases", "console", "log", "length", "fetchSavedTestCases", "async", "response", "appApi", "getSavedCases", "data", "error", "ElMessage", "refreshTestCases", "startTestExecution", "startTest", "success", "fetchTestStatus", "startStatusPolling", "warning", "pauseTestExecution", "pauseTest", "resumeTestExecution", "resumeTest", "stopTestExecution", "stopTest", "getTestStatus", "rebuildIndexMap", "findFirstPendingIndex", "pageNumber", "Math", "floor", "getTestStatusPaged", "pageSize", "pagedCaseResult", "items", "updateCaseResults", "Date", "toLocaleString", "isTesterCompleted", "stopStatusPolling", "cases", "i", "state", "clear", "for<PERSON>ach", "caseItem", "index", "set", "size", "updatedCases", "updates", "mapHits", "mapMisses", "updated", "get", "undefined", "push", "case", "warn", "newIndex", "updatedCase", "window", "setInterval", "clearInterval", "viewCaseDetail", "caseResult", "closeDetailDialog", "onMounted", "then", "onUnmounted", "_ctx", "_cache", "_component_el_button", "_resolveComponent", "_component_el_skeleton", "_component_el_empty", "_component_el_card", "_openBlock", "_createElementBlock", "_createElementVNode", "_createCommentVNode", "_createBlock", "type", "onClick", "disabled", "default", "_withCtx", "_createTextVNode", "_", "_createVNode", "TestMonitor", "visible", "shadow", "header", "_toDisplayString", "rows", "animated", "description", "CaseList", "onViewDetail", "CaseDetailDialog", "$event", "caseResultId", "onClose", "__exports__", "props", "tagType", "Success", "Failure", "getTestStateName", "stateName", "_component_el_tag", "Boolean", "completedCount", "_component_el_icon", "_component_el_progress", "_unref", "CircleCheckFilled", "CircleCloseFilled", "InfoFilled", "TestStateTag", "percentage", "round"], "sourceRoot": ""}