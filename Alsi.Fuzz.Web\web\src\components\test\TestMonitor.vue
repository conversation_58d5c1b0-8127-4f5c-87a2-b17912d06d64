<template>
  <div class="test-monitor" v-if="visible">
    <!-- 优化后的紧凑测试状态区域 -->
    <div class="status-area">
      <div class="compact-status">
        <!-- 内联状态头部 -->
        <div class="status-header-inline">
          <div class="stats-row" v-if="totalCount > 0">
            <div class="stat-item success">
              <el-icon>
                <CircleCheckFilled />
              </el-icon>
              <span>{{ runStatus.testResult?.successCount || 0 }}</span>
            </div>
            <div class="stat-item failure">
              <el-icon>
                <CircleCloseFilled />
              </el-icon>
              <span>{{ runStatus.testResult?.failureCount || 0 }}</span>
            </div>

            <div class="stat-item total">
              <el-icon>
                <InfoFilled />
              </el-icon>
              <span>{{ totalCount || 0 }}</span>
            </div>
          </div>

          <TestStateTag :state="runStatus.processState" />
        </div>

        <!-- 进度条 -->
        <el-progress :percentage="totalCount > 0 ? Math.round(completedCount / totalCount * 100) : 0" :stroke-width="8">
        </el-progress>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, computed } from 'vue';
import { CircleCheckFilled, CircleCloseFilled, InfoFilled } from '@element-plus/icons-vue';
import { TesterSnapshot } from '@/api/appApi';
import TestStateTag from '@/components/common/TestStateTag.vue';

// 组件属性定义
const props = defineProps<{
  runStatus: TesterSnapshot;
  visible: boolean;
}>();

const totalCount = computed(() => {
  return props.runStatus.testResult?.totalCount || 0;
});

const completedCount = computed(() => {
  return (props.runStatus.testResult?.successCount || 0) + (props.runStatus.testResult?.failureCount || 0);
});
</script>

<style scoped lang="scss">
.test-monitor {
  width: 100%;
  margin-bottom: 12px;
}

/* 状态区域样式 */
.status-area {
  padding: 0 12px;

  .compact-status {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .status-header-inline {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .progress-numbers {
    display: flex;
    align-items: center;
    gap: 4px;
  }

  .stats-row {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .stat-item {
    display: flex;
    align-items: center;
    gap: 4px;

    &.success {
      color: var(--el-color-success);
    }

    &.failure {
      color: var(--el-color-danger);
    }

    &.total {
      color: var(--el-color-primary);
    }
  }
}
</style>
