{"RootPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.App.Devices", "ProjectFileName": "Alsi.App.Devices.csproj", "Configuration": "Debug|AnyCPU", "FrameworkPath": "", "Sources": [{"SourceFile": "AppExtensions.cs"}, {"SourceFile": "Core\\CommunicationType.cs"}, {"SourceFile": "Core\\CanFrame.cs"}, {"SourceFile": "Core\\DataBus.cs"}, {"SourceFile": "Core\\DataBusTimer.cs"}, {"SourceFile": "Core\\ChannelConfig.cs"}, {"SourceFile": "Core\\ICanChannel.cs"}, {"SourceFile": "Core\\TimestampUtils.cs"}, {"SourceFile": "Core\\Channels\\TsLibCanChannel.cs"}, {"SourceFile": "Core\\Channels\\VectorCanChannel.cs"}, {"SourceFile": "DeviceMidware.cs"}, {"SourceFile": "Core\\DeviceChannel.cs"}, {"SourceFile": "DeviceEnv.cs"}, {"SourceFile": "Log\\BlfLogReader.cs"}, {"SourceFile": "Log\\BlfLogWriter.cs"}, {"SourceFile": "Log\\BlfStructs.cs"}, {"SourceFile": "TsLibCan\\EncapsulationLibTsCan.cs"}, {"SourceFile": "Vector\\EncapsulationVectorCan.cs"}, {"SourceFile": "Core\\Manufacturer.cs"}, {"SourceFile": "Properties\\AssemblyInfo.cs"}, {"SourceFile": "TsLibCan\\TosunConsts.cs"}, {"SourceFile": "TsLibCan\\TsLibApi.cs"}, {"SourceFile": "TsLibCan\\TsLibCanChannelInfo.cs"}, {"SourceFile": "Vector\\VectorDeviceApi.cs"}, {"SourceFile": "Vector\\VectorInfo.cs"}, {"SourceFile": "obj\\Debug\\.NETFramework,Version=v4.6.2.AssemblyAttributes.cs"}], "References": [{"Reference": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.App\\bin\\Debug\\Alsi.App.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": true, "ProjectPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.App\\bin\\Debug\\Alsi.App.dll"}, {"Reference": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.App.Devices\\x64\\libTsCan\\Interop.TsCANApi.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6.2\\Microsoft.CSharp.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6.2\\mscorlib.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6.2\\System.Core.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6.2\\System.Data.DataSetExtensions.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6.2\\System.Data.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6.2\\System.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6.2\\System.Xml.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "C:\\Program Files (x86)\\Reference Assemblies\\Microsoft\\Framework\\.NETFramework\\v4.6.2\\System.Xml.Linq.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}, {"Reference": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.App.Devices\\x64\\vector\\vxlapi_NET.dll", "ResolvedFrom": "", "OriginalItemSpec": "", "Name": "", "EmbedInteropTypes": false, "CopyLocal": false, "IsProjectReference": false, "ProjectPath": ""}], "Analyzers": [], "Outputs": [{"OutputItemFullPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.App.Devices\\bin\\Debug\\Alsi.App.Devices.dll", "OutputItemRelativePath": "Alsi.App.Devices.dll"}, {"OutputItemFullPath": "", "OutputItemRelativePath": ""}], "CopyToOutputEntries": []}