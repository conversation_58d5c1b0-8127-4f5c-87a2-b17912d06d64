using Alsi.Fuzz.Core.Contracts.Tester;
using Alsi.Fuzz.Core.Service.Results;
using System;
using System.Threading.Tasks;

namespace Alsi.Fuzz.Core.Service.Tester
{
    /// <summary>
    /// Tester进程API客户端接口
    /// </summary>
    public interface ITesterApiClient : IDisposable
    {
        /// <summary>
        /// 获取Tester是否正在运行
        /// </summary>
        /// <returns>Tester运行状态</returns>
        Task<bool> IsRunningAsync();

        /// <summary>
        /// 请求Tester进程退出
        /// </summary>
        /// <returns>操作是否成功</returns>
        Task<ApiResponse> ExitAsync();

        /// <summary>
        /// 暂停 Tester
        /// </summary>
        Task<ApiResponse> PauseAsync();

        /// <summary>
        /// 恢复暂停的 Tester
        /// </summary>
        Task<ApiResponse> ResumeAsync();

        /// <summary>
        /// 检查Tester API是否可访问
        /// </summary>
        /// <returns>API是否可访问</returns>
        Task<ApiResponse> PingAsync();

        /// <summary>
        /// 启动互操作测试
        /// </summary>
        /// <returns>启动是否成功</returns>
        Task<ApiResponse> StartInteroperationTestAsync();

        Task<ApiResponse> StopAsync();

        Task<TesterSnapshot> GetTesterSnapshotAsync();

        /// <summary>
        /// 获取带分页的Tester快照，可指定获取策略
        /// </summary>
        Task<TesterSnapshotResponse> GetTesterSnapshotPagedAsync(PagedQuery pagedQuery);

        Task StartAsync(TestResult testResult);
    }
}
