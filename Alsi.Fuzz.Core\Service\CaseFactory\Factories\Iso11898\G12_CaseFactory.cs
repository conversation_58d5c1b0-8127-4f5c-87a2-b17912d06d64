using Alsi.App.Devices.Core;
using Alsi.Common.Utils;
using Alsi.Common.Utils.Autosar;
using Alsi.Fuzz.Core.Models.TestPlans;
using Alsi.Fuzz.Core.Service.CaseFactory.Core;
using Alsi.Fuzz.Core.Utils;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Alsi.Fuzz.Core.Service.CaseFactory.Factories.Iso11898
{
    public class G12_CaseFactory : ICaseFactory
    {
        public IsoType IsoType => IsoType.Iso11898;

        public CaseMutation[] Generate(MutationOptions options)
        {
            var list = new List<CaseMutation>();

            var txWhiteListFrames = options.WhiteListFrames.Where(x => x.Transmitter == options.SelectedNodeName);
            var rxWhiteListFrames = options.WhiteListFrames.Where(x => x.Receivers.Contains(options.SelectedNodeName));

            if (options.CommunicationType == CommunicationType.CanFd)
            {
                foreach (var txWhiteListFrame in txWhiteListFrames)
                {
                    InternalGenerate(list, txWhiteListFrame, true, options);
                }

                foreach (var rxWhiteListFrame in rxWhiteListFrames)
                {
                    InternalGenerate(list, rxWhiteListFrame, true, options);
                }
            }

            return list.ToArray();
        }

        private void InternalGenerate(
            List<CaseMutation> list, WhiteListFrame whiteListFrame, bool isTx, MutationOptions options)
        {
            var id = whiteListFrame.Id;
            var dlc = whiteListFrame.Dlc;
            var length = DlcUtils.GetDataLength(dlc);
            var dir = isTx ? "Tx" : "Rx";

            var random = options.Random;

            // G121
            var payload0 = Enumerable.Range(0, length).Select(x => (byte)0).ToArray();

            // 修改为随机使用 0x7F 或 0x80
            var is0x7F = random.Next(2) == 0;
            var payloadMiddle = Enumerable.Range(0, length).Select(x => is0x7F ? (byte)0x7F : (byte)0x80).ToArray();

            var payloadFF = Enumerable.Range(0, length).Select(x => (byte)0xFF).ToArray();

            foreach (var payload in new[] { payload0, payloadMiddle, payloadFF })
            {
                var mutation = CaseMutation.Create($"G121_ID_{id:X}")
                    .MutateId(id)
                    .MutateDlc(dlc)
                    .MutateRtr(0)
                    .MutateExt(whiteListFrame.IsExt)
                    .MutateData(payload);
                list.Add(mutation);
            }

            // G122
            var dataList = new List<byte[]>();
            if (options.Coverage == CoverageType.High)
            {
                dataList = new LhsSampler().GenerateByteBasedSamples(length);
            }
            else if (options.Coverage == CoverageType.Normal)
            {
                dataList = new LhsSampler().GenerateBitBasedSamples(length);
            }

            dataList = dataList.OrderBy(x => x.ToHex()).ToList();
            foreach (var data in dataList)
            {
                var mutation = CaseMutation.Create($"G122_ID_{id:X}")
                    .MutateId(id)
                    .MutateDlc(dlc)
                    .MutateRtr(0)
                    .MutateExt(whiteListFrame.IsExt)
                    .MutateData(data);
                list.Add(mutation);
            }

            // G123
            var mutationG123 = CaseMutation.Create($"G123_ID_{id:X}")
                .MutateId(id)
                .MutateDlc(0)
                .MutateRtr(0)
                .MutateExt(whiteListFrame.IsExt)
                .MutateData(Array.Empty<byte>());
            list.Add(mutationG123);

            // G124
            var mutationG124 = CaseMutation.Create($"G124_ID_{id:X}")
                .MutateId(id)
                .MutateDlc(dlc)
                .MutateRtr(1)
                .MutateExt(whiteListFrame.IsExt)
                .MutateData(Array.Empty<byte>());
            list.Add(mutationG124);

            // G125
            var mutationG125 = CaseMutation.Create($"G125_ID_{id:X}")
                .MutateId(id)
                .MutateDlc(0)
                .MutateRtr(0)
                .MutateExt(whiteListFrame.IsExt)
                .MutateData(Array.Empty<byte>());
            list.Add(mutationG125);

            // G126
            var mutationG126 = CaseMutation.Create($"G125_ID_{id:X}")
                .MutateId(id)
                .MutateDlc(0)
                .MutateRtr(1)
                .MutateExt(whiteListFrame.IsExt)
                .MutateData(Array.Empty<byte>());
            list.Add(mutationG126);

            // G127
            for (var mutateDlc = 0; mutateDlc <= 15; mutateDlc++)
            {
                if (mutateDlc != dlc)
                {
                    var bytes = new byte[DlcUtils.GetDataLength(mutateDlc)];
                    random.NextBytes(bytes);
                    var mutationG127 = CaseMutation.Create($"G127_ID_{id:X}")
                        .MutateId(id)
                        .MutateDlc(mutateDlc)
                        .MutateRtr(0)
                        .MutateExt(whiteListFrame.IsExt)
                        .MutateData(bytes);
                    list.Add(mutationG127);
                }
            }

            // G128
            for (var mutateDlc = 0; mutateDlc <= 15; mutateDlc++)
            {
                if (mutateDlc != dlc)
                {
                    var bytes = new byte[0];
                    var mutationG128 = CaseMutation.Create($"G128_ID_{id:X}")
                        .MutateId(id)
                        .MutateDlc(mutateDlc)
                        .MutateRtr(1)
                        .MutateExt(whiteListFrame.IsExt)
                        .MutateData(bytes);
                    list.Add(mutationG128);
                }
            }
        }
    }
}
