using Alsi.App;
using Alsi.Fuzz.Core.Contracts.Tester;
using Alsi.Fuzz.Core.Service.Results;
using Alsi.Fuzz.Core.Service.Tester;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace Alsi.Fuzz.Core.Service
{
    public class StatusPollingService
    {
        public bool IsTesterRunning { get; set; } = false;
        public TesterSnapshot TesterSnapshot = new TesterSnapshot();

        private Timer _statusPollingTimer;

        private int _lastPendingIndex = 0;

        /// <summary>
        /// 开始状态轮询
        /// </summary>
        public void StartStatusPolling()
        {
            // 每2秒轮询一次状态
            _statusPollingTimer = new Timer(
                async _ => await UpdateStatusAsync(false),
                null,
                TimeSpan.Zero,
                TimeSpan.FromMilliseconds(300));
        }

        /// <summary>
        /// 停止状态轮询
        /// </summary>
        public void StopStatusPolling()
        {
            _statusPollingTimer?.Dispose();
            _statusPollingTimer = null;
        }

        /// <summary>
        /// 更新状态（用于定时轮询）
        /// </summary>
        public async Task UpdateStatusAsync(bool updateAll)
        {
            try
            {
                if (updateAll)
                {
                    // 第一次获取全部测试用例
                    TesterSnapshot = await TesterManager.Instance.GetApiClient().GetTesterSnapshotAsync();

                    // 初始化已获取的用例结果
                    if (TesterSnapshot?.CaseResults != null)
                    {
                        _lastPendingIndex = FindFirstPendingIndex(TesterSnapshot.CaseResults);
                    }
                }
                else
                {
                    // 后续更新，获取从第一个Pending状态开始的1000个用例
                    var pagedQuery = new PagedQuery
                    {
                        PageNumber = (_lastPendingIndex / 1000) + 1,
                        PageSize = 1000
                    };

                    var snapshotResponse = await TesterManager.Instance.GetApiClient().GetTesterSnapshotPagedAsync(pagedQuery);

                    if (snapshotResponse != null)
                    {
                        // 更新状态信息
                        TesterSnapshot.ProcessState = snapshotResponse.ProcessState;
                        TesterSnapshot.CurrentOperation = snapshotResponse.CurrentOperation;
                        TesterSnapshot.TestResult = snapshotResponse.TestResult;

                        // 更新对应的用例结果
                        if (snapshotResponse.PagedCaseResult != null && snapshotResponse.PagedCaseResult.Items.Length > 0)
                        {
                            UpdateCaseResults(snapshotResponse.PagedCaseResult.Items);

                            // 更新下一次需要查询的索引
                            _lastPendingIndex = FindFirstPendingIndex(TesterSnapshot.CaseResults);
                        }
                    }
                }

                IsTesterRunning = !TesterSnapshot?.IsCompleted() ?? false;

                if (TesterSnapshot?.TestResult != null)
                {
                    var resultReader = new TestResultReaderService();
                    resultReader.UpdateTestResult(TesterSnapshot.TestResult);
                }

                if (TesterSnapshot?.IsCompleted() ?? false)
                {
                    StopStatusPolling();
                    await TesterManager.Instance.GetApiClient().ExitAsync();
                }
            }
            catch (Exception e)
            {
                AppEnv.Logger.Error(e, "error in tester snapshot polling");
            }
        }

        /// <summary>
        /// 查找第一个Pending状态的用例索引
        /// </summary>
        private int FindFirstPendingIndex(CaseResult[] caseResults)
        {
            if (caseResults == null || caseResults.Length == 0)
                return 0;

            for (int i = 0; i < caseResults.Length; i++)
            {
                if (caseResults[i].State == ExecutionState.Pending)
                    return i;
            }

            // 如果没有Pending状态的，返回数组长度（表示全部完成）
            return caseResults.Length;
        }

        /// <summary>
        /// 更新测试用例结果
        /// </summary>
        private void UpdateCaseResults(CaseResult[] updatedResults)
        {
            if (TesterSnapshot.CaseResults == null || TesterSnapshot.CaseResults.Length == 0)
            {
                TesterSnapshot.CaseResults = updatedResults;
                return;
            }

            // 根据ID匹配并更新状态
            foreach (var updated in updatedResults)
            {
                for (int i = 0; i < TesterSnapshot.CaseResults.Length; i++)
                {
                    if (TesterSnapshot.CaseResults[i].Id == updated.Id)
                    {
                        TesterSnapshot.CaseResults[i] = updated;
                        break;
                    }
                }
            }
        }
    }
}
