"use strict";(self["webpackChunkfuzz_web"]=self["webpackChunkfuzz_web"]||[]).push([[153],{4153:function(e,a,l){l.r(a),l.d(a,{default:function(){return g}});l(8111),l(116);var t=l(6768),u=l(144),n=l(1219),c=l(1748),s=l(8769),o=l(687);const i={class:"sequence-setting-container"},v={class:"left-panel"},d={class:"right-panel"};var r=(0,t.pM)({__name:"SequenceSetting",setup(e){const a=(0,u.KR)(!0),l=(0,u.KR)([]),r=(0,u.KR)(""),k=(0,u.KR)(""),m=(0,u.KR)({testSuiteName:"",sequencePackageName:""}),g={testSuiteName:"",sequencePackageName:""},f=(0,t.EW)((()=>{const e=l.value.find((e=>e.name===r.value));return e?.packages||[]})),p=(0,u.KR)(""),b=async()=>{if(r.value&&k.value)try{const e=await c.p.getXml(r.value,k.value);p.value=e.data}catch(e){n.nk.error("Failed to load XML content"),console.error("Error loading XML:",e)}},y=()=>{k.value="",p.value=""},S=async()=>{try{const e=await c.p.getBuiltIn();l.value=e.data}catch(e){n.nk.error("Failed to load test suites")}},h=async()=>{a.value=!0;try{const e=await s.Wm.getSequenceConfig();m.value=e.data,r.value=m.value.testSuiteName||"",k.value=m.value.sequencePackageName||"",r.value&&k.value&&await b()}catch(e){n.nk.error("Failed to load configuration")}finally{a.value=!1}},w=async()=>{if(r.value&&k.value){a.value=!0;try{const e={testSuiteName:r.value,sequencePackageName:k.value,sequencePackageXml:p.value};await s.Wm.updateSequenceConfig(e),n.nk.success("Save successful"),await b()}catch(e){console.error("Save failed:",e)}finally{a.value=!1}}else n.nk.warning("Please select both test suite and sequence package")},q=()=>{r.value=g.testSuiteName,k.value=g.sequencePackageName};return(0,t.sV)((async()=>{await S(),await h()})),(e,u)=>{const n=(0,t.g2)("el-option"),c=(0,t.g2)("el-select"),s=(0,t.g2)("el-form-item"),g=(0,t.g2)("el-button"),S=(0,t.g2)("el-form"),h=(0,t.g2)("el-empty"),_=(0,t.gN)("loading");return(0,t.bo)(((0,t.uX)(),(0,t.CE)("div",i,[(0,t.Lk)("div",v,[(0,t.bF)(S,{model:m.value,"label-width":"160px","label-position":"top"},{default:(0,t.k6)((()=>[(0,t.bF)(s,{label:"Test Suite"},{default:(0,t.k6)((()=>[(0,t.bF)(c,{modelValue:r.value,"onUpdate:modelValue":u[0]||(u[0]=e=>r.value=e),placeholder:"Select Test Suite",onChange:y,style:{width:"100%"}},{default:(0,t.k6)((()=>[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(l.value,(e=>((0,t.uX)(),(0,t.Wv)(n,{key:e.name,label:`${e.name} v${e.version}`,value:e.name},null,8,["label","value"])))),128))])),_:1},8,["modelValue"])])),_:1}),(0,t.bF)(s,{label:"Sequence Package"},{default:(0,t.k6)((()=>[(0,t.bF)(c,{modelValue:k.value,"onUpdate:modelValue":u[1]||(u[1]=e=>k.value=e),placeholder:"Select Sequence Package",disabled:!r.value,style:{width:"100%"},onChange:b},{default:(0,t.k6)((()=>[((0,t.uX)(!0),(0,t.CE)(t.FK,null,(0,t.pI)(f.value,((e,a)=>((0,t.uX)(),(0,t.Wv)(n,{key:a,label:e.name,value:e.name},null,8,["label","value"])))),128))])),_:1},8,["modelValue","disabled"])])),_:1}),(0,t.bF)(s,null,{default:(0,t.k6)((()=>[(0,t.bF)(g,{type:"primary",onClick:w},{default:(0,t.k6)((()=>u[3]||(u[3]=[(0,t.eW)("Save")]))),_:1}),(0,t.bF)(g,{onClick:q,style:{"margin-left":"10px"}},{default:(0,t.k6)((()=>u[4]||(u[4]=[(0,t.eW)("Reset")]))),_:1})])),_:1})])),_:1},8,["model"])]),(0,t.Lk)("div",d,[r.value&&k.value?((0,t.uX)(),(0,t.Wv)(o.A,{key:0,title:`${r.value} - ${k.value}`,content:p.value,"onUpdate:content":u[2]||(u[2]=e=>p.value=e)},null,8,["title","content"])):((0,t.uX)(),(0,t.Wv)(h,{key:1,style:{background:"#DDD2"},description:"No sequence content","image-size":150}))])])),[[_,a.value]])}}}),k=l(1241);const m=(0,k.A)(r,[["__scopeId","data-v-3860eaac"]]);var g=m}}]);
//# sourceMappingURL=153.8f9ab41e.js.map