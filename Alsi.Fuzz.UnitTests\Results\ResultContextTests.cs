using Alsi.Fuzz.Core.Service.Results;
using Shouldly;

namespace Alsi.Fuzz.UnitTests.Results
{
    public class ResultContextTests : UnitTestBase
    {
        public ResultContextTests()
        {
            // 确保测试目录存在
            Directory.CreateDirectory(Path.Combine(UnitTestAppContext.WebHostApp.DataFolder, "case_results"));
            Directory.CreateDirectory(Path.Combine(UnitTestAppContext.WebHostApp.DataFolder, "interoperation"));
        }

        [Fact]
        public void Constructor_WithValidParams_ShouldCreateInstance()
        {
            // Arrange
            var testType = TestType.Case;
            var folderName = "test_folder";

            // Act
            var context = new ResultContext(testType, folderName);

            // Assert
            context.ShouldNotBeNull();
            context.TestType.ShouldBe(testType);
            context.ResultFolder.ShouldContain(folderName);
            context.ResultDbPath.ShouldContain("result.sqlite");
            context.DataLogPath.ShouldContain("result.blf");
        }

        [Fact]
        public void Constructor_WithEmptyFolderName_ShouldThrowException()
        {
            // Arrange & Act & Assert
            Should.Throw<ArgumentException>(() => new ResultContext(TestType.Case, ""));
        }

        [Fact]
        public void Constructor_WithLongFolderName_ShouldThrowException()
        {
            // Arrange
            var longFolderName = new string('a', 51); // 51个字符，超过50的限制

            // Act & Assert
            Should.Throw<ArgumentException>(() => new ResultContext(TestType.Case, longFolderName));
        }

        [Fact]
        public void Constructor_WithPathSeparators_ShouldThrowException()
        {
            // Arrange & Act & Assert
            Should.Throw<ArgumentException>(() => new ResultContext(TestType.Case, "folder/subFolder"));
            Should.Throw<ArgumentException>(() => new ResultContext(TestType.Case, @"folder\subFolder"));
        }

        [Fact]
        public void Constructor_WithTraversalSequence_ShouldThrowException()
        {
            // Arrange & Act & Assert
            Should.Throw<ArgumentException>(() => new ResultContext(TestType.Case, "folder.."));
            Should.Throw<ArgumentException>(() => new ResultContext(TestType.Case, "..folder"));
            Should.Throw<ArgumentException>(() => new ResultContext(TestType.Case, "folder..other"));
        }

        [Fact]
        public void Constructor_WithInvalidFileName_ShouldThrowException()
        {
            // Arrange & Act & Assert
            Should.Throw<ArgumentException>(() => new ResultContext(TestType.Case, "folder:name"));
            Should.Throw<ArgumentException>(() => new ResultContext(TestType.Case, "folder*"));
            Should.Throw<ArgumentException>(() => new ResultContext(TestType.Case, "folder?"));
        }

        [Theory]
        [InlineData(TestType.Case, "case_results")]
        [InlineData(TestType.Interoperation, "interoperation")]
        public void Constructor_DifferentTestTypes_ShouldCreateCorrectPaths(TestType testType, string expectedFolderType)
        {
            // Arrange
            var folderName = "test_results";

            // Act
            var context = new ResultContext(testType, folderName);

            // Assert
            context.ResultFolder.ShouldContain(expectedFolderType);
            context.ResultFolder.ShouldContain(folderName);
        }
    }
}
