using Alsi.Common.Utils;
using Alsi.Fuzz.Core.Models.TestPlans;
using ICSharpCode.SharpZipLib.Zip;
using System;
using System.IO;
using System.Text;

namespace Alsi.Fuzz.Core.Storage
{
    public class TestPlanStorage
    {
        private const string ManifestFile = "manifest.json";
        private const string ConfigFile = "config.json";
        public const string SecurityAccessFile = "SecurityAccess.dll";

        public void Save(string filePath, TestPlan testPlan)
        {
            using (var zipStream = new ZipOutputStream(File.Create(filePath)))
            {
                zipStream.SetLevel(9);

                testPlan.Manifest.Modified = DateTime.Now;

                // 保存manifest.json
                var manifestJson = JsonUtils.Serialize(testPlan.Manifest, isIndented: true);
                var manifestBytes = Encoding.UTF8.GetBytes(manifestJson);
                var manifestEntry = new ZipEntry(ManifestFile);
                zipStream.PutNextEntry(manifestEntry);
                zipStream.Write(manifestBytes, 0, manifestBytes.Length);

                // 保存config.json
                var configJson = JsonUtils.Serialize(testPlan.Config, isIndented: true);
                var configBytes = Encoding.UTF8.GetBytes(configJson);
                var configEntry = new ZipEntry(ConfigFile);
                zipStream.PutNextEntry(configEntry);
                zipStream.Write(configBytes, 0, configBytes.Length);
                
                // 保存SecurityAccess.dll
                if (testPlan.Config.SecurityConfig != null && 
                    testPlan.Config.SecurityConfig.DllBytes != null && 
                    testPlan.Config.SecurityConfig.DllBytes.Length > 0)
                {
                    var securityAccessEntry = new ZipEntry(SecurityAccessFile);
                    zipStream.PutNextEntry(securityAccessEntry);
                    zipStream.Write(testPlan.Config.SecurityConfig.DllBytes, 0, testPlan.Config.SecurityConfig.DllBytes.Length);
                }
            }
        }

        public TestPlan Load(string filePath)
        {
            var testPlan = new TestPlan();

            using (var zipStream = new ZipFile(filePath))
            {
                // 读取manifest.json
                var manifestEntry = zipStream.GetEntry(ManifestFile);
                if (manifestEntry == null)
                    throw new FileNotFoundException("manifest.json not found in test plan file.");

                using (var stream = zipStream.GetInputStream(manifestEntry))
                using (var reader = new StreamReader(stream))
                {
                    var json = reader.ReadToEnd();
                    testPlan.Manifest = JsonUtils.Deserialize<TestPlanManifest>(json);
                }

                // 读取config.json
                var configEntry = zipStream.GetEntry(ConfigFile);
                if (configEntry == null)
                    throw new FileNotFoundException("config.json not found in test plan file.");

                using (var stream = zipStream.GetInputStream(configEntry))
                using (var reader = new StreamReader(stream))
                {
                    var json = reader.ReadToEnd();
                    testPlan.Config = JsonUtils.Deserialize<TestPlanConfig>(json);
                }

                // 读取 Security Access 的 dll
                var securityAccessEntry = zipStream.GetEntry(SecurityAccessFile);
                if (securityAccessEntry != null)
                {
                    using (var stream = zipStream.GetInputStream(securityAccessEntry))
                    using (var memoryStream = new MemoryStream())
                    {
                        byte[] buffer = new byte[4096];
                        int bytesRead;
                        while ((bytesRead = stream.Read(buffer, 0, buffer.Length)) > 0)
                        {
                            memoryStream.Write(buffer, 0, bytesRead);
                        }
                        testPlan.Config.SecurityConfig.DllBytes = memoryStream.ToArray();
                    }
                }
            }

            return testPlan;
        }
    }
}
