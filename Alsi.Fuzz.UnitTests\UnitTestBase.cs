using Alsi.App.Database.Midwares;

namespace Alsi.Fuzz.UnitTests
{
    public class UnitTestBase : IDisposable
    {
        protected readonly UnitTestAppContext UnitTestAppContext;
        protected DbContext AppDbContext => UnitTestAppContext.AppDbContext;

        public UnitTestBase()
        {
            UnitTestAppContext = new UnitTestAppContext();
        }

        public virtual void Dispose()
        {
            if (UnitTestAppContext != null)
            {
                UnitTestAppContext.Dispose();
            }
        }
    }
}
