using Alsi.Common.Utils;
using Alsi.Fuzz.Core.Service.CaseFactory.Core;
using Alsi.Fuzz.Core.Utils;
using System.Collections.Generic;
using System.Linq;

namespace Alsi.Fuzz.Core.Service.CaseFactory.Factories.Iso14229
{
    public class G431_G432_CaseFactory : CaseFactoryBase
    {
        public override CaseMutation[] Generate(MutationOptions options)
        {
            var caseMutations = new List<CaseMutation>();

            var xmlServices = options.XmlServices;
            var supportedXmlServicesSF = xmlServices
                .Where(x => x.IsSupported && !x.HasMultipleFrameRequest)
                .ToArray();

            foreach (var byteN_PCI in new byte[] { 0x10, 0x21, 0x22, 0x23 })
            {
                foreach (var xmlService in supportedXmlServicesSF)
                {
                    var sid = xmlService.Id;
                    var parameter2k = xmlService.Parameter2k;

                    var payload = new List<byte> { sid };
                    if (xmlService.SubfunctionId.HasValue)
                    {
                        payload.Add(xmlService.SubfunctionId.Value);
                    }
                    payload.AddRange(parameter2k);

                    var caseMutation = CaseMutation.Create($"G431-Sid{sid:X2}")
                        .Mutate(MutationFieldType.UDS_Insert_N_PCI_Before_SF, $"0x{byteN_PCI:X}")
                        .MutatePayload(payload.ToArray())
                        .MutatePayloadLength(payload.Count);
                    caseMutations.Add(caseMutation);
                }
            }

            var supportedXmlServicesMF = xmlServices
                .Where(x => x.IsSupported && x.HasMultipleFrameRequest)
                .ToArray();
            // #4-3-2 Send Multi Frame service Content As SingleFrame
            foreach (var xmlService in supportedXmlServicesMF)
            {
                var sid = xmlService.Id;
                var parameter2k = xmlService.Parameter2k;

                var payload = new List<byte> { sid };
                if (xmlService.SubfunctionId.HasValue)
                {
                    payload.Add(xmlService.SubfunctionId.Value);
                }
                payload.AddRange(parameter2k);

                foreach (var length in new[] { 1, 2, 3, 4, 5, 6 })
                {
                    var caseMutation = CaseMutation.Create($"G432-Sid{sid:X2}")
                        .Mutate(MutationFieldType.UDS_Send_MF_As_SF_Length, $"{length}")
                        .MutatePayload(payload.ToArray())
                        .MutatePayloadLength(payload.Count);
                    caseMutations.Add(caseMutation);
                }
            }

            // #4-3-3 Multiframe Transmit N_PCI Error
            // 通过增加/减少数据，调整到 23 bytes
            // 然后按 MF 发送，异变其中 FF、CF 的 HighNibble 和 SN
            foreach (var xmlService in supportedXmlServicesMF)
            {
                var sid = xmlService.Id;
                var parameter2k = xmlService.Parameter2k;

                var payload = new List<byte> { sid };
                if (xmlService.SubfunctionId.HasValue)
                {
                    payload.Add(xmlService.SubfunctionId.Value);
                }
                payload.AddRange(parameter2k);

                payload = payload.Take(23).ToList();
                while (payload.Count < 23)
                {
                    payload.Add(0x55);
                }

                for (var invalidHighNibble = 0; invalidHighNibble <= 0xF; invalidHighNibble++)
                {
                    if (invalidHighNibble != 1)
                    {
                        var caseMutation = CaseMutation.Create($"G4331-Sid{sid:X2}")
                            .Mutate(MutationFieldType.UDS_FF_HighNibble, $"0x{invalidHighNibble:X}")
                            .MutatePayload(payload.ToArray())
                            .MutatePayloadLength(payload.Count);
                        caseMutations.Add(caseMutation);
                    }
                }


                var sampler = new UniformSampler(options.Random);
                foreach (var ffdl_12b in sampler.UniformSample(1, 0xFFF, 15))
                {
                    var caseMutation = CaseMutation.Create($"G4332-Sid{sid:X2}")
                        .Mutate(MutationFieldType.UDS_FF_DL_12b, $"0x{ffdl_12b:X}")
                        .MutatePayload(payload.ToArray())
                        .MutatePayloadLength(payload.Count);
                    caseMutations.Add(caseMutation);
                }

                foreach (var invalidHighNibble in Enumerable.Range(0, 0xF + 1).Except(new[] { 2 }))
                {
                    var data = new byte[] { (byte)(invalidHighNibble * 0x10 + 1), 0x22, 0x23 };
                    var caseMutation = CaseMutation.Create($"G4333-Sid{sid:X2}")
                        .Mutate(MutationFieldType.UDS_CF_BYTE1_LIST, $"hex: {data.ToHex()}")
                        .MutatePayload(payload.ToArray())
                        .MutatePayloadLength(payload.Count);
                    caseMutations.Add(caseMutation);
                }

                foreach (var invalidSN in Enumerable.Range(0, 0xF + 1).Except(new[] { 1 }))
                {
                    var data = new byte[] { (byte)(0x20 + invalidSN), 0x22, 0x23 };
                    var caseMutation = CaseMutation.Create($"G4334-Sid{sid:X2}")
                        .Mutate(MutationFieldType.UDS_CF_BYTE1_LIST, $"hex: {data.ToHex()}")
                        .MutatePayload(payload.ToArray())
                        .MutatePayloadLength(payload.Count);
                    caseMutations.Add(caseMutation);
                }

                foreach (var invalidHighNibble in Enumerable.Range(0, 0xF + 1).Except(new[] { 2 }))
                {
                    var data = new byte[] { 0x21, (byte)(invalidHighNibble * 0x10 + 2), 0x23 };
                    var caseMutation = CaseMutation.Create($"G4335-Sid{sid:X2}")
                        .Mutate(MutationFieldType.UDS_CF_BYTE1_LIST, $"hex: {data.ToHex()}")
                        .MutatePayload(payload.ToArray())
                        .MutatePayloadLength(payload.Count);
                    caseMutations.Add(caseMutation);
                }

                foreach (var invalidSN in Enumerable.Range(0, 0xF + 1).Except(new[] { 2 }))
                {
                    var data = new byte[] { 0x21, (byte)(0x20 + invalidSN), 0x23 };
                    var caseMutation = CaseMutation.Create($"G4336-Sid{sid:X2}")
                        .Mutate(MutationFieldType.UDS_CF_BYTE1_LIST, $"hex: {data.ToHex()}")
                        .MutatePayload(payload.ToArray())
                        .MutatePayloadLength(payload.Count);
                    caseMutations.Add(caseMutation);
                }

                foreach (var invalidHighNibble in Enumerable.Range(0, 0xF + 1).Except(new[] { 2 }))
                {
                    var data = new byte[] { 0x21, 0x22, (byte)(invalidHighNibble * 0x10 + 3) };
                    var caseMutation = CaseMutation.Create($"G4337-Sid{sid:X2}")
                        .Mutate(MutationFieldType.UDS_CF_BYTE1_LIST, $"hex: {data.ToHex()}")
                        .MutatePayload(payload.ToArray())
                        .MutatePayloadLength(payload.Count);
                    caseMutations.Add(caseMutation);
                }

                foreach (var invalidSN in Enumerable.Range(0, 0xF + 1).Except(new[] { 3 }))
                {
                    var data = new byte[] { 0x21, 0x22, (byte)(0x20 + invalidSN) };
                    var caseMutation = CaseMutation.Create($"G4338-Sid{sid:X2}")
                        .Mutate(MutationFieldType.UDS_CF_BYTE1_LIST, $"hex: {data.ToHex()}")
                        .MutatePayload(payload.ToArray())
                        .MutatePayloadLength(payload.Count);
                    caseMutations.Add(caseMutation);
                }
            }

            // #4-3-4 Multiframe Transmit Sequence Error
            foreach (var xmlService in supportedXmlServicesMF)
            {
                var sid = xmlService.Id;
                var parameter2k = xmlService.Parameter2k;

                var payload = new List<byte> { sid };
                if (xmlService.SubfunctionId.HasValue)
                {
                    payload.Add(xmlService.SubfunctionId.Value);
                }
                payload.AddRange(parameter2k);

                payload = payload.Take(23).ToList();
                while (payload.Count < 23)
                {
                    payload.Add(0x55);
                }

                // 1/2 : Replace Initial CF1 with manipulated FF
                for (var invalidHighNibble = 0; invalidHighNibble <= 0xF; invalidHighNibble++)
                {
                    if (invalidHighNibble != 1)
                    {
                        var caseMutation = CaseMutation.Create($"G4341-Sid{sid:X2}-CF1")
                            .Mutate(MutationFieldType.UDS_Replace_CF_With_FF_NUM, "1")
                            .Mutate(MutationFieldType.UDS_Replace_CF_With_FF_N_PCI, $"0x{invalidHighNibble:X}")
                            .Mutate(MutationFieldType.UDS_Replace_CF_With_FF_DL_12b, "0x017")
                            .MutatePayload(payload.ToArray())
                            .MutatePayloadLength(payload.Count);
                        caseMutations.Add(caseMutation);
                    }
                }

                var sampler = new UniformSampler(options.Random);
                foreach (var ffdl_12b in new[] { 0 }.Concat(sampler.UniformSample(1, 0xFFF, 15)))
                {
                    var caseMutation = CaseMutation.Create($"G4341-Sid{sid:X2}-CF1")
                        .Mutate(MutationFieldType.UDS_Replace_CF_With_FF_NUM, "1")
                        .Mutate(MutationFieldType.UDS_Replace_CF_With_FF_N_PCI, "0x1")
                        .Mutate(MutationFieldType.UDS_Replace_CF_With_FF_DL_12b, $"0x{ffdl_12b:X}")
                        .MutatePayload(payload.ToArray())
                        .MutatePayloadLength(payload.Count);
                    caseMutations.Add(caseMutation);
                }

                // 3/4 : Replace Initial CF2 with manipulated FF
                for (var invalidHighNibble = 0; invalidHighNibble <= 0xF; invalidHighNibble++)
                {
                    if (invalidHighNibble != 1)
                    {
                        var caseMutation = CaseMutation.Create($"G4341-Sid{sid:X2}-CF2")
                            .Mutate(MutationFieldType.UDS_Replace_CF_With_FF_NUM, "2")
                            .Mutate(MutationFieldType.UDS_Replace_CF_With_FF_N_PCI, $"0x{invalidHighNibble:X}")
                            .Mutate(MutationFieldType.UDS_Replace_CF_With_FF_DL_12b, "0x017")
                            .MutatePayload(payload.ToArray())
                            .MutatePayloadLength(payload.Count);
                        caseMutations.Add(caseMutation);
                    }
                }

                foreach (var ffdl_12b in new[] { 0 }.Concat(sampler.UniformSample(1, 0xFFF, 15)))
                {
                    var caseMutation = CaseMutation.Create($"G4341-Sid{sid:X2}-CF2")
                        .Mutate(MutationFieldType.UDS_Replace_CF_With_FF_NUM, "2")
                        .Mutate(MutationFieldType.UDS_Replace_CF_With_FF_N_PCI, "0x1")
                        .Mutate(MutationFieldType.UDS_Replace_CF_With_FF_DL_12b, $"0x{ffdl_12b:X}")
                        .MutatePayload(payload.ToArray())
                        .MutatePayloadLength(payload.Count);
                    caseMutations.Add(caseMutation);
                }

                // 5/6 : Replace Initial CF3 with manipulated FF
                for (var invalidHighNibble = 0; invalidHighNibble <= 0xF; invalidHighNibble++)
                {
                    if (invalidHighNibble != 1)
                    {
                        var caseMutation = CaseMutation.Create($"G4341-Sid{sid:X2}-CF3")
                            .Mutate(MutationFieldType.UDS_Replace_CF_With_FF_NUM, "3")
                            .Mutate(MutationFieldType.UDS_Replace_CF_With_FF_N_PCI, $"0x{invalidHighNibble:X}")
                            .Mutate(MutationFieldType.UDS_Replace_CF_With_FF_DL_12b, "0x017")
                            .MutatePayload(payload.ToArray())
                            .MutatePayloadLength(payload.Count);
                        caseMutations.Add(caseMutation);
                    }
                }

                foreach (var ffdl_12b in new[] { 0 }.Concat(sampler.UniformSample(1, 0xFFF, 15)))
                {
                    var caseMutation = CaseMutation.Create($"G4341-Sid{sid:X2}-CF3")
                        .Mutate(MutationFieldType.UDS_Replace_CF_With_FF_NUM, "3")
                        .Mutate(MutationFieldType.UDS_Replace_CF_With_FF_N_PCI, "0x1")
                        .Mutate(MutationFieldType.UDS_Replace_CF_With_FF_DL_12b, $"0x{ffdl_12b:X}")
                        .MutatePayload(payload.ToArray())
                        .MutatePayloadLength(payload.Count);
                    caseMutations.Add(caseMutation);
                }

                // 7-10: Repeat Initial Frames
                foreach (int frameIndex in new[] { 0, 1, 2, 3 }) // 0=FF, 1=CF1, 2=CF2, 3=CF3
                {
                    foreach (int times in new[] { 5, 50, 100 })
                    {
                        var caseMutation = CaseMutation.Create($"G4342-Sid{sid:X2}")
                            .Mutate(MutationFieldType.UDS_Repeat_Index, $"{frameIndex}")
                            .Mutate(MutationFieldType.UDS_Repeat_Times, $"{times}")
                            .MutatePayload(payload.ToArray())
                            .MutatePayloadLength(payload.Count);
                        caseMutations.Add(caseMutation);
                    }
                }

                // 11-14: Skip Initial Frames
                foreach (int frameIndex in new[] { 0, 1, 2, 3 }) // 0=FF, 1=CF1, 2=CF2, 3=CF3
                {
                    var caseMutation = CaseMutation.Create($"G4343-Sid{sid:X2}")
                        .Mutate(MutationFieldType.UDS_Skip_Index, $"{frameIndex}")
                        .MutatePayload(payload.ToArray())
                        .MutatePayloadLength(payload.Count);
                    caseMutations.Add(caseMutation);
                }

                // 15-20: Swap Frames
                // Swap FF with CFx
                foreach (int cfIndex in new[] { 1, 2, 3 })
                {
                    var caseMutation = CaseMutation.Create($"G4344-Sid{sid:X2}")
                        .Mutate(MutationFieldType.UDS_Swap_A, "0")  // FF
                        .Mutate(MutationFieldType.UDS_Swap_B, $"{cfIndex}")  // CFx
                        .MutatePayload(payload.ToArray())
                        .MutatePayloadLength(payload.Count);
                    caseMutations.Add(caseMutation);
                }

                // Swap CF1 with CF2/CF3
                foreach (int cfIndex in new[] { 2, 3 })
                {
                    var caseMutation = CaseMutation.Create($"G4344-Sid{sid:X2}")
                        .Mutate(MutationFieldType.UDS_Swap_A, "1")  // CF1
                        .Mutate(MutationFieldType.UDS_Swap_B, $"{cfIndex}")  // CF2 or CF3
                        .MutatePayload(payload.ToArray())
                        .MutatePayloadLength(payload.Count);
                    caseMutations.Add(caseMutation);
                }

                // Swap CF2 with CF3
                var caseSwapCF2CF3 = CaseMutation.Create($"G4344-Sid{sid:X2}")
                    .Mutate(MutationFieldType.UDS_Swap_A, "2")  // CF2
                    .Mutate(MutationFieldType.UDS_Swap_B, "3")  // CF3
                    .MutatePayload(payload.ToArray())
                    .MutatePayloadLength(payload.Count);
                caseMutations.Add(caseSwapCF2CF3);

                // 21-22: Reverse Frame Order
                // Reverse: CF3->CF2->CF1->FF
                var caseReverse1 = CaseMutation.Create($"G4345-Sid{sid:X2}")
                    .Mutate(MutationFieldType.UDS_Reverse_From, "0")  // FF
                    .Mutate(MutationFieldType.UDS_Reverse_To, "3")    // CF3
                    .MutatePayload(payload.ToArray())
                    .MutatePayloadLength(payload.Count);
                caseMutations.Add(caseReverse1);

                // Reverse: FF->CF3->CF2->CF1
                var caseReverse2 = CaseMutation.Create($"G4345-Sid{sid:X2}")
                    .Mutate(MutationFieldType.UDS_Reverse_From, "1")  // FF
                    .Mutate(MutationFieldType.UDS_Reverse_To, "3")    // CF1
                    .MutatePayload(payload.ToArray())
                    .MutatePayloadLength(payload.Count);
                caseMutations.Add(caseReverse2);
            }

            return caseMutations.ToArray();
        }
    }
}
