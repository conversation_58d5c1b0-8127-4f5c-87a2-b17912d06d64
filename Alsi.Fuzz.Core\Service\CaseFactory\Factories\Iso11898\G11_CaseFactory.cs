using Alsi.App.Devices.Core;
using Alsi.Common.Utils;
using Alsi.Common.Utils.Autosar;
using Alsi.Fuzz.Core.Models.TestPlans;
using Alsi.Fuzz.Core.Service.CaseFactory.Core;
using Alsi.Fuzz.Core.Utils;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Alsi.Fuzz.Core.Service.CaseFactory.Factories.Iso11898
{
    public class G11_CaseFactory : ICaseFactory
    {
        public IsoType IsoType => IsoType.Iso11898;

        public CaseMutation[] Generate(MutationOptions options)
        {
            var list = new List<CaseMutation>();

            var txWhiteListFrames = options.WhiteListFrames.Where(x => x.Transmitter == options.SelectedNodeName);
            var rxWhiteListFrames = options.WhiteListFrames.Where(x => x.Receivers.Contains(options.SelectedNodeName));

            if (options.CommunicationType == CommunicationType.Can)
            {
                foreach (var txWhiteListFrame in txWhiteListFrames)
                {
                    InternalGenerate(list, txWhiteListFrame, true, options);
                }

                foreach (var rxWhiteListFrame in rxWhiteListFrames)
                {
                    InternalGenerate(list, rxWhiteListFrame, true, options);
                }
            }

            return list.ToArray();
        }

        private void InternalGenerate(
            List<CaseMutation> list, WhiteListFrame whiteListFrame, bool isTx, MutationOptions options)
        {
            var id = whiteListFrame.Id;
            var dlc = whiteListFrame.Dlc;
            var length = DlcUtils.GetDataLength(dlc);
            var dir = isTx ? "Tx" : "Rx";

            var random = options.Random;

            // G111
            var payload0 = Enumerable.Range(0, length).Select(x => (byte)0).ToArray();

            // 修改为随机使用 0x7F 或 0x80
            var is0x7F = random.Next(2) == 0;
            var payloadMiddle = Enumerable.Range(0, length).Select(x => is0x7F ? (byte)0x7F : (byte)0x80).ToArray();

            var payloadFF = Enumerable.Range(0, length).Select(x => (byte)0xFF).ToArray();

            foreach (var payload in new[] { payload0, payloadMiddle, payloadFF })
            {
                var mutation = CaseMutation.Create($"G111_ID_{id:X}")
                    .MutateId(id)
                    .MutateDlc(dlc)
                    .MutateRtr(0)
                    .MutateExt(whiteListFrame.IsExt)
                    .MutateData(payload);
                list.Add(mutation);
            }

            // G112
            var dataList = new List<byte[]>();
            if (options.Coverage == CoverageType.High)
            {
                dataList = new LhsSampler().GenerateByteBasedSamples(length);
            }
            else if (options.Coverage == CoverageType.Normal)
            {
                dataList = new LhsSampler().GenerateBitBasedSamples(length);
            }

            dataList = dataList.OrderBy(x => x.ToHex()).ToList();
            foreach (var data in dataList)
            {
                var mutation = CaseMutation.Create($"G112_ID_{id:X}")
                    .MutateId(id)
                    .MutateDlc(dlc)
                    .MutateRtr(0)
                    .MutateExt(whiteListFrame.IsExt)
                    .MutateData(data);
                list.Add(mutation);
            }

            // G113
            var mutationG113 = CaseMutation.Create($"G113_ID_{id:X}")
                .MutateId(id)
                .MutateDlc(0)
                .MutateRtr(0)
                .MutateExt(whiteListFrame.IsExt)
                .MutateData(Array.Empty<byte>());
            list.Add(mutationG113);

            // G114
            var mutationG114 = CaseMutation.Create($"G114_ID_{id:X}")
                .MutateId(id)
                .MutateDlc(dlc)
                .MutateRtr(1)
                .MutateExt(whiteListFrame.IsExt)
                .MutateData(Array.Empty<byte>());
            list.Add(mutationG114);

            // G115
            var mutationG115 = CaseMutation.Create($"G115_ID_{id:X}")
                .MutateId(id)
                .MutateDlc(0)
                .MutateRtr(0)
                .MutateExt(whiteListFrame.IsExt)
                .MutateData(Array.Empty<byte>());
            list.Add(mutationG115);

            // G116
            var mutationG116 = CaseMutation.Create($"G115_ID_{id:X}")
                .MutateId(id)
                .MutateDlc(0)
                .MutateRtr(1)
                .MutateExt(whiteListFrame.IsExt)
                .MutateData(Array.Empty<byte>());
            list.Add(mutationG116);

            // G117
            for (var mutateDlc = 1; mutateDlc <= 8; mutateDlc++)
            {
                if (mutateDlc != dlc)
                {
                    var bytes = new byte[DlcUtils.GetDataLength(mutateDlc)];
                    random.NextBytes(bytes);
                    var mutationG117 = CaseMutation.Create($"G117_ID_{id:X}")
                        .MutateId(id)
                        .MutateDlc(mutateDlc)
                        .MutateRtr(0)
                        .MutateExt(whiteListFrame.IsExt)
                        .MutateData(bytes);
                    list.Add(mutationG117);
                }
            }

            // G118
            for (var mutateDlc = 1; mutateDlc <= 8; mutateDlc++)
            {
                if (mutateDlc != dlc)
                {
                    var bytes = new byte[0];
                    var mutationG118 = CaseMutation.Create($"G118_ID_{id:X}")
                        .MutateId(id)
                        .MutateDlc(mutateDlc)
                        .MutateRtr(1)
                        .MutateExt(whiteListFrame.IsExt)
                        .MutateData(bytes);
                    list.Add(mutationG118);
                }
            }

            // G119
            for (var invalidDlc = 0x9; invalidDlc <= 0xF; invalidDlc++)
            {
                var bytes = new byte[DlcUtils.GetDataLength(invalidDlc)];
                random.NextBytes(bytes);
                var mutationG119 = CaseMutation.Create($"G119_ID_{id:X}-Rtr0")
                    .MutateId(id)
                    .MutateDlc(invalidDlc)
                    .MutateRtr(0)
                    .MutateExt(whiteListFrame.IsExt)
                    .MutateData(bytes);
                list.Add(mutationG119);
            }

            for (var invalidDlc = 0x9; invalidDlc <= 0xF; invalidDlc++)
            {
                var bytes = new byte[0];
                var mutation = CaseMutation.Create($"G119_ID_{id:X}-Rtr1")
                    .MutateId(id)
                    .MutateDlc(invalidDlc)
                    .MutateRtr(1)
                    .MutateExt(whiteListFrame.IsExt)
                    .MutateData(bytes);
                list.Add(mutation);
            }
        }
    }
}
