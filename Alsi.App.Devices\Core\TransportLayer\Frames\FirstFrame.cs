using System.Linq;

namespace Alsi.App.Devices.Core.TransportLayer.Frames
{
    public class FirstFrame : IDiagFrame
    {
        public FirstFrame(byte[] data, bool isTx)
        {
            Data = data;
            IsTx = isTx;
        }

        public bool IsTx { get; }
        public byte[] Data { get; }
        public int TotalLength => ((Data[0] & 0xF) << 8) + Data[1];
        public byte[] Payload => Data.Skip(2).Take(TotalLength).ToArray();

        public static bool TryMatch(byte[] data, bool isTx, out FirstFrame singleFrame)
        {
            singleFrame = null;
            if (data[0] >> 4 == 1)
            {
                singleFrame = new FirstFrame(data.ToArray(), isTx);
                return true;
            }
            return false;
        }
    }
}
