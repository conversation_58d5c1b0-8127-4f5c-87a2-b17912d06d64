using Alsi.Fuzz.Core.Models.TestPlans;
using System.Threading.Tasks;

namespace Alsi.Fuzz.Core.Service
{
    public interface ITestPlanService
    {
        Task<TestPlan> CreateAsync(string description);
        Task SaveAsync(TestPlan plan, string path);
        Task<TestPlan> LoadAsync(string path);
        Task<TestPlan> UpdateBasicInfoAsync(string path, string description);
        Task<TestPlan> UpdateHardwareConfigAsync(string path, HardwareConfig hardwareConfig);
        Task<TestPlan> UpdateSecurityConfigAsync(string path, SecurityConfig securityConfig);
        Task<TestPlan> UpdateSequenceConfigAsync(string path, SequenceConfigDto request);
        Task<TestPlan> UpdateCaseConfigAsync(string path, CaseConfig caseConfig);
    }
}
