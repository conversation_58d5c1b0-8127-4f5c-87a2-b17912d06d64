﻿using Alsi.App;
using System.Windows.Controls;

namespace Alsi.Fuzz.Views
{
    public partial class MainView : UserControl
    {
        public MainView()
        {
            InitializeComponent();
        }

        private void UserControl_Loaded(object sender, System.Windows.RoutedEventArgs e)
        {
            cefBrowser.Initialize();
            cefBrowser.LoadUrl(AppEnv.WebEnv.BaseUrl);
        }
    }
}
