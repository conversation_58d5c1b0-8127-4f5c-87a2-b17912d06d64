using Alsi.App.Database.Midwares;
using System;
using System.IO;

namespace Alsi.Fuzz.Core.Service.Results
{
    public class TestResultWriterService : ITestResultWriterService, IDisposable
    {
        public ResultContext ResultContext { get; private set; }

        private TestResult _currentTest;
        private DbContext _resultDbContext;
        private bool _isDisposed;

        public void Begin(TestResult testResult)
        {
            if (_currentTest != null)
            {
                throw new InvalidOperationException("There is already an active test. Call End() before starting a new test.");
            }

            _currentTest = testResult;

            // 创建测试结果上下文
            ResultContext = new ResultContext(_currentTest.TestType, testResult.ResultFolderName);

            // 确保结果目录存在
            Directory.CreateDirectory(ResultContext.ResultFolder);

            // 初始化结果数据库
            _resultDbContext = new DbContext(ResultContext.ResultDbPath);
            _resultDbContext.Initialize();

            // 创建必要的表
            var resultFreeSql = _resultDbContext.FreeSql;
            resultFreeSql.CodeFirst.SyncStructure<CaseResult>();
            resultFreeSql.CodeFirst.SyncStructure<CaseStep>();
        }

        public void End()
        {
            if (_currentTest == null)
            {
                throw new InvalidOperationException("No active test. Call Begin() before calling End().");
            }

            // 关闭数据库连接
            _resultDbContext?.Dispose();
            _resultDbContext = null;

            // 清除当前测试引用
            _currentTest = null;
            ResultContext = null;
        }

        public CaseResult[] GetCaseResults()
        {
            EnsureTestActive();
            return _resultDbContext.FreeSql
                .Select<CaseResult>()
                .Where(x => x.TestResultId == _currentTest.Id)
                .OrderBy(x => x.Id)
                .ToList()
                .ToArray();
        }

        public void BatchAddCaseResult(CaseResult[] caseResults)
        {
            EnsureTestActive();
            _resultDbContext.FreeSql
                .Insert(caseResults)
                .ExecuteAffrows();
        }

        public void AddOrUpdateCaseResult(CaseResult caseResult)
        {
            EnsureTestActive();

            var existedResult = _resultDbContext.FreeSql
                .Select<CaseResult>()
                .Where(x => x.Id == caseResult.Id)
                .First();
            if (existedResult == null)
            {
                _resultDbContext.FreeSql
                    .Insert(caseResult)
                    .ExecuteAffrows();
            }
            else
            {
                _resultDbContext.FreeSql.Update<CaseResult>()
                    .SetSource(caseResult)
                    .ExecuteAffrows();
            }
        }

        public void AddCaseStep(CaseStep caseStep)
        {
            EnsureTestActive();

            _resultDbContext.FreeSql.Insert(caseStep).ExecuteAffrows();
        }

        public void UpdateCaseStep(CaseStep caseStep)
        {
            EnsureTestActive();

            _resultDbContext.FreeSql.Update<CaseStep>()
                .Where(x => x.Id == caseStep.Id)
                .Set(x => x.FrameTimestamp, caseStep.FrameTimestamp)
                .Set(x => x.Detail, caseStep.Detail)
                .ExecuteAffrows();
        }

        private void EnsureTestActive()
        {
            if (_currentTest == null || _resultDbContext == null)
            {
                throw new InvalidOperationException("No active test. Call Begin() first.");
            }
        }

        public void Dispose()
        {
            if (!_isDisposed)
            {
                if (_currentTest != null)
                {
                    End();
                }

                _resultDbContext?.Dispose();

                _isDisposed = true;
            }
        }
    }
}
