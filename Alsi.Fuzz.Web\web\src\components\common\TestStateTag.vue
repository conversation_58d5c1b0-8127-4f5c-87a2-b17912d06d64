<template>
  <el-tag :type="tagType" size="small">
    {{ stateName }}
  </el-tag>
</template>

<script setup lang="ts">
import { computed, defineProps } from 'vue';
import { ExecutionState } from '@/api/appApi';

const props = defineProps<{
  state: string;
}>();

const tagType = computed<'success' | 'warning' | 'danger' | 'info'>(() => {
  switch (props.state) {
    case ExecutionState.Success:
      return 'success';
    case ExecutionState.Running:
      return 'warning';
    case ExecutionState.Failure:
      return 'danger';
    case ExecutionState.Pending:
    default:
      return 'info';
  }
});

const getTestStateName = (state: string): 'Not Run' | 'Running' | 'Completed' | 'Faulted' | 'Paused' | 'Unknown' => {
  switch (state) {
    case ExecutionState.Running:
      return 'Running';
    case ExecutionState.Pending:
      return 'Not Run';
    case ExecutionState.Success:
      return 'Completed';
    case ExecutionState.Failure:
      return 'Faulted';
    case ExecutionState.Paused:
      return 'Paused';
    default:
      return 'Unknown';
  }
};

const stateName = computed(() => {
  return getTestStateName(props.state);
});
</script>
