{"version": 3, "file": "js/app.b40f5e86.js", "mappings": "6JA2BA,GAA4BA,EAAAA,EAAAA,IAAiB,CAC3CC,OAAQ,YACRC,MAAO,CACLC,MAAO,CAAC,EACRC,SAAU,CAAC,EACXC,SAAU,CAAEC,KAAMC,UAEpBC,MAAO,CAAC,eAAgB,UACxBC,KAAAA,CAAMC,GAAgBC,KAAMC,ICP9B,MAAMV,EAAQQ,EAMRC,EAAOC,EAEPC,GAAkBC,EAAAA,EAAAA,IAAwB,MAChD,IAAIC,EAA0B,KAG9B,MAAMC,EAAuB,EAC3BC,EAAAA,EAAAA,OACAC,EAAAA,EAAAA,OACAC,EAAAA,EAAAA,OACAC,EAAAA,EAAAA,OACAC,EAAAA,EAAAA,OACAC,EAAAA,EAAAA,KACAC,EAAAA,GAAYC,wBAAwBC,IAAG,IACvCC,EAAAA,EAAAA,OACAC,EAAAA,EAAAA,IAAmBC,EAAAA,GAAuB,CAAEC,UAAU,KACtDC,EAAAA,EAAAA,MACAC,EAAAA,GAAON,GAAG,IACLO,EAAAA,MACAC,EAAAA,MACAC,EAAAA,MAKDC,EAAeA,KACnB,IAAKtB,EAAgBV,MAAO,OAG5B,MAAMiC,EAAa,IACdpB,GACHqB,EAAAA,EAAAA,MACAC,EAAAA,GAAWC,eAAed,IAAGe,IAC3B,GAAIA,EAAOC,WAAY,CACrB,MAAMtC,EAAQqC,EAAOE,MAAMC,IAAIC,WAC/BjC,EAAK,eAAgBR,GACrBQ,EAAK,SAAUR,E,KAInBmC,EAAAA,GAAWO,kBAAkBpB,GAAG,CAE9BqB,gBAAiB5C,EAAMG,SAAW,QAAU,OAE5C0C,MAAO7C,EAAMG,SAAW,0BAA4B,KAGtDiC,EAAAA,GAAWU,SAASvB,IAAIvB,EAAMG,WAI1BqC,EAAQnB,EAAAA,GAAY0B,OAAO,CAC/BN,IAAKzC,EAAMC,MACXiC,eAIFrB,EAAO,IAAIuB,EAAAA,GAAW,CACpBI,QACAQ,OAAQrC,EAAgBV,OACxB,EDuDJ,OCnDAgD,EAAAA,EAAAA,KAAU,KACRhB,GAAc,KAIhBiB,EAAAA,EAAAA,KAAM,IAAMlD,EAAMC,QAAQkD,IACxB,GAAItC,GAAQsC,IAAatC,EAAK2B,MAAMC,IAAIC,WAAY,CAClD,MAAMU,EAAmBvC,GAAMwC,UAAUC,WAAa,EACtDzC,EAAK0C,SAAS,CACZC,QAAS,CACPC,KAAM,EACNC,GAAI7C,EAAK2B,MAAMC,IAAIkB,OACnBC,OAAQT,KAIZU,YAAW,KACLhD,IAAMA,EAAKwC,UAAUC,UAAYF,EAAgB,GACpD,E,MAKPF,EAAAA,EAAAA,KAAM,IAAMlD,EAAMG,WAAWgD,IACtBtC,GAGLA,EAAK0C,SAAS,CACZO,QAAS,CACP1B,EAAAA,GAAWU,SAASiB,aAAaZ,GACjCf,EAAAA,GAAWO,kBAAkBoB,YAAY,CACvCnB,gBAAiBO,EAAW,QAAU,OACtCN,MAAOM,EAAW,0BAA4B,OAGlD,KAIJa,EAAAA,EAAAA,KAAgB,KACVnD,IACFA,EAAKoD,UACLpD,EAAO,K,IDSJ,CAACqD,EAAUC,MACRC,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAoB,MAAO,CAC/CC,QAAS,kBACT1D,IAAKD,EACLkC,MAAO,oBACN,KAAM,KAEX,I,UEtJA,MAAM0B,GAA2B,OAAgB,EAAQ,CAAC,CAAC,YAAY,qBAEvE,QCLA,MAAMC,EAAa,CAAE3B,MAAO,cACtB4B,EAAa,CAAE5B,MAAO,oBAM5B,OAA4B/C,EAAAA,EAAAA,IAAiB,CAC3CC,OAAQ,YACRC,MAAO,CACL0E,MAAO,CAAC,EACRC,QAAS,CAAC,GAEZrE,MAAO,CAAC,kBACRC,KAAAA,CAAMC,GAAgBC,KAAMC,ICI9B,MAAMD,EAAOC,EAEPkE,EAAuB3E,IAC3BQ,EAAK,iBAAkBR,EAAM,EDG/B,MAAO,CAACiE,EAAUC,MACRC,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAoB,MAAOG,EAAY,EAC3DK,EAAAA,EAAAA,IAAoB,MAAOJ,EAAY,EACrCK,EAAAA,EAAAA,IAAaC,EAAW,CACtB9E,MAAOiE,EAAKS,QACZzE,SAAU,MACV,iBAAkB0E,GACjB,KAAM,EAAG,CAAC,cAInB,IEhCA,MAAM,GAA2B,OAAgB,EAAQ,CAAC,CAAC,YAAY,qBAEvE,O,oICaYI,EAYAC,E,4BAmDN,SAAUC,EAAkBC,GAChC,OAAOA,EAASC,eAAiBH,EAAeI,SACzCF,EAASC,eAAiBH,EAAeK,OAClD,EAlEA,SAAYN,GACVA,EAAAA,EAAA,sBACAA,EAAAA,EAAA,iBACD,EAHD,CAAYA,IAAAA,EAAY,KAYxB,SAAYC,GACVA,EAAA,qBACAA,EAAA,qBACAA,EAAA,qBACAA,EAAA,qBACAA,EAAA,kBACD,CAND,CAAYA,IAAAA,EAAc,KAqE1B,MAAMM,EAAW,WAEJC,EAAS,CAEpBC,UAAAA,GACE,OAAIC,EAAAA,GACKC,EAAAA,GAAQC,IAAIH,aAEdI,EAAAA,EAAMC,IAAI,GAAGP,YACtB,EAGAQ,SAAWC,GACLN,EAAAA,GACKC,EAAAA,GAAQC,IAAIG,SAASC,GAEvBH,EAAAA,EAAMI,KAAK,GAAGV,aAAqBS,GAI5CE,KAAMA,IACAR,EAAAA,GACKC,EAAAA,GAAQC,IAAIM,OAEdL,EAAAA,EAAMI,KAAK,GAAGV,UAIvBY,kCAAAA,GACE,OAAIT,EAAAA,GACKC,EAAAA,GAAQC,IAAIO,qCAEdN,EAAAA,EAAMC,IAAI,+CACnB,EAGAM,aAAAA,CAAcC,EAAwBC,GACpC,MAAMC,EAAgC,CACpCF,WACAC,iBAGF,OAAIZ,EAAAA,GAEKC,EAAAA,GAAQC,IAAIQ,cAAcC,EAAUC,GAItCT,EAAAA,EAAMI,KAAK,2BAA4B,CAC5CI,SAAUE,EAAQF,SAClBG,cAAeD,EAAQD,eAE3B,EAGAG,SAAAA,CAAUJ,EAAwBC,GAChC,MAAMC,EAAgC,CACpCF,WACAC,iBAGF,OAAIZ,EAAAA,GACKC,EAAAA,GAAQC,IAAIa,UAAUJ,EAAUC,GAGlCT,EAAAA,EAAMI,KAAK,uBAAwB,CACxCI,SAAUE,EAAQF,SAClBG,cAAeD,EAAQD,eAE3B,EAGAI,aAAAA,GACE,OAAIhB,EAAAA,GACKC,EAAAA,GAAQC,IAAIc,gBAEdb,EAAAA,EAAMC,IAAI,kBACnB,EAGAa,SAAAA,GACE,OAAIjB,EAAAA,GACKC,EAAAA,GAAQC,IAAIe,YAEdd,EAAAA,EAAMI,KAAK,kBACpB,EAGAW,QAAAA,GACE,OAAIlB,EAAAA,GACKC,EAAAA,GAAQC,IAAIgB,WAEdf,EAAAA,EAAMI,KAAK,iBACpB,EAGAY,SAAAA,GACE,OAAInB,EAAAA,GACKC,EAAAA,GAAQC,IAAIiB,YAEdhB,EAAAA,EAAMI,KAAK,kBACpB,EAGAa,UAAAA,GACE,OAAIpB,EAAAA,GACKC,EAAAA,GAAQC,IAAIkB,aAEdjB,EAAAA,EAAMI,KAAK,mBACpB,EAGAc,aAAAA,GACE,OAAIrB,EAAAA,GACKC,EAAAA,GAAQC,IAAImB,gBAEdlB,EAAAA,EAAMC,IAAI,mBACnB,EAGAkB,kBAAAA,CAAmBC,GACjB,OAAIvB,EAAAA,GACKC,EAAAA,GAAQC,IAAIoB,mBAAmBC,GAEjCpB,EAAAA,EAAMI,KAAK,mBAAoBgB,EACxC,EAGAC,cAAAA,GACE,OAAIxB,EAAAA,GACKC,EAAAA,GAAQC,IAAIsB,iBAEdrB,EAAAA,EAAMC,IAAI,yBACnB,EAGAqB,QAAAA,CAASC,GACP,OAAI1B,EAAAA,GACKC,EAAAA,GAAQC,IAAIuB,SAASC,GAEvBvB,EAAAA,EAAMC,IAAI,gCAAgCsB,IACnD,EAGAC,YAAAA,CAAaD,EAAsBE,GACjC,OAAI5B,EAAAA,GACKC,EAAAA,GAAQC,IAAIyB,aAAaD,EAAcE,GAEzCzB,EAAAA,EAAMC,IAAI,qCAAqCsB,kBAA6BE,IACrF,EAGAC,aAAAA,CAAcH,EAAsBE,GAClC,OAAI5B,EAAAA,GACKC,EAAAA,GAAQ6B,KAAKD,cAAcH,EAAcE,GAE3CzB,EAAAA,EAAMC,IAAI,sCAAsCsB,kBAA6BE,IACtF,EAGAG,gBAAAA,CAAiBL,GACf,OAAI1B,EAAAA,GACKC,EAAAA,GAAQC,IAAI6B,iBAAiBL,GAE/BvB,EAAAA,EAAM6B,OAAO,sCAAsCN,IAC5D,EAGAO,kBAAAA,CAAmBP,GACjB,GAAI1B,EAAAA,GACF,OAAOC,EAAAA,GAAQC,IAAI+B,mBAAmBP,GAIxC,MAAMQ,EAAM,0CAA0CR,IACtD,OAAOvB,EAAAA,EAAMI,KAAK2B,EACpB,E,4GC3QU3C,E,qBAAZ,SAAYA,GACVA,EAAA,qBACAA,EAAA,qBACAA,EAAA,qBACAA,EAAA,oBACD,EALD,CAAYA,IAAAA,EAAc,KAkDpB,SAAUC,EAAkB2C,GAChC,OAAOA,EAAOzC,eAAiBH,EAAeI,SACvCwC,EAAOzC,eAAiBH,EAAeK,OAChD,CAEA,MAAMC,EAAW,sBAEJuC,EAAoB,CAE/BnB,UAAWA,IACLjB,EAAAA,GACKC,EAAAA,GAAQoC,eAAepB,YAEzBd,EAAAA,EAAMI,KAAK,GAAGV,WAIvBqB,SAAUA,IACJlB,EAAAA,GACKC,EAAAA,GAAQoC,eAAenB,WAEzBf,EAAAA,EAAMI,KAAK,GAAGV,UAIvByC,UAAWA,IACLtC,EAAAA,GACKC,EAAAA,GAAQoC,eAAeC,YAEzBnC,EAAAA,EAAMC,IAAI,GAAGP,Y,+EC5ExB,MAAMA,EAAW,iBAEJ0C,EAAe,CAC1BC,WAAYA,IACNxC,EAAAA,GACKC,EAAAA,GAAQwC,UAAUD,aAEpBrC,EAAAA,EAAMC,IAAI,GAAGP,aAGtB6C,cAAeA,CAACC,EAAmBC,IAC7B5C,EAAAA,GACKC,EAAAA,GAAQwC,UAAUI,OAAOF,EAAWC,GAEtCzC,EAAAA,EAAMC,IAAI,GAAGP,gBAAwB,CAAEiD,OAAQ,CAAEH,YAAWC,iBAGrEC,OAAQA,CAACF,EAAmBC,IACtB5C,EAAAA,GACKC,EAAAA,GAAQwC,UAAUI,OAAOF,EAAWC,GAEtCzC,EAAAA,EAAMC,IAAI,GAAGP,QAAgB,CAAEiD,OAAQ,CAAEH,YAAWC,iB,sHCrB/D,MAAM9F,GAAQiG,EAAAA,EAAAA,IAAwB,CACpCC,YAAa,KACbC,YAAa,GACbC,WAAW,IAIb,MAAMC,EAAoBC,WAAAA,IAAAC,EAAAA,EAAAA,GAAA,cACQ,KAAI,CAGpCC,SAAAA,CAAUC,GACRC,KAAKD,OAASA,CAChB,CAGAE,QAAAA,GACE,OAAOC,EAAAA,EAAAA,IAAS5G,EAClB,CAGA,sBAAM6G,GACJ7G,EAAMoG,WAAY,EAClB,IACE,MAAMU,QAAiBC,EAAAA,EAAYC,iBACnC,GAAIF,EAASG,KAKX,OAJAjH,EAAMkG,YAAcY,EAASG,KAC7BC,EAAAA,GAAUC,QAAQ,iCAClBT,KAAKU,2BACCV,KAAKW,kBACJP,EAASG,I,CAElB,QACAjH,EAAMoG,WAAY,C,CAEpB,OAAO,IACT,CAGA,kBAAMkB,CAAaC,GACjBvH,EAAMoG,WAAY,EAClB,IACE,MAAMU,QAAiBC,EAAAA,EAAYS,KAAKD,GACxC,GAAIT,EAASG,KAKX,OAJAjH,EAAMkG,YAAcY,EAASG,KAC7BC,EAAAA,GAAUC,QAAQ,iCAClBT,KAAKU,2BACCV,KAAKW,kBACJP,EAASG,I,CAElB,QACAjH,EAAMoG,WAAY,C,CAEpB,OAAO,IACT,CAGA,oBAAMqB,CAAeC,GACnB1H,EAAMoG,WAAY,EAClB,IACE,MAAMU,QAAiBC,EAAAA,EAAYxG,OAAOmH,GAC1C,GAAIZ,EAASG,KAKX,OAJAjH,EAAMkG,YAAcY,EAASG,KAC7BC,EAAAA,GAAUC,QAAQ,kCAClBT,KAAKU,2BACCV,KAAKW,kBACJP,EAASG,I,CAElB,MAAOU,GACPT,EAAAA,GAAUS,MAAM,8BAChBC,QAAQD,MAAMA,E,CACd,QACA3H,EAAMoG,WAAY,C,CAEpB,OAAO,IACT,CAGA,mBAAMyB,GACJ,UACQd,EAAAA,EAAYe,QAClB9H,EAAMkG,YAAc,KACpBgB,EAAAA,GAAUC,QAAQ,iCACdT,KAAKD,QACPC,KAAKD,OAAOsB,KAAK,I,CAEnB,MAAOJ,GACPT,EAAAA,GAAUS,MAAM,6BAChBC,QAAQD,MAAMA,E,CAElB,CAGA,qBAAMN,GACJ,IACE,MAAMP,QAAiBkB,EAAAA,EAAmB1E,MAE1C,OADAtD,EAAMmG,YAAcW,EAASG,KACtBH,EAASG,I,CAChB,MAAOU,GAEP,OADAC,QAAQD,MAAM,+BAAgCA,GACvC,E,CAEX,CAGA,kBAAMM,GACJ,IAIE,aAHMD,EAAAA,EAAmBE,QACzBlI,EAAMmG,YAAc,GACpBe,EAAAA,GAAUC,QAAQ,oBACX,C,CACP,MAAOQ,GAGP,OAFAT,EAAAA,GAAUS,MAAM,2BAChBC,QAAQD,MAAMA,IACP,C,CAEX,CAGA,qBAAMQ,CAAgBC,GACpBpI,EAAMoG,WAAY,EAClB,IACE,MAAMU,QAAiBC,EAAAA,EAAYoB,gBAAgBC,GACnD,GAAItB,EAASG,KAGX,OAFAjH,EAAMkG,YAAcY,EAASG,KAC7BC,EAAAA,GAAUC,QAAQ,oCACXL,EAASG,I,CAElB,MAAOU,GACPT,EAAAA,GAAUS,MAAM,gCAChBC,QAAQD,MAAM,8BAA+BA,E,CAC7C,QACA3H,EAAMoG,WAAY,C,CAEpB,OAAO,IACT,CAGA,oBAAMiC,GACJ,GAAIrI,EAAMkG,YACR,OAAOlG,EAAMkG,YAGflG,EAAMoG,WAAY,EAClB,IACE,MAAMU,QAAiBC,EAAAA,EAAYsB,iBAEnC,OADArI,EAAMkG,YAAcY,EAASG,KACtBH,EAASG,I,CAChB,MAAOU,GAEP,OADAC,QAAQD,MAAM,8BAA+BA,GACtC,I,CACP,QACA3H,EAAMoG,WAAY,C,CAEtB,CAGQgB,kBAAAA,GACFV,KAAKD,QACPC,KAAKD,OAAOsB,KAAK,CAAEO,KAAM,2BAE7B,EAIK,MAAMC,EAAkB,IAAIlC,C,+KC7KnC,MAAMmC,EAAaA,IAAMC,KAAKC,SAASxI,SAAS,IAAIyI,UAAU,EAAG,IAG3DC,EAAoBA,KAAM,IAAIC,MAAOC,cAGrCC,EAAoBA,KACxB,MAAMC,EAAM,IAAIH,KACVI,EAAW,IAAIJ,KAAKG,EAAIE,UAA6C,GAAjCT,KAAKU,MAAsB,GAAhBV,KAAKC,UAAsB,GAAK,GAAK,KAC1F,OAAOO,EAASH,aAAa,EAIlBM,EAAwB,CACnC,CACE7B,KAAM,+BACN8B,SAAU,CACRf,KAAM,YACNF,YAAa,mBACbkB,QAASP,IACTQ,SAAUX,KAEZY,OAAQ,CACNC,aAAc,MACdC,SAAU,MACVC,UAAW,CAAC,QAAS,QAAS,WAGlC,CACEpC,KAAM,6BACN8B,SAAU,CACRf,KAAM,UACNF,YAAa,iBACbkB,QAASP,IACTQ,SAAUR,KAEZS,OAAQ,CACNC,aAAc,MACdC,SAAU,MACVC,UAAW,CAAC,SAAU,YAG1B,CACEpC,KAAM,iCACN8B,SAAU,CACRf,KAAM,cACNF,YAAa,wBACbkB,QAASP,IACTQ,SAAUR,KAEZS,OAAQ,CACNC,aAAc,UACdC,SAAU,QACVC,UAAW,CAAC,QAAS,QAAS,QAAS,YAMhCC,EAA0C,CACrD,CACEC,GAAIrB,IACJsB,SAAU,8BACVC,SAAU,YACVC,eAAgBjB,IAChBkB,aAAclB,IACdmB,WAAW,GAEb,CACEL,GAAIrB,IACJsB,SAAU,kCACVC,SAAU,UACVC,eAAgBjB,IAChBkB,aAAclB,IACdmB,WAAW,GAEb,CACEL,GAAIrB,IACJsB,SAAU,gCACVC,SAAU,cACVC,eAAgBpB,IAChBqB,aAAclB,IACdmB,WAAW,IAKFC,EAAyB,GCvFtC,IAAIC,EAAmC,KAGhC,MAAMC,EAAkB,CAE7BrD,eAAgBA,KAEd,MAAMsD,EAAc7B,KAAKU,MAAMV,KAAKC,SAAWU,EAAUjI,QACnDoJ,EAAenB,EAAUkB,GAK/B,OAJAF,EAAkBG,EAGlBC,EAAsBD,EAAahD,KAAMgD,EAAajC,MAC/CmC,EAAYF,EAAa,EAIlC/C,KAAOD,IAEL,MAAMmD,EAAcd,EAAqBe,MAAKC,GAAQA,EAAKd,WAAavC,IACxE,IAAIsD,EAGJ,GAAKH,EAIE,CAEL,MAAMI,EAAe1B,EAAUuB,MAAKI,GAAKA,EAAE1B,SAASf,OAASoC,EAAYX,WACzEc,EAAOC,GAAgB1B,EAAU,GAEjCoB,EAAsBjD,EAAMuD,GAAczB,SAASf,MAAQ,G,MAR3DuC,EAAOzB,EAAU,GAEjBoB,EAAsBjD,EAAMsD,EAAKvC,MAUnC,OADA8B,EAAkBS,EACXJ,EAAYI,EAAK,EAI1BtK,OAAS0G,IAEP,MAAM+B,GAAM,IAAIH,MAAOC,cAGjBgB,EAAW,kBAAkB7C,EAAKqB,KAAK0C,QAAQ,OAAQ,KAAKC,oBAE5DC,EAAoB,CACxB3D,KAAMuC,EACNT,SAAU,CACRf,KAAMrB,EAAKqB,KACXF,YAAanB,EAAKmB,YAClBkB,QAASN,EACTO,SAAUP,GAEZQ,OAAQ,CACNC,aAAc,UACdC,SAAU,MACVC,UAAW,KAYf,OAPAP,EAAUrB,KAAKmD,GAGfV,EAAsBV,EAAU7C,EAAKqB,MAErC8B,EAAkBc,EAEXT,EAAYS,EAAQ,EAI7BpD,MAAOA,KACLsC,EAAkB,KAClBxC,QAAQuD,IAAI,YACLV,OAAYW,IAIrB/C,eAAgBA,IACT+B,EAGEK,EAAYL,GAFViB,EAAU,IAAK,kCAM1BlD,gBAAkBC,IAChB,IAAKgC,EACH,OAAOiB,EAAU,IAAK,kCAIxBjB,EAAgBf,SAASjB,YAAcA,EACvCgC,EAAgBf,SAASE,UAAW,IAAIV,MAAOC,cAG/C,MAAM4B,EAAcd,EAAqBe,MAAKC,GAAQA,EAAKd,WAAaM,GAAiB7C,OAKzF,OAJImD,IACFA,EAAYT,cAAe,IAAIpB,MAAOC,eAGjC2B,EAAYL,EAAgB,EAIrCkB,gBAAkB/D,IAIhB,MAAMgE,EAAS9C,KAAKC,SAAW,GAQ/B,OAAO+B,EAAY,CAAEc,UAAS,GAKlC,SAASf,EAAsBV,EAAkBC,GAC/C,MAAMf,GAAM,IAAIH,MAAOC,cACjB0C,EAAe5B,EAAqBe,MAAKC,GAAQA,EAAKd,WAAaA,IAErE0B,GAEFA,EAAaxB,eAAiBhB,EAC9BwC,EAAavB,aAAejB,EAC5BwC,EAAatB,WAAY,GAGzBN,EAAqB7B,KAAK,CACxB8B,GAAIrB,IACJsB,WACAC,WACAC,eAAgBhB,EAChBiB,aAAcjB,EACdkB,WAAW,IAKfN,EAAqB6B,MAAK,CAACC,EAAGC,IACxBD,EAAExB,YAAcyB,EAAEzB,UAAkB,GACnCwB,EAAExB,WAAayB,EAAEzB,WAAmB,EAElC,IAAIrB,KAAK8C,EAAE3B,gBAAgBd,UAAY,IAAIL,KAAK6C,EAAE1B,gBAAgBd,WAE7E,C,gBCzJO,MAAM0C,EAAyB,CAEpCtI,IAAKA,KAEH,MAAMuI,EAAgBjC,EACnBkC,QAAOlB,IAASA,EAAKV,YACrBuB,MAAK,CAACC,EAAGC,IAED,IAAI9C,KAAK8C,EAAE3B,gBAAgBd,UAAY,IAAIL,KAAK6C,EAAE1B,gBAAgBd,YAG7E,OAAOuB,EAAYoB,EAAc,EAInC3D,MAAOA,KAEL0B,EAAqBmC,SAAQnB,IAC3BA,EAAKV,WAAY,CAAI,IAGhBO,OAAYW,IAIrBY,aAAelC,IAEb,MAAMmC,EAASrC,EAAqBe,MAAKC,GAAQA,EAAKd,WAAaA,IAAac,EAAKV,YAOrF,OAJI+B,IACFA,EAAO/B,WAAY,GAGdO,OAAYW,EAAU,G,sBCrBjC,SAASc,IACP,MAAMC,EAAwB,GAGxBC,EAAkB,CACtB,2BACA,WACA,iBACA,uBACA,uBACA,wBACA,6BACA,qBACA,iCACA,iBACA,kBACA,gBACA,eACA,sBACA,wBACA,0BACA,oBACA,kBACA,cACA,cAIIC,EAAe5D,KAAKU,MAAsB,GAAhBV,KAAKC,UAAiB,EAGhD4D,EAAoB,IAAIF,GAC3BX,MAAK,IAAMhD,KAAKC,SAAW,KAC3B6D,MAAM,EAAGF,EAAe,GAgC3B,OA7BAC,EAAkBP,SAAQ,CAACS,EAAcC,KAEvC,IAAIzM,EACJ,GAAIyM,EAAQJ,EACVrM,EAAQ,cACH,CAEL,MAAM0M,EAAsC,CAAC,UAAW,UAAW,WACnE1M,EAAQ0M,EAAYjE,KAAKU,MAAMV,KAAKC,SAAWgE,EAAYvL,Q,CAI7D,MAAMwL,EAAQ,IAAI9D,KAAKA,KAAKG,MAAwB,IAAhBP,KAAKC,UACnCkE,EAAM,IAAI/D,KAAK8D,EAAMzD,UAA4B,IAAhBT,KAAKC,UAE5CyD,EAAQpE,KAAK,CACX8B,GAAI4C,EAAQ,EACZ7H,aAAc,QAAQ6D,KAAKC,SAASxI,SAAS,IAAIyI,UAAU,KAC3DkE,WAAY,OAAOpE,KAAKC,SAASxI,SAAS,IAAIyI,UAAU,KACxD6D,aAAcA,EACdM,UAAW,SAASL,IACpBnE,KAAM,QAAQmE,IACdzM,MAAOA,EACP2M,MAAOA,EAAM7D,cACb8D,IAAKA,EAAI9D,cACTiE,OAAkB,YAAV/M,EAAsB,cAAgB,QAAQA,EAAMiL,iBAC5D,IAGGkB,CACT,CAGA,SAASa,EAAsBnJ,EAAwBC,GACrD,MAAMqI,EAAwB,GAExBc,GAAapJ,IAAarB,EAAAA,GAAa0K,KAAO,IAAS,KAAQ,EAG/DC,EAAe,CAAC,OAAQ,OAAQ,OAAQ,OAAQ,QAGtD,IAAK,MAAMX,KAAgB1I,EAEzB,IAAK,IAAIsJ,EAAI,EAAGA,EAAIH,EAAWG,IAAK,CAElC,MAAMC,EAAc5E,KAAKU,MAAMV,KAAKC,SAAWyE,EAAahM,QACtDmM,EAASH,EAAaE,GAE5BlB,EAAQpE,KAAK,CACX8B,GAAIsC,EAAQhL,OAAS,EACrByD,aAAc,QAAQ6D,KAAKC,SAASxI,SAAS,IAAIyI,UAAU,KAC3DkE,WAAY,OAAOpE,KAAKC,SAASxI,SAAS,IAAIyI,UAAU,KACxD6D,aAAcA,EACdM,UAAW,SAASrE,KAAKU,MAAsB,KAAhBV,KAAKC,UAAmB,GAAGxI,SAAS,IAAIqN,SAAS,EAAG,uBAAuBC,MAAM,IAAIC,KAAK,GAAGC,KAAI,IAAMjF,KAAKU,MAAsB,IAAhBV,KAAKC,UAAgBxI,SAAS,IAAIqN,SAAS,EAAG,OAAMI,KAAK,MAC1M3N,MAAO,UACP2M,MAAO,KACPC,IAAK,KACLG,OAAQ,GACRzE,KAAM,GAAGgF,aAAkBF,EAAI,K,CAKrC,OAAOjB,CACT,CAGA,SAASyB,IACP,MAAMzB,EAAwB,GAG9B,IAAK,IAAIiB,EAAI,EAAGA,EAAI,EAAGA,IAAK,CAC1B,MAAMS,EAAe,IAAIhF,KAAKA,KAAKG,MAAY,MAAJoE,GACrCU,EAAa,GAAKrF,KAAKU,MAAsB,GAAhBV,KAAKC,UAClC2D,EAAe5D,KAAKU,MAAM2E,GAAc,GAAsB,GAAhBrF,KAAKC,WACnDqF,EAAeD,EAAazB,EAElCF,EAAQpE,KAAK,CACX8B,GAAI,eAAeuD,KAAKvE,KAAKG,QAC7BgF,iBAAkB,QAAQZ,EAAI,IAC9Ba,SAAU,OACVJ,aAAcA,EAAa/E,cAC3BoF,eAAgB,IAAIrF,KAAKgF,EAAa3E,UAAY,MAASJ,cAC3DgF,aACAzB,eACA0B,gB,CAIJ,OAAO5B,CACT,CAGA,SAASgC,EAAsBrJ,GAC7B,MAAMsJ,EAAoB,GACpBC,EAAY,EAAI5F,KAAKU,MAAsB,GAAhBV,KAAKC,UAEtC,IAAK,IAAI0E,EAAI,EAAGA,EAAIiB,EAAWjB,IAAK,CAClC,MAAMkB,EAAYzF,KAAKG,MAA0B,KAAjBqF,EAAYjB,GACtCmB,EAAe,CAAC,UAAW,WAC3BvO,EAAQyI,KAAKC,SAAW,GAAM6F,EAAa,GAAKA,EAAa,GAEnEH,EAAMrG,KAAK,CACT8B,GAAIuD,EAAI,EACR9E,KAAM,QAAQ8E,EAAI,IAClBtI,aAAcA,EACdwJ,UAAWA,EACXE,eAAgBF,EAChBtO,MAAOA,EACP2M,MAAO,IAAI9D,KAAKyF,EAAY,KAAKxF,cACjC8D,IAAK,IAAI/D,KAAKyF,GAAWxF,cACzBiE,OAAkB,YAAV/M,EAAsB,6BAA+B,0B,CAIjE,OAAOoO,CACT,CAGA,IAcIK,EAdAC,EAAmCjM,EAAAA,GAAekM,QAClDC,EAAuB,GACvBC,EAA6B,CAC/BhF,GAAI,QAAQhB,KAAKG,QACjBgF,iBAAkB,gBAClBC,SAAU,OACVJ,cAAc,IAAIhF,MAAOC,cACzBgF,WAAY,EACZzB,aAAc,EACd0B,aAAc,GAEZe,EAAgC,GAMpC,SAASC,SAEkB3D,IAArBqD,IACFO,cAAcP,GACdA,OAAmBrD,GAIrBqD,EAAmBQ,OAAOC,aAAY,KACpC,GAAIR,IAAqBjM,EAAAA,GAAe0M,SAAWN,EAAexC,aAAewC,EAAed,aAAec,EAAef,WAAY,CACxI,MAAMsB,EAAeP,EAAexC,aAAewC,EAAed,aAG5DsB,EAAY5G,KAAKC,SAAW,GAClCoG,EAAgBM,GAAcpP,MAAQqP,EAAY,UAAY,UAC9DP,EAAgBM,GAAczC,MAAQ,IAAI9D,KAAKA,KAAKG,MAAQ,KAAMF,cAClEgG,EAAgBM,GAAcxC,KAAM,IAAI/D,MAAOC,cAG3CuG,EACFR,EAAexC,eAEfwC,EAAed,eAIjB,MAAMuB,EAAYF,EAAe,EAC7BE,EAAYT,EAAef,YAC7BgB,EAAgBQ,GAAWtP,MAAQ,UACnC4O,EAAuB,sBAAsBE,EAAgBQ,GAAW9C,iBAExEkC,EAAmBjM,EAAAA,GAAeI,QAClC+L,EAAuB,2BACvBC,EAAeX,gBAAiB,IAAIrF,MAAOC,mBAClBsC,IAArBqD,IACFO,cAAcP,GACdA,OAAmBrD,G,MAGdsD,IAAqBjM,EAAAA,GAAe0M,cAIpB/D,IAArBqD,IACFO,cAAcP,GACdA,OAAmBrD,E,GAGtB,IACL,CAGO,MAAMmE,EAAa,CAExBtM,UAAAA,GACE,OAAOwH,EAAY,CACjB+E,WAAY,yBACZC,UAAW,0BAEf,EAGAlM,SAAW0D,IAETkD,EAAUpC,KAAKd,GACRwD,OAAYW,IAIrB1H,KAAMA,KACJkE,QAAQuD,IAAI,YAELV,OAAYW,IAIrBzH,kCAAAA,GACE,OAAO8G,EAAYyB,IACrB,EAGAtI,aAAAA,CAAc8L,EAAmD5L,GAE/D,IAAI6L,EACAC,EAYJ,MAV4B,kBAAjBF,GAETC,EAAqBD,EAAa7L,SAClC+L,EAAsBF,EAAa5L,eAAiB,KAGpD6L,EAAqBD,EACrBE,EAAsB9L,GAAiB,IAGlC2G,EAAYuC,EAAsB2C,EAAoBC,GAC/D,EAGA3L,SAAAA,CAAUyL,EAAmD5L,GAE3D,IAAI6L,EACAC,EAEwB,kBAAjBF,GACTC,EAAqBD,EAAa7L,SAClC+L,EAAsBF,EAAa5L,eAAiB,KAEpD6L,EAAqBD,EACrBE,EAAsB9L,GAAiB,IAGzC,MAAM+L,EAAQ7C,EAAsB2C,EAAoBC,GACxDhI,QAAQuD,IAAI,YAAa0E,EAAM1O,QAG/B,MAAMyD,EAAe,QAAQiE,KAAKG,QAGlC,OAFA6G,EAAM9D,SAAQ+D,GAAKA,EAAElL,aAAeA,IAE7B6F,EAAYoF,EACrB,EAGA3L,aAAAA,GAEE,MAAM6L,EAAa/C,EAAsBxK,EAAAA,GAAawN,OAAQ,CAAC,aAAc,6BACvEpL,EAAe,uBAQrB,OAPAmL,EAAWhE,SAAQ+D,IACjBA,EAAElL,aAAeA,EAEjB,MAAMqL,EAAiC,CAAC,UAAW,UAAW,UAAW,WACzEH,EAAE9P,MAAQiQ,EAAOxH,KAAKU,MAAMV,KAAKC,SAAWuH,EAAO9O,QAAQ,IAGtDsJ,EAAYsF,EACrB,EAGA5L,SAAAA,GACEyD,QAAQuD,IAAI,UACZuD,EAAmBjM,EAAAA,GAAe0M,QAClCP,EAAuB,0BAGvB,MAAMmB,EAAa/C,EAAsBxK,EAAAA,GAAawN,OAAQ,CAAC,aAAc,6BAqB7E,OApBAlB,EAAkBiB,EAClBlB,EAAiB,CACfhF,GAAI,QAAQhB,KAAKG,QACjBgF,iBAAkB,gBAClBC,SAAU,OACVJ,cAAc,IAAIhF,MAAOC,cACzBgF,WAAYiC,EAAW5O,OACvBkL,aAAc,EACd0B,aAAc,GAIZe,EAAgB3N,OAAS,IAC3B2N,EAAgB,GAAG9O,MAAQ,UAC3B4O,EAAuB,sBAAsBE,EAAgB,GAAGtC,eAGhEuC,KAGKtE,OAAYW,EACrB,EAGAhH,QAAAA,GAKE,OAJAwD,QAAQuD,IAAI,UACZuD,EAAmBjM,EAAAA,GAAeK,QAClC8L,EAAuB,iCACvBC,EAAeX,gBAAiB,IAAIrF,MAAOC,cACpC2B,OAAYW,EACrB,EAGA/G,SAAAA,GAIE,OAHAuD,QAAQuD,IAAI,UACZuD,EAAmBjM,EAAAA,GAAeyN,OAClCtB,EAAuB,gCAChBnE,OAAYW,EACrB,EAGA9G,UAAAA,GAUE,OATAsD,QAAQuD,IAAI,UACZuD,EAAmBjM,EAAAA,GAAe0M,QAClCP,EAAuB,sCAGExD,IAArBqD,GACFM,IAGKtE,OAAYW,EACrB,EAGA7G,aAAAA,GAEE,MAAM5B,EAA2B,CAC/BC,aAAc8L,EACdyB,iBAAkBvB,EAClBwB,WAAYvB,EACZwB,YAAavB,GAGf,OAAOrE,EAAY9H,EACrB,EAGA6B,kBAAAA,CAAmBC,GACjB,MAAM9B,EAAW,CACfC,aAAc8L,EACdyB,iBAAkBvB,EAClBwB,WAAYvB,GAIRyB,EAAaxB,EAAgB3N,OAC7BoP,GAAc9L,EAAW+L,WAAa,GAAK/L,EAAWgM,SACtDC,EAAWjI,KAAKkI,IAAIJ,EAAa9L,EAAWgM,SAAUH,GACtDM,EAAa9B,EAAgBvC,MAAMgE,EAAYG,GAE/C5J,EAAmC,IACpCnE,EACHkO,gBAAiB,CACfC,MAAOF,EACPG,MAAOT,EACPG,SAAUhM,EAAWgM,SACrBD,WAAY/L,EAAW+L,aAI3B,OAAO/F,EAAY3D,EACrB,EAGApC,cAAAA,GACE,OAAO+F,EAAYmD,IACrB,EAGAjJ,QAAAA,CAASC,GAEP,MAAMyL,EAAcrD,EAAsBxK,EAAAA,GAAawN,OAAQ,CAAC,aAAc,6BAY9E,OATAK,EAAYtE,SAAQ+D,IAClBA,EAAElL,aAAeA,EAEjB,MAAMqL,EAAiC,CAAC,UAAW,WACnDH,EAAE9P,MAAQiQ,EAAOxH,KAAKU,MAAMV,KAAKC,SAAWuH,EAAO9O,SACnD2O,EAAEnD,MAAQ,IAAI9D,KAAKA,KAAKG,MAAQ,MAASF,cACzCgH,EAAElD,IAAM,IAAI/D,KAAKA,KAAKG,MAAQ,MAASF,aAAa,IAG/C2B,EAAY4F,EACrB,EAGAxL,YAAAA,CAAaD,EAAsBE,GACjC,OAAO2F,EAAY0D,EAAsBrJ,GAC3C,EAGAG,gBAAAA,CAAiBL,GAGf,OAFAgD,QAAQuD,IAAI,aAAavG,KAElB6F,OAAYW,EACrB,EAGAjG,kBAAAA,CAAmBP,GAGjB,OAFAgD,QAAQuD,IAAI,iDAAiDvG,KAEtDoM,QAAQC,SACjB,G,cCndK,MAAMC,EAAkB,CAE7BC,aAAcA,IAEL1G,EAAY,oCAIrB2G,aAAe7J,IACbL,EAAAA,GAAUC,QAAQ,sBAAsBI,KACjCkD,OAAYW,KCZjBiG,EAAiB,CACrB,CACE/I,KAAM,qBACNgJ,QAAS,MACTC,SAAU,CACR,CACEjJ,KAAM,qBACNkJ,UAAW,CACT,CACE5T,KAAM,MACNwK,YAAa,CAAEE,KAAM,cACrBmJ,SAAU,CAAEC,WAAW,EAAMjU,MAAO,8BAM9C,CACE6K,KAAM,oBACNgJ,QAAS,MACTC,SAAU,CACR,CACEjJ,KAAM,kCACNkJ,UAAW,CACT,CACE5T,KAAM,MACNwK,YAAa,CAAEE,KAAM,sCACrBmJ,SAAU,CAAEC,WAAW,EAAMjU,MAAO,+BAS1CkU,EAA0C,CAC9C,qBAAsB,6SAQtB,oBAAqB,mVAWVC,EAAmB,CAC9BlM,WAAYA,IACH+E,EAAY4G,GAGrBtL,OAAQA,CAACF,EAAmBC,KAC1B,MAAM+L,EAAQR,EAAe1G,MAAKmH,GAAKA,EAAExJ,OAASzC,IAClD,IAAKgM,EACH,OAAOxG,EAAU,IAAK,eAAexF,gBAGvC,MAAMkM,EAAMF,EAAMN,SAAS5G,MAAKI,GAAKA,EAAEzC,OAASxC,IAChD,OAAKiM,EAIEtH,EAAYkH,EAAgB9L,IAH1BwF,EAAU,IAAK,YAAYvF,+BAAyCD,KAG/B,GClE5CmM,EAAsC,CAC1C,CACE1J,KAAM,gBACN2J,kBAAmB,MACnBC,aAAa,GAEf,CACE5J,KAAM,gBACN2J,kBAAmB,QACnBC,aAAa,GAEf,CACE5J,KAAM,gBACN2J,kBAAmB,MACnBC,aAAa,GAEf,CACE5J,KAAM,gBACN2J,kBAAmB,QACnBC,aAAa,IAKjB,IAAIC,EAAwC,CAC1CF,kBAAmB,MACnBG,UAAW,CACTC,kBAAmB,gBACnBC,YAAa,KAEfC,YAAa,CACXF,kBAAmB,gBACnBG,mBAAoB,IACpBF,YAAa,MAKjBpD,aAAY,KACV8C,EAAmBjG,SAAQ0G,IAErBhK,KAAKC,SAAW,KAClB+J,EAAOP,aAAeO,EAAOP,Y,GAE/B,GACD,KAEI,MAAMQ,EAAkB,CAE7BC,kBAAmBA,KACjB,MAAMnJ,EAAyB,CAC7BoJ,eAAgBZ,EAChBa,eAAgBV,GAGlB,OAAO1H,EAAYjB,EAAO,EAI5BsJ,qBAAuBtJ,IACrB,IAAKA,EACH,OAAO6B,EAAU,IAAK,WAIxB8G,EAAwB,IACnBA,KACA3I,GAI4B,QAA7BA,EAAOyI,mBAA+BzI,EAAO4I,UAC/CD,EAAsBC,UAAY,IAAK5I,EAAO4I,WACR,UAA7B5I,EAAOyI,mBAAiCzI,EAAO+I,cACxDJ,EAAsBI,YAAc,IAAK/I,EAAO+I,cAIlD,MAAMQ,EAAgC,CACpCH,eAAgBZ,EAChBa,eAAgBV,GAGlB,OAAO1H,EAAYsI,EAAc,GCzFrC,IAAIC,EAAwB,CAC1BC,cAAe,qBACfC,oBAAqB,qBACrBC,mBAAoB,mGAGf,MAAMC,EAAkB,CAE7BC,kBAAmBA,IACV5I,EAAYuI,GAIrBM,qBAAuB9J,IAErBwJ,EAAwB,IACnBA,KACAxJ,GAEEiB,EAAYuI,K,cChBvB,MAAMO,EAAwC,CAC5C3Q,aAAcH,EAAAA,GAAekM,QAC7BwB,iBAAkB,GAClBC,WAAY,CACVvG,GAAI2J,OAAOC,gBAAkB,IAC7BzF,iBAAkB,YAClBC,SAAU,iBACVJ,cAAc,IAAIhF,MAAOC,cACzBgF,WAAY,GACZzB,aAAc,EACd0B,aAAc,GAEhBsC,YAAa,IAIf,IAAIqD,EAA6B,IAAIH,GAGjCI,EAA+B,KAGnC,SAASC,IACP,MAAM9P,EAAgB,CACpB,2BACA,WACA,iBACA,uBACA,uBACA,cAGF,OAAOA,EAAc4J,KAAI,CAACpF,EAAMmE,KAAU,CACxC5C,GAAI4C,EAAQ,EACZ7H,aAAc8O,EAAWtD,WAAWvG,GACpCgD,WAAY,OAAOJ,IACnBD,aAAclE,EACdwE,UAAW,SAASL,IACpBnE,KAAM,QAAQmE,EAAQ,IACtBzM,MAAOyI,KAAKC,SAAW,GAAMjG,EAAAA,GAAeI,QACpC4F,KAAKC,SAAW,GAAMjG,EAAAA,GAAeK,QAAUL,EAAAA,GAAekM,QACtEhC,MAAO,IAAI9D,KAAKA,KAAKG,MAAQ,KAAOF,cACpC8D,KAAK,IAAI/D,MAAOC,cAChBiE,OAAQ,kBAEZ,CAGA,SAAS8G,IACHF,IACF3E,cAAc2E,GACdA,EAAgB,KAEpB,CAEO,MAAMG,EAAwB,CAEnC3P,UAAWA,KACT0P,IAGAH,EAAa,CACX9Q,aAAcH,EAAAA,GAAe0M,QAC7BgB,iBAAkB,UAClBC,WAAY,CACVvG,GAAI2J,OAAOC,gBAAkB,IAC7BzF,iBAAkB,2BAClBC,SAAU,iBACVJ,cAAc,IAAIhF,MAAOC,cACzBgF,WAAY,GACZzB,aAAc,EACd0B,aAAc,GAEhBsC,YAAa,IAIfsD,EAAgB1E,OAAOC,aAAY,KAC7BwE,EAAW9Q,eAAiBH,EAAAA,GAAe0M,UAE/CuE,EAAWtD,WAAW/D,aAAe5D,KAAKkI,IAAI+C,EAAWtD,WAAWtC,WACvB4F,EAAWtD,WAAW/D,aAAe,GAClFqH,EAAWtD,WAAWrC,aAAetF,KAAKkI,IAAI+C,EAAWtD,WAAWtC,WAAa4F,EAAWtD,WAAW/D,aAC1DqH,EAAWtD,WAAWrC,cAAgBtF,KAAKC,SAAW,GAAM,EAAI,IAE7GgL,EAAWrD,YAAcuD,IACzBF,EAAWvD,iBAAmB,SAASuD,EAAWrD,YAAY5H,KAAKU,MAAMV,KAAKC,SAAWgL,EAAWrD,YAAYlP,SAASqL,eAGrHkH,EAAWtD,WAAW/D,aAAeqH,EAAWtD,WAAWrC,cAAgB2F,EAAWtD,WAAWtC,aACnG4F,EAAW9Q,aAAeH,EAAAA,GAAeI,QACzC6Q,EAAWvD,iBAAmB,QAC9B0D,K,GAED,KAEIpJ,EAAY,CAAEtD,SAAS,EAAM4M,QAAS,WAI/C3P,SAAUA,KACJsP,EAAW9Q,eAAiBH,EAAAA,GAAe0M,UAC7CuE,EAAW9Q,aAAeH,EAAAA,GAAeK,QACzC4Q,EAAWvD,iBAAmB,QAC9B0D,KAGKpJ,EAAY,CAAEtD,SAAS,EAAM4M,QAAS,WAI/CvO,UAAWA,IACFiF,EAAY,IAAIiJ,KClHrBM,EAA+B,CACnCC,gBAAiB,GACjBC,iBAAkB,GAClBC,gBAAgB,EAChBC,WAAY,KACZC,eAAe,EACfC,YAAa,EACbC,aAAc,CAAC,GAAM,GAAM,IAAM,IAAM,IAAM,IAAM,IAAM,KACzDC,0BAA2B,MAC3BC,gBAAiB,IACjBC,gBAAiB,IACjBC,UAAW,KACXC,cAAc,EACdC,UAAW,KACXC,cAAe,IACfC,sBAAsB,EACtBC,2BAA2B,EAC3BC,2BAA4B,CAAC,GAAM,GAGnCC,aAAc,CACZC,QAAQ,EACRC,iBAAahK,EACbiK,QAAS,IAKPC,EAAmC,CACvCzL,GAAI,EACJjF,aAAc,uCACdiI,WAAY,uCACZL,aAAc,gBACdlE,KAAM,mBACNwE,UAAW,kBACX9M,MAAO,UACP2M,OAAO,IAAI9D,MAAOC,cAClB8D,KAAK,IAAI/D,MAAOC,cAChBiE,OAAQ,uBAGGwI,EAAc,CACzBC,aAAAA,GACE,OAAO/K,EAAYuJ,EACrB,EAEAjP,aAAAA,CAAcH,EAAsBE,GAElC,OAAO2F,EAAY,IACd6K,EACHzL,GAAI/E,EACJF,aAAcA,GAElB,EAEA6Q,gBAAAA,CAAiBjM,GAEf,GAAIA,EAAOkM,kBAETlM,EAAO0L,aAAe,CACpBC,QAAQ,EACRC,iBAAahK,EACbiK,QAAS,QAEN,GAAI7L,EAAOmM,gBAAiB,CAEjC,MAAMC,EAAUpM,EAAOmM,gBAAgBE,MAAM,MAAMC,OAAS,qBACtDC,EAAc,MAEpBvM,EAAO0L,aAAe,CACpBC,QAAQ,EACRC,YAAaQ,EACbP,QAASU,E,CAQb,cAHOvM,EAAOmM,uBACPnM,EAAOkM,kBAEPjL,EAAYjB,EACrB,EAEAwM,SAAAA,GACE,OAAOvL,EAAY,CACjBwJ,gBAAiB,CACf,CAAEpK,GAAI,IAAOvB,KAAM,gBAAiB2N,IAAK,EAAGC,OAAO,EAAOC,YAAa,MAAOC,UAAW,CAAC,MAAO,QACjG,CAAEvM,GAAI,KAAOvB,KAAM,oBAAqB2N,IAAK,EAAGC,OAAO,EAAMC,YAAa,MAAOC,UAAW,CAAC,MAAO,QACpG,CAAEvM,GAAI,KAAOvB,KAAM,gBAAiB2N,IAAK,EAAGC,OAAO,EAAOC,YAAa,MAAOC,UAAW,CAAC,MAAO,MAAO,SAE1GC,UAAW,CAAC,MAAO,MAAO,MAAO,QAErC,EAGAC,iBAAAA,GACE,OAAO7L,EAAY,CACjBlD,KAAM,oCAEV,GC3FWrE,GAAWqT,EAGlBC,EAAQA,CAACC,EAAK,MAAQ,IAAIzF,SAAQC,GAAW5P,WAAW4P,EAASwF,KAG1DhM,EAAkBxD,GACtBuP,IAAQE,MAAK,KAElB,MAAM5P,EAA6B,CACjCG,OACA0P,OAAQ,IACRC,WAAY,KACZC,QAAS,CAAC,EACVrN,OAAQ,CACNqN,QAAS,CAAC,IAGd,OAAO/P,CAAQ,IAKNuE,EAAYA,CAACsL,EAAS,IAAK5C,EAAU,UACzCyC,IAAQE,MAAK,KAClB,MAAM/O,EAAa,IAAImP,MAAM/C,GAK7B,OAJApM,EAAMb,SAAW,CACf6P,SACA1P,KAAM8M,GAED/C,QAAQ+F,OAAOpP,EAAM,IAKnBa,EAAaA,IACjB,uCAAuCwC,QAAQ,SAAS,SAAU8E,GACvE,MAAMkH,EAAoB,GAAhBvO,KAAKC,SAAgB,EAAGuO,EAAS,KAALnH,EAAWkH,EAAS,EAAJA,EAAU,EAChE,OAAOC,EAAE/W,SAAS,GACpB,IAIWiD,EAAU,CAErB+T,SAAU7M,EACV8M,gBAAiBvL,EACjBxI,IAAKmM,EACL6H,SAAUlG,EACVvL,UAAWiM,EACXyF,SAAU3E,EACV4E,SAAUlE,EACV7N,eAAgBuO,EAChB9O,KAAMuQ,E,+FCpCR,MAAMxS,EAAW,gBAGXwU,EAAyBC,GACtBA,EAAU9J,KAAIhP,IAAW,CAC9BmL,GAAInL,EAAQmL,GACZC,SAAUpL,EAAQoL,SAClBC,SAAUrL,EAAQqL,SAClBC,eAAgBtL,EAAQsL,eACxBC,aAAcvL,EAAQuL,aACtBC,UAAWxL,EAAQwL,cAIVnD,EAAc,CAEzBC,eAAgBA,IACV9D,EAAAA,GACKC,EAAAA,GAAQ+T,SAASlQ,iBAEnB3D,EAAAA,EAAMC,IAAI,GAAGP,oBAItByE,KAAOD,GACDrE,EAAAA,GACKC,EAAAA,GAAQ+T,SAAS1P,KAAKD,GAExBlE,EAAAA,EAAMI,KAAK,GAAGV,SAAiB,CAAEwE,SAI1ChH,OAASmH,GACHxE,EAAAA,GACKC,EAAAA,GAAQ+T,SAAS3W,OAAOmH,GAE1BrE,EAAAA,EAAMI,KAAK,GAAGV,WAAmB2E,GAI1C+P,WAAYA,IACNvU,EAAAA,GAEKC,EAAAA,GAAQgU,gBAAgB7T,MAAMoT,MAAK5P,IACxC,MAAM4Q,EAAgBH,EAAsBzQ,EAASG,MACrD,MAAO,IACFH,EACHG,KAAMyQ,EACP,IAGErU,EAAAA,EAAMC,IAAI,GAAGP,aAItBsF,eAAgBA,IACVnF,EAAAA,GACKC,EAAAA,GAAQ+T,SAAS7O,iBAEnBhF,EAAAA,EAAMC,IAAI,GAAGP,aAItB+E,MAAOA,IACD5E,EAAAA,GACKC,EAAAA,GAAQ+T,SAASpP,QAEnBzE,EAAAA,EAAMI,KAAK,GAAGV,WAIvBoF,gBAAkBC,GACZlF,EAAAA,GACKC,EAAAA,GAAQ+T,SAAS/O,gBAAgBC,GAEnC/E,EAAAA,EAAMI,KAAK,GAAGV,oBAA4B,CAAEqF,gBAIrDkD,gBAAkB/D,GACZrE,EAAAA,GAEEC,EAAAA,GAAQ+T,SAAS5L,gBACZnI,EAAAA,GAAQ+T,SAAS5L,gBAAgB/D,GAGjCyJ,QAAQC,QAAQ,CACrBhK,KAAM,CAAEsE,QAAQ,GAChBoL,OAAQ,IACRC,WAAY,KACZC,QAAS,CAAC,EACVrN,OAAQ,CAAEqN,QAAS,CAAC,KAInBxT,EAAAA,EAAMI,KAAK,GAAGV,oBAA4B,CAAEwE,S,+ECzHvD,MAAMxE,EAAW,gBAEJ4U,EAAc,CAEzBxG,aAAcA,IACRjO,EAAAA,GACKC,EAAAA,GAAQiU,UAAUjG,gBAAkBH,QAAQ+F,OAAO,IAAID,MAAM,6BAE/DzT,EAAAA,EAAMC,IAAI,GAAGP,mBAItBqO,aAAe7J,GACTrE,EAAAA,GACKC,EAAAA,GAAQiU,UAAUhG,eAAe7J,IAASyJ,QAAQ+F,OAAO,IAAID,MAAM,6BAErEzT,EAAAA,EAAMC,IAAI,GAAGP,kBAA0B,CAAEiD,OAAQ,CAAEuB,U,+ECN9D,MAAMxE,EAAW,uBAEJiF,EAAqB,CAEhC1E,IAAKA,IACCJ,EAAAA,GACKC,EAAAA,GAAQgU,gBAAgB7T,MAE1BD,EAAAA,EAAMC,IAAI,GAAGP,SAItBmF,MAAOA,IACDhF,EAAAA,GACKC,EAAAA,GAAQgU,gBAAgBjP,QAE1B7E,EAAAA,EAAM6B,OAAO,GAAGnC,WAIzBiJ,aAAelC,GACT5G,EAAAA,GAEEC,EAAAA,GAAQgU,gBAAgBnL,aACnB7I,EAAAA,GAAQgU,gBAAgBnL,aAAalC,GAGrCkH,QAAQC,QAAQ,CACrBhK,UAAMmE,EACNuL,OAAQ,IACRC,WAAY,KACZC,QAAS,CAAC,EACVrN,OAAQ,CAAEqN,QAAS,CAAC,KAInBxT,EAAAA,EAAM6B,OAAO,GAAGnC,iBAAyB,CAAEkE,KAAM,CAAE6C,c,iICtCjD,IACO,IACZ9G,EAAAA,G,SCXR,MAAMhB,EAAa,CCKN3B,MAAM,gBDHb,SAAUuX,EAAOlW,EAAUC,EAAYkW,EAAYC,EAAYC,EAAWC,GAC9E,MAAMC,GAAsBC,EAAAA,EAAAA,IAAkB,YACxCC,GAA0BD,EAAAA,EAAAA,IAAkB,gBAC5CE,GAAuBF,EAAAA,EAAAA,IAAkB,aACzCG,GAAqBH,EAAAA,EAAAA,IAAkB,WACvCI,GAAuBJ,EAAAA,EAAAA,IAAkB,aAE/C,OAAQtW,EAAAA,EAAAA,OCVR2W,EAAAA,EAAAA,IAuBYD,EAAA,CAxBdE,WACsB9W,EAAA+W,cADtB,sBAAA9W,EAAA,KAAAA,EAAA,GAAA+W,GACsBhX,EAAA+W,cAAaC,GAAExW,MAAM,mBAAmByW,MAAM,SDe/D,CCAUC,QAAMC,EAAAA,EAAAA,KACf,IAKO,EALPxW,EAAAA,EAAAA,IAKO,cAJLC,EAAAA,EAAAA,IAA4C8V,EAAA,CAAhCU,QAAOpX,EAAAoG,OAAK,CAlBhCiR,SAAAF,EAAAA,EAAAA,KAkBkC,IAAMlX,EAAA,KAAAA,EAAA,KAlBxCqX,EAAAA,EAAAA,IAkBkC,cAlBlCC,EAAA,GDwBW,EAAG,CAAC,aCLP3W,EAAAA,EAAAA,IAEY8V,EAAA,CAFDxa,KAAK,UAAWkb,QAAOpX,EAAAwX,sBDS/B,CC5BXH,SAAAF,EAAAA,EAAAA,KAmBgE,IAExDlX,EAAA,KAAAA,EAAA,KArBRqX,EAAAA,EAAAA,IAmBgE,gBAnBhEC,EAAA,GDiCW,EAAG,CAAC,iBCjCfF,SAAAF,EAAAA,EAAAA,KAEI,IAaU,EAbVvW,EAAAA,EAAAA,IAaU+V,EAAA,CAbAc,MAAOzX,EAAA0X,KAAM,cAAY,QDsC9B,CCxCTL,SAAAF,EAAAA,EAAAA,KAGM,IAEe,EAFfvW,EAAAA,EAAAA,IAEe6V,EAAA,CAFDkB,MAAM,OAAOC,SAAA,ID0CpB,CC7CbP,SAAAF,EAAAA,EAAAA,KAIQ,IAAmE,EAAnEvW,EAAAA,EAAAA,IAAmE2V,EAAA,CAJ3EO,WAI2B9W,EAAA0X,KAAK9Q,KAJhC,sBAAA3G,EAAA,KAAAA,EAAA,GAAA+W,GAI2BhX,EAAA0X,KAAK9Q,KAAIoQ,GAAEa,YAAY,wBD+CjC,KAAM,EAAG,CAAC,kBCnD3BN,EAAA,KAMM3W,EAAAA,EAAAA,IAKe6V,EAAA,CALDkB,MAAM,SAASC,SAAA,IDoDtB,CC1DbP,SAAAF,EAAAA,EAAAA,KAOQ,IAGM,EAHNxW,EAAAA,EAAAA,IAGM,MAHNL,EAGM,EAFJM,EAAAA,EAAAA,IAAmE2V,EAAA,CAR7EO,WAQ6B9W,EAAA0X,KAAKI,OARlC,sBAAA7X,EAAA,KAAAA,EAAA,GAAA+W,GAQ6BhX,EAAA0X,KAAKI,OAAMd,GAAEa,YAAY,sBDyDnC,KAAM,EAAG,CAAC,gBCxDnBjX,EAAAA,EAAAA,IAAyD8V,EAAA,CAA7CU,QAAOpX,EAAA+X,oBAAkB,CAT/CV,SAAAF,EAAAA,EAAAA,KASiD,IAAMlX,EAAA,KAAAA,EAAA,KATvDqX,EAAAA,EAAAA,IASiD,cATjDC,EAAA,GDuEmB,EAAG,CAAC,iBCvEvBA,EAAA,KAYM3W,EAAAA,EAAAA,IAEe6V,EAAA,CAFDkB,MAAM,eAAa,CAZvCN,SAAAF,EAAAA,EAAAA,KAaQ,IAAiG,EAAjGvW,EAAAA,EAAAA,IAAiG2V,EAAA,CAbzGO,WAa2B9W,EAAA0X,KAAKhR,YAbhC,sBAAAzG,EAAA,KAAAA,EAAA,GAAA+W,GAa2BhX,EAAA0X,KAAKhR,YAAWsQ,GAAE9a,KAAK,WAAW2b,YAAY,+BDsExD,KAAM,EAAG,CAAC,kBCnF3BN,EAAA,OAAAA,EAAA,GDyFS,EAAG,CAAC,aCzFbA,EAAA,GD4FK,EAAG,CAAC,cACT,C,cC5DA,GAAeS,EAAAA,EAAAA,IAAgB,CAC7BpR,KAAM,iBACNxK,MAAO,CAAC,WACRC,KAAAA,CAAMP,GAAO,KAAES,IACb,MAAMwa,GAAgBra,EAAAA,EAAAA,KAAI,GACpBgb,GAAOnT,EAAAA,EAAAA,IAAS,CACpBqC,KAAM,GACNF,YAAa,GACboR,OAAQ,KAGJC,EAAqBE,UACzB,IACE,MAAM7S,QAAiB6Q,EAAAA,EAAYxG,eAC/BrK,EAASG,OACXmS,EAAKI,OAAS1S,EAASG,K,CAEzB,MAAOU,GACP,GAA+B,MAA3BA,EAAMb,UAAU6P,QAA0C,iBAAxBhP,EAAMb,SAASG,KAEnD,OAEFC,EAAAA,GAAUS,MAAM,0B,GAIduR,EAAuBS,UAC3B,IAAKP,EAAK9Q,KAER,YADApB,EAAAA,GAAU0S,QAAQ,+BAIpB,IAAKR,EAAKI,OAER,YADAtS,EAAAA,GAAU0S,QAAQ,6BAIpB,MAAM9S,QAAiBC,EAAAA,EAAYxG,OAAO,CACxC+H,KAAM8Q,EAAK9Q,KACXF,YAAagR,EAAKhR,YAClBoR,OAAQJ,EAAKI,SAGX1S,EAASG,OACXa,IACA7J,EAAK,UAAW6I,EAASG,M,EAIvBa,EAAQA,KACZ2Q,EAAchb,OAAQ,EACtB2b,EAAK9Q,KAAO,GACZ8Q,EAAKhR,YAAc,GACnBgR,EAAKI,OAAS,EAAE,EAGZK,EAAOA,KACXpB,EAAchb,OAAQ,CAAI,EAG5B,MAAO,CACLgb,gBACAW,OACAF,uBACAO,qBACA3R,QACA+R,OAEJ,I,UC9FF,MAAM9X,GAA2B,OAAgB,EAAQ,CAAC,CAAC,SAAS6V,GAAQ,CAAC,YAAY,qBAEzF,Q,kBCNA,MAAM5V,EAAa,CACjB8X,IAAK,EACLzZ,MAAO,uBAEH4B,EAAa,CAAE5B,MAAO,aACtB0Z,EAAa,CAAC,SACdC,EAAa,CAAE3Z,MAAO,gBAS5B,OAA4B/C,EAAAA,EAAAA,IAAiB,CAC3CC,OAAQ,0BACRQ,KAAAA,CAAMC,GCiBR,MAAMyI,GAASwT,EAAAA,EAAAA,MACTC,GAAQC,EAAAA,EAAAA,MACRna,EAAQuI,EAAAA,EAAgB5B,WACxByT,GAAehc,EAAAA,EAAAA,KAAI,GACnBic,GAAYjc,EAAAA,EAAAA,KAAI,GAGhBkc,GAAmBC,EAAAA,EAAAA,KAAS,IAAML,EAAM3S,KAAKiT,WAAW,gBAGxDC,GAAkBF,EAAAA,EAAAA,KAAS,MAAQva,EAAMkG,cACzCA,GAAcqU,EAAAA,EAAAA,KAAS,IAAMva,EAAMkG,cAGnCwU,EAAqBf,UACzB,GAAKc,EAAgBhd,MAArB,CAEA2c,EAAa3c,OAAQ,EACrB,UACQgJ,EAAOsB,KAAK,a,CAClB,MAAOJ,GACPC,QAAQD,MAAM,wCAAyCA,GACvDT,EAAAA,GAAUS,MAAM,gC,CAChB,QACAyS,EAAa3c,OAAQ,C,CATW,C,EAc9Bkd,EAAkBhB,UACtB,GAAKc,EAAgBhd,MAErB,IACE,MAAMmd,QAAeC,EAAAA,EAAaC,QAChC,qFACA,kBACA,CACEC,kBAAmB,KACnBC,iBAAkB,SAClBpd,KAAM,YAIK,YAAXgd,IACFP,EAAU5c,OAAQ,QACZ8K,EAAAA,EAAgBV,gB,CAExB,MAAOF,GACP,CACA,QACA0S,EAAU5c,OAAQ,C,GDXtB,MAAO,CAACiE,EAAUC,KAChB,MAAMsZ,GAAqB/C,EAAAA,EAAAA,IAAkB,WACvCE,GAAuBF,EAAAA,EAAAA,IAAkB,aAE/C,OAAQuC,EAAgBhd,QACnBmE,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAoB,MAAOG,EAAY,EACpDK,EAAAA,EAAAA,IAAoB,MAAOJ,EAAY,EACrCK,EAAAA,EAAAA,IAAa2Y,EAAoB,KAAM,CACrClC,SAASF,EAAAA,EAAAA,KAAS,IAAM,EACtBvW,EAAAA,EAAAA,KAAa4Y,EAAAA,EAAAA,IAAOC,EAAAA,cAEtBlC,EAAG,KAEL5W,EAAAA,EAAAA,IAAoB,OAAQ,CAC1BhC,MAAO,YACP6B,MAAOgE,EAAYzI,MAAM8J,OACxB6T,EAAAA,EAAAA,IAAiBlV,EAAYzI,MAAM4L,SAASf,MAAO,EAAGyR,MAE3D1X,EAAAA,EAAAA,IAAoB,MAAO2X,EAAY,CACnCM,EAAiB7c,OAmBf4d,EAAAA,EAAAA,IAAoB,IAAI,KAlBvBzZ,EAAAA,EAAAA,OAAc2W,EAAAA,EAAAA,IAAaH,EAAsB,CAChD0B,IAAK,EACLlc,KAAM,UACN0d,KAAM,QACNxC,QAAS4B,EACTa,QAASnB,EAAa3c,OACrB,CACDsb,SAASF,EAAAA,EAAAA,KAAS,IAAM,EACtBvW,EAAAA,EAAAA,IAAa2Y,EAAoB,KAAM,CACrClC,SAASF,EAAAA,EAAAA,KAAS,IAAM,EACtBvW,EAAAA,EAAAA,KAAa4Y,EAAAA,EAAAA,IAAOM,EAAAA,UAEtBvC,EAAG,IAELtX,EAAO,KAAOA,EAAO,IAAKqX,EAAAA,EAAAA,IAAiB,cAE7CC,EAAG,GACF,EAAG,CAAC,cAEX3W,EAAAA,EAAAA,IAAa8V,EAAsB,CACjCxa,KAAM,SACN0d,KAAM,QACNxC,QAAS6B,EACTY,QAASlB,EAAU5c,OAClB,CACDsb,SAASF,EAAAA,EAAAA,KAAS,IAAM,EACtBvW,EAAAA,EAAAA,IAAa2Y,EAAoB,KAAM,CACrClC,SAASF,EAAAA,EAAAA,KAAS,IAAM,EACtBvW,EAAAA,EAAAA,KAAa4Y,EAAAA,EAAAA,IAAOO,EAAAA,WAEtBxC,EAAG,IAELtX,EAAO,KAAOA,EAAO,IAAKqX,EAAAA,EAAAA,IAAiB,eAE7CC,EAAG,GACF,EAAG,CAAC,kBAGXoC,EAAAA,EAAAA,IAAoB,IAAI,EAAK,CAEnC,IElIA,MAAM,GAA2B,OAAgB,EAAQ,CAAC,CAAC,YAAY,qBAEvE,QCLA,MAAMrZ,EAAa,CAAE3B,MAAO,gBACtB4B,EAAa,CACjB6X,IAAK,EACLzZ,MAAO,gBAEH0Z,EAAa,CAAE1Z,MAAO,oBACtB2Z,EAAa,CAAE3Z,MAAO,oBAc5B,OAA4B/C,EAAAA,EAAAA,IAAiB,CAC3CC,OAAQ,aACRQ,KAAAA,CAAMC,GCiCR,MAAMyI,GAASwT,EAAAA,EAAAA,MACTC,GAAQC,EAAAA,EAAAA,MACRuB,GAAetd,EAAAA,EAAAA,IAAgD,MAG/Dud,GAAcpB,EAAAA,EAAAA,KAAS,IAEvBL,EAAM3S,KAAKiT,WAAW,cACjB,GAEFN,EAAM3S,OAIfgB,EAAAA,EAAgB/B,UAAUC,GAG1B,MAAMzG,EAAQuI,EAAAA,EAAgB5B,WACxBR,GAAcoU,EAAAA,EAAAA,KAAS,IAAMva,EAAMmG,YAAYoG,MAAM,EAAG,KAGxDqP,EAAajC,gBACXpR,EAAAA,EAAgB1B,kBAAkB,EAIpCgV,EAAwBlC,UAC5B,IAEE,MAAM7S,QAAiBC,EAAAA,EAAYuE,gBAAgB/D,GAE/CT,EAASG,KAAKsE,aAEVhD,EAAAA,EAAgBjB,aAAaC,GAGnCsT,EAAAA,EAAaC,QACX,wEACA,iBACA,CACEC,kBAAmB,SACnBC,iBAAkB,SAClBpd,KAAM,YAER8Y,MAAKiD,gBAEC3R,EAAAA,EAAmBgE,aAAazE,SAEhCgB,EAAAA,EAAgBlB,iBAAiB,IACtCyU,OAAM,Q,CAIX,MAAOnU,GACPC,QAAQD,MAAM,kCAAmCA,E,GAK/CoU,EAAeA,KACnBL,EAAaje,OAAOoc,MAAM,EAItBmC,EAAwBA,OAKxBC,EAAaA,KACjBpB,EAAAA,EAAaC,QACX,iDACA,OACA,CACEC,kBAAmB,KACnBC,iBAAkB,SAClBpd,KAAM,YAER8Y,MAAK,KAEL1T,EAAAA,GAAOU,OACJgT,MAAK,KACJxP,EAAAA,GAAUgV,KAAK,kCAAkC,IAGlDJ,OAAOnU,IACNT,EAAAA,GAAUS,MAAM,kCAChBC,QAAQD,MAAM,kCAAmCA,EAAM,GACvD,IACHmU,OAAM,QAEP,EDxBJ,OC2BArb,EAAAA,EAAAA,KAAU,KACR8H,EAAAA,EAAgBlB,iBAAiB,ID5B5B,CAAC3F,EAAUC,KAChB,MAAMsZ,GAAqB/C,EAAAA,EAAAA,IAAkB,WACvCiE,GAA0BjE,EAAAA,EAAAA,IAAkB,gBAC5CkE,GAAwBlE,EAAAA,EAAAA,IAAkB,cAC1CmE,GAAyBnE,EAAAA,EAAAA,IAAkB,eAC3CoE,GAAqBpE,EAAAA,EAAAA,IAAkB,WAE7C,OAAQtW,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAoB,MAAOG,EAAY,EAC3DM,EAAAA,EAAAA,IAAaga,EAAoB,CAC/BC,KAAM,aACNlc,MAAO,iBACPoG,OAAQ,GACR,iBAAkBkV,EAAYle,OAC7B,CACDsb,SAASF,EAAAA,EAAAA,KAAS,IAAM,EACtBvW,EAAAA,EAAAA,IAAa6Z,EAAyB,CACpC1P,MAAO,IACPpM,MAAO,kBACN,CACD0Y,SAASF,EAAAA,EAAAA,KAAS,IAAM,EACtBvW,EAAAA,EAAAA,IAAa2Y,EAAoB,KAAM,CACrClC,SAASF,EAAAA,EAAAA,KAAS,IAAM,EACtBvW,EAAAA,EAAAA,KAAa4Y,EAAAA,EAAAA,IAAOsB,EAAAA,gBAEtBvD,EAAG,OAGPA,EAAG,KAEL3W,EAAAA,EAAAA,IAAa+Z,EAAwB,CAAE5P,MAAO,QAAU,CACtDvK,OAAO2W,EAAAA,EAAAA,KAAS,IAAMlX,EAAO,KAAOA,EAAO,GAAK,EAC9CqX,EAAAA,EAAAA,IAAiB,YAEnBD,SAASF,EAAAA,EAAAA,KAAS,IAAM,EACtBvW,EAAAA,EAAAA,IAAa6Z,EAAyB,CACpCrD,QAAS8C,EACTvb,MAAO,aACN,CACD0Y,SAASF,EAAAA,EAAAA,KAAS,IAAMlX,EAAO,KAAOA,EAAO,GAAK,EAChDqX,EAAAA,EAAAA,IAAiB,YAEnBC,EAAG,KAEL3W,EAAAA,EAAAA,IAAa6Z,EAAyB,CACpCrD,QAASiD,EACT1b,MAAO,aACN,CACD0Y,SAASF,EAAAA,EAAAA,KAAS,IAAMlX,EAAO,KAAOA,EAAO,GAAK,EAChDqX,EAAAA,EAAAA,IAAiB,cAEnBC,EAAG,KAEL3W,EAAAA,EAAAA,IAAa8Z,IACb9Z,EAAAA,EAAAA,IAAa+Z,EAAwB,CACnC5P,MAAO,SACP,eAAgB,kBACf,CACDvK,OAAO2W,EAAAA,EAAAA,KAAS,IAAMlX,EAAO,KAAOA,EAAO,GAAK,EAC9CqX,EAAAA,EAAAA,IAAiB,cAEnBD,SAASF,EAAAA,EAAAA,KAAS,IAAM,CACQ,IAA7B1S,EAAY1I,MAAM0D,SACdS,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAoB,MAAOI,EAAYN,EAAO,KAAOA,EAAO,GAAK,EAC9EU,EAAAA,EAAAA,IAAoB,OAAQ,KAAM,mBAAoB,SAEvDT,EAAAA,EAAAA,KAAW,IAAOC,EAAAA,EAAAA,IAAoB4a,EAAAA,GAAW,CAAE3C,IAAK,IAAK4C,EAAAA,EAAAA,IAAYvW,EAAY1I,OAAQoN,KACpFjJ,EAAAA,EAAAA,OAAc2W,EAAAA,EAAAA,IAAa4D,EAAyB,CAC1DrC,IAAKjP,EAAKhB,GACViP,QAAUJ,GAAiBmD,EAAsBhR,EAAKf,UACtDzJ,MAAO,eACN,CACD0Y,SAASF,EAAAA,EAAAA,KAAS,IAAM,EACtBxW,EAAAA,EAAAA,IAAoB,OAAQ0X,GAAYqB,EAAAA,EAAAA,IAAiBvQ,EAAKd,UAAW,IACzE1H,EAAAA,EAAAA,IAAoB,OAAQ2X,GAAYoB,EAAAA,EAAAA,IAAiBvQ,EAAKf,UAAW,MAE3EmP,EAAG,GACF,KAAM,CAAC,eACR,SAEVA,EAAG,KAEL3W,EAAAA,EAAAA,IAAa8Z,IACb9Z,EAAAA,EAAAA,IAAa6Z,EAAyB,CACpCrD,QAASmD,EACT5b,MAAO,aACN,CACD0Y,SAASF,EAAAA,EAAAA,KAAS,IAAMlX,EAAO,KAAOA,EAAO,GAAK,EAChDqX,EAAAA,EAAAA,IAAiB,YAEnBC,EAAG,OAGPA,EAAG,KAEL3W,EAAAA,EAAAA,IAAa6Z,EAAyB,CACpC1P,MAAO,cACPpM,MAAO,wBACN,CACD0Y,SAASF,EAAAA,EAAAA,KAAS,IAAMlX,EAAO,KAAOA,EAAO,GAAK,EAChDU,EAAAA,EAAAA,IAAoB,OAAQ,CAAEhC,MAAO,mBAAqB,cAAe,OAE3E4Y,EAAG,KAEL3W,EAAAA,EAAAA,IAAa6Z,EAAyB,CACpC1P,MAAO,SACPpM,MAAO,mBACN,CACD0Y,SAASF,EAAAA,EAAAA,KAAS,IAAMlX,EAAO,KAAOA,EAAO,GAAK,EAChDU,EAAAA,EAAAA,IAAoB,OAAQ,CAAEhC,MAAO,cAAgB,SAAU,OAEjE4Y,EAAG,OAGPA,EAAG,GACF,EAAG,CAAC,oBACP3W,EAAAA,EAAAA,IAAaqa,EAAyB,CAAEtc,MAAO,sBAC/CiC,EAAAA,EAAAA,IAAasa,EAAgB,CAC3B9a,QAAS,eACT1D,IAAKsd,EACLmB,UAAWb,GACV,KAAM,MACT,CAEJ,IElPA,MAAM,GAA2B,OAAgB,EAAQ,CAAC,CAAC,YAAY,qBAEvE,Q,iQCHA,MAAM,EAAc,EAEpB,QCLA,MAAMha,EAAa,CCDZ3B,MAAM,aDEP4B,EAAa,CCIF5B,MAAM,gBDHjB0Z,EAAa,CCOA1Z,MAAM,gBDNnB2Z,EAAa,CCoBJ3Z,MAAM,qBDlBf,SAAUuX,EAAOlW,EAAUC,EAAYkW,EAAYC,EAAYC,EAAWC,GAC9E,MAAM8E,GAAoB5E,EAAAA,EAAAA,IAAkB,UACtC+C,GAAqB/C,EAAAA,EAAAA,IAAkB,WACvCE,GAAuBF,EAAAA,EAAAA,IAAkB,aACzC6E,GAAkB7E,EAAAA,EAAAA,IAAkB,QACpC8E,GAA+B9E,EAAAA,EAAAA,IAAkB,qBACjD+E,GAAqB/E,EAAAA,EAAAA,IAAkB,WACvCgF,GAAoBhF,EAAAA,EAAAA,IAAkB,UACtCiF,GAA6BjF,EAAAA,EAAAA,IAAkB,mBAC/CkF,GAAoBlF,EAAAA,EAAAA,IAAkB,UACtCmF,GAA8BnF,EAAAA,EAAAA,IAAkB,oBAEtD,OAAQtW,EAAAA,EAAAA,OClBRC,EAAAA,EAAAA,IA8CM,MA9CNG,EA8CM,EA7CJM,EAAAA,EAAAA,IA0CS8a,EAAA,CA1CD/c,MAAM,mBAAoBid,OAAQ,IDqBvC,CCvBPvE,SAAAF,EAAAA,EAAAA,KAIM,IAyBS,EAzBTvW,EAAAA,EAAAA,IAyBS4a,EAAA,CAzBAK,KAAM,GAAIld,MAAM,cDwBpB,CC5BX0Y,SAAAF,EAAAA,EAAAA,KAKQ,IAuBU,EAvBVvW,EAAAA,EAAAA,IAuBU2a,EAAA,CAvBD5c,MAAM,gBAAc,CAChBmd,QAAM3E,EAAAA,EAAAA,KACf,IAcM,EAdNxW,EAAAA,EAAAA,IAcM,MAdNJ,EAcM,CDYAN,EAAO,KAAOA,EAAO,ICzBzBU,EAAAA,EAAAA,IAEM,OAFDhC,MAAM,iBAAe,EACxBgC,EAAAA,EAAAA,IAAmB,UAAf,gBD0BE,KCxBRA,EAAAA,EAAAA,IASM,MATN0X,EASM,EARJzX,EAAAA,EAAAA,IAGY8V,EAAA,CAHDxa,KAAK,UAAUyC,MAAM,aAAcyY,QAAOpX,EAAA+b,oBD6B9C,CCzCvB1E,SAAAF,EAAAA,EAAAA,KAakB,IAA6B,EAA7BvW,EAAAA,EAAAA,IAA6B2Y,EAAA,MAb/ClC,SAAAF,EAAAA,EAAAA,KAa2B,IAAU,EAAVvW,EAAAA,EAAAA,IAAUwa,MAbrC7D,EAAA,IDiDwBtX,EAAO,KAAOA,EAAO,ICnC3BU,EAAAA,EAAAA,IAAiB,YAAX,QAAI,OAd5B4W,EAAA,GDoDuB,EAAG,CAAC,aCpCX3W,EAAAA,EAAAA,IAGY8V,EAAA,CAHDxa,KAAK,UAAUyC,MAAM,aAAcyY,QAAOpX,EAAAgc,kBDyC9C,CCzDvB3E,SAAAF,EAAAA,EAAAA,KAiBkB,IAA2B,EAA3BvW,EAAAA,EAAAA,IAA2B2Y,EAAA,MAjB7ClC,SAAAF,EAAAA,EAAAA,KAiB2B,IAAQ,EAARvW,EAAAA,EAAAA,IAAQya,MAjBnC9D,EAAA,IDiEwBtX,EAAO,KAAOA,EAAO,IC/C3BU,EAAAA,EAAAA,IAAmB,YAAb,UAAM,OAlB9B4W,EAAA,GDoEuB,EAAG,CAAC,mBCpE3BF,SAAAF,EAAAA,EAAAA,KAyBU,IAEM,EAFNxW,EAAAA,EAAAA,IAEM,MAFN2X,EAEM,EADJ1X,EAAAA,EAAAA,IAA+E0a,EAAA,CAA5D5e,IAAI,mBAAoBuf,WAAWjc,EAAAma,uBDmD7C,KAAM,EAAG,CAAC,oBC7E/B5C,EAAA,OAAAA,EAAA,KAgCM3W,EAAAA,EAAAA,IAWS4a,EAAA,CAXAK,KAAM,GAAIld,MAAM,eDwDpB,CCxFX0Y,SAAAF,EAAAA,EAAAA,KAiCQ,IASU,EATVvW,EAAAA,EAAAA,IASU2a,EAAA,CATD5c,MAAM,cAAY,CACdmd,QAAM3E,EAAAA,EAAAA,KACf,IAIMlX,EAAA,KAAAA,EAAA,KAJNU,EAAAA,EAAAA,IAIM,OAJDhC,MAAM,gBAAc,EACvBgC,EAAAA,EAAAA,IAEM,OAFDhC,MAAM,iBAAe,EACxBgC,EAAAA,EAAAA,IAAc,UAAV,aD2DA,OChGpB0W,SAAAF,EAAAA,EAAAA,KAyCU,IAAmB,EAAnBvW,EAAAA,EAAAA,IAAmB6a,MAzC7BlE,EAAA,OAAAA,EAAA,OAAAA,EAAA,KA8CI3W,EAAAA,EAAAA,IAAwE+a,EAAA,CAAtDjf,IAAI,eAAgBye,UAASnb,EAAAsa,uBDkE5C,KAAM,EAAG,CAAC,eAEjB,CEhHA,MAAMha,EAAa,CCDZ3B,MAAM,mBDEP4B,EAAa,CCDV5B,MAAM,kBDET0Z,EAAa,CCJnBD,IAAA,EAmBwBzZ,MAAM,qBDXxB2Z,EAAa,CC8BR3Z,MAAM,gBD7BXud,ECTN,YDUMC,EAAa,CCoCJxd,MAAM,aDnCfyd,EAAa,CCqCFzd,MAAM,aDpCjB0d,GAAa,CCyCF1d,MAAM,eDxCjB2d,GCbN,UDcMC,GAAc,CCqDA5d,MAAM,aDpDpB6d,GAAc,CCwDH7d,MAAM,eDtDjB,SAAUuX,GAAOlW,EAAUC,EAAYkW,EAAYC,EAAYC,EAAWC,GAC9E,MAAMmG,GAA+BjG,EAAAA,EAAAA,IAAkB,qBACjDE,GAAuBF,EAAAA,EAAAA,IAAkB,aACzCkG,GAAyBlG,EAAAA,EAAAA,IAAkB,eAC3CmG,GAAsBnG,EAAAA,EAAAA,IAAkB,YACxCoG,GAAoBpG,EAAAA,EAAAA,IAAkB,UACtCqG,GAA2BrG,EAAAA,EAAAA,IAAkB,iBAC7CsG,GAAqBC,EAAAA,EAAAA,IAAkB,WAE7C,OAAQ7c,EAAAA,EAAAA,OCzBRC,EAAAA,EAAAA,IA0FM,MA1FNG,EA0FM,EAzFJK,EAAAA,EAAAA,IAcM,MAdNJ,EAcM,CDYJN,EAAO,KAAOA,EAAO,ICzBrBU,EAAAA,EAAAA,IAEM,OAFDhC,MAAM,gBAAc,EACvBgC,EAAAA,EAAAA,IAAwB,UAApB,qBD0BF,ICvBIX,EAAAgd,YAAYvd,OAAS,IDyBxBS,EAAAA,EAAAA,OC1BL2W,EAAAA,EAAAA,IASYH,EAAA,CAflB0B,IAAA,EAQQlc,KAAK,OACL0d,KAAK,QACJxC,QAAOpX,EAAAid,mBACRte,MAAM,YACN6B,MAAM,iBD2BD,CCvCb6W,SAAAF,EAAAA,EAAAA,KAcQ,IAAsC,EAAtCvW,EAAAA,EAAAA,IAAsC6b,EAAA,CAAnBS,KAAK,iBAdhC3F,EAAA,GD4Ca,EAAG,CAAC,cC5CjBoC,EAAAA,EAAAA,IAAA,SAmBe3Z,EAAA6Z,UD6BN3Z,EAAAA,EAAAA,OC7BLC,EAAAA,EAAAA,IAEM,MAFNkY,EAEM,EADJzX,EAAAA,EAAAA,IAAyB8b,EAAA,CAAXS,KAAM,OAIRnd,EAAAgd,YAAYvd,SD6CnBS,EAAAA,EAAAA,OChCPC,EAAAA,EAAAA,IAqDW4a,EAAAA,GAAA,CA1Ff3C,IAAA,KDsEYgF,EAAAA,EAAAA,MAAiBld,EAAAA,EAAAA,OChCvBC,EAAAA,EAAAA,IAuCM,MAvCNmY,EAuCM,GDNGpY,EAAAA,EAAAA,KAAW,IChClBC,EAAAA,EAAAA,IAqCM4a,EAAAA,GAAA,MA5EdC,EAAAA,EAAAA,IAwCkChb,EAAAqd,kBAxClC,CAwCkBnU,EAAM6B,MDgCA7K,EAAAA,EAAAA,OCjChBC,EAAAA,EAAAA,IAqCM,OAnCHiY,IAAKlP,EAAKf,GACXxJ,OA1CV2e,EAAAA,EAAAA,IAAA,CA0CgB,eAAc,oBACUvS,EAAQ,IAAM,KAC3CqM,QAAKJ,GAAEhX,EAAAud,eAAerU,EAAKd,WDgCnB,EC9BTzH,EAAAA,EAAAA,IA6BM,MA7BNwb,EA6BM,EA3BJxb,EAAAA,EAAAA,IAEM,MAFNyb,EAEM,EADJxb,EAAAA,EAAAA,IAAkFgc,EAAA,CAA1EhD,KAAK,QAAQ4D,OAAO,QAAQ7e,MAAM,aDkC/B,CCnFzB0Y,SAAAF,EAAAA,EAAAA,KAiDoE,IAAmB,EAjDvFG,EAAAA,EAAAA,KAAAoC,EAAAA,EAAAA,IAiDuExQ,EAAKb,UAAQ,MAjDpFkP,EAAA,GDwFyB,SCnCb5W,EAAAA,EAAAA,IASM,MATN0b,GASM,EARJzb,EAAAA,EAAAA,IAOY8V,EAAA,CANVxa,KAAK,OACL0d,KAAK,QACJxC,SAzDjBqG,EAAAA,EAAAA,KAAAzG,GAyD6BhX,EAAA0d,iBAAiBxU,EAAKd,WAAQ,UAC3C5H,MAAM,yBDsCG,CChGzB6W,SAAAF,EAAAA,EAAAA,KA4DgB,IAAwC,EAAxCvW,EAAAA,EAAAA,IAAwC6b,EAAA,CAArBS,KAAK,mBA5DxC3F,EAAA,GDqGyB,KAAM,CAAC,eCpCpB5W,EAAAA,EAAAA,IAGM,OAHDhC,MAAM,YAAa6B,MAAO0I,EAAKd,UDyCzB,ECxCTxH,EAAAA,EAAAA,IAAmC6b,EAAA,CAAhBS,KAAK,YACxBvc,EAAAA,EAAAA,IAAkD,OAAlD4b,IAAkD7C,EAAAA,EAAAA,IAAvBxQ,EAAKd,UAAQ,ID0C/B,EC7GvBkU,KAuEY3b,EAAAA,EAAAA,IAGM,MAHN6b,GAGM,EAFJ5b,EAAAA,EAAAA,IAAkC6b,EAAA,CAAfS,KAAK,WAxEtC5F,EAAAA,EAAAA,IAwEgD,KAClCoC,EAAAA,EAAAA,IAAG1Z,EAAA2d,eAAezU,EAAKZ,iBAAc,QD0ChC,GCnHnB4T,MDoHkB,SACD,CACH,CAACY,EChF4B9c,EAAA6Z,WA0C7B7Z,EAAAgd,YAAYvd,OAASO,EAAA+O,WDyClB7O,EAAAA,EAAAA,OC1CX2W,EAAAA,EAAAA,IAUEgG,EAAA,CAzFRzE,IAAA,EAiFQwF,OAAO,oBACNvO,MAAOrP,EAAAgd,YAAYvd,OACnB,YAAWO,EAAA+O,SACX,eAAc/O,EAAA6d,YACdC,gBAAgB9d,EAAA+d,iBACjBpf,MAAM,aACNqf,WAAA,GACA,0BD2CW,KAAM,EAAG,CAAC,QAAS,YAAa,eAAgB,sBCnInErE,EAAAA,EAAAA,IAAA,QDqIa,OAjFFzZ,EAAAA,EAAAA,OC7BP2W,EAAAA,EAAAA,IAYW8F,EAAA,CAnCfvE,IAAA,EAyBM1R,YAAY,kBACX,aAAY,KD8BN,CC5BIuX,OAAK9G,EAAAA,EAAAA,KACd,IAAuE,EAAvEvW,EAAAA,EAAAA,IAAuE6b,EAAA,CAApDS,KAAK,0BAA0Bve,MAAM,kBAE/C+H,aAAWyQ,EAAAA,EAAAA,KACpB,IAAgDlX,EAAA,KAAAA,EAAA,KAAhDU,EAAAA,EAAAA,IAAgD,KAA7ChC,MAAM,qBAAoB,mBAAe,IAC5CgC,EAAAA,EAAAA,IAA+D,KAA5DhC,MAAM,aAAY,0CAAsC,OAjCnE4Y,EAAA,MDuIA,CCjCA,QAAeS,EAAAA,EAAAA,IAAgB,CAC7BpR,KAAM,kBACNxK,MAAO,CAAC,aACRC,KAAAA,CAAMP,GAAO,KAAES,EAAI,OAAE2hB,IACnB,MAAM5f,EAAQuI,EAAAA,EAAgB5B,WACxB8J,EAAW,GACX8O,GAAcnhB,EAAAA,EAAAA,IAAI,GAClBmd,GAAUnd,EAAAA,EAAAA,KAAI,GAEdsgB,GAAcnE,EAAAA,EAAAA,KAAS,IAAMva,EAAMmG,cAEnC4Y,GAAmBxE,EAAAA,EAAAA,KAAS,KAChC,MAAMsF,GAASN,EAAY9hB,MAAQ,GAAKgT,EAClC7D,EAAMiT,EAAQpP,EACpB,OAAOiO,EAAYjhB,MAAM8O,MAAMsT,EAAOjT,EAAI,IAGtCkT,EAAenG,UACnB4B,EAAQ9d,OAAQ,EAChB,UACQ8K,EAAAA,EAAgBlB,kBACtBkY,EAAY9hB,MAAQ,C,CACtB,QACE8d,EAAQ9d,OAAQ,C,GAIdgiB,EAAoBM,IACxBR,EAAY9hB,MAAQsiB,CAAI,EAGpBpB,EAAqBA,KACzB9D,EAAAA,EAAaC,QACX,mDACA,UACA,CACEC,kBAAmB,UACnBC,iBAAkB,SAClBpd,KAAM,YAER8Y,MAAKiD,gBACCpR,EAAAA,EAAgBN,cAAc,IACnC6T,OAAM,QAEP,EAGEmD,EAAiBtF,UACrB,IAEE,MAAM7S,QAAiBC,EAAAA,EAAYuE,gBAAgB/D,GAE/CT,EAASG,KAAKsE,OAEhBtN,EAAK,YAAasJ,GAGlBsT,EAAAA,EAAaC,QACX,2BACA,QACA,CACEC,kBAAmB,KACnBC,iBAAkB,KAClBpd,KAAM,YAER8Y,MAAKiD,gBAEC3R,EAAAA,EAAmBgE,aAAazE,GAEtCuY,GAAc,IACbhE,OAAM,Q,CAIX,MAAOnU,GACPC,QAAQD,MAAM,aAAcA,E,GAI1ByX,EAAmBzF,UACvB,UACQhC,EAAAA,EAAYvG,aAAa7J,E,CAC/B,MAAOI,GACPC,QAAQD,MAAM,wBAAyBA,E,GAIrC0X,EAAkBW,IACtB,MAAMC,EAAO,IAAIpX,KAAKmX,GACtB,OAAOC,EAAKC,eAAe,QAAS,CAClCC,KAAM,UACNC,MAAO,UACPC,IAAK,UACLC,KAAM,UACNC,OAAQ,WACR,EAWJ,OAPA9f,EAAAA,EAAAA,IAAUqf,GAGVF,EAAO,CACLY,QAASV,IAGJ,CACLpB,cACAK,mBACAQ,cACA9O,WACA8K,UACAoD,qBACAM,iBACAG,mBACAC,iBACAI,mBAEJ,ICrNF,MAAM,IAA2B,OAAgB,GAAQ,CAAC,CAAC,SAAS,IAAQ,CAAC,YAAY,qBAEzF,UCNA,MAAMzd,GAAa,CAAE3B,MAAO,iBACtB4B,GAAa,CAAE5B,MAAO,gBACtB0Z,GAAa,CAAE1Z,MAAO,eACtB2Z,GAAa,CAAE3Z,MAAO,cACtBud,GAAa,CAAEvd,MAAO,aAG5B,QAA4B/C,EAAAA,EAAAA,IAAiB,CAC3CC,OAAQ,gBACRQ,KAAAA,CAAMC,GCWR,MAAMoQ,EAAQ,CACZ,CACElM,MAAO,gBACPkG,YAAa,oEAEf,CACElG,MAAO,mBACPkG,YAAa,qEAEf,CACElG,MAAO,iBACPkG,YAAa,yDAEf,CACElG,MAAO,aACPkG,YAAa,iFAEf,CACElG,MAAO,WACPkG,YAAa,8CAEf,CACElG,MAAO,SACPkG,YAAa,6DDJjB,MAAO,CAAC1G,EAAUC,KAChB,MAAM8e,GAA8BvI,EAAAA,EAAAA,IAAkB,oBAChDwI,GAAyBxI,EAAAA,EAAAA,IAAkB,eAEjD,OAAQtW,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAoB,MAAOG,GAAY,EAC3DM,EAAAA,EAAAA,IAAaoe,EAAwB,KAAM,CACzC3H,SAASF,EAAAA,EAAAA,KAAS,IAAM,GACrBjX,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAoB4a,EAAAA,GAAW,MAAMC,EAAAA,EAAAA,IAAYtO,GAAQuS,IAC/Dre,EAAAA,EAAAA,IAAame,EAA6B,CAC/C3G,IAAK6G,EAAKze,MACVtE,KAAM,OACN0d,KAAM,SACNhN,UAAW,IACV,CACDyK,SAASF,EAAAA,EAAAA,KAAS,IAAM,EACtBxW,EAAAA,EAAAA,IAAoB,MAAOJ,GAAY,EACrCI,EAAAA,EAAAA,IAAoB,MAAO0X,GAAY,EACrC1X,EAAAA,EAAAA,IAAoB,OAAQ2X,IAAYoB,EAAAA,EAAAA,IAAiBuF,EAAKze,OAAQ,MAExEG,EAAAA,EAAAA,IAAoB,IAAKub,IAAYxC,EAAAA,EAAAA,IAAiBuF,EAAKvY,aAAc,QAG7E6Q,EAAG,GACF,QACD,QAENA,EAAG,KAEL,CAEJ,IElEA,MAAM,IAA2B,OAAgB,GAAQ,CAAC,CAAC,YAAY,qBAEvE,UNoDA,IAAeS,EAAAA,EAAAA,IAAgB,CAC7BpR,KAAM,WACNsY,WAAY,CACVhE,eAAc,EACdiE,gBAAe,GACfC,cAAa,GACbC,OAAM,SACNC,KAAIA,EAAAA,MAENjjB,KAAAA,GACE,MAAM0I,GAASwT,EAAAA,EAAAA,MACTyB,GAAetd,EAAAA,EAAAA,IAAmC,MAClD6iB,GAAmB7iB,EAAAA,EAAAA,IAAoC,MAG7DmK,EAAAA,EAAgB/B,UAAUC,GAG1B,MAAMgX,EAAqB9D,gBACnBpR,EAAAA,EAAgB1B,mBACtBoa,EAAiBxjB,OAAO+iB,SAAS,EAI7B3E,EAAwBlC,gBACtBpR,EAAAA,EAAgBjB,aAAaC,GACnC0Z,EAAiBxjB,OAAO+iB,SAAS,EAI7B9C,EAAmBA,KACvBhC,EAAaje,OAAOoc,MAAM,EAItBmC,EAAwBA,KAC5BiF,EAAiBxjB,OAAO+iB,SAAS,EAGnC,MAAO,CACL9E,eACAuF,mBACAxD,qBACAC,mBACA1B,wBACAH,wBAEJ,IOpGF,MAAM,IAA2B,OAAgB,GAAQ,CAAC,CAAC,SAAS,GAAQ,CAAC,YAAY,qBAEzF,U,qBCNA,MAAM7Z,GAAa,CAAE3B,MAAO,wBACtB4B,GAAa,CAAE5B,MAAO,eACtB0Z,GAAa,CAAE1Z,MAAO,gBACtB2Z,GAAa,CAAE3Z,MAAO,WACtBud,GAAa,CAAEvd,MAAO,iBACtBwd,GAAa,CAAC,WASpB,QAA4BvgB,EAAAA,EAAAA,IAAiB,CAC3CC,OAAQ,gBACRQ,KAAAA,CAAMC,GCyBR,MAAMkjB,GAAa9iB,EAAAA,EAAAA,IAAiB,IAC9B+iB,GAAgB/iB,EAAAA,EAAAA,IAAsB,MACtCgjB,GAAahjB,EAAAA,EAAAA,IAAI,IACjBijB,GAAuBjjB,EAAAA,EAAAA,KAAa,GACpCkjB,GAAsBljB,EAAAA,EAAAA,IAAI,IAE1BmjB,EAAiB5H,UACrB,IACE,MAAM7S,QAAiBrB,GAAAA,EAAaC,aACpCwb,EAAWzjB,MAAQqJ,EAASG,I,CAC5B,MAAOU,GACPT,EAAAA,GAAUS,MAAM,8BAChBC,QAAQD,MAAM,6BAA8BA,E,GAI1C6Z,EAAmB7H,UACnBwH,EAAc1jB,OAAO6K,OAASuJ,EAAMvJ,OACxC6Y,EAAc1jB,MAAQoU,EACtBwP,EAAqB5jB,OAAS,EAC9B6jB,EAAoB7jB,MAAQ,GAC5B2jB,EAAW3jB,MAAQ,GAAE,EAGjBgkB,EAAqB9H,MAAO9H,EAAkBpF,EAAeiV,KACjEP,EAAc1jB,MAAQoU,EACtBwP,EAAqB5jB,MAAQgP,EAC7B6U,EAAoB7jB,MAAQikB,EAAgBpZ,KAC5C,IACE,MAAMxB,QAAiBrB,GAAAA,EAAaG,cAAciM,EAAMvJ,KAAMoZ,EAAgBpZ,MAC9E8Y,EAAW3jB,MAAQqJ,EAASG,I,CAC5B,MAAOU,GACPT,EAAAA,GAAUS,MAAM,8BAChBC,QAAQD,MAAM,qBAAsBA,E,GDfxC,OCmBAlH,EAAAA,EAAAA,KAAU,KACR8gB,GAAgB,IDpBX,CAAC7f,EAAUC,KAChB,MAAMsZ,GAAqB/C,EAAAA,EAAAA,IAAkB,WACvC+E,GAAqB/E,EAAAA,EAAAA,IAAkB,WAE7C,OAAQtW,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAoB,MAAOG,GAAY,EAC3DK,EAAAA,EAAAA,IAAoB,MAAOJ,GAAY,GACpCL,EAAAA,EAAAA,KAAW,IAAOC,EAAAA,EAAAA,IAAoB4a,EAAAA,GAAW,MAAMC,EAAAA,EAAAA,IAAYwE,EAAWzjB,OAAQoU,KAC7EjQ,EAAAA,EAAAA,OAAc2W,EAAAA,EAAAA,IAAa0E,EAAoB,CACrDnD,IAAKjI,EAAMvJ,KACXjI,OAAO2e,EAAAA,EAAAA,IAAgB,CAAC,aAAc,CAAE,OAAUmC,EAAc1jB,OAAO6K,OAASuJ,EAAMvJ,QACtFwQ,QAAUJ,GAAiB8I,EAAiB3P,IAC3C,CACD2L,QAAQ3E,EAAAA,EAAAA,KAAS,IAAM,EACrBxW,EAAAA,EAAAA,IAAoB,MAAO0X,GAAY,EACrC1X,EAAAA,EAAAA,IAAoB,KAAM,MAAM+Y,EAAAA,EAAAA,IAAiBvJ,EAAMvJ,MAAO,IAC9DjG,EAAAA,EAAAA,IAAoB,OAAQ2X,GAAY,KAAMoB,EAAAA,EAAAA,IAAiBvJ,EAAMP,SAAU,QAGnFyH,SAASF,EAAAA,EAAAA,KAAS,IAAM,EACtBxW,EAAAA,EAAAA,IAAoB,MAAOub,GAAY,GACpChc,EAAAA,EAAAA,KAAW,IAAOC,EAAAA,EAAAA,IAAoB4a,EAAAA,GAAW,MAAMC,EAAAA,EAAAA,IAAY7K,EAAMN,UAAU,CAACmQ,EAAiBjV,MAC5F7K,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAoB,MAAO,CAC/CiY,IAAKrN,EACLpM,OAAO2e,EAAAA,EAAAA,IAAgB,CAAC,eAAgB,CAAE,OAAUmC,EAAc1jB,OAAO6K,OAASuJ,EAAMvJ,MAAQ+Y,EAAqB5jB,QAAUgP,KAC/HqM,SAASqG,EAAAA,EAAAA,KAAgBzG,GAAiB+I,EAAmB5P,EAAOpF,EAAOiV,IAAmB,CAAC,UAC9F,EACDpf,EAAAA,EAAAA,IAAa2Y,EAAoB,KAAM,CACrClC,SAASF,EAAAA,EAAAA,KAAS,IAAM,EACtBvW,EAAAA,EAAAA,KAAa4Y,EAAAA,EAAAA,IAAOC,EAAAA,cAEtBlC,EAAG,KAEL5W,EAAAA,EAAAA,IAAoB,OAAQ,MAAM+Y,EAAAA,EAAAA,IAAiBsG,EAAgBpZ,MAAO,IACzE,GAAIuV,OACL,WAGR5E,EAAG,GACF,KAAM,CAAC,QAAS,eACjB,QAELkI,EAAc1jB,QACVmE,EAAAA,EAAAA,OAAc2W,EAAAA,EAAAA,IAAaoJ,GAAAA,EAAW,CACrC7H,IAAK,EACL5X,MAAO,GAAGif,EAAc1jB,MAAM6K,UAAUgZ,EAAoB7jB,QAC5D0E,QAASif,EAAW3jB,OACnB,KAAM,EAAG,CAAC,QAAS,cACtB4d,EAAAA,EAAAA,IAAoB,IAAI,IAC5B,CAEJ,IE1GA,MAAM,IAA2B,OAAgB,GAAQ,CAAC,CAAC,YAAY,qBAEvE,UCFA,MAAMuG,GAAgC,CACpC,CACEra,KAAM,IACNe,KAAM,OACNuZ,UAAWC,IAEb,CACEva,KAAM,aACNe,KAAM,YACNuZ,UAAWA,IAAM,8BACjBrkB,MAAO0c,IAAS,CAAG3S,KAAM2S,EAAM6H,MAAMxa,OACrCya,SAAU,CACR,CACEza,KAAM,GACNe,KAAM,0BACNuZ,UAAWA,IAAM,+BAEnB,CACEta,KAAM,iBACNe,KAAM,2BACNuZ,UAAWA,IAAM,+BAEnB,CACEta,KAAM,WACNe,KAAM,qBACNuZ,UAAWA,IAAM,+BAEnB,CACEta,KAAM,eACNe,KAAM,yBACNuZ,UAAWA,IAAM,+BAEnB,CACEta,KAAM,iBACNe,KAAM,2BACNuZ,UAAWA,IAAM,+BAEnB,CACEta,KAAM,aACNe,KAAM,uBACNuZ,UAAWA,IAAM,+BAEnB,CACEta,KAAM,WACNe,KAAM,qBACNuZ,UAAWA,IAAM,sDAEnB,CACEta,KAAM,eACNe,KAAM,yBACNuZ,UAAWA,IAAM,uDAEnB,CACEta,KAAM,mBACNe,KAAM,6BACNuZ,UAAWA,IAAM,iCAIvB,CACEta,KAAM,cACNe,KAAM,aACNuZ,UAAWI,IAEb,CACE1a,KAAM,SACNe,KAAM,QACNuZ,UAAWA,IAAM,gCAIfpb,IAASyb,EAAAA,EAAAA,IAAa,CAC1BxjB,SAASyjB,EAAAA,EAAAA,MACTP,YAIFnb,GAAO2b,YAAWzI,MAAOzY,EAAID,EAAMohB,KAKjC,GAHA9Z,EAAAA,EAAgB/B,UAAUC,IAGtBvF,EAAGqG,KAAKiT,WAAW,eAAiBtZ,EAAGqG,KAAKiT,WAAW,aAAc,CACvE,MAAMxa,EAAQuI,EAAAA,EAAgB5B,WAC9B,IAAK3G,EAAMkG,YAAa,CACtB,MAAMA,QAAoBqC,EAAAA,EAAgBF,iBAC1C,IAAKnC,EAGH,OAFAmc,EAAK,UACLnb,EAAAA,GAAU0S,QAAQ,iC,EAKxByI,GAAM,IAGR,U,UCpGA,IAAeC,EAAAA,GAAAA,IAAY,CACzBtiB,MAAO,CAAC,EACRuiB,QAAS,CAAC,EACVC,UAAW,CAAC,EACZC,QAAS,CAAC,EACVC,QAAS,CAAC,I,WCHZ,MAAMC,GAAsBhb,IAC1B,IAAKA,EAAMb,WAAaa,EAAMb,SAASG,KACrC,OAAOU,EAAMoM,SAAW,gBAG1B,MAAMvQ,EAAYmE,EAAMb,SAASG,KAC3B2b,EAAgB,GAGlBpf,EAAUqf,kBACZD,EAAc7a,KAAKvE,EAAUqf,kBAI/B,IAAIC,EAAmBtf,EAAUuf,eACjC,MAAOD,EACDA,EAAiBD,kBACnBD,EAAc7a,KAAK+a,EAAiBD,kBAEtCC,EAAmBA,EAAiBC,eAItC,OAA6B,IAAzBH,EAAczhB,OACTqC,EAAUuQ,SAAW,oBAIvB6O,EAAcjV,KAAK,OAAO,EAI7BqV,GAAqBrb,IACzB,IAAKA,EAAMb,WAAaa,EAAMb,SAASG,KAErC,YADAC,EAAAA,GAAUS,MAAMA,EAAMoM,SAAW,iBAKnC,MAAMkP,EAAeN,GAAmBhb,GAGxCkT,EAAAA,EAAaqI,MACXD,EACA,QACA,CACElI,kBAAmB,KACnBoI,0BAA0B,EAC1BC,mBAAmB,EACnBC,oBAAoB,EACpBC,WAAW,GAEd,EAIGC,GAAkB5b,IAEtB,GAAIA,EAAMb,UAAYa,EAAMb,SAASG,KAAM,CAEzC,GAA4B,iBAAxBU,EAAMb,SAASG,KACjB,OAAO,EAIT,GAAoC,iBAAhCU,EAAMb,SAASG,KAAK8M,QACtB,OAAO,EAIT,GAAsC,iBAAlCpM,EAAMb,SAASG,KAAKuc,UACtB,OAAO,C,CAIX,OAAO,CAAK,EAIDC,GAAoBA,KAC/BpgB,GAAAA,EAAMqgB,aAAa5c,SAAS6c,KAC1B7c,GAAYA,IACZa,GAEM4b,GAAe5b,IAEjBT,EAAAA,GAAUgV,KAAK,+BAGRlL,QAAQ+F,OAAOpP,KAIxBqb,GAAkBrb,GAGXqJ,QAAQ+F,OAAOpP,KAEzB,EAGH,I,gEC5EAic,GAAAA,GAAQC,IACNC,GAAAA,IAAQC,GAAAA,IAAcC,GAAAA,IAAQC,GAAAA,IAC9BC,GAAAA,IAAWC,GAAAA,IAAYC,GAAAA,IAAaC,GAAAA,GACpCC,GAAAA,IAAUC,GAAAA,IAAYC,GAAAA,IACtBC,GAAAA,IAAmBC,GAAAA,IACnBC,GAAAA,IAAaC,GAAAA,IAAWC,GAAAA,IAAUC,GAAAA,IAClCC,GAAAA,IAAgCC,GAAAA,KAGlC,MAAM5hB,IAAM6hB,EAAAA,EAAAA,IAAUC,GAGtB9hB,GAAIye,UAAU,oBAAqBsD,GAAAA,IAGnC,IAAK,MAAOrL,GAAK+H,MAAcuD,OAAOC,QAAQC,GAC5CliB,GAAIye,UAAU/H,GAAK+H,IAIrB4B,KAEArgB,GAAIugB,IAAI4B,IACJ5B,IAAIld,IACJkd,IAAI6B,GAAAA,EAAa,CAChBC,OAAQC,GAAAA,EACRpK,KAAM,YAEPqK,MAAM,QAMVviB,GAAIoG,OAAOoc,aAAe,CAACC,EAAcC,EAAI5J,KAE3CtU,QAAQD,MAAM,YAAake,GAG3B,MAAMriB,EAAuB,CAC3BuQ,QAAS8R,aAAe/O,MAAQ+O,EAAI9R,QAAUgS,OAAOF,GACrDG,MAAOH,aAAe/O,MAAQ+O,EAAIG,MAAQ,QAC1CC,YAAa/J,EACb9W,IAAK6J,OAAOiX,SAASC,MAGvBnjB,EAAAA,GAAOO,SAASC,GAAWsY,OAAOsK,IAChCxe,QAAQD,MAAM,cAAeye,EAAU,GACvC,EAIJnX,OAAOoX,iBAAiB,sBAAuBC,IAC7C,MAAM9iB,EAAuB,CAC3BuQ,QACEuS,EAAMC,kBAAkBzP,MACpBwP,EAAMC,OAAOxS,QACb,gBACNiS,MAAOM,EAAMC,kBAAkBzP,MAAQwP,EAAMC,OAAOP,MAAQ,QAC5D5gB,IAAK6J,OAAOiX,SAASC,KACrBvoB,KAAM,sBAGRoF,EAAAA,GAAOO,SAASC,GAAWsY,OAAOsK,IAChCxe,QAAQD,MAAM,qBAAsBye,EAAU,GAC9C,IAIJnX,OAAOoX,iBAAiB,SAAUC,IAEhC,GAAIA,EAAMvS,QAAS,CACjB,MAAMvQ,EAAuB,CAC3BuQ,QAASuS,EAAMvS,QACfyS,SAAU,GAAGF,EAAMG,YAAYH,EAAMI,UAAUJ,EAAMK,QACrDvhB,IAAK6J,OAAOiX,SAASC,KACrBvoB,KAAM,gBAGRoF,EAAAA,GAAOO,SAASC,GAAWsY,OAAOsK,IAChCxe,QAAQD,MAAM,gBAAiBye,EAAU,G,gECnGnCQ,E,qBAAZ,SAAYA,GACVA,EAAAA,EAAA,gBACAA,EAAAA,EAAA,mBACD,EAHD,CAAYA,IAAAA,EAAiB,KAK7B,MAAM7jB,EAAW,sBAEJ8jB,EAAc,CAEzBxT,kBAAmBA,IACbnQ,EAAAA,GACKC,EAAAA,GAAQmU,SAASjE,oBAEnBhQ,EAAAA,EAAMC,IAAwB,GAAGP,KAI1CuQ,qBAAuB9J,GACjBtG,EAAAA,GACKC,EAAAA,GAAQmU,SAAShE,qBAAqB9J,GAExCnG,EAAAA,EAAMI,KAAyB,GAAGV,IAAYyG,G,GC9BrDsd,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,GAE5B,IAAIC,EAAeH,EAAyBE,GAC5C,QAAqB5b,IAAjB6b,EACH,OAAOA,EAAaC,QAGrB,IAAIC,EAASL,EAAyBE,GAAY,CACjDnd,GAAImd,EACJI,QAAQ,EACRF,QAAS,CAAC,GAUX,OANAG,EAAoBL,GAAUM,KAAKH,EAAOD,QAASC,EAAQA,EAAOD,QAASH,GAG3EI,EAAOC,QAAS,EAGTD,EAAOD,OACf,CAGAH,EAAoBQ,EAAIF,E,WC5BxB,IAAIG,EAAW,GACfT,EAAoBU,EAAI,SAAS7M,EAAQ8M,EAAUC,EAAIC,GACtD,IAAGF,EAAH,CAMA,IAAIG,EAAeC,IACnB,IAAS1a,EAAI,EAAGA,EAAIoa,EAASrmB,OAAQiM,IAAK,CACrCsa,EAAWF,EAASpa,GAAG,GACvBua,EAAKH,EAASpa,GAAG,GACjBwa,EAAWJ,EAASpa,GAAG,GAE3B,IAJA,IAGI2a,GAAY,EACPC,EAAI,EAAGA,EAAIN,EAASvmB,OAAQ6mB,MACpB,EAAXJ,GAAsBC,GAAgBD,IAAaxC,OAAO6C,KAAKlB,EAAoBU,GAAGS,OAAM,SAASpO,GAAO,OAAOiN,EAAoBU,EAAE3N,GAAK4N,EAASM,GAAK,IAChKN,EAASS,OAAOH,IAAK,IAErBD,GAAY,EACTH,EAAWC,IAAcA,EAAeD,IAG7C,GAAGG,EAAW,CACbP,EAASW,OAAO/a,IAAK,GACrB,IAAI4J,EAAI2Q,SACEvc,IAAN4L,IAAiB4D,EAAS5D,EAC/B,CACD,CACA,OAAO4D,CArBP,CAJCgN,EAAWA,GAAY,EACvB,IAAI,IAAIxa,EAAIoa,EAASrmB,OAAQiM,EAAI,GAAKoa,EAASpa,EAAI,GAAG,GAAKwa,EAAUxa,IAAKoa,EAASpa,GAAKoa,EAASpa,EAAI,GACrGoa,EAASpa,GAAK,CAACsa,EAAUC,EAAIC,EAwB/B,C,eC5BAb,EAAoBqB,EAAI,SAASjB,GAChC,IAAIkB,EAASlB,GAAUA,EAAOmB,WAC7B,WAAa,OAAOnB,EAAO,UAAY,EACvC,WAAa,OAAOA,CAAQ,EAE7B,OADAJ,EAAoBwB,EAAEF,EAAQ,CAAE3c,EAAG2c,IAC5BA,CACR,C,eCNAtB,EAAoBwB,EAAI,SAASrB,EAASsB,GACzC,IAAI,IAAI1O,KAAO0O,EACXzB,EAAoB0B,EAAED,EAAY1O,KAASiN,EAAoB0B,EAAEvB,EAASpN,IAC5EsL,OAAOsD,eAAexB,EAASpN,EAAK,CAAE6O,YAAY,EAAMrlB,IAAKklB,EAAW1O,IAG3E,C,eCPAiN,EAAoB6B,EAAI,CAAC,EAGzB7B,EAAoB8B,EAAI,SAASC,GAChC,OAAO9X,QAAQ+X,IAAI3D,OAAO6C,KAAKlB,EAAoB6B,GAAGI,QAAO,SAASC,EAAUnP,GAE/E,OADAiN,EAAoB6B,EAAE9O,GAAKgP,EAASG,GAC7BA,CACR,GAAG,IACJ,C,eCPAlC,EAAoBmC,EAAI,SAASJ,GAEhC,MAAO,MAAQA,EAAU,IAAM,CAAC,GAAK,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,YAAYA,GAAW,KACvO,C,eCHA/B,EAAoBoC,SAAW,SAASL,GAEvC,MAAO,OAASA,EAAU,IAAM,CAAC,GAAK,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,WAAW,IAAM,YAAYA,GAAW,MACvN,C,eCJA/B,EAAoBqC,EAAI,WACvB,GAA0B,kBAAfC,WAAyB,OAAOA,WAC3C,IACC,OAAO3iB,MAAQ,IAAI4iB,SAAS,cAAb,EAChB,CAAE,MAAOT,GACR,GAAsB,kBAAX5Z,OAAqB,OAAOA,MACxC,CACA,CAPuB,E,eCAxB8X,EAAoB0B,EAAI,SAASc,EAAKC,GAAQ,OAAOpE,OAAOqE,UAAUC,eAAepC,KAAKiC,EAAKC,EAAO,C,eCAtG,IAAIG,EAAa,CAAC,EACdC,EAAoB,YAExB7C,EAAoB8C,EAAI,SAASzkB,EAAK0kB,EAAMhQ,EAAKgP,GAChD,GAAGa,EAAWvkB,GAAQukB,EAAWvkB,GAAK2C,KAAK+hB,OAA3C,CACA,IAAIC,EAAQC,EACZ,QAAW5e,IAAR0O,EAEF,IADA,IAAImQ,EAAUC,SAASC,qBAAqB,UACpC/c,EAAI,EAAGA,EAAI6c,EAAQ9oB,OAAQiM,IAAK,CACvC,IAAI0E,EAAImY,EAAQ7c,GAChB,GAAG0E,EAAEsY,aAAa,QAAUhlB,GAAO0M,EAAEsY,aAAa,iBAAmBR,EAAoB9P,EAAK,CAAEiQ,EAASjY,EAAG,KAAO,CACpH,CAEGiY,IACHC,GAAa,EACbD,EAASG,SAASG,cAAc,UAEhCN,EAAOO,QAAU,QACjBP,EAAOQ,QAAU,IACbxD,EAAoByD,IACvBT,EAAOU,aAAa,QAAS1D,EAAoByD,IAElDT,EAAOU,aAAa,eAAgBb,EAAoB9P,GAExDiQ,EAAOW,IAAMtlB,GAEdukB,EAAWvkB,GAAO,CAAC0kB,GACnB,IAAIa,EAAmB,SAASC,EAAMtE,GAErCyD,EAAOc,QAAUd,EAAOe,OAAS,KACjCC,aAAaR,GACb,IAAIS,EAAUrB,EAAWvkB,GAIzB,UAHOukB,EAAWvkB,GAClB2kB,EAAOkB,YAAclB,EAAOkB,WAAWC,YAAYnB,GACnDiB,GAAWA,EAAQjf,SAAQ,SAAS4b,GAAM,OAAOA,EAAGrB,EAAQ,IACzDsE,EAAM,OAAOA,EAAKtE,EACtB,EACIiE,EAAUlpB,WAAWspB,EAAiBQ,KAAK,UAAM/f,EAAW,CAAExN,KAAM,UAAWwtB,OAAQrB,IAAW,MACtGA,EAAOc,QAAUF,EAAiBQ,KAAK,KAAMpB,EAAOc,SACpDd,EAAOe,OAASH,EAAiBQ,KAAK,KAAMpB,EAAOe,QACnDd,GAAcE,SAASmB,KAAKC,YAAYvB,EApCkB,CAqC3D,C,eCxCAhD,EAAoB/P,EAAI,SAASkQ,GACX,qBAAXqE,QAA0BA,OAAOC,aAC1CpG,OAAOsD,eAAexB,EAASqE,OAAOC,YAAa,CAAE/tB,MAAO,WAE7D2nB,OAAOsD,eAAexB,EAAS,aAAc,CAAEzpB,OAAO,GACvD,C,eCNAspB,EAAoB0E,IAAM,SAAStE,GAGlC,OAFAA,EAAOuE,MAAQ,GACVvE,EAAOnF,WAAUmF,EAAOnF,SAAW,IACjCmF,CACR,C,eCJAJ,EAAoBhc,EAAI,G,eCAxB,GAAwB,qBAAbmf,SAAX,CACA,IAAIyB,EAAmB,SAAS7C,EAAS8C,EAAUC,EAAQ5a,EAAS8F,GACnE,IAAI+U,EAAU5B,SAASG,cAAc,QAErCyB,EAAQC,IAAM,aACdD,EAAQluB,KAAO,WACXmpB,EAAoByD,KACvBsB,EAAQE,MAAQjF,EAAoByD,IAErC,IAAIyB,EAAiB,SAAS3F,GAG7B,GADAwF,EAAQjB,QAAUiB,EAAQhB,OAAS,KAChB,SAAfxE,EAAM1oB,KACTqT,QACM,CACN,IAAIib,EAAY5F,GAASA,EAAM1oB,KAC3BuuB,EAAW7F,GAASA,EAAM8E,QAAU9E,EAAM8E,OAAOjF,MAAQyF,EACzD/F,EAAM,IAAI/O,MAAM,qBAAuBgS,EAAU,cAAgBoD,EAAY,KAAOC,EAAW,KACnGtG,EAAIvd,KAAO,iBACXud,EAAIuG,KAAO,wBACXvG,EAAIjoB,KAAOsuB,EACXrG,EAAI9hB,QAAUooB,EACVL,EAAQb,YAAYa,EAAQb,WAAWC,YAAYY,GACvD/U,EAAO8O,EACR,CACD,EAUA,OATAiG,EAAQjB,QAAUiB,EAAQhB,OAASmB,EACnCH,EAAQ3F,KAAOyF,EAGXC,EACHA,EAAOZ,WAAWoB,aAAaP,EAASD,EAAOS,aAE/CpC,SAASmB,KAAKC,YAAYQ,GAEpBA,CACR,EACIS,EAAiB,SAASpG,EAAMyF,GAEnC,IADA,IAAIY,EAAmBtC,SAASC,qBAAqB,QAC7C/c,EAAI,EAAGA,EAAIof,EAAiBrrB,OAAQiM,IAAK,CAChD,IAAIqf,EAAMD,EAAiBpf,GACvBsf,EAAWD,EAAIrC,aAAa,cAAgBqC,EAAIrC,aAAa,QACjE,GAAe,eAAZqC,EAAIV,MAAyBW,IAAavG,GAAQuG,IAAad,GAAW,OAAOa,CACrF,CACA,IAAIE,EAAoBzC,SAASC,qBAAqB,SACtD,IAAQ/c,EAAI,EAAGA,EAAIuf,EAAkBxrB,OAAQiM,IAAK,CAC7Cqf,EAAME,EAAkBvf,GACxBsf,EAAWD,EAAIrC,aAAa,aAChC,GAAGsC,IAAavG,GAAQuG,IAAad,EAAU,OAAOa,CACvD,CACD,EACIG,EAAiB,SAAS9D,GAC7B,OAAO,IAAI9X,SAAQ,SAASC,EAAS8F,GACpC,IAAIoP,EAAOY,EAAoBoC,SAASL,GACpC8C,EAAW7E,EAAoBhc,EAAIob,EACvC,GAAGoG,EAAepG,EAAMyF,GAAW,OAAO3a,IAC1C0a,EAAiB7C,EAAS8C,EAAU,KAAM3a,EAAS8F,EACpD,GACD,EAEI8V,EAAqB,CACxB,IAAK,GAGN9F,EAAoB6B,EAAEkE,QAAU,SAAShE,EAASG,GACjD,IAAI8D,EAAY,CAAC,GAAK,EAAE,IAAM,EAAE,IAAM,EAAE,IAAM,EAAE,IAAM,EAAE,IAAM,EAAE,IAAM,EAAE,IAAM,EAAE,IAAM,EAAE,IAAM,GAC3FF,EAAmB/D,GAAUG,EAASlhB,KAAK8kB,EAAmB/D,IACzB,IAAhC+D,EAAmB/D,IAAkBiE,EAAUjE,IACtDG,EAASlhB,KAAK8kB,EAAmB/D,GAAW8D,EAAe9D,GAASpS,MAAK,WACxEmW,EAAmB/D,GAAW,CAC/B,IAAG,SAASD,GAEX,aADOgE,EAAmB/D,GACpBD,CACP,IAEF,CA3E2C,C,eCK3C,IAAImE,EAAkB,CACrB,IAAK,GAGNjG,EAAoB6B,EAAEZ,EAAI,SAASc,EAASG,GAE1C,IAAIgE,EAAqBlG,EAAoB0B,EAAEuE,EAAiBlE,GAAWkE,EAAgBlE,QAAW1d,EACtG,GAA0B,IAAvB6hB,EAGF,GAAGA,EACFhE,EAASlhB,KAAKklB,EAAmB,QAC3B,CAGL,IAAIC,EAAU,IAAIlc,SAAQ,SAASC,EAAS8F,GAAUkW,EAAqBD,EAAgBlE,GAAW,CAAC7X,EAAS8F,EAAS,IACzHkS,EAASlhB,KAAKklB,EAAmB,GAAKC,GAGtC,IAAI9nB,EAAM2hB,EAAoBhc,EAAIgc,EAAoBmC,EAAEJ,GAEpDnhB,EAAQ,IAAImP,MACZqW,EAAe,SAAS7G,GAC3B,GAAGS,EAAoB0B,EAAEuE,EAAiBlE,KACzCmE,EAAqBD,EAAgBlE,GACX,IAAvBmE,IAA0BD,EAAgBlE,QAAW1d,GACrD6hB,GAAoB,CACtB,IAAIf,EAAY5F,IAAyB,SAAfA,EAAM1oB,KAAkB,UAAY0oB,EAAM1oB,MAChEwvB,EAAU9G,GAASA,EAAM8E,QAAU9E,EAAM8E,OAAOV,IACpD/iB,EAAMoM,QAAU,iBAAmB+U,EAAU,cAAgBoD,EAAY,KAAOkB,EAAU,IAC1FzlB,EAAMW,KAAO,iBACbX,EAAM/J,KAAOsuB,EACbvkB,EAAM5D,QAAUqpB,EAChBH,EAAmB,GAAGtlB,EACvB,CAEF,EACAof,EAAoB8C,EAAEzkB,EAAK+nB,EAAc,SAAWrE,EAASA,EAE/D,CAEH,EAUA/B,EAAoBU,EAAEO,EAAI,SAASc,GAAW,OAAoC,IAA7BkE,EAAgBlE,EAAgB,EAGrF,IAAIuE,EAAuB,SAASC,EAA4BrmB,GAC/D,IAKI+f,EAAU8B,EALVpB,EAAWzgB,EAAK,GAChBsmB,EAActmB,EAAK,GACnBumB,EAAUvmB,EAAK,GAGImG,EAAI,EAC3B,GAAGsa,EAAS+F,MAAK,SAAS5jB,GAAM,OAA+B,IAAxBmjB,EAAgBnjB,EAAW,IAAI,CACrE,IAAImd,KAAYuG,EACZxG,EAAoB0B,EAAE8E,EAAavG,KACrCD,EAAoBQ,EAAEP,GAAYuG,EAAYvG,IAGhD,GAAGwG,EAAS,IAAI5S,EAAS4S,EAAQzG,EAClC,CAEA,IADGuG,GAA4BA,EAA2BrmB,GACrDmG,EAAIsa,EAASvmB,OAAQiM,IACzB0b,EAAUpB,EAASta,GAChB2Z,EAAoB0B,EAAEuE,EAAiBlE,IAAYkE,EAAgBlE,IACrEkE,EAAgBlE,GAAS,KAE1BkE,EAAgBlE,GAAW,EAE5B,OAAO/B,EAAoBU,EAAE7M,EAC9B,EAEI8S,EAAqBC,KAAK,wBAA0BA,KAAK,yBAA2B,GACxFD,EAAmB3hB,QAAQshB,EAAqBlC,KAAK,KAAM,IAC3DuC,EAAmB3lB,KAAOslB,EAAqBlC,KAAK,KAAMuC,EAAmB3lB,KAAKojB,KAAKuC,G,ICpFvF,IAAIE,EAAsB7G,EAAoBU,OAAErc,EAAW,CAAC,MAAM,WAAa,OAAO2b,EAAoB,KAAO,IACjH6G,EAAsB7G,EAAoBU,EAAEmG,E", "sources": ["webpack://fuzz-web/./src/components/XmlEditor/XmlEditor.vue?3cb8", "webpack://fuzz-web/./src/components/XmlEditor/XmlEditor.vue", "webpack://fuzz-web/./src/components/XmlEditor/XmlEditor.vue?1c79", "webpack://fuzz-web/./src/components/TestSuite/XmlViewer.vue?997c", "webpack://fuzz-web/./src/components/TestSuite/XmlViewer.vue", "webpack://fuzz-web/./src/components/TestSuite/XmlViewer.vue?364e", "webpack://fuzz-web/./src/api/appApi.ts", "webpack://fuzz-web/./src/api/interoperationApi.ts", "webpack://fuzz-web/./src/api/testSuiteApi.ts", "webpack://fuzz-web/./src/services/testPlanService.ts", "webpack://fuzz-web/./src/mock/mockData.ts", "webpack://fuzz-web/./src/mock/modules/mockTestPlanApi.ts", "webpack://fuzz-web/./src/mock/modules/mockTestPlanHistoryApi.ts", "webpack://fuzz-web/./src/mock/modules/mockAppApi.ts", "webpack://fuzz-web/./src/mock/modules/mockExplorerApi.ts", "webpack://fuzz-web/./src/mock/modules/mockTestSuiteApi.ts", "webpack://fuzz-web/./src/mock/modules/mockHardwareApi.ts", "webpack://fuzz-web/./src/mock/modules/mockSequenceApi.ts", "webpack://fuzz-web/./src/mock/modules/mockInteroperationApi.ts", "webpack://fuzz-web/./src/mock/modules/mockCaseApi.ts", "webpack://fuzz-web/./src/mock/mockApi.ts", "webpack://fuzz-web/./src/api/testPlanApi.ts", "webpack://fuzz-web/./src/api/explorerApi.ts", "webpack://fuzz-web/./src/api/testPlanHistoryApi.ts", "webpack://fuzz-web/./src/api/index.ts", "webpack://fuzz-web/./src/components/TestPlan/CreateTestPlan.vue?24dd", "webpack://fuzz-web/./src/components/TestPlan/CreateTestPlan.vue", "webpack://fuzz-web/./src/components/TestPlan/CreateTestPlan.vue?8768", "webpack://fuzz-web/./src/components/layout/TestPlanStatusIndicator.vue?85b3", "webpack://fuzz-web/./src/components/layout/TestPlanStatusIndicator.vue", "webpack://fuzz-web/./src/components/layout/TestPlanStatusIndicator.vue?17c3", "webpack://fuzz-web/./src/components/layout/TopMenuBar.vue?7962", "webpack://fuzz-web/./src/components/layout/TopMenuBar.vue", "webpack://fuzz-web/./src/components/layout/TopMenuBar.vue?4c80", "webpack://fuzz-web/./src/App.vue", "webpack://fuzz-web/./src/views/HomeView.vue?188e", "webpack://fuzz-web/./src/views/HomeView.vue", "webpack://fuzz-web/./src/components/TestPlan/TestPlanHistory.vue?7cc4", "webpack://fuzz-web/./src/components/TestPlan/TestPlanHistory.vue", "webpack://fuzz-web/./src/components/TestPlan/TestPlanHistory.vue?30ae", "webpack://fuzz-web/./src/components/Guide/TestPlanGuide.vue?a174", "webpack://fuzz-web/./src/components/Guide/TestPlanGuide.vue", "webpack://fuzz-web/./src/components/Guide/TestPlanGuide.vue?064e", "webpack://fuzz-web/./src/views/HomeView.vue?1da1", "webpack://fuzz-web/./src/views/TestSuiteView.vue?bc6f", "webpack://fuzz-web/./src/views/TestSuiteView.vue", "webpack://fuzz-web/./src/views/TestSuiteView.vue?3191", "webpack://fuzz-web/./src/router/index.ts", "webpack://fuzz-web/./src/store/index.ts", "webpack://fuzz-web/./src/utils/errorHandler.ts", "webpack://fuzz-web/./src/main.ts", "webpack://fuzz-web/./src/api/sequenceApi.ts", "webpack://fuzz-web/webpack/bootstrap", "webpack://fuzz-web/webpack/runtime/chunk loaded", "webpack://fuzz-web/webpack/runtime/compat get default export", "webpack://fuzz-web/webpack/runtime/define property getters", "webpack://fuzz-web/webpack/runtime/ensure chunk", "webpack://fuzz-web/webpack/runtime/get javascript chunk filename", "webpack://fuzz-web/webpack/runtime/get mini-css chunk filename", "webpack://fuzz-web/webpack/runtime/global", "webpack://fuzz-web/webpack/runtime/hasOwnProperty shorthand", "webpack://fuzz-web/webpack/runtime/load script", "webpack://fuzz-web/webpack/runtime/make namespace object", "webpack://fuzz-web/webpack/runtime/node module decorator", "webpack://fuzz-web/webpack/runtime/publicPath", "webpack://fuzz-web/webpack/runtime/css loading", "webpack://fuzz-web/webpack/runtime/jsonp chunk loading", "webpack://fuzz-web/webpack/startup"], "sourcesContent": ["import { defineComponent as _defineComponent } from 'vue'\nimport { openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\"\n\nimport { ref, onMounted, onBeforeUnmount, watch } from 'vue'\r\nimport { EditorState } from '@codemirror/state'\r\nimport { EditorView } from '@codemirror/view'\r\nimport { defaultKeymap } from '@codemirror/commands'\r\nimport { \r\n  lineNumbers, \r\n  highlightActiveLineGutter, \r\n  highlightSpecialChars,\r\n  drawSelection, \r\n  dropCursor\r\n} from \"@codemirror/view\"\r\n\r\nimport { xml } from '@codemirror/lang-xml'\r\nimport { \r\n  indentOnInput, \r\n  syntaxHighlighting, \r\n  defaultHighlightStyle,\r\n  foldGutter\r\n} from \"@codemirror/language\"\r\nimport { history, historyKeymap } from \"@codemirror/commands\"\r\nimport { foldKeymap } from \"@codemirror/language\"\r\nimport { keymap } from \"@codemirror/view\"\r\n\r\n\nexport default /*@__PURE__*/_defineComponent({\n  __name: 'XmlEditor',\n  props: {\n    value: {},\n    language: {},\n    readOnly: { type: Boolean }\n  },\n  emits: ['update:value', 'change'],\n  setup(__props: any, { emit: __emit }) {\n\r\nconst props = __props\r\n\r\nconst emit = __emit\r\n\r\nconst editorContainer = ref<HTMLElement | null>(null)\r\nlet view: EditorView | null = null\r\n\r\n// 创建一个基础设置，代替 basicSetup\r\nconst basicSetupExtensions = [\r\n  lineNumbers(),\r\n  highlightActiveLineGutter(),\r\n  highlightSpecialChars(),\r\n  history(),\r\n  drawSelection(),\r\n  dropCursor(),\r\n  EditorState.allowMultipleSelections.of(true),\r\n  indentOnInput(),\r\n  syntaxHighlighting(defaultHighlightStyle, { fallback: true }),\r\n  foldGutter(),\r\n  keymap.of([\r\n    ...defaultKeymap,\r\n    ...historyKeymap,\r\n    ...foldKeymap\r\n  ])\r\n]\r\n\r\n// 创建编辑器实例\r\nconst createEditor = () => {\r\n  if (!editorContainer.value) return\r\n  \r\n  // 使用重新构建的扩展集\r\n  const extensions = [\r\n    ...basicSetupExtensions,\r\n    xml(),\r\n    EditorView.updateListener.of(update => {\r\n      if (update.docChanged) {\r\n        const value = update.state.doc.toString()\r\n        emit('update:value', value)\r\n        emit('change', value)\r\n      }\r\n    }),\r\n    // 当 readOnly 为 true 时，允许选择但不允许编辑\r\n    EditorView.contentAttributes.of({\r\n      // 只在只读模式下允许选择\r\n      contenteditable: props.readOnly ? \"false\" : \"true\",\r\n      // 但始终允许用户选择文本\r\n      class: props.readOnly ? \"readonly-but-selectable\" : \"\"\r\n    }),\r\n    // 确保 EditorView.editable 配置也被设置\r\n    EditorView.editable.of(!props.readOnly)\r\n  ]\r\n\r\n  // 创建编辑器状态\r\n  const state = EditorState.create({\r\n    doc: props.value,\r\n    extensions\r\n  })\r\n\r\n  // 创建编辑器视图\r\n  view = new EditorView({\r\n    state,\r\n    parent: editorContainer.value\r\n  })\r\n}\r\n\r\n// 当组件挂载时创建编辑器\r\nonMounted(() => {\r\n  createEditor()\r\n})\r\n\r\n// 监听值的变化\r\nwatch(() => props.value, (newValue) => {\r\n  if (view && newValue !== view.state.doc.toString()) {\r\n    const currentScrollPos = view?.scrollDOM.scrollTop || 0\r\n    view.dispatch({\r\n      changes: {\r\n        from: 0,\r\n        to: view.state.doc.length,\r\n        insert: newValue\r\n      }\r\n    })\r\n    // 保持滚动位置\r\n    setTimeout(() => {\r\n      if (view) view.scrollDOM.scrollTop = currentScrollPos\r\n    }, 0)\r\n  }\r\n})\r\n\r\n// 监听只读状态的变化\r\nwatch(() => props.readOnly, (newValue) => {\r\n  if (!view) return\r\n  \r\n  // 创建一个新的视图配置来更新只读状态\r\n  view.dispatch({\r\n    effects: [\r\n      EditorView.editable.reconfigure(!newValue),\r\n      EditorView.contentAttributes.reconfigure({\r\n        contenteditable: newValue ? \"false\" : \"true\",\r\n        class: newValue ? \"readonly-but-selectable\" : \"\"\r\n      })\r\n    ]\r\n  })\r\n})\r\n\r\n// 在组件卸载前销毁编辑器\r\nonBeforeUnmount(() => {\r\n  if (view) {\r\n    view.destroy()\r\n    view = null\r\n  }\r\n})\r\n\nreturn (_ctx: any,_cache: any) => {\n  return (_openBlock(), _createElementBlock(\"div\", {\n    ref_key: \"editorContainer\",\n    ref: editorContainer,\n    class: \"editor-container\"\n  }, null, 512))\n}\n}\n\n})", "<template>\r\n  <div ref=\"editorContainer\" class=\"editor-container\"></div>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { ref, onMounted, onBeforeUnmount, watch, defineProps, defineEmits } from 'vue'\r\nimport { EditorState } from '@codemirror/state'\r\nimport { EditorView } from '@codemirror/view'\r\nimport { defaultKeymap } from '@codemirror/commands'\r\nimport { \r\n  lineNumbers, \r\n  highlightActiveLineGutter, \r\n  highlightSpecialChars,\r\n  drawSelection, \r\n  dropCursor\r\n} from \"@codemirror/view\"\r\n\r\nimport { xml } from '@codemirror/lang-xml'\r\nimport { \r\n  indentOnInput, \r\n  syntaxHighlighting, \r\n  defaultHighlightStyle,\r\n  foldGutter\r\n} from \"@codemirror/language\"\r\nimport { history, historyKeymap } from \"@codemirror/commands\"\r\nimport { foldKeymap } from \"@codemirror/language\"\r\nimport { keymap } from \"@codemirror/view\"\r\n\r\nconst props = defineProps<{\r\n  value: string\r\n  language?: string\r\n  readOnly?: boolean\r\n}>()\r\n\r\nconst emit = defineEmits(['update:value', 'change'])\r\n\r\nconst editorContainer = ref<HTMLElement | null>(null)\r\nlet view: EditorView | null = null\r\n\r\n// 创建一个基础设置，代替 basicSetup\r\nconst basicSetupExtensions = [\r\n  lineNumbers(),\r\n  highlightActiveLineGutter(),\r\n  highlightSpecialChars(),\r\n  history(),\r\n  drawSelection(),\r\n  dropCursor(),\r\n  EditorState.allowMultipleSelections.of(true),\r\n  indentOnInput(),\r\n  syntaxHighlighting(defaultHighlightStyle, { fallback: true }),\r\n  foldGutter(),\r\n  keymap.of([\r\n    ...defaultKeymap,\r\n    ...historyKeymap,\r\n    ...foldKeymap\r\n  ])\r\n]\r\n\r\n// 创建编辑器实例\r\nconst createEditor = () => {\r\n  if (!editorContainer.value) return\r\n  \r\n  // 使用重新构建的扩展集\r\n  const extensions = [\r\n    ...basicSetupExtensions,\r\n    xml(),\r\n    EditorView.updateListener.of(update => {\r\n      if (update.docChanged) {\r\n        const value = update.state.doc.toString()\r\n        emit('update:value', value)\r\n        emit('change', value)\r\n      }\r\n    }),\r\n    // 当 readOnly 为 true 时，允许选择但不允许编辑\r\n    EditorView.contentAttributes.of({\r\n      // 只在只读模式下允许选择\r\n      contenteditable: props.readOnly ? \"false\" : \"true\",\r\n      // 但始终允许用户选择文本\r\n      class: props.readOnly ? \"readonly-but-selectable\" : \"\"\r\n    }),\r\n    // 确保 EditorView.editable 配置也被设置\r\n    EditorView.editable.of(!props.readOnly)\r\n  ]\r\n\r\n  // 创建编辑器状态\r\n  const state = EditorState.create({\r\n    doc: props.value,\r\n    extensions\r\n  })\r\n\r\n  // 创建编辑器视图\r\n  view = new EditorView({\r\n    state,\r\n    parent: editorContainer.value\r\n  })\r\n}\r\n\r\n// 当组件挂载时创建编辑器\r\nonMounted(() => {\r\n  createEditor()\r\n})\r\n\r\n// 监听值的变化\r\nwatch(() => props.value, (newValue) => {\r\n  if (view && newValue !== view.state.doc.toString()) {\r\n    const currentScrollPos = view?.scrollDOM.scrollTop || 0\r\n    view.dispatch({\r\n      changes: {\r\n        from: 0,\r\n        to: view.state.doc.length,\r\n        insert: newValue\r\n      }\r\n    })\r\n    // 保持滚动位置\r\n    setTimeout(() => {\r\n      if (view) view.scrollDOM.scrollTop = currentScrollPos\r\n    }, 0)\r\n  }\r\n})\r\n\r\n// 监听只读状态的变化\r\nwatch(() => props.readOnly, (newValue) => {\r\n  if (!view) return\r\n  \r\n  // 创建一个新的视图配置来更新只读状态\r\n  view.dispatch({\r\n    effects: [\r\n      EditorView.editable.reconfigure(!newValue),\r\n      EditorView.contentAttributes.reconfigure({\r\n        contenteditable: newValue ? \"false\" : \"true\",\r\n        class: newValue ? \"readonly-but-selectable\" : \"\"\r\n      })\r\n    ]\r\n  })\r\n})\r\n\r\n// 在组件卸载前销毁编辑器\r\nonBeforeUnmount(() => {\r\n  if (view) {\r\n    view.destroy()\r\n    view = null\r\n  }\r\n})\r\n</script>\r\n\r\n<style scoped>\r\n.editor-container {\r\n  width: 100%;\r\n  height: 100%;\r\n  border: 1px solid #dcdfe6;\r\n  border-radius: 4px;\r\n  overflow: hidden;\r\n}\r\n\r\n:deep(.cm-editor) {\r\n  height: 100%;\r\n  font-family: 'Courier New', Courier, monospace;\r\n  font-size: 14px;\r\n}\r\n\r\n:deep(.cm-scroller) {\r\n  overflow: auto;\r\n  height: 100%;\r\n}\r\n\r\n:deep(.cm-content) {\r\n  white-space: pre-wrap;\r\n  word-break: normal;\r\n  word-wrap: break-word;\r\n}\r\n\r\n:deep(.cm-content[contenteditable=false]) {\r\n  background-color: #f5f7fa;\r\n  cursor: text;   /* 确保显示文本选择光标 */\r\n}\r\n\r\n/* 允许在只读模式下选择文本 */\r\n:deep(.readonly-but-selectable) {\r\n  user-select: text !important; /* 确保文本可以被选择 */\r\n  cursor: text !important;     /* 显示文本选择光标 */\r\n}\r\n\r\n:deep(.cm-editor.cm-focused) {\r\n  outline: none;  /* 移除聚焦时的轮廓 */\r\n}\r\n</style>\r\n", "import script from \"./XmlEditor.vue?vue&type=script&setup=true&lang=ts\"\nexport * from \"./XmlEditor.vue?vue&type=script&setup=true&lang=ts\"\n\nimport \"./XmlEditor.vue?vue&type=style&index=0&id=48b1a46a&scoped=true&lang=css\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-48b1a46a\"]])\n\nexport default __exports__", "import { defineComponent as _defineComponent } from 'vue'\nimport { createVNode as _createVNode, createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\"\n\nconst _hoisted_1 = { class: \"xml-viewer\" }\nconst _hoisted_2 = { class: \"editor-container\" }\n\n\r\nimport XmlEditor from '@/components/XmlEditor/XmlEditor.vue'\r\n\r\n\nexport default /*@__PURE__*/_defineComponent({\n  __name: 'XmlViewer',\n  props: {\n    title: {},\n    content: {}\n  },\n  emits: ['update:content'],\n  setup(__props: any, { emit: __emit }) {\n\r\n\r\n\r\nconst emit = __emit;\r\n\r\nconst handleContentChange = (value: string) => {\r\n  emit('update:content', value);\r\n}\r\n\nreturn (_ctx: any,_cache: any) => {\n  return (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [\n    _createElementVNode(\"div\", _hoisted_2, [\n      _createVNode(XmlEditor, {\n        value: _ctx.content,\n        language: \"xml\",\n        \"onUpdate:value\": handleContentChange\n      }, null, 8, [\"value\"])\n    ])\n  ]))\n}\n}\n\n})", "<template>\r\n  <div class=\"xml-viewer\">\r\n    <div class=\"editor-container\">\r\n        <XmlEditor\r\n          :value=\"content\"\r\n          language=\"xml\"\r\n          @update:value=\"handleContentChange\"\r\n        />\r\n      </div>\r\n  </div>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { defineProps, defineEmits } from 'vue'\r\nimport XmlEditor from '@/components/XmlEditor/XmlEditor.vue'\r\n\r\ndefineProps<{\r\n  title: string;\r\n  content: string;\r\n}>()\r\n\r\nconst emit = defineEmits(['update:content']);\r\n\r\nconst handleContentChange = (value: string) => {\r\n  emit('update:content', value);\r\n}\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.xml-viewer {\r\n  flex: 1;\r\n  overflow: hidden;\r\n  height: 100%;\r\n  \r\n  .viewer-card {\r\n    height: 100%;\r\n  }\r\n  \r\n  .viewer-header {\r\n    h3 {\r\n      margin: 0;\r\n    }\r\n  }\r\n  \r\n  .editor-container {\r\n    height: 100%;\r\n  }\r\n}\r\n\r\n:deep(.el-card__body){\r\n  height: 96%;\r\n  padding: 0;\r\n}\r\n</style>\r\n", "import script from \"./XmlViewer.vue?vue&type=script&setup=true&lang=ts\"\nexport * from \"./XmlViewer.vue?vue&type=script&setup=true&lang=ts\"\n\nimport \"./XmlViewer.vue?vue&type=style&index=0&id=1d6f83a0&scoped=true&lang=scss\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-1d6f83a0\"]])\n\nexport default __exports__", "import axios, { AxiosResponse } from 'axios';\r\nimport { USE_MOCK, mockApi } from '@/mock/mockApi';\r\nimport { CaseResult } from './interoperationApi';\r\n\r\n// 定义错误数据结构\r\nexport interface ErrorData {\r\n  message: string;\r\n  stack?: string;\r\n  url: string;\r\n  type?: string;\r\n  vueHookInfo?: string; \r\n  codeInfo?: string;\r\n}\r\n\r\n// 定义应用信息接口\r\nexport interface AppInfo {\r\n  dataFolder: string;\r\n  logFolder: string;\r\n}\r\n\r\n// 覆盖率选项枚举\r\nexport enum CoverageType {\r\n  Normal = 0,\r\n  High = 1\r\n}\r\n\r\n// 生成测试用例请求对象\r\nexport interface GenerateCasesRequest {\r\n  coverage: CoverageType;\r\n  sequenceNames: string[]; // 修改为与后端一致的名称\r\n}\r\n\r\n// 执行状态枚举 - 与后端保持一致\r\nexport enum ExecutionState {\r\n  Pending = 'Pending',\r\n  Running = 'Running',\r\n  Success = 'Success',\r\n  Failure = 'Failure',\r\n  Paused = 'Paused',\r\n}\r\n\r\n// 测试结果接口\r\nexport interface TestResult {\r\n  id: string;\r\n  resultFolderName: string;\r\n  testType: string;\r\n  creationTime: string;\r\n  completionTime?: string;\r\n  totalCount: number;\r\n  successCount: number;\r\n  failureCount: number;\r\n}\r\n\r\n// 测试器快照接口 - 与后端 TesterSnapshot 保持一致\r\nexport interface TesterSnapshot {\r\n  processState: ExecutionState;\r\n  currentOperation: string;\r\n  testResult: TestResult;\r\n  caseResults: CaseResult[]; \r\n}\r\n\r\n// 新增分页查询参数类型\r\nexport interface PagedQuery {\r\n  pageNumber: number;\r\n  pageSize: number;\r\n}\r\n\r\n// 分页结果类型\r\nexport interface PagedResult<T> {\r\n  items: T[];\r\n  total: number;\r\n  pageSize: number;\r\n  pageNumber: number;\r\n}\r\n\r\n// 测试器快照响应\r\nexport interface TesterSnapshotResponse {\r\n  processState: ExecutionState;\r\n  currentOperation: string;\r\n  testResult: TestResult;\r\n  pagedCaseResult: PagedResult<CaseResult>;\r\n}\r\n\r\n// 提供一个工具函数来实现isCompleted方法的逻辑\r\nexport function isTesterCompleted(snapshot: TesterSnapshot): boolean {\r\n  return snapshot.processState === ExecutionState.Success || \r\n         snapshot.processState === ExecutionState.Failure;\r\n}\r\n\r\n// 新增 CaseStep 接口定义\r\nexport interface CaseStep {\r\n  id: number;\r\n  name: string;\r\n  caseResultId: number;\r\n  timestamp: number; // ulong in C#\r\n  frameTimestamp?: number; // ulong? in C#\r\n  state: string; // ExecutionState enum\r\n  begin?: string; // DateTime? in C#\r\n  end?: string; // DateTime? in C#\r\n  detail?: string;\r\n}\r\n\r\nconst BASE_URL = '/api/app'\r\n\r\nexport const appApi = {\r\n  // 获取应用信息\r\n  getAppInfo(): Promise<AxiosResponse<AppInfo>> {\r\n    if (USE_MOCK) {\r\n      return mockApi.app.getAppInfo();\r\n    }\r\n    return axios.get(`${BASE_URL}/appInfo`);\r\n  },\r\n\r\n  // 记录错误日志\r\n  logError: (errorData: ErrorData) => {\r\n    if (USE_MOCK) {\r\n      return mockApi.app.logError(errorData);\r\n    }\r\n    return axios.post(`${BASE_URL}/logError`, errorData);\r\n  },\r\n\r\n  // 退出应用程序\r\n  exit: () => {\r\n    if (USE_MOCK) {\r\n      return mockApi.app.exit();\r\n    }\r\n    return axios.post(`${BASE_URL}/exit`);\r\n  },\r\n\r\n  // 获取最新的互操作测试结果\r\n  getLatestInteroperationCaseResults(): Promise<AxiosResponse<CaseResult[]>> {\r\n    if (USE_MOCK) {\r\n      return mockApi.app.getLatestInteroperationCaseResults();\r\n    }\r\n    return axios.get('/api/case/latest-interoperation-case-results');\r\n  },\r\n  \r\n  // 生成测试用例 - 更新为接受sequenceNames参数\r\n  generateCases(coverage: CoverageType, sequenceNames: string[]): Promise<AxiosResponse<CaseResult[]>> {\r\n    const request: GenerateCasesRequest = { \r\n      coverage, \r\n      sequenceNames \r\n    };\r\n    \r\n    if (USE_MOCK) {\r\n      // mockApi.app.generateCases 接口更新为支持两个参数\r\n      return mockApi.app.generateCases(coverage, sequenceNames);\r\n    }\r\n    \r\n    // 后端 API 使用 SequenceNames (首字母大写)\r\n    return axios.post('/api/case/generate-cases', {\r\n      coverage: request.coverage,\r\n      SequenceNames: request.sequenceNames\r\n    });\r\n  },\r\n  \r\n  // 保存测试用例\r\n  saveCases(coverage: CoverageType, sequenceNames: string[]): Promise<AxiosResponse<CaseResult[]>> {\r\n    const request: GenerateCasesRequest = { \r\n      coverage, \r\n      sequenceNames \r\n    };\r\n    \r\n    if (USE_MOCK) {\r\n      return mockApi.app.saveCases(coverage, sequenceNames);\r\n    }\r\n    \r\n    return axios.post('/api/case/save-cases', {\r\n      coverage: request.coverage,\r\n      SequenceNames: request.sequenceNames\r\n    });\r\n  },\r\n  \r\n  // 获取保存的测试用例\r\n  getSavedCases(): Promise<AxiosResponse<CaseResult[]>> {\r\n    if (USE_MOCK) {\r\n      return mockApi.app.getSavedCases();\r\n    }\r\n    return axios.get('/api/case/cases');\r\n  },\r\n  \r\n  // 开始执行测试\r\n  startTest(): Promise<AxiosResponse<void>> {\r\n    if (USE_MOCK) {\r\n      return mockApi.app.startTest();\r\n    }\r\n    return axios.post('/api/case/start');\r\n  },\r\n  \r\n  // 停止测试\r\n  stopTest(): Promise<AxiosResponse<void>> {\r\n    if (USE_MOCK) {\r\n      return mockApi.app.stopTest();\r\n    }\r\n    return axios.post('/api/case/stop');\r\n  },\r\n  \r\n  // 暂停测试\r\n  pauseTest(): Promise<AxiosResponse<void>> {\r\n    if (USE_MOCK) {\r\n      return mockApi.app.pauseTest();\r\n    }\r\n    return axios.post('/api/case/pause');\r\n  },\r\n\r\n  // 恢复测试\r\n  resumeTest(): Promise<AxiosResponse<void>> {\r\n    if (USE_MOCK) {\r\n      return mockApi.app.resumeTest();\r\n    }\r\n    return axios.post('/api/case/resume');\r\n  },\r\n\r\n  // 获取测试状态\r\n  getTestStatus(): Promise<AxiosResponse<TesterSnapshot>> {\r\n    if (USE_MOCK) {\r\n      return mockApi.app.getTestStatus();\r\n    }\r\n    return axios.get('/api/case/status');\r\n  },\r\n\r\n  // 新增获取分页测试状态的方法\r\n  getTestStatusPaged(pagedQuery: PagedQuery): Promise<AxiosResponse<TesterSnapshotResponse>> {\r\n    if (USE_MOCK) {\r\n      return mockApi.app.getTestStatusPaged(pagedQuery);\r\n    }\r\n    return axios.post('/api/case/status', pagedQuery);\r\n  },\r\n  \r\n  // 获取测试结果列表\r\n  getTestResults(): Promise<AxiosResponse<TestResult[]>> {\r\n    if (USE_MOCK) {\r\n      return mockApi.app.getTestResults();\r\n    }\r\n    return axios.get('/api/case/test-results');\r\n  },\r\n  \r\n  // 获取测试用例列表\r\n  getCases(testResultId: string): Promise<AxiosResponse<CaseResult[]>> {\r\n    if (USE_MOCK) {\r\n      return mockApi.app.getCases(testResultId);\r\n    }\r\n    return axios.get(`/api/case/cases?testResultId=${testResultId}`);\r\n  },\r\n  \r\n  // 获取用例步骤列表\r\n  getCaseSteps(testResultId: string, caseResultId: number): Promise<AxiosResponse<CaseStep[]>> {\r\n    if (USE_MOCK) {\r\n      return mockApi.app.getCaseSteps(testResultId, caseResultId);\r\n    }\r\n    return axios.get(`/api/case/case-steps?testResultId=${testResultId}&caseResultId=${caseResultId}`);\r\n  },\r\n\r\n  // 获取用例步骤列表\r\n  getCaseResult(testResultId: string, caseResultId: number): Promise<AxiosResponse<CaseResult>> {\r\n    if (USE_MOCK) {\r\n      return mockApi.case.getCaseResult(testResultId, caseResultId);\r\n    }\r\n    return axios.get(`/api/case/case-result?testResultId=${testResultId}&caseResultId=${caseResultId}`);\r\n  },\r\n\r\n  // 删除测试结果\r\n  deleteTestResult(testResultId: string): Promise<AxiosResponse<void>> {\r\n    if (USE_MOCK) {\r\n      return mockApi.app.deleteTestResult(testResultId);\r\n    }\r\n    return axios.delete(`/api/case/test-result?testResultId=${testResultId}`);\r\n  },\r\n\r\n  // 下载HTML报告\r\n  downloadHtmlReport(testResultId: string): Promise<void> {\r\n    if (USE_MOCK) {\r\n      return mockApi.app.downloadHtmlReport(testResultId);\r\n    }\r\n    \r\n    // 使用浏览器直接下载功能，而不是处理axios响应\r\n    const url = `/api/case/generate-report?testResultId=${testResultId}`;\r\n    return axios.post(url);\r\n  }\r\n}\r\n\r\nexport default appApi\r\n\r\n// 导出其他API模块\r\nexport { default as sequenceApi } from './sequenceApi';\r\n", "import axios, { AxiosResponse } from 'axios'\r\nimport { USE_MOCK, mockApi } from '@/mock/mockApi'\r\nimport { TesterSnapshot } from './appApi';\r\n\r\n// 基础响应类型\r\nexport interface BaseResponse {\r\n  success: boolean;\r\n  message: string;\r\n}\r\n\r\n// 执行状态枚举 - 与C#后端ExecutionState保持一致\r\nexport enum ExecutionState {\r\n  Pending = 'Pending',\r\n  Running = 'Running',\r\n  Success = 'Success',\r\n  Failure = 'Failure'\r\n}\r\n\r\n// 执行状态字符串类型 - 后端API实际返回的字符串值\r\nexport type ExecutionStateString = 'Pending' | 'Running' | 'Success' | 'Failure';\r\n\r\n// 状态字符串与枚举值的映射函数\r\nexport const mapStateStringToEnum = (stateStr: ExecutionStateString): ExecutionState => {\r\n  switch (stateStr) {\r\n    case 'Pending': return ExecutionState.Pending;\r\n    case 'Running': return ExecutionState.Running;\r\n    case 'Success': return ExecutionState.Success;\r\n    case 'Failure': return ExecutionState.Failure;\r\n    default: return ExecutionState.Pending;\r\n  }\r\n};\r\n\r\n// 用例结果类型\r\nexport interface CaseResult {\r\n  id: number;\r\n  testResultId: string; // Guid in C#\r\n  sequenceId: string; // Guid in C#\r\n  sequenceName: string;\r\n  name: string;\r\n  parameter: string;\r\n  state: ExecutionStateString; // 使用字符串类型，与后端API一致\r\n  begin: string | null;\r\n  end: string | null;\r\n  detail: string;\r\n}\r\n\r\n// 互操作测试状态类型\r\nexport interface InteroperationStatus {\r\n  isRunning: boolean;\r\n  progress: number;\r\n  currentOperation: string;\r\n  beginTime: string | null;\r\n  endTime: string | null;\r\n  summary: {\r\n    passed: number;\r\n    failed: number;\r\n    skipped: number;\r\n  };\r\n  caseResults: CaseResult[] | null;\r\n}\r\n\r\nexport function isTesterCompleted(tester: TesterSnapshot): boolean {\r\n  return tester.processState === ExecutionState.Success || \r\n         tester.processState === ExecutionState.Failure;\r\n}\r\n\r\nconst BASE_URL = '/api/interoperation'\r\n\r\nexport const interoperationApi = {\r\n  // 启动互操作测试\r\n  startTest: (): Promise<AxiosResponse<BaseResponse>> => {\r\n    if (USE_MOCK) {\r\n      return mockApi.interoperation.startTest();\r\n    }\r\n    return axios.post(`${BASE_URL}/start`);\r\n  },\r\n\r\n  // 停止互操作测试\r\n  stopTest: (): Promise<AxiosResponse<BaseResponse>> => {\r\n    if (USE_MOCK) {\r\n      return mockApi.interoperation.stopTest();\r\n    }\r\n    return axios.post(`${BASE_URL}/stop`);\r\n  },\r\n\r\n  // 获取测试状态\r\n  getStatus: (): Promise<AxiosResponse<TesterSnapshot>> => {\r\n    if (USE_MOCK) {\r\n      return mockApi.interoperation.getStatus();\r\n    }\r\n    return axios.get(`${BASE_URL}/status`);\r\n  }\r\n}\r\n\r\nexport default interoperationApi;\r\n", "import axios, { AxiosResponse } from 'axios'\r\nimport { USE_MOCK, mockApi } from '@/mock/mockApi'\r\n\r\nexport interface SequencePackage {\r\n  name: string;\r\n  sequences: any[];\r\n}\r\n\r\nexport interface TestSuite {\r\n  name: string;\r\n  version: string;\r\n  packages: SequencePackage[];\r\n}\r\n\r\nconst BASE_URL = '/api/testsuite'\r\n\r\nexport const testSuiteApi = {\r\n  getBuiltIn: (): Promise<AxiosResponse<TestSuite[]>> => {\r\n    if (USE_MOCK) {\r\n      return mockApi.testSuite.getBuiltIn();\r\n    }\r\n    return axios.get(`${BASE_URL}/builtin`);\r\n  },\r\n\r\n  getBuiltInXml: (suiteName: string, packageName: string): Promise<AxiosResponse<string>> => {\r\n    if (USE_MOCK) {\r\n      return mockApi.testSuite.getXml(suiteName, packageName);\r\n    }\r\n    return axios.get(`${BASE_URL}/builtin-xml`, { params: { suiteName, packageName } });\r\n  },\r\n\r\n  getXml: (suiteName: string, packageName: string): Promise<AxiosResponse<string>> => {\r\n    if (USE_MOCK) {\r\n      return mockApi.testSuite.getXml(suiteName, packageName);\r\n    }\r\n    return axios.get(`${BASE_URL}/xml`, { params: { suiteName, packageName } });\r\n  }\r\n}\r\n\r\nexport default testSuiteApi\r\n", "import { reactive, readonly } from 'vue';\r\nimport { ElMessage } from 'element-plus';\r\nimport { Router } from 'vue-router';\r\nimport { testPlanApi, type TestPlan } from '@/api/testPlanApi';\r\nimport { testPlanHistoryApi, type TestPlanHistory } from '@/api/testPlanHistoryApi';\r\n\r\n// 定义状态接口\r\ninterface TestPlanState {\r\n  currentPlan: TestPlan | null;\r\n  recentPlans: TestPlanHistory[];\r\n  isLoading: boolean;\r\n}\r\n\r\n// 创建内部状态\r\nconst state = reactive<TestPlanState>({\r\n  currentPlan: null,\r\n  recentPlans: [],\r\n  isLoading: false\r\n});\r\n\r\n// 测试计划服务类\r\nclass TestPlanServiceClass {\r\n  private router: Router | null = null;\r\n\r\n  // 初始化路由\r\n  setRouter(router: Router) {\r\n    this.router = router;\r\n  }\r\n\r\n  // 获取只读状态\r\n  getState() {\r\n    return readonly(state);\r\n  }\r\n\r\n  // 打开文件浏览器选择测试计划\r\n  async openFromExplorer() {\r\n    state.isLoading = true;\r\n    try {\r\n      const response = await testPlanApi.openInExplorer();\r\n      if (response.data) {\r\n        state.currentPlan = response.data;\r\n        ElMessage.success(\"Test plan opened successfully\");\r\n        this.navigateToTestPlan();\r\n        await this.loadRecentPlans();\r\n        return response.data;\r\n      }\r\n    } finally {\r\n      state.isLoading = false;\r\n    }\r\n    return null;\r\n  }\r\n\r\n  // 从历史记录中打开测试计划\r\n  async openFromPath(path: string) {\r\n    state.isLoading = true;\r\n    try {\r\n      const response = await testPlanApi.open(path);\r\n      if (response.data) {\r\n        state.currentPlan = response.data;\r\n        ElMessage.success(\"Test plan opened successfully\");\r\n        this.navigateToTestPlan();\r\n        await this.loadRecentPlans();\r\n        return response.data;\r\n      }\r\n    } finally {\r\n      state.isLoading = false;\r\n    }\r\n    return null;\r\n  }\r\n\r\n  // 创建新的测试计划\r\n  async createTestPlan(testPlanData: { name: string; description: string; folder: string }) {\r\n    state.isLoading = true;\r\n    try {\r\n      const response = await testPlanApi.create(testPlanData);\r\n      if (response.data) {\r\n        state.currentPlan = response.data;\r\n        ElMessage.success(\"Test plan created successfully\");\r\n        this.navigateToTestPlan();\r\n        await this.loadRecentPlans();\r\n        return response.data;\r\n      }\r\n    } catch (error: any) {\r\n      ElMessage.error(\"Failed to create test plan\");\r\n      console.error(error);\r\n    } finally {\r\n      state.isLoading = false;\r\n    }\r\n    return null;\r\n  }\r\n\r\n  // 关闭测试计划\r\n  async closeTestPlan() {\r\n    try {\r\n      await testPlanApi.close();\r\n      state.currentPlan = null;\r\n      ElMessage.success(\"Test plan closed successfully\");\r\n      if (this.router) {\r\n        this.router.push('/');\r\n      }\r\n    } catch (error: any) {\r\n      ElMessage.error(\"Failed to close test plan\");\r\n      console.error(error);\r\n    }\r\n  }\r\n\r\n  // 加载最近的测试计划\r\n  async loadRecentPlans() {\r\n    try {\r\n      const response = await testPlanHistoryApi.get();\r\n      state.recentPlans = response.data;\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Failed to load recent plans:', error);\r\n      return [];\r\n    }\r\n  }\r\n\r\n  // 清空历史记录\r\n  async clearHistory() {\r\n    try {\r\n      await testPlanHistoryApi.clear();\r\n      state.recentPlans = [];\r\n      ElMessage.success(\"History cleared\");\r\n      return true;\r\n    } catch (error) {\r\n      ElMessage.error(\"Failed to clear history\");\r\n      console.error(error);\r\n      return false;\r\n    }\r\n  }\r\n\r\n  // 更新测试计划基本信息\r\n  async updateBasicInfo(description: string) {\r\n    state.isLoading = true;\r\n    try {\r\n      const response = await testPlanApi.updateBasicInfo(description);\r\n      if (response.data) {\r\n        state.currentPlan = response.data;\r\n        ElMessage.success(\"Description updated successfully\");\r\n        return response.data;\r\n      }\r\n    } catch (error) {\r\n      ElMessage.error(\"Failed to update description\");\r\n      console.error('Error updating description:', error);\r\n    } finally {\r\n      state.isLoading = false;\r\n    }\r\n    return null;\r\n  }\r\n\r\n  // 获取当前测试计划\r\n  async getCurrentPlan() {\r\n    if (state.currentPlan) {\r\n      return state.currentPlan;\r\n    }\r\n\r\n    state.isLoading = true;\r\n    try {\r\n      const response = await testPlanApi.getCurrentPlan();\r\n      state.currentPlan = response.data;\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Error loading current plan:', error);\r\n      return null;\r\n    } finally {\r\n      state.isLoading = false;\r\n    }\r\n  }\r\n\r\n  // 导航到测试计划页面\r\n  private navigateToTestPlan() {\r\n    if (this.router) {\r\n      this.router.push({ name: 'test-plan.basic-setting' });\r\n    }\r\n  }\r\n}\r\n\r\n// 创建服务实例\r\nexport const testPlanService = new TestPlanServiceClass();\r\n", "// 导入类型定义\r\nimport { TestPlan } from '@/api/testPlanApi';\r\nimport { TestPlanHistory } from '@/api/testPlanHistoryApi';\r\nimport { ErrorData } from '@/api/appApi';\r\n\r\n// 生成随机ID\r\nconst generateId = () => Math.random().toString(36).substring(2, 15);\r\n\r\n// 生成当前日期时间的ISO字符串\r\nconst getCurrentISOTime = () => new Date().toISOString();\r\n\r\n// 生成过去随机时间\r\nconst getRandomPastTime = () => {\r\n  const now = new Date();\r\n  const pastTime = new Date(now.getTime() - Math.floor(Math.random() * 30) * 24 * 60 * 60 * 1000);\r\n  return pastTime.toISOString();\r\n};\r\n\r\n// 预设测试计划数据\r\nexport const testPlans: TestPlan[] = [\r\n  {\r\n    path: \"D:\\\\TestPlans\\\\CAN总线测试计划.fzp\",\r\n    manifest: {\r\n      name: \"CAN总线测试计划\",\r\n      description: \"针对汽车CAN总线通信的模糊测试\",\r\n      created: getRandomPastTime(),\r\n      modified: getCurrentISOTime()\r\n    },\r\n    config: {\r\n      targetDevice: \"ECM\",\r\n      protocol: \"CAN\",\r\n      testCases: [\"TC001\", \"TC002\", \"TC003\"]\r\n    }\r\n  },\r\n  {\r\n    path: \"D:\\\\TestPlans\\\\UDS协议测试.fzp\",\r\n    manifest: {\r\n      name: \"UDS协议测试\",\r\n      description: \"针对UDS协议的诊断服务测试\",\r\n      created: getRandomPastTime(),\r\n      modified: getRandomPastTime()\r\n    },\r\n    config: {\r\n      targetDevice: \"BCM\",\r\n      protocol: \"UDS\",\r\n      testCases: [\"UDS001\", \"UDS002\"]\r\n    }\r\n  },\r\n  {\r\n    path: \"D:\\\\TestPlans\\\\CANFD高速协议测试.fzp\",\r\n    manifest: {\r\n      name: \"CANFD高速协议测试\",\r\n      description: \"针对CANFD高速数据传输协议的稳定性测试\",\r\n      created: getRandomPastTime(),\r\n      modified: getRandomPastTime()\r\n    },\r\n    config: {\r\n      targetDevice: \"Gateway\",\r\n      protocol: \"CANFD\",\r\n      testCases: [\"FD001\", \"FD002\", \"FD003\", \"FD004\"]\r\n    }\r\n  }\r\n];\r\n\r\n// 测试计划历史记录数据\r\nexport const testPlanHistoryItems: TestPlanHistory[] = [\r\n  {\r\n    id: generateId(),\r\n    filePath: \"D:\\\\TestPlans\\\\can_test.fzp\",\r\n    planName: \"CAN总线测试计划\",\r\n    lastAccessTime: getRandomPastTime(),\r\n    lastModified: getRandomPastTime(),\r\n    isDeleted: false\r\n  },\r\n  {\r\n    id: generateId(),\r\n    filePath: \"D:\\\\TestPlans\\\\uds_protocol.fzp\",\r\n    planName: \"UDS协议测试\",\r\n    lastAccessTime: getRandomPastTime(),\r\n    lastModified: getRandomPastTime(),\r\n    isDeleted: false\r\n  },\r\n  {\r\n    id: generateId(),\r\n    filePath: \"D:\\\\TestPlans\\\\canfd_test.fzp\",\r\n    planName: \"CANFD高速协议测试\",\r\n    lastAccessTime: getCurrentISOTime(),\r\n    lastModified: getRandomPastTime(),\r\n    isDeleted: false\r\n  }\r\n];\r\n\r\n// 用于模拟内存中的错误日志存储\r\nexport const errorLogs: ErrorData[] = [];\r\n", "import { TestPlan } from '@/api/testPlanApi';\r\nimport { mockSuccess, mockError, generateId } from '../mockApi';\r\nimport { testPlans, testPlanHistoryItems } from '../mockData';\r\nimport { AxiosResponse } from 'axios';\r\n\r\n// 添加当前测试计划的状态管理\r\nlet currentTestPlan: TestPlan | null = null;\r\n\r\n// 测试计划 API 模拟实现\r\nexport const mockTestPlanApi = {\r\n  // 在文件浏览器中打开测试计划\r\n  openInExplorer: (): Promise<AxiosResponse<TestPlan>> => {\r\n    // 随机选择一个测试计划\r\n    const randomIndex = Math.floor(Math.random() * testPlans.length);\r\n    const selectedPlan = testPlans[randomIndex];\r\n    currentTestPlan = selectedPlan; // 设置当前测试计划\r\n\r\n    // 记录历史记录\r\n    updateTestPlanHistory(selectedPlan.path, selectedPlan.name);\r\n    return mockSuccess(selectedPlan);\r\n  },\r\n\r\n  // 从指定路径打开测试计划\r\n  open: (path: string): Promise<AxiosResponse<TestPlan>> => {\r\n    // 检查路径是否存在于历史记录中\r\n    const historyItem = testPlanHistoryItems.find(item => item.filePath === path);\r\n    let plan: TestPlan;\r\n\r\n    // 如果没找到匹配的历史记录，返回第一个测试计划\r\n    if (!historyItem) {\r\n      plan = testPlans[0];\r\n      // 添加新的历史记录\r\n      updateTestPlanHistory(path, plan.name);\r\n    } else {\r\n      // 查找匹配的测试计划\r\n      const matchingPlan = testPlans.find(p => p.manifest.name === historyItem.planName);\r\n      plan = matchingPlan || testPlans[0];\r\n      // 更新访问时间\r\n      updateTestPlanHistory(path, matchingPlan?.manifest.name || \"\");\r\n    }\r\n\r\n    currentTestPlan = plan; // 设置当前测试计划\r\n    return mockSuccess(plan);\r\n  },\r\n\r\n  // 创建测试计划\r\n  create: (data: any): Promise<AxiosResponse<TestPlan>> => {\r\n    // 创建新的测试计划\r\n    const now = new Date().toISOString();\r\n\r\n    // 创建文件路径\r\n    const filePath = `D:\\\\TestPlans\\\\${data.name.replace(/\\s+/g, '_').toLowerCase()}.fzp`;\r\n\r\n    const newPlan: TestPlan = {\r\n      path: filePath,\r\n      manifest: {\r\n        name: data.name,\r\n        description: data.description,\r\n        created: now,\r\n        modified: now\r\n      },\r\n      config: {\r\n        targetDevice: \"Default\",\r\n        protocol: \"CAN\",\r\n        testCases: []\r\n      }\r\n    };\r\n\r\n    // 保存到内存中\r\n    testPlans.push(newPlan);\r\n\r\n    // 添加到历史记录\r\n    updateTestPlanHistory(filePath, data.name);\r\n\r\n    currentTestPlan = newPlan;\r\n    // 返回创建结果\r\n    return mockSuccess(newPlan);\r\n  },\r\n\r\n  // 关闭测试计划\r\n  close: (): Promise<AxiosResponse<void>> => {\r\n    currentTestPlan = null; // 清除当前测试计划\r\n    console.log(\"模拟关闭测试计划\");\r\n    return mockSuccess(undefined);\r\n  },\r\n\r\n  // 获取当前测试计划\r\n  getCurrentPlan: (): Promise<AxiosResponse<TestPlan>> => {\r\n    if (!currentTestPlan) {\r\n      return mockError(404, \"No test plan is currently open\");\r\n    }\r\n    return mockSuccess(currentTestPlan);\r\n  },\r\n\r\n  // 更新测试计划基本信息\r\n  updateBasicInfo: (description: string): Promise<AxiosResponse<TestPlan>> => {\r\n    if (!currentTestPlan) {\r\n      return mockError(404, \"No test plan is currently open\");\r\n    }\r\n\r\n    // 更新描述和修改时间\r\n    currentTestPlan.manifest.description = description;\r\n    currentTestPlan.manifest.modified = new Date().toISOString();\r\n\r\n    // 如果有对应的历史记录，也更新历史记录的修改时间\r\n    const historyItem = testPlanHistoryItems.find(item => item.filePath === currentTestPlan?.path);\r\n    if (historyItem) {\r\n      historyItem.lastModified = new Date().toISOString();\r\n    }\r\n\r\n    return mockSuccess(currentTestPlan);\r\n  },\r\n\r\n  // 检查文件是否存在\r\n  checkFileExists: (path: string): Promise<AxiosResponse<{exists: boolean}>> => {\r\n    // 模拟实现：随机返回文件存在或不存在\r\n    // 在实际应用中，可以根据需要调整逻辑\r\n    // 这里我们假设90%的文件存在，10%的文件不存在\r\n    const exists = Math.random() > 0.1;\r\n\r\n    // 也可以根据特定路径模拟不同的结果\r\n    // 例如：特定路径总是返回不存在\r\n    // if (path.includes('nonexistent')) {\r\n    //   exists = false;\r\n    // }\r\n\r\n    return mockSuccess({ exists });\r\n  }\r\n};\r\n\r\n// 辅助函数：更新测试计划历史记录\r\nfunction updateTestPlanHistory(filePath: string, planName: string): void {\r\n  const now = new Date().toISOString();\r\n  const existingItem = testPlanHistoryItems.find(item => item.filePath === filePath);\r\n\r\n  if (existingItem) {\r\n    // 更新现有记录\r\n    existingItem.lastAccessTime = now;\r\n    existingItem.lastModified = now;\r\n    existingItem.isDeleted = false;\r\n  } else {\r\n    // 添加新记录\r\n    testPlanHistoryItems.push({\r\n      id: generateId(),\r\n      filePath,\r\n      planName,\r\n      lastAccessTime: now,\r\n      lastModified: now,\r\n      isDeleted: false\r\n    });\r\n  }\r\n\r\n  // 重新排序历史记录，按最后访问时间降序排序\r\n  testPlanHistoryItems.sort((a, b) => {\r\n    if (a.isDeleted && !b.isDeleted) return 1;  // 已删除的放后面\r\n    if (!a.isDeleted && b.isDeleted) return -1; // 未删除的放前面\r\n    // 都是未删除或都是已删除的情况下，按最后访问时间降序排序\r\n    return new Date(b.lastAccessTime).getTime() - new Date(a.lastAccessTime).getTime();\r\n  });\r\n}\r\n", "import { TestPlanHistory } from '@/api/testPlanHistoryApi';\r\nimport { mockSuccess } from '../mockApi';\r\nimport { testPlanHistoryItems } from '../mockData';\r\nimport { AxiosResponse } from 'axios';\r\n\r\n// 测试计划历史 API 模拟实现\r\nexport const mockTestPlanHistoryApi = {\r\n  // 获取历史记录\r\n  get: (): Promise<AxiosResponse<TestPlanHistory[]>> => {\r\n    // 过滤掉已删除的记录，并按最后访问时间降序排序\r\n    const sortedHistory = testPlanHistoryItems\r\n      .filter(item => !item.isDeleted)\r\n      .sort((a, b) => {\r\n        // 按最后访问时间降序排序（新的在前）\r\n        return new Date(b.lastAccessTime).getTime() - new Date(a.lastAccessTime).getTime();\r\n      });\r\n\r\n    return mockSuccess(sortedHistory);\r\n  },\r\n\r\n  // 清空历史记录\r\n  clear: (): Promise<AxiosResponse<void>> => {\r\n    // 标记所有记录为已删除\r\n    testPlanHistoryItems.forEach(item => {\r\n      item.isDeleted = true;\r\n    });\r\n\r\n    return mockSuccess(undefined);\r\n  },\r\n\r\n  // 删除单个历史记录\r\n  deleteRecord: (filePath: string): Promise<AxiosResponse<void>> => {\r\n    // 查找匹配的记录\r\n    const record = testPlanHistoryItems.find(item => item.filePath === filePath && !item.isDeleted);\r\n\r\n    // 如果找到记录，标记为已删除\r\n    if (record) {\r\n      record.isDeleted = true;\r\n    }\r\n\r\n    return mockSuccess(undefined);\r\n  }\r\n};\r\n", "import { \r\n  ErrorData, \r\n  AppInfo, \r\n  TesterSnapshot, \r\n  TestResult, \r\n  ExecutionState, \r\n  CaseStep, \r\n  isTesterCompleted,\r\n  PagedQuery,          // 添加这两个导入\r\n  TesterSnapshotResponse,\r\n  PagedResult\r\n} from '@/api/appApi';\r\nimport { mockSuccess } from '../mockApi';\r\nimport { errorLogs } from '../mockData';\r\nimport { AxiosResponse } from 'axios';\r\nimport { CaseResult, ExecutionStateString } from '@/api/interoperationApi';\r\nimport { CoverageType, GenerateCasesRequest } from '@/api/appApi';\r\n\r\n// 生成模拟的互操作测试结果\r\nfunction generateMockInteroperationResults(): CaseResult[] {\r\n  const results: CaseResult[] = [];\r\n\r\n  // 预定义的序列名称数组 - 确保唯一性\r\n  const uniqueSequences = [\r\n    'DiagnosticSessionControl',\r\n    'ECUReset',\r\n    'SecurityAccess',\r\n    'CommunicationControl',\r\n    'ReadDataByIdentifier',\r\n    'WriteDataByIdentifier',\r\n    'ClearDiagnosticInformation',\r\n    'ReadDTCInformation',\r\n    'InputOutputControlByIdentifier',\r\n    'RoutineControl',\r\n    'RequestDownload',\r\n    'RequestUpload',\r\n    'TransferData',\r\n    'RequestTransferExit',\r\n    'AccessTimingParameter',\r\n    'SecuredDataTransmission',\r\n    'ControlDTCSetting',\r\n    'ResponseOnEvent',\r\n    'LinkControl',\r\n    'can-frames'\r\n  ];\r\n\r\n  // 生成随机的执行状态分布\r\n  const successCount = Math.floor(Math.random() * 10) + 5; // 5-15个成功结果\r\n\r\n  // 随机选择一些序列作为测试结果\r\n  const selectedSequences = [...uniqueSequences]\r\n    .sort(() => Math.random() - 0.5) // 随机打乱顺序\r\n    .slice(0, successCount + 5); // 多取5个以便有一些非成功状态的结果\r\n\r\n  // 为每个选定的序列创建一个结果\r\n  selectedSequences.forEach((sequenceName, index) => {\r\n    // 决定状态 - 前successCount个为Success，其余随机\r\n    let state: ExecutionStateString;\r\n    if (index < successCount) {\r\n      state = 'Success';\r\n    } else {\r\n      // 随机选择非Success状态\r\n      const otherStates: ExecutionStateString[] = ['Pending', 'Running', 'Failure'];\r\n      state = otherStates[Math.floor(Math.random() * otherStates.length)];\r\n    }\r\n\r\n    // 创建结果对象\r\n    const begin = new Date(Date.now() - Math.random() * 60000);\r\n    const end = new Date(begin.getTime() + Math.random() * 30000);\r\n\r\n    results.push({\r\n      id: index + 1,\r\n      testResultId: `test-${Math.random().toString(36).substring(2)}`,\r\n      sequenceId: `seq-${Math.random().toString(36).substring(2)}`,\r\n      sequenceName: sequenceName, // 使用唯一的序列名称\r\n      parameter: `param-${index}`,\r\n      name: `name-${index}`,\r\n      state: state,\r\n      begin: begin.toISOString(),\r\n      end: end.toISOString(),\r\n      detail: state === 'Success' ? 'Test passed' : `Test ${state.toLowerCase()}`\r\n    });\r\n  });\r\n\r\n  return results;\r\n}\r\n\r\n// 生成模拟的测试用例\r\nfunction generateMockTestCases(coverage: CoverageType, sequenceNames: string[]): CaseResult[] {\r\n  const results: CaseResult[] = [];\r\n  // 提高用例数量 - High覆盖模式生成100000个用例，Normal模式生成5000个\r\n  const caseCount = (coverage === CoverageType.High ? 100000 : 5000) / 5;\r\n\r\n  // 定义一些可能的前缀组\r\n  const prefixGroups = ['G100', 'G200', 'G300', 'G400', 'G500'];\r\n\r\n  // 仅为选定的序列生成测试用例\r\n  for (const sequenceName of sequenceNames) {\r\n    // 为每个选定的序列生成测试用例\r\n    for (let i = 0; i < caseCount; i++) {\r\n      // 随机选择一个前缀组\r\n      const prefixIndex = Math.floor(Math.random() * prefixGroups.length);\r\n      const prefix = prefixGroups[prefixIndex];\r\n\r\n      results.push({\r\n        id: results.length + 1,\r\n        testResultId: `test-${Math.random().toString(36).substring(2)}`,\r\n        sequenceId: `seq-${Math.random().toString(36).substring(2)}`,\r\n        sequenceName: sequenceName,\r\n        parameter: `id=0x${(Math.floor(Math.random() * 4095) + 1).toString(16).padStart(3, '0')};dlc=8;data=hex:${Array(16).fill(0).map(() => Math.floor(Math.random() * 255).toString(16).padStart(2, '0')).join('')}`,\r\n        state: 'Pending',\r\n        begin: null,\r\n        end: null,\r\n        detail: '',\r\n        name: `${prefix}-TestCase${i + 1}`\r\n      });\r\n    }\r\n  }\r\n\r\n  return results;\r\n}\r\n\r\n// 生成模拟的测试结果列表\r\nfunction generateMockTestResults(): TestResult[] {\r\n  const results: TestResult[] = [];\r\n\r\n  // 生成5个模拟测试结果\r\n  for (let i = 0; i < 5; i++) {\r\n    const creationTime = new Date(Date.now() - i * 86400000); // 每个相差一天\r\n    const totalCount = 10 + Math.floor(Math.random() * 20);\r\n    const successCount = Math.floor(totalCount * (0.7 + Math.random() * 0.2)); // 70-90% 成功率\r\n    const failureCount = totalCount - successCount;\r\n\r\n    results.push({\r\n      id: `test-result-${i}-${Date.now()}`,\r\n      resultFolderName: `测试运行 ${i + 1}`,\r\n      testType: 'Case',\r\n      creationTime: creationTime.toISOString(),\r\n      completionTime: new Date(creationTime.getTime() + 3600000).toISOString(), // 一小时后完成\r\n      totalCount,\r\n      successCount,\r\n      failureCount\r\n    });\r\n  }\r\n\r\n  return results;\r\n}\r\n\r\n// 生成模拟的用例步骤\r\nfunction generateMockCaseSteps(caseResultId: number): CaseStep[] {\r\n  const steps: CaseStep[] = [];\r\n  const stepCount = 5 + Math.floor(Math.random() * 10); // 5-15个步骤\r\n\r\n  for (let i = 0; i < stepCount; i++) {\r\n    const timestamp = Date.now() - (stepCount - i) * 1000; // 每步相差1秒\r\n    const stateOptions = ['Success', 'Failure'];\r\n    const state = Math.random() > 0.2 ? stateOptions[0] : stateOptions[1]; // 80%成功率\r\n\r\n    steps.push({\r\n      id: i + 1,\r\n      name: `Step ${i + 1}`,\r\n      caseResultId: caseResultId,\r\n      timestamp: timestamp,\r\n      frameTimestamp: timestamp,\r\n      state: state,\r\n      begin: new Date(timestamp - 500).toISOString(),\r\n      end: new Date(timestamp).toISOString(),\r\n      detail: state === 'Success' ? 'Step executed successfully' : 'Step failed with error'\r\n    });\r\n  }\r\n\r\n  return steps;\r\n}\r\n\r\n// 模拟测试状态\r\nlet mockProcessState: ExecutionState = ExecutionState.Pending;\r\nlet mockCurrentOperation = '';\r\nlet mockTestResult: TestResult = {\r\n  id: `test-${Date.now()}`,\r\n  resultFolderName: 'Mock Test Run',\r\n  testType: 'Case',\r\n  creationTime: new Date().toISOString(),\r\n  totalCount: 0,\r\n  successCount: 0,\r\n  failureCount: 0\r\n};\r\nlet mockCaseResults: CaseResult[] = [];\r\n\r\n// 增加全局变量记录当前正在执行的定时器\r\nlet progressInterval: number | undefined = undefined;\r\n\r\n// 启动进度更新的函数\r\nfunction startProgressUpdate() {\r\n  // 先清除可能存在的定时器\r\n  if (progressInterval !== undefined) {\r\n    clearInterval(progressInterval);\r\n    progressInterval = undefined;\r\n  }\r\n\r\n  // 创建新的定时器\r\n  progressInterval = window.setInterval(() => {\r\n    if (mockProcessState === ExecutionState.Running && mockTestResult.successCount + mockTestResult.failureCount < mockTestResult.totalCount) {\r\n      const currentIndex = mockTestResult.successCount + mockTestResult.failureCount;\r\n\r\n      // 将当前用例标记为已完成\r\n      const isSuccess = Math.random() > 0.2;\r\n      mockCaseResults[currentIndex].state = isSuccess ? 'Success' : 'Failure';\r\n      mockCaseResults[currentIndex].begin = new Date(Date.now() - 3000).toISOString();\r\n      mockCaseResults[currentIndex].end = new Date().toISOString();\r\n\r\n      // 更新测试结果统计\r\n      if (isSuccess) {\r\n        mockTestResult.successCount++;\r\n      } else {\r\n        mockTestResult.failureCount++;\r\n      }\r\n\r\n      // 更新当前正在执行的用例\r\n      const nextIndex = currentIndex + 1;\r\n      if (nextIndex < mockTestResult.totalCount) {\r\n        mockCaseResults[nextIndex].state = 'Running';\r\n        mockCurrentOperation = `Running test case: ${mockCaseResults[nextIndex].sequenceName}`;\r\n      } else {\r\n        mockProcessState = ExecutionState.Success;\r\n        mockCurrentOperation = 'Test execution completed';\r\n        mockTestResult.completionTime = new Date().toISOString();\r\n        if (progressInterval !== undefined) {\r\n          clearInterval(progressInterval);\r\n          progressInterval = undefined;\r\n        }\r\n      }\r\n    } else if (mockProcessState !== ExecutionState.Running) {\r\n      // 如果不是运行状态，暂停执行但不清除定时器\r\n      // 这里不做任何操作，定时器继续存在但不产生效果\r\n    } else {\r\n      if (progressInterval !== undefined) {\r\n        clearInterval(progressInterval);\r\n        progressInterval = undefined;\r\n      }\r\n    }\r\n  }, 500);\r\n}\r\n\r\n// 保存模拟状态\r\nexport const mockAppApi = {\r\n  // 获取应用信息\r\n  getAppInfo(): Promise<AxiosResponse<AppInfo>> {\r\n    return mockSuccess({\r\n      dataFolder: 'D:\\\\mock\\\\data\\\\folder',\r\n      logFolder: 'D:\\\\mock\\\\logs\\\\folder'\r\n    });\r\n  },\r\n\r\n  // 记录错误\r\n  logError: (data: ErrorData): Promise<AxiosResponse<void>> => {\r\n    // 将错误添加到内存中\r\n    errorLogs.push(data);\r\n    return mockSuccess(undefined);\r\n  },\r\n\r\n  // 退出应用程序\r\n  exit: (): Promise<AxiosResponse<void>> => {\r\n    console.log(\"模拟应用程序退出\");\r\n    // 在模拟环境中，我们不能真正退出应用，只打印日志\r\n    return mockSuccess(undefined);\r\n  },\r\n\r\n  // 获取最新互操作测试结果 - 修改确保序列名称唯一\r\n  getLatestInteroperationCaseResults(): Promise<AxiosResponse<CaseResult[]>> {\r\n    return mockSuccess(generateMockInteroperationResults());\r\n  },\r\n\r\n  // 生成测试用例 - 更新为支持序列筛选\r\n  generateCases(coverageType: CoverageType | GenerateCasesRequest, sequenceNames?: string[]): Promise<AxiosResponse<CaseResult[]>> {\r\n    // 处理两种不同的参数传递方式\r\n    let actualCoverageType: CoverageType;\r\n    let actualSequenceNames: string[];\r\n\r\n    if (typeof coverageType === 'object') {\r\n      // 如果传入的是GenerateCasesRequest对象\r\n      actualCoverageType = coverageType.coverage;\r\n      actualSequenceNames = coverageType.sequenceNames || [];\r\n    } else {\r\n      // 如果分别传入了两个参数\r\n      actualCoverageType = coverageType;\r\n      actualSequenceNames = sequenceNames || [];\r\n    }\r\n\r\n    return mockSuccess(generateMockTestCases(actualCoverageType, actualSequenceNames));\r\n  },\r\n\r\n  // 保存测试用例 - 与生成用例类似，但会有保存的操作\r\n  saveCases(coverageType: CoverageType | GenerateCasesRequest, sequenceNames?: string[]): Promise<AxiosResponse<CaseResult[]>> {\r\n    // 处理两种不同的参数传递方式\r\n    let actualCoverageType: CoverageType;\r\n    let actualSequenceNames: string[];\r\n\r\n    if (typeof coverageType === 'object') {\r\n      actualCoverageType = coverageType.coverage;\r\n      actualSequenceNames = coverageType.sequenceNames || [];\r\n    } else {\r\n      actualCoverageType = coverageType;\r\n      actualSequenceNames = sequenceNames || [];\r\n    }\r\n\r\n    const cases = generateMockTestCases(actualCoverageType, actualSequenceNames);\r\n    console.log(\"模拟保存测试用例:\", cases.length);\r\n\r\n    // 模拟给每个用例分配一个测试结果ID\r\n    const testResultId = `test-${Date.now()}`;\r\n    cases.forEach(c => c.testResultId = testResultId);\r\n\r\n    return mockSuccess(cases);\r\n  },\r\n\r\n  // 获取保存的测试用例\r\n  getSavedCases(): Promise<AxiosResponse<CaseResult[]>> {\r\n    // 模拟已保存的测试用例，使用相同的测试用例生成函数但固定测试结果ID\r\n    const savedCases = generateMockTestCases(CoverageType.Normal, ['can-frames', 'DiagnosticSessionControl']);\r\n    const testResultId = 'saved-test-result-id';\r\n    savedCases.forEach(c => {\r\n      c.testResultId = testResultId;\r\n      // 随机分配一些不同的状态\r\n      const states: ExecutionStateString[] = ['Pending', 'Running', 'Success', 'Failure'];\r\n      c.state = states[Math.floor(Math.random() * states.length)];\r\n    });\r\n\r\n    return mockSuccess(savedCases);\r\n  },\r\n\r\n  // 开始测试\r\n  startTest(): Promise<AxiosResponse<void>> {\r\n    console.log(\"模拟开始测试\");\r\n    mockProcessState = ExecutionState.Running;\r\n    mockCurrentOperation = 'Starting test execution';\r\n\r\n    // 重置测试进度\r\n    const savedCases = generateMockTestCases(CoverageType.Normal, ['can-frames', 'DiagnosticSessionControl']);\r\n    mockCaseResults = savedCases;\r\n    mockTestResult = {\r\n      id: `test-${Date.now()}`,\r\n      resultFolderName: 'Mock Test Run',\r\n      testType: 'Case',\r\n      creationTime: new Date().toISOString(),\r\n      totalCount: savedCases.length,\r\n      successCount: 0,\r\n      failureCount: 0\r\n    };\r\n\r\n    // 启动模拟测试进度更新\r\n    if (mockCaseResults.length > 0) {\r\n      mockCaseResults[0].state = 'Running';\r\n      mockCurrentOperation = `Running test case: ${mockCaseResults[0].sequenceName}`;\r\n\r\n      // 启动进度更新\r\n      startProgressUpdate();\r\n    }\r\n\r\n    return mockSuccess(undefined);\r\n  },\r\n\r\n  // 停止测试\r\n  stopTest(): Promise<AxiosResponse<void>> {\r\n    console.log(\"模拟停止测试\");\r\n    mockProcessState = ExecutionState.Failure;\r\n    mockCurrentOperation = 'Test execution stopped by user';\r\n    mockTestResult.completionTime = new Date().toISOString();\r\n    return mockSuccess(undefined);\r\n  },\r\n\r\n  // 暂停测试\r\n  pauseTest(): Promise<AxiosResponse<void>> {\r\n    console.log(\"模拟暂停测试\");\r\n    mockProcessState = ExecutionState.Paused;\r\n    mockCurrentOperation = 'Test execution paused by user';\r\n    return mockSuccess(undefined);\r\n  },\r\n\r\n  // 恢复测试\r\n  resumeTest(): Promise<AxiosResponse<void>> {\r\n    console.log(\"模拟恢复测试\");\r\n    mockProcessState = ExecutionState.Running;\r\n    mockCurrentOperation = 'Test execution resumed by user';\r\n\r\n    // 如果没有活动的定时器，重新启动进度更新\r\n    if (progressInterval === undefined) {\r\n      startProgressUpdate();\r\n    }\r\n\r\n    return mockSuccess(undefined);\r\n  },\r\n\r\n  // 获取测试状态\r\n  getTestStatus(): Promise<AxiosResponse<TesterSnapshot>> {\r\n    // 移除方法实现，保持与接口一致\r\n    const snapshot: TesterSnapshot = {\r\n      processState: mockProcessState,\r\n      currentOperation: mockCurrentOperation,\r\n      testResult: mockTestResult,\r\n      caseResults: mockCaseResults\r\n    };\r\n\r\n    return mockSuccess(snapshot);\r\n  },\r\n\r\n  // 新增 getTestStatusPaged 方法\r\n  getTestStatusPaged(pagedQuery: PagedQuery): Promise<AxiosResponse<TesterSnapshotResponse>> {\r\n    const snapshot = {\r\n      processState: mockProcessState,\r\n      currentOperation: mockCurrentOperation,\r\n      testResult: mockTestResult\r\n    };\r\n    \r\n    // 实现分页逻辑\r\n    const totalCases = mockCaseResults.length;\r\n    const startIndex = (pagedQuery.pageNumber - 1) * pagedQuery.pageSize;\r\n    const endIndex = Math.min(startIndex + pagedQuery.pageSize, totalCases);\r\n    const pagedItems = mockCaseResults.slice(startIndex, endIndex);\r\n    \r\n    const response: TesterSnapshotResponse = {\r\n      ...snapshot,\r\n      pagedCaseResult: {\r\n        items: pagedItems,\r\n        total: totalCases,\r\n        pageSize: pagedQuery.pageSize,\r\n        pageNumber: pagedQuery.pageNumber\r\n      }\r\n    };\r\n    \r\n    return mockSuccess(response);\r\n  },\r\n\r\n  // 获取测试结果列表\r\n  getTestResults(): Promise<AxiosResponse<TestResult[]>> {\r\n    return mockSuccess(generateMockTestResults());\r\n  },\r\n\r\n  // 获取测试用例列表\r\n  getCases(testResultId: string): Promise<AxiosResponse<CaseResult[]>> {\r\n    // 针对特定测试结果ID生成用例\r\n    const caseResults = generateMockTestCases(CoverageType.Normal, ['can-frames', 'DiagnosticSessionControl']);\r\n\r\n    // 确保所有用例都关联到这个测试结果ID\r\n    caseResults.forEach(c => {\r\n      c.testResultId = testResultId;\r\n      // 随机分配状态\r\n      const states: ExecutionStateString[] = ['Success', 'Failure'];\r\n      c.state = states[Math.floor(Math.random() * states.length)];\r\n      c.begin = new Date(Date.now() - 3600000).toISOString();\r\n      c.end = new Date(Date.now() - 3500000).toISOString();\r\n    });\r\n\r\n    return mockSuccess(caseResults);\r\n  },\r\n\r\n  // 获取用例步骤列表\r\n  getCaseSteps(testResultId: string, caseResultId: number): Promise<AxiosResponse<CaseStep[]>> {\r\n    return mockSuccess(generateMockCaseSteps(caseResultId));\r\n  },\r\n\r\n  // 删除测试结果\r\n  deleteTestResult(testResultId: string): Promise<AxiosResponse<void>> {\r\n    console.log(`模拟删除测试结果: ${testResultId}`);\r\n    // 在模拟环境中，我们从生成的测试结果中过滤掉指定ID的结果\r\n    return mockSuccess(undefined);\r\n  },\r\n\r\n  // 添加下载HTML报告的mock方法\r\n  downloadHtmlReport(testResultId: string): Promise<void> {\r\n    console.log(`Mock downloading HTML report for test result: ${testResultId}`);\r\n    // 在mock模式下，我们只需返回一个成功的Promise\r\n    return Promise.resolve();\r\n  }\r\n};\r\n", "import { mockSuccess } from '../mockApi';\r\nimport { AxiosResponse } from 'axios';\r\nimport { ElMessage } from 'element-plus';\r\n\r\n// 文件资源管理器 API 模拟实现\r\nexport const mockExplorerApi = {\r\n  // 模拟选择文件夹操作\r\n  selectFolder: (): Promise<AxiosResponse<string>> => {\r\n    // 返回一个模拟的文件夹路径\r\n    return mockSuccess('D:\\\\mock\\\\selected\\\\folder\\\\path');\r\n  },\r\n\r\n  // 模拟在资源管理器中打开指定路径\r\n  openExplorer: (path: string): Promise<AxiosResponse<void>> => {\r\n    ElMessage.success(`[模拟] 已在资源管理器中打开路径: ${path}`);\r\n    return mockSuccess(undefined);\r\n  }\r\n};\r\n", "import { mockSuccess, mockError } from '../mockApi';\r\n\r\n// 添加模拟的测试套件数据\r\nconst mockTestSuites = [\r\n  {\r\n    name: 'CAN-Bus Test Suite',\r\n    version: '1.0',\r\n    packages: [\r\n      {\r\n        name: 'CAN Frame examples',\r\n        sequences: [\r\n          {\r\n            type: 'can',\r\n            description: { name: 'can-frames' },\r\n            preamble: { onConnect: true, value: 'can-preamble-sequence' }\r\n          }\r\n        ]\r\n      }\r\n    ]\r\n  },\r\n  {\r\n    name: 'CAN-FD Test Suite',\r\n    version: '1.0',\r\n    packages: [\r\n      {\r\n        name: 'CAN Unified Diagnostic Services',\r\n        sequences: [\r\n          {\r\n            type: 'uds',\r\n            description: { name: 'can-uds.diagnostic-session-control' },\r\n            preamble: { onConnect: true, value: 'uds-preamble-sequence' }\r\n          }\r\n        ]\r\n      }\r\n    ]\r\n  }\r\n];\r\n\r\n// 添加模拟的XML数据\r\nconst mockSequenceXml: Record<string, string> = {\r\n  'CAN-Bus Test Suite': `\r\n<sequences setting=\"sequence\">\r\n  <description name=\"CAN Frame examples\">Raw CAN Frame test sequences.</description>\r\n  <sequence type=\"can\">\r\n    <description name=\"can-frames\">CAN frame</description>\r\n    <preamble on-connect=\"true\">can-preamble-sequence</preamble>\r\n  </sequence>\r\n</sequences>`,\r\n  'CAN-FD Test Suite': `\r\n<sequences setting=\"sequence\">\r\n  <description name=\"CAN Unified Diagnostic Services\">UDS test sequences.</description>\r\n  <sequence type=\"uds\">\r\n    <description name=\"can-uds.diagnostic-session-control\">UDS session control</description>\r\n    <preamble on-connect=\"true\">uds-preamble-sequence</preamble>\r\n  </sequence>\r\n</sequences>`\r\n};\r\n\r\n// 测试套件 API 模拟实现\r\nexport const mockTestSuiteApi = {\r\n  getBuiltIn: () => {\r\n    return mockSuccess(mockTestSuites);\r\n  },\r\n  \r\n  getXml: (suiteName: string, packageName: string) => {\r\n    const suite = mockTestSuites.find(s => s.name === suiteName);\r\n    if (!suite) {\r\n      return mockError(400, `Test suite '${suiteName}' not found`);\r\n    }\r\n\r\n    const pkg = suite.packages.find(p => p.name === packageName);\r\n    if (!pkg) {\r\n      return mockError(400, `Package '${packageName}' not found in test suite '${suiteName}'`);\r\n    }\r\n\r\n    return mockSuccess(mockSequenceXml[suiteName]);\r\n  }\r\n};\r\n", "import { mockSuccess, mockError } from '../mockApi';\r\nimport {\r\n  HardwareConfig,\r\n  DeviceChannel,\r\n  TestPlanConfig\r\n} from '@/api/hardwareApi';\r\nimport { AxiosResponse } from 'axios';\r\n\r\n// 模拟设备数据\r\nconst mockDeviceChannels: DeviceChannel[] = [\r\n  {\r\n    name: \"VN1630 123456\",\r\n    communicationType: \"Can\",\r\n    isConnected: true\r\n  },\r\n  {\r\n    name: \"VN1640 789012\",\r\n    communicationType: \"CanFd\",\r\n    isConnected: true\r\n  },\r\n  {\r\n    name: \"TC1001 ABCDEF\",\r\n    communicationType: \"Can\",\r\n    isConnected: true\r\n  },\r\n  {\r\n    name: \"TC2001 XYZ789\",\r\n    communicationType: \"CanFd\",\r\n    isConnected: false\r\n  }\r\n];\r\n\r\n// 默认测试计划配置\r\nlet currentTestPlanConfig: TestPlanConfig = {\r\n  communicationType: \"Can\",\r\n  canConfig: {\r\n    deviceChannelName: \"VN1630 123456\",\r\n    dataBitrate: 500000\r\n  },\r\n  canFdConfig: {\r\n    deviceChannelName: \"VN1640 789012\",\r\n    arbitrationBitrate: 500000,\r\n    dataBitrate: 2000000\r\n  }\r\n};\r\n\r\n// 设置定时器，随机改变设备连接状态\r\nsetInterval(() => {\r\n  mockDeviceChannels.forEach(device => {\r\n    // 20%的概率改变连接状态\r\n    if (Math.random() < 0.8) {\r\n      device.isConnected = !device.isConnected;\r\n    }\r\n  });\r\n}, 3000); // 每3秒检查一次\r\n\r\nexport const mockHardwareApi = {\r\n  // 获取硬件配置\r\n  getHardwareConfig: (): Promise<AxiosResponse<HardwareConfig>> => {\r\n    const config: HardwareConfig = {\r\n      deviceChannels: mockDeviceChannels,\r\n      testPlanConfig: currentTestPlanConfig\r\n    };\r\n\r\n    return mockSuccess(config);\r\n  },\r\n\r\n  // 更新硬件配置\r\n  updateHardwareConfig: (config: TestPlanConfig): Promise<AxiosResponse<HardwareConfig>> => {\r\n    if (!config) {\r\n      return mockError(400, \"无效的配置数据\");\r\n    }\r\n\r\n    // 更新当前配置\r\n    currentTestPlanConfig = {\r\n      ...currentTestPlanConfig,\r\n      ...config\r\n    };\r\n\r\n    // 基于通道类型确保相应配置存在\r\n    if (config.communicationType === \"Can\" && config.canConfig) {\r\n      currentTestPlanConfig.canConfig = { ...config.canConfig };\r\n    } else if (config.communicationType === \"CanFd\" && config.canFdConfig) {\r\n      currentTestPlanConfig.canFdConfig = { ...config.canFdConfig };\r\n    }\r\n\r\n    // 返回更新后的完整配置\r\n    const updatedConfig: HardwareConfig = {\r\n      deviceChannels: mockDeviceChannels,\r\n      testPlanConfig: currentTestPlanConfig\r\n    };\r\n\r\n    return mockSuccess(updatedConfig);\r\n  }\r\n};\r\n", "import { mockSuccess } from '../mockApi';\r\n\r\n// 存储当前的序列配置数据（模拟数据库）\r\nlet currentSequenceConfig = {\r\n  testSuiteName: 'CAN-Bus Test Suite',\r\n  sequencePackageName: 'CAN Frame examples',\r\n  sequencePackageXml: '<sequence><name>CAN Frame examples</name><description>Example sequence</description></sequence>'\r\n};\r\n\r\nexport const mockSequenceApi = {\r\n  // 获取序列配置\r\n  getSequenceConfig: () => {\r\n    return mockSuccess(currentSequenceConfig);\r\n  },\r\n\r\n  // 更新序列配置 \r\n  updateSequenceConfig: (config: any) => {\r\n    // 更新当前配置\r\n    currentSequenceConfig = {\r\n      ...currentSequenceConfig,\r\n      ...config\r\n    };\r\n    return mockSuccess(currentSequenceConfig);\r\n  }\r\n};\r\n", "import { AxiosResponse } from 'axios';\r\nimport { mockSuccess } from '../mockApi';\r\nimport { BaseResponse, ExecutionState } from '@/api/interoperationApi';\r\nimport { TesterSnapshot } from '@/api/appApi';\r\n\r\n// 默认测试状态\r\nconst defaultTesterSnapshot: TesterSnapshot = {\r\n  processState: ExecutionState.Pending,\r\n  currentOperation: '',\r\n  testResult: {\r\n    id: crypto.randomUUID?.() || '1',\r\n    resultFolderName: 'Mock Test',\r\n    testType: 'Interoperation',\r\n    creationTime: new Date().toISOString(),\r\n    totalCount: 20,\r\n    successCount: 0,\r\n    failureCount: 0\r\n  },\r\n  caseResults: []\r\n};\r\n\r\n// 测试状态 - 随机生成的测试数据\r\nlet mockStatus: TesterSnapshot = {...defaultTesterSnapshot};\r\n\r\n// 用于模拟进度更新的定时器\r\nlet progressTimer: number | null = null;\r\n\r\n// 简化的测试用例生成函数\r\nfunction generateTestCases() {\r\n  const sequenceNames = [\r\n    'DiagnosticSessionControl',\r\n    'ECUReset',\r\n    'SecurityAccess',\r\n    'CommunicationControl',\r\n    'ReadDataByIdentifier',\r\n    'can-frames'\r\n  ];\r\n  \r\n  return sequenceNames.map((name, index) => ({\r\n    id: index + 1,\r\n    testResultId: mockStatus.testResult.id,\r\n    sequenceId: `seq-${index}`,\r\n    sequenceName: name,\r\n    parameter: `param-${index}`,\r\n    name: `Test ${index + 1}`,\r\n    state: Math.random() > 0.7 ? ExecutionState.Success : \r\n           (Math.random() > 0.5 ? ExecutionState.Failure : ExecutionState.Pending),\r\n    begin: new Date(Date.now() - 60000).toISOString(),\r\n    end: new Date().toISOString(),\r\n    detail: 'Test details'\r\n  }));\r\n}\r\n\r\n// 停止进度模拟\r\nfunction stopProgressSimulation() {\r\n  if (progressTimer) {\r\n    clearInterval(progressTimer);\r\n    progressTimer = null;\r\n  }\r\n}\r\n\r\nexport const mockInteroperationApi = {\r\n  // 启动互操作测试\r\n  startTest: (): Promise<AxiosResponse<BaseResponse>> => {\r\n    stopProgressSimulation();\r\n    \r\n    // 重置状态为运行中\r\n    mockStatus = {\r\n      processState: ExecutionState.Running,\r\n      currentOperation: '初始化测试环境',\r\n      testResult: {\r\n        id: crypto.randomUUID?.() || '1',\r\n        resultFolderName: 'Mock Interoperation Test',\r\n        testType: 'Interoperation',\r\n        creationTime: new Date().toISOString(),\r\n        totalCount: 20,\r\n        successCount: 0,\r\n        failureCount: 0\r\n      },\r\n      caseResults: []\r\n    };\r\n    \r\n    // 简单模拟定时更新\r\n    progressTimer = window.setInterval(() => {\r\n      if (mockStatus.processState !== ExecutionState.Running) return;\r\n      \r\n      mockStatus.testResult.successCount = Math.min(mockStatus.testResult.totalCount, \r\n                                                   mockStatus.testResult.successCount + 1);\r\n      mockStatus.testResult.failureCount = Math.min(mockStatus.testResult.totalCount - mockStatus.testResult.successCount,\r\n                                                   mockStatus.testResult.failureCount + (Math.random() > 0.8 ? 1 : 0));\r\n      \r\n      mockStatus.caseResults = generateTestCases();\r\n      mockStatus.currentOperation = `执行测试: ${mockStatus.caseResults[Math.floor(Math.random() * mockStatus.caseResults.length)].sequenceName}`;\r\n      \r\n      // 模拟测试完成\r\n      if (mockStatus.testResult.successCount + mockStatus.testResult.failureCount >= mockStatus.testResult.totalCount) {\r\n        mockStatus.processState = ExecutionState.Success;\r\n        mockStatus.currentOperation = '测试已完成';\r\n        stopProgressSimulation();\r\n      }\r\n    }, 1000);\r\n    \r\n    return mockSuccess({ success: true, message: '测试已启动' });\r\n  },\r\n  \r\n  // 停止互操作测试\r\n  stopTest: (): Promise<AxiosResponse<BaseResponse>> => {\r\n    if (mockStatus.processState === ExecutionState.Running) {\r\n      mockStatus.processState = ExecutionState.Failure;\r\n      mockStatus.currentOperation = '测试已停止';\r\n      stopProgressSimulation();\r\n    }\r\n    \r\n    return mockSuccess({ success: true, message: '测试已停止' });\r\n  },\r\n  \r\n  // 获取测试状态\r\n  getStatus: (): Promise<AxiosResponse<TesterSnapshot>> => {\r\n    return mockSuccess({...mockStatus});\r\n  }\r\n};\r\n", "import { mockSuccess } from '../mockApi';\r\nimport { CaseConfigDto, WhiteListFrame } from '@/api/caseApi';\r\nimport { CaseResult } from '@/api/interoperationApi';\r\n\r\nconst defaultConfig: CaseConfigDto = {\r\n  whiteListFrames: [], // 修改为空数组\r\n  selectedNodeName: '', // 新增：默认没有选中节点\r\n  enableNmWakeup: true,\r\n  nmWakeupId: 0x53F,\r\n  nmWakeupIsExt: false,\r\n  nmWakeupDlc: 8,\r\n  nmWakeupData: [0x3F, 0x50, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF],\r\n  nmWakeupCommunicationType: 'Can',\r\n  nmWakeupCycleMs: 100,\r\n  nmWakeupDelayMs: 2000,\r\n  diagReqId: 0x731,\r\n  diagReqIsExt: false,\r\n  diagResId: 0x631,\r\n  diagTimeoutMs: 500,\r\n  isDutMtuLessThan4096: false, // 新增：默认不限制MTU大小\r\n  enableDiagFallbackRequest: false, // 默认不启用备用诊断请求\r\n  diagFallbackRequestPayload: [0x10, 0x01], // 默认备用诊断请求数据\r\n\r\n  // 安全配置信息\r\n  securityInfo: {\r\n    hasDll: false,\r\n    dllFileName: undefined,\r\n    dllSize: 0\r\n  }\r\n};\r\n\r\n// 模拟一个CaseResult对象，用于返回单个用例详情\r\nconst mockSingleCaseResult: CaseResult = {\r\n  id: 1,\r\n  testResultId: '00000000-0000-0000-0000-000000000000',\r\n  sequenceId: '00000000-0000-0000-0000-000000000000',\r\n  sequenceName: 'Test Sequence',\r\n  name: 'mock case result',\r\n  parameter: 'Parameter Value',\r\n  state: 'Success',\r\n  begin: new Date().toISOString(),\r\n  end: new Date().toISOString(),\r\n  detail: 'Case result details'\r\n};\r\n\r\nexport const mockCaseApi = {\r\n  getCaseConfig() {\r\n    return mockSuccess(defaultConfig);\r\n  },\r\n\r\n  getCaseResult(testResultId: string, caseResultId: number) {\r\n    // 简单模拟，实际应用中可以根据ID返回不同的结果\r\n    return mockSuccess({\r\n      ...mockSingleCaseResult,\r\n      id: caseResultId,\r\n      testResultId: testResultId\r\n    });\r\n  },\r\n\r\n  updateCaseConfig(config: CaseConfigDto) {\r\n    // 处理安全DLL操作\r\n    if (config.removeSecurityDll) {\r\n      // 移除DLL\r\n      config.securityInfo = {\r\n        hasDll: false,\r\n        dllFileName: undefined,\r\n        dllSize: 0\r\n      };\r\n    } else if (config.securityDllPath) {\r\n      // 选择新的DLL - 模拟DLL文件已加载\r\n      const dllName = config.securityDllPath.split('\\\\').pop() || 'SecurityAccess.dll';\r\n      const mockDllSize = 15360; // 15KB\r\n\r\n      config.securityInfo = {\r\n        hasDll: true,\r\n        dllFileName: dllName,\r\n        dllSize: mockDllSize\r\n      };\r\n    }\r\n\r\n    // 清除操作标记，模拟后端API的行为\r\n    delete config.securityDllPath;\r\n    delete config.removeSecurityDll;\r\n\r\n    return mockSuccess(config);\r\n  },\r\n\r\n  importDbc() {\r\n    return mockSuccess({\r\n      whiteListFrames: [ // 更新帧列表，添加发送者和接收者信息\r\n        { id: 0x123, name: 'Engine_Status', dlc: 8, isExt: false, transmitter: 'ECM', receivers: ['TCM', 'BCM'] },\r\n        { id: 0x456, name: 'Transmission_Data', dlc: 8, isExt: true, transmitter: 'TCM', receivers: ['ECM', 'ICM'] },\r\n        { id: 0x789, name: 'Brake_Control', dlc: 8, isExt: false, transmitter: 'BCM', receivers: ['ECM', 'TCM', 'ICM'] }\r\n      ],\r\n      nodeNames: ['ECM', 'TCM', 'BCM', 'ICM'] // 添加节点名称列表\r\n    });\r\n  },\r\n\r\n  // 新增：选择安全DLL文件\r\n  selectSecurityDll() {\r\n    return mockSuccess({\r\n      path: \"C:\\\\fakepath\\\\SecurityAccess.dll\"\r\n    });\r\n  }\r\n};\r\n", "import { AxiosResponse, InternalAxiosRequestConfig } from 'axios';\r\nimport { mockTestPlanApi } from './modules/mockTestPlanApi';\r\nimport { mockTestPlanHistoryApi } from './modules/mockTestPlanHistoryApi';\r\nimport { mockAppApi } from './modules/mockAppApi';\r\nimport { mockExplorerApi } from './modules/mockExplorerApi';\r\nimport { mockTestSuiteApi } from './modules/mockTestSuiteApi';\r\nimport { mockHardwareApi } from './modules/mockHardwareApi';\r\nimport { mockSequenceApi } from './modules/mockSequenceApi';\r\nimport { mockInteroperationApi } from './modules/mockInteroperationApi';\r\nimport { mockCaseApi } from './modules/mockCaseApi';\r\n\r\n// 控制是否启用Mock\r\nexport const USE_MOCK = process.env.NODE_ENV === 'development';\r\n\r\n// 模拟网络延迟\r\nconst delay = (ms = 100) => new Promise(resolve => setTimeout(resolve, ms));\r\n\r\n// 创建一个符合AxiosResponse类型的响应对象\r\nexport const mockSuccess = <T>(data: T): Promise<AxiosResponse<T>> => {\r\n  return delay().then(() => {\r\n    // 创建一个符合AxiosResponse要求的对象\r\n    const response: AxiosResponse<T> = {\r\n      data,\r\n      status: 200,\r\n      statusText: 'OK',\r\n      headers: {},\r\n      config: {\r\n        headers: {} // 添加必要的headers属性\r\n      } as InternalAxiosRequestConfig\r\n    };\r\n    return response;\r\n  });\r\n};\r\n\r\n// 模拟错误响应\r\nexport const mockError = (status = 400, message = 'Error'): Promise<never> => {\r\n  return delay().then(() => {\r\n    const error: any = new Error(message);\r\n    error.response = {\r\n      status,\r\n      data: message\r\n    };\r\n    return Promise.reject(error);\r\n  });\r\n};\r\n\r\n// 随机生成一个UUID\r\nexport const generateId = () => {\r\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {\r\n    const r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);\r\n    return v.toString(16);\r\n  });\r\n};\r\n\r\n// 模拟API实现\r\nexport const mockApi = {\r\n  // 导出各个模块化的API\r\n  testPlan: mockTestPlanApi,\r\n  testPlanHistory: mockTestPlanHistoryApi,\r\n  app: mockAppApi,\r\n  explorer: mockExplorerApi,\r\n  testSuite: mockTestSuiteApi,\r\n  hardware: mockHardwareApi,\r\n  sequence: mockSequenceApi,\r\n  interoperation: mockInteroperationApi,  // 添加互操作测试API\r\n  case: mockCaseApi\r\n};\r\n", "import axios, { AxiosResponse } from 'axios'\r\nimport { USE_MOCK, mockApi } from '@/mock/mockApi'\r\nimport { TestPlanHistory } from '@/api/testPlanHistoryApi';\r\n\r\n// 定义测试计划相关的类型\r\nexport interface TestPlanManifest {\r\n  name: string;\r\n  description: string;\r\n  created?: string;\r\n  modified?: string;\r\n}\r\n\r\nexport interface TestPlan {\r\n  path: string;\r\n  manifest: TestPlanManifest;\r\n  config?: any;\r\n  [key: string]: any;\r\n}\r\n\r\n// 调整TestPlanHistoryItem类型，与TestPlanHistory保持一致\r\nexport interface TestPlanHistoryItem {\r\n  id: string;\r\n  filePath: string; // 与path同义\r\n  planName: string;\r\n  lastAccessTime: string; // 与lastAccessed同义\r\n  lastModified: string;\r\n  isDeleted: boolean;\r\n}\r\n\r\nconst BASE_URL = '/api/testPlan'\r\n\r\n// 类型转换辅助函数，将TestPlanHistory转换为TestPlanHistoryItem\r\nconst convertToHistoryItems = (histories: TestPlanHistory[]): TestPlanHistoryItem[] => {\r\n  return histories.map(history => ({\r\n    id: history.id,\r\n    filePath: history.filePath,\r\n    planName: history.planName,\r\n    lastAccessTime: history.lastAccessTime,\r\n    lastModified: history.lastModified,\r\n    isDeleted: history.isDeleted\r\n  }));\r\n};\r\n\r\nexport const testPlanApi = {\r\n  // 在文件浏览器中打开测试计划\r\n  openInExplorer: (): Promise<AxiosResponse<TestPlan>> => {\r\n    if (USE_MOCK) {\r\n      return mockApi.testPlan.openInExplorer();\r\n    }\r\n    return axios.get(`${BASE_URL}/OpenInExplorer`);\r\n  },\r\n\r\n  // 从指定路径打开测试计划\r\n  open: (path: string): Promise<AxiosResponse<TestPlan>> => {\r\n    if (USE_MOCK) {\r\n      return mockApi.testPlan.open(path);\r\n    }\r\n    return axios.post(`${BASE_URL}/open`, { path });\r\n  },\r\n\r\n  // 创建测试计划\r\n  create: (testPlanData: { name: string; description: string; folder: string }): Promise<AxiosResponse<TestPlan>> => {\r\n    if (USE_MOCK) {\r\n      return mockApi.testPlan.create(testPlanData);\r\n    }\r\n    return axios.post(`${BASE_URL}/create`, testPlanData);\r\n  },\r\n\r\n  // 获取测试计划历史记录 - 修改调用到历史API\r\n  getHistory: (): Promise<AxiosResponse<TestPlanHistoryItem[]>> => {\r\n    if (USE_MOCK) {\r\n      // 改为调用历史API而不是测试计划API\r\n      return mockApi.testPlanHistory.get().then(response => {\r\n        const convertedData = convertToHistoryItems(response.data);\r\n        return {\r\n          ...response,\r\n          data: convertedData\r\n        };\r\n      });\r\n    }\r\n    return axios.get(`${BASE_URL}/history`);\r\n  },\r\n\r\n  // 获取当前测试计划\r\n  getCurrentPlan: (): Promise<AxiosResponse<TestPlan>> => {\r\n    if (USE_MOCK) {\r\n      return mockApi.testPlan.getCurrentPlan();\r\n    }\r\n    return axios.get(`${BASE_URL}/current`);\r\n  },\r\n\r\n  // 关闭测试计划\r\n  close: (): Promise<AxiosResponse<void>> => {\r\n    if (USE_MOCK) {\r\n      return mockApi.testPlan.close();\r\n    }\r\n    return axios.post(`${BASE_URL}/close`);\r\n  },\r\n\r\n  // 更新测试计划基本信息\r\n  updateBasicInfo: (description: string): Promise<AxiosResponse<TestPlan>> => {\r\n    if (USE_MOCK) {\r\n      return mockApi.testPlan.updateBasicInfo(description);\r\n    }\r\n    return axios.post(`${BASE_URL}/updateBasicInfo`, { description });\r\n  },\r\n\r\n  // 检查文件是否存在\r\n  checkFileExists: (path: string): Promise<AxiosResponse<{exists: boolean}>> => {\r\n    if (USE_MOCK) {\r\n      // 模拟实现，可以根据需要调整\r\n      if (mockApi.testPlan.checkFileExists) {\r\n        return mockApi.testPlan.checkFileExists(path);\r\n      } else {\r\n        // 创建一个符合AxiosResponse类型的对象\r\n        return Promise.resolve({\r\n          data: { exists: true },\r\n          status: 200,\r\n          statusText: 'OK',\r\n          headers: {},\r\n          config: { headers: {} } as any\r\n        });\r\n      }\r\n    }\r\n    return axios.post(`${BASE_URL}/checkFileExists`, { path });\r\n  }\r\n}\r\n\r\nexport default testPlanApi\r\n", "import axios, { AxiosResponse } from 'axios'\r\nimport { USE_MOCK, mockApi } from '@/mock/mockApi'\r\n\r\nconst BASE_URL = '/api/explorer'\r\n\r\nexport const explorerApi = {\r\n  // 选择保存文件夹\r\n  selectFolder: (): Promise<AxiosResponse<string>> => {\r\n    if (USE_MOCK) {\r\n      return mockApi.explorer?.selectFolder() || Promise.reject(new Error('Mock API not implemented'));\r\n    }\r\n    return axios.get(`${BASE_URL}/select-folder`);\r\n  },\r\n\r\n  // 在资源管理器中打开指定路径\r\n  openExplorer: (path: string): Promise<AxiosResponse<void>> => {\r\n    if (USE_MOCK) {\r\n      return mockApi.explorer?.openExplorer?.(path) || Promise.reject(new Error('Mock API not implemented'));\r\n    }\r\n    return axios.get(`${BASE_URL}/open-explorer`, { params: { path } });\r\n  }\r\n}\r\n\r\nexport default explorerApi\r\n", "import axios, { AxiosResponse } from 'axios'\r\nimport { USE_MOCK, mockApi } from '@/mock/mockApi'\r\n\r\n// 定义历史记录类型\r\nexport interface TestPlanHistory {\r\n  id: string;\r\n  filePath: string;\r\n  planName: string;\r\n  lastAccessTime: string;\r\n  lastModified: string;\r\n  isDeleted: boolean;\r\n}\r\n\r\nconst BASE_URL = '/api/testPlanHistory'\r\n\r\nexport const testPlanHistoryApi = {\r\n  // 获取测试计划历史记录\r\n  get: (): Promise<AxiosResponse<TestPlanHistory[]>> => {\r\n    if (USE_MOCK) {\r\n      return mockApi.testPlanHistory.get();\r\n    }\r\n    return axios.get(`${BASE_URL}/get`);\r\n  },\r\n\r\n  // 清空历史记录\r\n  clear: (): Promise<AxiosResponse<void>> => {\r\n    if (USE_MOCK) {\r\n      return mockApi.testPlanHistory.clear();\r\n    }\r\n    return axios.delete(`${BASE_URL}/clear`);\r\n  },\r\n\r\n  // 删除单个历史记录\r\n  deleteRecord: (filePath: string): Promise<AxiosResponse<void>> => {\r\n    if (USE_MOCK) {\r\n      // 模拟实现，可以根据需要调整\r\n      if (mockApi.testPlanHistory.deleteRecord) {\r\n        return mockApi.testPlanHistory.deleteRecord(filePath);\r\n      } else {\r\n        // 创建一个符合AxiosResponse类型的对象\r\n        return Promise.resolve({\r\n          data: undefined,\r\n          status: 200,\r\n          statusText: 'OK',\r\n          headers: {},\r\n          config: { headers: {} } as any\r\n        });\r\n      }\r\n    }\r\n    return axios.delete(`${BASE_URL}/deleteRecord`, { data: { filePath } });\r\n  }\r\n}\r\n\r\nexport default testPlanHistoryApi\r\n", "import { testPlan<PERSON>pi } from './testPlanApi'\r\nimport { testPlanHistoryApi } from './testPlanHistoryApi'\r\nimport { appApi } from './appApi'\r\n\r\nexport {\r\n  testPlanApi,\r\n  testPlanHistoryApi,\r\n  appApi\r\n}\r\n\r\nexport default {\r\n  testPlanApi,\r\n  testPlanHistoryApi,\r\n  appApi\r\n}\r\n", "import { resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, createTextVNode as _createTextVNode, createElementVNode as _createElementVNode, openBlock as _openBlock, createBlock as _createBlock } from \"vue\"\n\nconst _hoisted_1 = { class: \"folder-input\" }\n\nexport function render(_ctx: any,_cache: any,$props: any,$setup: any,$data: any,$options: any) {\n  const _component_el_input = _resolveComponent(\"el-input\")!\n  const _component_el_form_item = _resolveComponent(\"el-form-item\")!\n  const _component_el_button = _resolveComponent(\"el-button\")!\n  const _component_el_form = _resolveComponent(\"el-form\")!\n  const _component_el_dialog = _resolveComponent(\"el-dialog\")!\n\n  return (_openBlock(), _createBlock(_component_el_dialog, {\n    modelValue: _ctx.dialogVisible,\n    \"onUpdate:modelValue\": _cache[3] || (_cache[3] = ($event: any) => ((_ctx.dialogVisible) = $event)),\n    title: \"Create Test Plan\",\n    width: \"500px\"\n  }, {\n    footer: _withCtx(() => [\n      _createElementVNode(\"span\", null, [\n        _createVNode(_component_el_button, { onClick: _ctx.close }, {\n          default: _withCtx(() => _cache[5] || (_cache[5] = [\n            _createTextVNode(\"Cancel\")\n          ])),\n          _: 1\n        }, 8, [\"onClick\"]),\n        _createVNode(_component_el_button, {\n          type: \"primary\",\n          onClick: _ctx.handleCreateTestPlan\n        }, {\n          default: _withCtx(() => _cache[6] || (_cache[6] = [\n            _createTextVNode(\" Create \")\n          ])),\n          _: 1\n        }, 8, [\"onClick\"])\n      ])\n    ]),\n    default: _withCtx(() => [\n      _createVNode(_component_el_form, {\n        model: _ctx.form,\n        \"label-width\": \"90px\"\n      }, {\n        default: _withCtx(() => [\n          _createVNode(_component_el_form_item, {\n            label: \"Name\",\n            required: \"\"\n          }, {\n            default: _withCtx(() => [\n              _createVNode(_component_el_input, {\n                modelValue: _ctx.form.name,\n                \"onUpdate:modelValue\": _cache[0] || (_cache[0] = ($event: any) => ((_ctx.form.name) = $event)),\n                placeholder: \"Enter test plan name\"\n              }, null, 8, [\"modelValue\"])\n            ]),\n            _: 1\n          }),\n          _createVNode(_component_el_form_item, {\n            label: \"Folder\",\n            required: \"\"\n          }, {\n            default: _withCtx(() => [\n              _createElementVNode(\"div\", _hoisted_1, [\n                _createVNode(_component_el_input, {\n                  modelValue: _ctx.form.folder,\n                  \"onUpdate:modelValue\": _cache[1] || (_cache[1] = ($event: any) => ((_ctx.form.folder) = $event)),\n                  placeholder: \"Select save folder\"\n                }, null, 8, [\"modelValue\"]),\n                _createVNode(_component_el_button, { onClick: _ctx.handleSelectFolder }, {\n                  default: _withCtx(() => _cache[4] || (_cache[4] = [\n                    _createTextVNode(\"Browse\")\n                  ])),\n                  _: 1\n                }, 8, [\"onClick\"])\n              ])\n            ]),\n            _: 1\n          }),\n          _createVNode(_component_el_form_item, { label: \"Description\" }, {\n            default: _withCtx(() => [\n              _createVNode(_component_el_input, {\n                modelValue: _ctx.form.description,\n                \"onUpdate:modelValue\": _cache[2] || (_cache[2] = ($event: any) => ((_ctx.form.description) = $event)),\n                type: \"textarea\",\n                placeholder: \"Enter test plan description\"\n              }, null, 8, [\"modelValue\"])\n            ]),\n            _: 1\n          })\n        ]),\n        _: 1\n      }, 8, [\"model\"])\n    ]),\n    _: 1\n  }, 8, [\"modelValue\"]))\n}", "<template>\r\n  <el-dialog v-model=\"dialogVisible\" title=\"Create Test Plan\" width=\"500px\">\r\n    <el-form :model=\"form\" label-width=\"90px\">\r\n      <el-form-item label=\"Name\" required>\r\n        <el-input v-model=\"form.name\" placeholder=\"Enter test plan name\" />\r\n      </el-form-item>\r\n      <el-form-item label=\"Folder\" required>\r\n        <div class=\"folder-input\">\r\n          <el-input v-model=\"form.folder\" placeholder=\"Select save folder\" />\r\n          <el-button @click=\"handleSelectFolder\">Browse</el-button>\r\n        </div>\r\n      </el-form-item>\r\n      <el-form-item label=\"Description\">\r\n        <el-input v-model=\"form.description\" type=\"textarea\" placeholder=\"Enter test plan description\" />\r\n      </el-form-item>\r\n    </el-form>\r\n    <template #footer>\r\n      <span>\r\n        <el-button @click=\"close\">Cancel</el-button>\r\n        <el-button type=\"primary\" @click=\"handleCreateTestPlan\">\r\n          Create\r\n        </el-button>\r\n      </span>\r\n    </template>\r\n  </el-dialog>\r\n</template>\r\n\r\n<script lang=\"ts\">\r\nimport { defineComponent, ref, reactive } from 'vue';\r\nimport { ElMessage } from 'element-plus';\r\nimport { testPlanApi } from '@/api';\r\nimport { explorerApi } from '@/api/explorerApi';\r\n\r\nexport default defineComponent({\r\n  name: 'CreateTestPlan',\r\n  emits: ['created'],\r\n  setup(props, { emit }) {\r\n    const dialogVisible = ref(false);\r\n    const form = reactive({\r\n      name: '',\r\n      description: '',\r\n      folder: ''\r\n    });\r\n\r\n    const handleSelectFolder = async () => {\r\n      try {\r\n        const response = await explorerApi.selectFolder();\r\n        if (response.data) {\r\n          form.folder = response.data;\r\n        }\r\n      } catch (error: any) {\r\n        if (error.response?.status === 400 && error.response.data === \"UserCanceled\") {\r\n          // 用户取消选择，不需要提示\r\n          return;\r\n        }\r\n        ElMessage.error(\"Failed to select folder\");\r\n      }\r\n    };\r\n\r\n    const handleCreateTestPlan = async () => {\r\n      if (!form.name) {\r\n        ElMessage.warning(\"Please enter test plan name\");\r\n        return;\r\n      }\r\n\r\n      if (!form.folder) {\r\n        ElMessage.warning(\"Please select save folder\");\r\n        return;\r\n      }\r\n\r\n      const response = await testPlanApi.create({\r\n        name: form.name,\r\n        description: form.description,\r\n        folder: form.folder\r\n      });\r\n\r\n      if (response.data) {\r\n        close();\r\n        emit('created', response.data);\r\n      }\r\n    };\r\n\r\n    const close = () => {\r\n      dialogVisible.value = false;\r\n      form.name = '';\r\n      form.description = '';\r\n      form.folder = '';\r\n    };\r\n\r\n    const show = () => {\r\n      dialogVisible.value = true;\r\n    };\r\n\r\n    return {\r\n      dialogVisible,\r\n      form,\r\n      handleCreateTestPlan,\r\n      handleSelectFolder,\r\n      close,\r\n      show\r\n    };\r\n  }\r\n});\r\n</script>\r\n\r\n<style scoped>\r\n.folder-input {\r\n  display: flex;\r\n  gap: 8px;\r\n  width: 100%;\r\n  /* 确保容器占满整个宽度 */\r\n}\r\n\r\n.folder-input .el-input {\r\n  flex: 1;\r\n  /* 让输入框占据除按钮外的所有空间 */\r\n}\r\n\r\n:deep(.el-form-item__content) {\r\n  width: 100%;\r\n  /* 确保表单项内容区域占满整个宽度 */\r\n}\r\n</style>\r\n", "import { render } from \"./CreateTestPlan.vue?vue&type=template&id=5f3c0e5c&scoped=true&ts=true\"\nimport script from \"./CreateTestPlan.vue?vue&type=script&lang=ts\"\nexport * from \"./CreateTestPlan.vue?vue&type=script&lang=ts\"\n\nimport \"./CreateTestPlan.vue?vue&type=style&index=0&id=5f3c0e5c&scoped=true&lang=css\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-5f3c0e5c\"]])\n\nexport default __exports__", "import { defineComponent as _defineComponent } from 'vue'\nimport { unref as _unref, createVNode as _createVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, createElementBlock as _createElementBlock } from \"vue\"\n\nconst _hoisted_1 = {\n  key: 0,\n  class: \"test-plan-indicator\"\n}\nconst _hoisted_2 = { class: \"plan-info\" }\nconst _hoisted_3 = [\"title\"]\nconst _hoisted_4 = { class: \"plan-actions\" }\n\nimport { computed, ref } from 'vue';\nimport { useRouter, useRoute } from 'vue-router';\nimport { ElMessageBox, ElMessage } from 'element-plus';\nimport { Document, Back, Close } from '@element-plus/icons-vue';\nimport { testPlanService } from '@/services/testPlanService';\n\n\nexport default /*@__PURE__*/_defineComponent({\n  __name: 'TestPlanStatusIndicator',\n  setup(__props) {\n\nconst router = useRouter();\nconst route = useRoute();\nconst state = testPlanService.getState();\nconst isNavigating = ref(false);\nconst isClosing = ref(false);\n\n// Check if currently on test plan page\nconst isOnTestPlanPage = computed(() => route.path.startsWith('/test-plan'));\n\n// Check if a test plan is currently open\nconst hasOpenTestPlan = computed(() => !!state.currentPlan);\nconst currentPlan = computed(() => state.currentPlan);\n\n// Return to test plan page\nconst handleReturnToPlan = async () => {\n  if (!hasOpenTestPlan.value) return;\n\n  isNavigating.value = true;\n  try {\n    await router.push('/test-plan');\n  } catch (error) {\n    console.error('Failed to navigate to test plan page:', error);\n    ElMessage.error('Failed to return to test plan');\n  } finally {\n    isNavigating.value = false;\n  }\n};\n\n// 关闭测试计划\nconst handleClosePlan = async () => {\n  if (!hasOpenTestPlan.value) return;\n\n  try {\n    const result = await ElMessageBox.confirm(\n      'Are you sure you want to close the current test plan? Unsaved changes may be lost.',\n      'Close Test Plan',\n      {\n        confirmButtonText: 'OK',\n        cancelButtonText: 'Cancel',\n        type: 'warning'\n      }\n    );\n\n    if (result === 'confirm') {\n      isClosing.value = true;\n      await testPlanService.closeTestPlan();\n    }\n  } catch (error) {\n    // User canceled the operation, do nothing\n  } finally {\n    isClosing.value = false;\n  }\n};\n\nreturn (_ctx: any,_cache: any) => {\n  const _component_el_icon = _resolveComponent(\"el-icon\")!\n  const _component_el_button = _resolveComponent(\"el-button\")!\n\n  return (hasOpenTestPlan.value)\n    ? (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [\n        _createElementVNode(\"div\", _hoisted_2, [\n          _createVNode(_component_el_icon, null, {\n            default: _withCtx(() => [\n              _createVNode(_unref(Document))\n            ]),\n            _: 1\n          }),\n          _createElementVNode(\"span\", {\n            class: \"plan-name\",\n            title: currentPlan.value.path\n          }, _toDisplayString(currentPlan.value.manifest.name), 9, _hoisted_3)\n        ]),\n        _createElementVNode(\"div\", _hoisted_4, [\n          (!isOnTestPlanPage.value)\n            ? (_openBlock(), _createBlock(_component_el_button, {\n                key: 0,\n                type: \"primary\",\n                size: \"small\",\n                onClick: handleReturnToPlan,\n                loading: isNavigating.value\n              }, {\n                default: _withCtx(() => [\n                  _createVNode(_component_el_icon, null, {\n                    default: _withCtx(() => [\n                      _createVNode(_unref(Back))\n                    ]),\n                    _: 1\n                  }),\n                  _cache[0] || (_cache[0] = _createTextVNode(\" Back \"))\n                ]),\n                _: 1\n              }, 8, [\"loading\"]))\n            : _createCommentVNode(\"\", true),\n          _createVNode(_component_el_button, {\n            type: \"danger\",\n            size: \"small\",\n            onClick: handleClosePlan,\n            loading: isClosing.value\n          }, {\n            default: _withCtx(() => [\n              _createVNode(_component_el_icon, null, {\n                default: _withCtx(() => [\n                  _createVNode(_unref(Close))\n                ]),\n                _: 1\n              }),\n              _cache[1] || (_cache[1] = _createTextVNode(\" Close \"))\n            ]),\n            _: 1\n          }, 8, [\"loading\"])\n        ])\n      ]))\n    : _createCommentVNode(\"\", true)\n}\n}\n\n})", "<template>\n  <div v-if=\"hasOpenTestPlan\" class=\"test-plan-indicator\">\n    <div class=\"plan-info\">\n      <el-icon><Document /></el-icon>\n      <span class=\"plan-name\" :title=\"currentPlan.path\">{{ currentPlan.manifest.name }}</span>\n    </div>\n    <div class=\"plan-actions\">\n      <el-button\n        v-if=\"!isOnTestPlanPage\"\n        type=\"primary\"\n        size=\"small\"\n        @click=\"handleReturnToPlan\"\n        :loading=\"isNavigating\"\n      >\n        <el-icon><Back /></el-icon>\n        Back\n      </el-button>\n      <el-button\n        type=\"danger\"\n        size=\"small\"\n        @click=\"handleClosePlan\"\n        :loading=\"isClosing\"\n      >\n        <el-icon><Close /></el-icon>\n        Close\n      </el-button>\n    </div>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport { computed, ref } from 'vue';\nimport { useRouter, useRoute } from 'vue-router';\nimport { ElMessageBox, ElMessage } from 'element-plus';\nimport { Document, Back, Close } from '@element-plus/icons-vue';\nimport { testPlanService } from '@/services/testPlanService';\n\nconst router = useRouter();\nconst route = useRoute();\nconst state = testPlanService.getState();\nconst isNavigating = ref(false);\nconst isClosing = ref(false);\n\n// Check if currently on test plan page\nconst isOnTestPlanPage = computed(() => route.path.startsWith('/test-plan'));\n\n// Check if a test plan is currently open\nconst hasOpenTestPlan = computed(() => !!state.currentPlan);\nconst currentPlan = computed(() => state.currentPlan);\n\n// Return to test plan page\nconst handleReturnToPlan = async () => {\n  if (!hasOpenTestPlan.value) return;\n\n  isNavigating.value = true;\n  try {\n    await router.push('/test-plan');\n  } catch (error) {\n    console.error('Failed to navigate to test plan page:', error);\n    ElMessage.error('Failed to return to test plan');\n  } finally {\n    isNavigating.value = false;\n  }\n};\n\n// 关闭测试计划\nconst handleClosePlan = async () => {\n  if (!hasOpenTestPlan.value) return;\n\n  try {\n    const result = await ElMessageBox.confirm(\n      'Are you sure you want to close the current test plan? Unsaved changes may be lost.',\n      'Close Test Plan',\n      {\n        confirmButtonText: 'OK',\n        cancelButtonText: 'Cancel',\n        type: 'warning'\n      }\n    );\n\n    if (result === 'confirm') {\n      isClosing.value = true;\n      await testPlanService.closeTestPlan();\n    }\n  } catch (error) {\n    // User canceled the operation, do nothing\n  } finally {\n    isClosing.value = false;\n  }\n};\n</script>\n\n<style scoped>\n.test-plan-indicator {\n  display: flex;\n  align-items: center;\n  padding: 0 15px;\n  margin-left: 10px;\n  height: 40px;\n  border-left: 1px solid var(--el-border-color-light);\n  border-bottom: 1px solid var(--el-menu-border-color);\n}\n\n.plan-info {\n  display: flex;\n  align-items: center;\n  margin-right: 15px;\n  color: var(--el-text-color-secondary);\n}\n\n.plan-name {\n  margin-left: 5px;\n  font-weight: 500;\n  max-width: 150px;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n  color: var(--el-text-color-primary);\n}\n\n.plan-actions {\n  display: flex;\n  gap: 8px;\n}\n</style>\n", "import script from \"./TestPlanStatusIndicator.vue?vue&type=script&setup=true&lang=ts\"\nexport * from \"./TestPlanStatusIndicator.vue?vue&type=script&setup=true&lang=ts\"\n\nimport \"./TestPlanStatusIndicator.vue?vue&type=style&index=0&id=c5caca82&scoped=true&lang=css\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-c5caca82\"]])\n\nexport default __exports__", "import { defineComponent as _defineComponent } from 'vue'\nimport { unref as _unref, createVNode as _createVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createTextVNode as _createTextVNode, createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, renderList as _renderList, Fragment as _Fragment, toDisplayString as _toDisplayString, createBlock as _createBlock } from \"vue\"\n\nconst _hoisted_1 = { class: \"top-menu-bar\" }\nconst _hoisted_2 = {\n  key: 0,\n  class: \"empty-recent\"\n}\nconst _hoisted_3 = { class: \"recent-plan-name\" }\nconst _hoisted_4 = { class: \"recent-plan-path\" }\n\nimport { ref, onMounted, computed } from 'vue'\r\nimport { useRouter, useRoute } from 'vue-router'\r\nimport { ElMessageBox, ElMessage } from 'element-plus'\r\nimport { appApi } from '@/api'\r\nimport { HomeFilled } from '@element-plus/icons-vue'\r\nimport CreateTestPlan from '@/components/TestPlan/CreateTestPlan.vue'\r\nimport { testPlanService } from '@/services/testPlanService'\r\nimport { testPlanApi } from '@/api/testPlanApi'\r\nimport { testPlanHistoryApi } from '@/api/testPlanHistoryApi'\r\nimport TestPlanStatusIndicator from './TestPlanStatusIndicator.vue'\r\n\r\n\nexport default /*@__PURE__*/_defineComponent({\n  __name: 'TopMenuBar',\n  setup(__props) {\n\r\nconst router = useRouter()\r\nconst route = useRoute()\r\nconst createDialog = ref<InstanceType<typeof CreateTestPlan> | null>(null)\r\n\r\n// Compute active menu index based on current route\r\nconst activeIndex = computed(() => {\r\n  // When on test plan page, don't highlight any menu item\r\n  if (route.path.startsWith('/test-plan')) {\r\n    return ''\r\n  }\r\n  return route.path\r\n})\r\n\r\n// Set router\r\ntestPlanService.setRouter(router)\r\n\r\n// Get service state\r\nconst state = testPlanService.getState()\r\nconst recentPlans = computed(() => state.recentPlans.slice(0, 5)) // Only show 5 most recent\r\n\r\n// Handle open\r\nconst handleOpen = async () => {\r\n  await testPlanService.openFromExplorer()\r\n}\r\n\r\n// Handle opening from history\r\nconst handleOpenFromHistory = async (path: string) => {\r\n  try {\r\n    // First check if file exists\r\n    const response = await testPlanApi.checkFileExists(path);\r\n\r\n    if (response.data.exists) {\r\n      // File exists, open normally\r\n      await testPlanService.openFromPath(path);\r\n    } else {\r\n      // File does not exist, prompt user\r\n      ElMessageBox.confirm(\r\n        'Test plan file does not exist. Do you want to remove it from history?',\r\n        'File Not Found',\r\n        {\r\n          confirmButtonText: 'Remove',\r\n          cancelButtonText: 'Cancel',\r\n          type: 'warning'\r\n        }\r\n      ).then(async () => {\r\n        // User chose to remove\r\n        await testPlanHistoryApi.deleteRecord(path);\r\n        // Refresh history\r\n        await testPlanService.loadRecentPlans();\r\n      }).catch(() => {\r\n        // User canceled, do nothing\r\n      });\r\n    }\r\n  } catch (error) {\r\n    console.error('Failed to check file existence:', error);\r\n  }\r\n}\r\n\r\n// Handle create\r\nconst handleCreate = () => {\r\n  createDialog.value?.show()\r\n}\r\n\r\n// Handle test plan creation completed\r\nconst handleTestPlanCreated = () => {\r\n  // Refresh state is handled by the service, no additional action needed\r\n}\r\n\r\n// Handle exit application\r\nconst handleExit = () => {\r\n  ElMessageBox.confirm(\r\n    'Are you sure you want to exit the application?',\r\n    'Exit',\r\n    {\r\n      confirmButtonText: 'OK',\r\n      cancelButtonText: 'Cancel',\r\n      type: 'warning'\r\n    }\r\n  ).then(() => {\r\n    // 确认退出时调用后端API\r\n    appApi.exit()\r\n      .then(() => {\r\n        ElMessage.info('Application is shutting down...');\r\n        // 在实际环境中，这里不会被执行到，因为应用已经退出\r\n      })\r\n      .catch((error) => {\r\n        ElMessage.error('Failed to exit the application');\r\n        console.error('Error when exiting application:', error);\r\n      });\r\n  }).catch(() => {\r\n    // 用户取消退出，不执行任何操作\r\n  });\r\n}\r\n\r\nonMounted(() => {\r\n  testPlanService.loadRecentPlans()\r\n})\r\n\nreturn (_ctx: any,_cache: any) => {\n  const _component_el_icon = _resolveComponent(\"el-icon\")!\n  const _component_el_menu_item = _resolveComponent(\"el-menu-item\")!\n  const _component_el_divider = _resolveComponent(\"el-divider\")!\n  const _component_el_sub_menu = _resolveComponent(\"el-sub-menu\")!\n  const _component_el_menu = _resolveComponent(\"el-menu\")!\n\n  return (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [\n    _createVNode(_component_el_menu, {\n      mode: \"horizontal\",\n      class: \"menu-container\",\n      router: \"\",\n      \"default-active\": activeIndex.value\n    }, {\n      default: _withCtx(() => [\n        _createVNode(_component_el_menu_item, {\n          index: \"/\",\n          class: \"home-menu-item\"\n        }, {\n          default: _withCtx(() => [\n            _createVNode(_component_el_icon, null, {\n              default: _withCtx(() => [\n                _createVNode(_unref(HomeFilled))\n              ]),\n              _: 1\n            })\n          ]),\n          _: 1\n        }),\n        _createVNode(_component_el_sub_menu, { index: \"file\" }, {\n          title: _withCtx(() => _cache[0] || (_cache[0] = [\n            _createTextVNode(\"File\")\n          ])),\n          default: _withCtx(() => [\n            _createVNode(_component_el_menu_item, {\n              onClick: handleOpen,\n              class: \"menu-item\"\n            }, {\n              default: _withCtx(() => _cache[1] || (_cache[1] = [\n                _createTextVNode(\"Open\")\n              ])),\n              _: 1\n            }),\n            _createVNode(_component_el_menu_item, {\n              onClick: handleCreate,\n              class: \"menu-item\"\n            }, {\n              default: _withCtx(() => _cache[2] || (_cache[2] = [\n                _createTextVNode(\"Create\")\n              ])),\n              _: 1\n            }),\n            _createVNode(_component_el_divider),\n            _createVNode(_component_el_sub_menu, {\n              index: \"recent\",\n              \"popper-class\": \"recent-submenu\"\n            }, {\n              title: _withCtx(() => _cache[3] || (_cache[3] = [\n                _createTextVNode(\"Recent\")\n              ])),\n              default: _withCtx(() => [\n                (recentPlans.value.length === 0)\n                  ? (_openBlock(), _createElementBlock(\"div\", _hoisted_2, _cache[4] || (_cache[4] = [\n                      _createElementVNode(\"span\", null, \"No recent items\", -1)\n                    ])))\n                  : (_openBlock(true), _createElementBlock(_Fragment, { key: 1 }, _renderList(recentPlans.value, (plan) => {\n                      return (_openBlock(), _createBlock(_component_el_menu_item, {\n                        key: plan.id,\n                        onClick: ($event: any) => (handleOpenFromHistory(plan.filePath)),\n                        class: \"recent-item\"\n                      }, {\n                        default: _withCtx(() => [\n                          _createElementVNode(\"span\", _hoisted_3, _toDisplayString(plan.planName), 1),\n                          _createElementVNode(\"span\", _hoisted_4, _toDisplayString(plan.filePath), 1)\n                        ]),\n                        _: 2\n                      }, 1032, [\"onClick\"]))\n                    }), 128))\n              ]),\n              _: 1\n            }),\n            _createVNode(_component_el_divider),\n            _createVNode(_component_el_menu_item, {\n              onClick: handleExit,\n              class: \"menu-item\"\n            }, {\n              default: _withCtx(() => _cache[5] || (_cache[5] = [\n                _createTextVNode(\"Exit\")\n              ])),\n              _: 1\n            })\n          ]),\n          _: 1\n        }),\n        _createVNode(_component_el_menu_item, {\n          index: \"/test-suite\",\n          class: \"test-suite-menu-item\"\n        }, {\n          default: _withCtx(() => _cache[6] || (_cache[6] = [\n            _createElementVNode(\"span\", { class: \"test-suite-text\" }, \"Test Suite\", -1)\n          ])),\n          _: 1\n        }),\n        _createVNode(_component_el_menu_item, {\n          index: \"/about\",\n          class: \"about-menu-item\"\n        }, {\n          default: _withCtx(() => _cache[7] || (_cache[7] = [\n            _createElementVNode(\"span\", { class: \"about-text\" }, \"About\", -1)\n          ])),\n          _: 1\n        })\n      ]),\n      _: 1\n    }, 8, [\"default-active\"]),\n    _createVNode(TestPlanStatusIndicator, { class: \"status-indicator\" }),\n    _createVNode(CreateTestPlan, {\n      ref_key: \"createDialog\",\n      ref: createDialog,\n      onCreated: handleTestPlanCreated\n    }, null, 512)\n  ]))\n}\n}\n\n})", "<template>\r\n  <div class=\"top-menu-bar\">\r\n    <el-menu mode=\"horizontal\" class=\"menu-container\" router :default-active=\"activeIndex\">\r\n      <el-menu-item index=\"/\" class=\"home-menu-item\">\r\n        <el-icon>\r\n          <HomeFilled />\r\n        </el-icon>\r\n      </el-menu-item>\r\n      <el-sub-menu index=\"file\">\r\n        <template #title>File</template>\r\n        <el-menu-item @click=\"handleOpen\" class=\"menu-item\">Open</el-menu-item>\r\n        <el-menu-item @click=\"handleCreate\" class=\"menu-item\">Create</el-menu-item>\r\n        <el-divider />\r\n        <el-sub-menu index=\"recent\" popper-class=\"recent-submenu\">\r\n          <template #title>Recent</template>\r\n          <div v-if=\"recentPlans.length === 0\" class=\"empty-recent\">\r\n            <span>No recent items</span>\r\n          </div>\r\n          <template v-else>\r\n            <el-menu-item v-for=\"plan in recentPlans\" :key=\"plan.id\" @click=\"handleOpenFromHistory(plan.filePath)\"\r\n              class=\"recent-item\">\r\n              <span class=\"recent-plan-name\">{{ plan.planName }}</span>\r\n              <span class=\"recent-plan-path\">{{ plan.filePath }}</span>\r\n            </el-menu-item>\r\n          </template>\r\n        </el-sub-menu>\r\n        <el-divider />\r\n        <el-menu-item @click=\"handleExit\" class=\"menu-item\">Exit</el-menu-item>\r\n      </el-sub-menu>\r\n\r\n      <el-menu-item index=\"/test-suite\" class=\"test-suite-menu-item\">\r\n        <span class=\"test-suite-text\">Test Suite</span>\r\n      </el-menu-item>\r\n\r\n      <el-menu-item index=\"/about\" class=\"about-menu-item\">\r\n        <span class=\"about-text\">About</span>\r\n      </el-menu-item>\r\n    </el-menu>\r\n\r\n    <!-- 添加测试计划状态指示器 -->\r\n    <test-plan-status-indicator class=\"status-indicator\" />\r\n\r\n    <create-test-plan ref=\"createDialog\" @created=\"handleTestPlanCreated\" />\r\n  </div>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { ref, onMounted, computed } from 'vue'\r\nimport { useRouter, useRoute } from 'vue-router'\r\nimport { ElMessageBox, ElMessage } from 'element-plus'\r\nimport { appApi } from '@/api'\r\nimport { HomeFilled } from '@element-plus/icons-vue'\r\nimport CreateTestPlan from '@/components/TestPlan/CreateTestPlan.vue'\r\nimport { testPlanService } from '@/services/testPlanService'\r\nimport { testPlanApi } from '@/api/testPlanApi'\r\nimport { testPlanHistoryApi } from '@/api/testPlanHistoryApi'\r\nimport TestPlanStatusIndicator from './TestPlanStatusIndicator.vue'\r\n\r\nconst router = useRouter()\r\nconst route = useRoute()\r\nconst createDialog = ref<InstanceType<typeof CreateTestPlan> | null>(null)\r\n\r\n// Compute active menu index based on current route\r\nconst activeIndex = computed(() => {\r\n  // When on test plan page, don't highlight any menu item\r\n  if (route.path.startsWith('/test-plan')) {\r\n    return ''\r\n  }\r\n  return route.path\r\n})\r\n\r\n// Set router\r\ntestPlanService.setRouter(router)\r\n\r\n// Get service state\r\nconst state = testPlanService.getState()\r\nconst recentPlans = computed(() => state.recentPlans.slice(0, 5)) // Only show 5 most recent\r\n\r\n// Handle open\r\nconst handleOpen = async () => {\r\n  await testPlanService.openFromExplorer()\r\n}\r\n\r\n// Handle opening from history\r\nconst handleOpenFromHistory = async (path: string) => {\r\n  try {\r\n    // First check if file exists\r\n    const response = await testPlanApi.checkFileExists(path);\r\n\r\n    if (response.data.exists) {\r\n      // File exists, open normally\r\n      await testPlanService.openFromPath(path);\r\n    } else {\r\n      // File does not exist, prompt user\r\n      ElMessageBox.confirm(\r\n        'Test plan file does not exist. Do you want to remove it from history?',\r\n        'File Not Found',\r\n        {\r\n          confirmButtonText: 'Remove',\r\n          cancelButtonText: 'Cancel',\r\n          type: 'warning'\r\n        }\r\n      ).then(async () => {\r\n        // User chose to remove\r\n        await testPlanHistoryApi.deleteRecord(path);\r\n        // Refresh history\r\n        await testPlanService.loadRecentPlans();\r\n      }).catch(() => {\r\n        // User canceled, do nothing\r\n      });\r\n    }\r\n  } catch (error) {\r\n    console.error('Failed to check file existence:', error);\r\n  }\r\n}\r\n\r\n// Handle create\r\nconst handleCreate = () => {\r\n  createDialog.value?.show()\r\n}\r\n\r\n// Handle test plan creation completed\r\nconst handleTestPlanCreated = () => {\r\n  // Refresh state is handled by the service, no additional action needed\r\n}\r\n\r\n// Handle exit application\r\nconst handleExit = () => {\r\n  ElMessageBox.confirm(\r\n    'Are you sure you want to exit the application?',\r\n    'Exit',\r\n    {\r\n      confirmButtonText: 'OK',\r\n      cancelButtonText: 'Cancel',\r\n      type: 'warning'\r\n    }\r\n  ).then(() => {\r\n    // 确认退出时调用后端API\r\n    appApi.exit()\r\n      .then(() => {\r\n        ElMessage.info('Application is shutting down...');\r\n        // 在实际环境中，这里不会被执行到，因为应用已经退出\r\n      })\r\n      .catch((error) => {\r\n        ElMessage.error('Failed to exit the application');\r\n        console.error('Error when exiting application:', error);\r\n      });\r\n  }).catch(() => {\r\n    // 用户取消退出，不执行任何操作\r\n  });\r\n}\r\n\r\nonMounted(() => {\r\n  testPlanService.loadRecentPlans()\r\n})\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.top-menu-bar {\r\n  border-bottom: 1px solid var(--el-border-color-light);\r\n  user-select: none;\r\n  display: flex; /* 添加flex布局 */\r\n  align-items: center; /* 垂直居中 */\r\n}\r\n\r\n.menu-container {\r\n  height: 40px;\r\n  line-height: 40px;\r\n  display: flex;\r\n  justify-content: flex-start;\r\n  flex-grow: 1; /* 让菜单占据剩余空间 */\r\n}\r\n\r\n.status-indicator {\r\n  margin-left: auto; /* 将状态指示器推到右侧 */\r\n}\r\n\r\n// 添加自定义样式，减少菜单下拉框与菜单项之间的间距\r\n:deep(.el-menu--horizontal) {\r\n  .el-sub-menu .el-menu--popup {\r\n    margin-top: 0;\r\n  }\r\n\r\n  // 二级菜单项左对齐\r\n  .el-menu-item,\r\n  .el-sub-menu__title {\r\n    text-align: left;\r\n    justify-content: flex-start;\r\n  }\r\n}\r\n\r\n:deep(.el-menu-item) {\r\n  user-select: none;\r\n}\r\n\r\n// 修改关于菜单项样式，确保正确显示\r\n.about-menu-item {\r\n  min-width: unset !important;\r\n  padding: 0 20px !important;\r\n  height: 40px !important;\r\n  line-height: 40px !important;\r\n  overflow: visible !important;\r\n}\r\n\r\n.about-text {\r\n  white-space: nowrap !important;\r\n  display: inline-block;\r\n  font-size: 14px;\r\n}\r\n\r\n// 保留图标样式，以防将来需要添加其他图标\r\n.el-icon {\r\n  margin-right: 5px;\r\n  vertical-align: middle;\r\n}\r\n\r\n.home-menu-item {\r\n  padding: 0 10px;\r\n\r\n  .el-icon {\r\n    font-size: 16px;\r\n    margin-right: 0;\r\n    color: var(--el-color-primary);\r\n  }\r\n}\r\n\r\n.recent-submenu {\r\n  min-width: 300px;\r\n}\r\n\r\n.empty-recent {\r\n  padding: 12px 20px;\r\n  color: var(--el-text-color-secondary);\r\n  font-size: 14px;\r\n}\r\n\r\n.recent-item {\r\n  display: flex;\r\n  padding: 8px 16px;\r\n  flex-direction: row;\r\n  gap: 10px;\r\n\r\n  .recent-plan-name {\r\n    font-size: 12px;\r\n  }\r\n\r\n  .recent-plan-path {\r\n    font-size: 12px;\r\n    color: var(--el-text-color-secondary);\r\n    white-space: nowrap;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n  }\r\n}\r\n\r\n.test-suite-menu-item {\r\n  min-width: unset !important;\r\n  padding: 0 20px !important;\r\n  height: 40px !important;\r\n  line-height: 40px !important;\r\n}\r\n\r\n.test-suite-text {\r\n  white-space: nowrap !important;\r\n  display: inline-block;\r\n  font-size: 14px;\r\n}\r\n</style>\r\n", "import script from \"./TopMenuBar.vue?vue&type=script&setup=true&lang=ts\"\nexport * from \"./TopMenuBar.vue?vue&type=script&setup=true&lang=ts\"\n\nimport \"./TopMenuBar.vue?vue&type=style&index=0&id=34acd3a4&scoped=true&lang=scss\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-34acd3a4\"]])\n\nexport default __exports__", "import script from \"./App.vue?vue&type=script&setup=true&lang=js\"\nexport * from \"./App.vue?vue&type=script&setup=true&lang=js\"\n\nimport \"./App.vue?vue&type=style&index=0&id=5dd277a8&lang=scss\"\n\nconst __exports__ = script;\n\nexport default __exports__", "import { createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\"\n\nconst _hoisted_1 = { class: \"home-view\" }\nconst _hoisted_2 = { class: \"panel-header\" }\nconst _hoisted_3 = { class: \"button-group\" }\nconst _hoisted_4 = { class: \"history-container\" }\n\nexport function render(_ctx: any,_cache: any,$props: any,$setup: any,$data: any,$options: any) {\n  const _component_Folder = _resolveComponent(\"Folder\")!\n  const _component_el_icon = _resolveComponent(\"el-icon\")!\n  const _component_el_button = _resolveComponent(\"el-button\")!\n  const _component_Plus = _resolveComponent(\"Plus\")!\n  const _component_test_plan_history = _resolveComponent(\"test-plan-history\")!\n  const _component_el_card = _resolveComponent(\"el-card\")!\n  const _component_el_col = _resolveComponent(\"el-col\")!\n  const _component_test_plan_guide = _resolveComponent(\"test-plan-guide\")!\n  const _component_el_row = _resolveComponent(\"el-row\")!\n  const _component_create_test_plan = _resolveComponent(\"create-test-plan\")!\n\n  return (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [\n    _createVNode(_component_el_row, {\n      class: \"layout-container\",\n      gutter: 20\n    }, {\n      default: _withCtx(() => [\n        _createVNode(_component_el_col, {\n          span: 12,\n          class: \"left-panel\"\n        }, {\n          default: _withCtx(() => [\n            _createVNode(_component_el_card, { class: \"control-card\" }, {\n              header: _withCtx(() => [\n                _createElementVNode(\"div\", _hoisted_2, [\n                  _cache[2] || (_cache[2] = _createElementVNode(\"div\", { class: \"title-section\" }, [\n                    _createElementVNode(\"h2\", null, \"Test Plans\")\n                  ], -1)),\n                  _createElementVNode(\"div\", _hoisted_3, [\n                    _createVNode(_component_el_button, {\n                      type: \"primary\",\n                      class: \"action-btn\",\n                      onClick: _ctx.handleOpenTestPlan\n                    }, {\n                      default: _withCtx(() => [\n                        _createVNode(_component_el_icon, null, {\n                          default: _withCtx(() => [\n                            _createVNode(_component_Folder)\n                          ]),\n                          _: 1\n                        }),\n                        _cache[0] || (_cache[0] = _createElementVNode(\"span\", null, \"Open\", -1))\n                      ]),\n                      _: 1\n                    }, 8, [\"onClick\"]),\n                    _createVNode(_component_el_button, {\n                      type: \"success\",\n                      class: \"action-btn\",\n                      onClick: _ctx.showCreateDialog\n                    }, {\n                      default: _withCtx(() => [\n                        _createVNode(_component_el_icon, null, {\n                          default: _withCtx(() => [\n                            _createVNode(_component_Plus)\n                          ]),\n                          _: 1\n                        }),\n                        _cache[1] || (_cache[1] = _createElementVNode(\"span\", null, \"Create\", -1))\n                      ]),\n                      _: 1\n                    }, 8, [\"onClick\"])\n                  ])\n                ])\n              ]),\n              default: _withCtx(() => [\n                _createElementVNode(\"div\", _hoisted_4, [\n                  _createVNode(_component_test_plan_history, {\n                    ref: \"historyComponent\",\n                    onOpenPlan: _ctx.handleOpenFromHistory\n                  }, null, 8, [\"onOpenPlan\"])\n                ])\n              ]),\n              _: 1\n            })\n          ]),\n          _: 1\n        }),\n        _createVNode(_component_el_col, {\n          span: 12,\n          class: \"right-panel\"\n        }, {\n          default: _withCtx(() => [\n            _createVNode(_component_el_card, { class: \"guide-card\" }, {\n              header: _withCtx(() => _cache[3] || (_cache[3] = [\n                _createElementVNode(\"div\", { class: \"panel-header\" }, [\n                  _createElementVNode(\"div\", { class: \"title-section\" }, [\n                    _createElementVNode(\"h2\", null, \"Guide\")\n                  ])\n                ], -1)\n              ])),\n              default: _withCtx(() => [\n                _createVNode(_component_test_plan_guide)\n              ]),\n              _: 1\n            })\n          ]),\n          _: 1\n        })\n      ]),\n      _: 1\n    }),\n    _createVNode(_component_create_test_plan, {\n      ref: \"createDialog\",\n      onCreated: _ctx.handleTestPlanCreated\n    }, null, 8, [\"onCreated\"])\n  ]))\n}", "<template>\n  <div class=\"home-view\">\n    <el-row class=\"layout-container\" :gutter=\"20\">\n      <!-- 左侧面板 -->\n      <el-col :span=\"12\" class=\"left-panel\">\n        <el-card class=\"control-card\">\n          <template #header>\n            <div class=\"panel-header\">\n              <div class=\"title-section\">\n                <h2>Test Plans</h2>\n              </div>\n              <div class=\"button-group\">\n                <el-button type=\"primary\" class=\"action-btn\" @click=\"handleOpenTestPlan\">\n                  <el-icon><Folder /></el-icon>\n                  <span>Open</span>\n                </el-button>\n                <el-button type=\"success\" class=\"action-btn\" @click=\"showCreateDialog\">\n                  <el-icon><Plus /></el-icon>\n                  <span>Create</span>\n                </el-button>\n              </div>\n            </div>\n          </template>\n\n          <!-- 历史记录 -->\n          <div class=\"history-container\">\n            <test-plan-history ref=\"historyComponent\" @open-plan=\"handleOpenFromHistory\" />\n          </div>\n        </el-card>\n      </el-col>\n\n      <!-- 右侧面板 -->\n      <el-col :span=\"12\" class=\"right-panel\">\n        <el-card class=\"guide-card\">\n          <template #header>\n            <div class=\"panel-header\">\n              <div class=\"title-section\">\n                <h2>Guide</h2>\n              </div>\n            </div>\n          </template>\n          <test-plan-guide />\n        </el-card>\n      </el-col>\n    </el-row>\n\n    <create-test-plan ref=\"createDialog\" @created=\"handleTestPlanCreated\" />\n  </div>\n</template>\n\n<script lang=\"ts\">\nimport { defineComponent, ref } from \"vue\";\nimport { Folder, Plus } from '@element-plus/icons-vue';\nimport { useRouter } from \"vue-router\";\nimport CreateTestPlan from '@/components/TestPlan/CreateTestPlan.vue';\nimport TestPlanHistory from '@/components/TestPlan/TestPlanHistory.vue';\nimport TestPlanGuide from '@/components/Guide/TestPlanGuide.vue';\nimport { testPlanService } from '@/services/testPlanService';\nimport type { CreateTestPlanInstance, TestPlanHistoryInstance } from '@/components/TestPlan/types';\n\nexport default defineComponent({\n  name: \"HomeView\",\n  components: {\n    CreateTestPlan,\n    TestPlanHistory,\n    TestPlanGuide,\n    Folder,\n    Plus\n  },\n  setup() {\n    const router = useRouter();\n    const createDialog = ref<CreateTestPlanInstance | null>(null);\n    const historyComponent = ref<TestPlanHistoryInstance | null>(null);\n\n    // 设置路由\n    testPlanService.setRouter(router);\n\n    // 处理打开测试计划\n    const handleOpenTestPlan = async () => {\n      await testPlanService.openFromExplorer();\n      historyComponent.value?.refresh();\n    };\n\n    // 从历史记录打开测试计划\n    const handleOpenFromHistory = async (path: string) => {\n      await testPlanService.openFromPath(path);\n      historyComponent.value?.refresh();\n    };\n\n    // 显示创建对话框\n    const showCreateDialog = () => {\n      createDialog.value?.show();\n    };\n\n    // 处理测试计划创建完成\n    const handleTestPlanCreated = () => {\n      historyComponent.value?.refresh();\n    };\n\n    return {\n      createDialog,\n      historyComponent,\n      handleOpenTestPlan,\n      showCreateDialog,\n      handleTestPlanCreated,\n      handleOpenFromHistory\n    };\n  },\n});\n</script>\n\n<style scoped>\n.home-view {\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  padding: 20px;\n  box-sizing: border-box;\n  background-color: #f5f7fa;\n}\n\n.layout-container {\n  flex: 1;\n  min-height: 0;\n}\n\n.left-panel, .right-panel {\n  height: 100%;\n}\n\n.control-card, .guide-card {\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  border-radius: 8px;\n}\n\n.panel-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  min-height: 32px;\n}\n\n.title-section {\n  display: flex;\n  align-items: center;\n}\n\n.panel-header h2 {\n  margin: 0;\n  font-weight: 600;\n  color: var(--el-text-color-primary);\n  font-size: 20px;\n}\n\n.button-group {\n  display: flex;\n  gap: 12px;\n}\n\n.action-btn {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 10px 16px;\n  justify-content: center;\n}\n\n.history-container {\n  flex: 1;\n}\n\n/* 响应式设计 */\n@media screen and (max-width: 1200px) {\n  .panel-header {\n    flex-direction: column;\n    align-items: flex-start;\n    gap: 16px;\n  }\n  \n  .button-group {\n    width: 100%;\n    justify-content: space-between;\n  }\n  \n  .action-btn {\n    flex: 1;\n    justify-content: center;\n  }\n}\n\n</style>\n", "import { createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, createVNode as _createVNode, withCtx as _withCtx, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, createElement<PERSON>lock as _createElementBlock, renderList as _renderList, Fragment as _Fragment, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, withModifiers as _withModifiers, normalizeClass as _normalizeClass, resolveDirective as _resolveDirective, withDirectives as _withDirectives } from \"vue\"\n\nconst _hoisted_1 = { class: \"history-section\" }\nconst _hoisted_2 = { class: \"history-header\" }\nconst _hoisted_3 = {\n  key: 0,\n  class: \"loading-container\"\n}\nconst _hoisted_4 = { class: \"history-list\" }\nconst _hoisted_5 = [\"onClick\"]\nconst _hoisted_6 = { class: \"item-grid\" }\nconst _hoisted_7 = { class: \"item-name\" }\nconst _hoisted_8 = { class: \"item-action\" }\nconst _hoisted_9 = [\"title\"]\nconst _hoisted_10 = { class: \"file-path\" }\nconst _hoisted_11 = { class: \"access-time\" }\n\nexport function render(_ctx: any,_cache: any,$props: any,$setup: any,$data: any,$options: any) {\n  const _component_font_awesome_icon = _resolveComponent(\"font-awesome-icon\")!\n  const _component_el_button = _resolveComponent(\"el-button\")!\n  const _component_el_skeleton = _resolveComponent(\"el-skeleton\")!\n  const _component_el_empty = _resolveComponent(\"el-empty\")!\n  const _component_el_tag = _resolveComponent(\"el-tag\")!\n  const _component_el_pagination = _resolveComponent(\"el-pagination\")!\n  const _directive_loading = _resolveDirective(\"loading\")!\n\n  return (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [\n    _createElementVNode(\"div\", _hoisted_2, [\n      _cache[0] || (_cache[0] = _createElementVNode(\"div\", { class: \"header-title\" }, [\n        _createElementVNode(\"h3\", null, \"Recently Opened\")\n      ], -1)),\n      (_ctx.historyList.length > 0)\n        ? (_openBlock(), _createBlock(_component_el_button, {\n            key: 0,\n            type: \"text\",\n            size: \"small\",\n            onClick: _ctx.handleClearHistory,\n            class: \"clear-btn\",\n            title: \"Clear history\"\n          }, {\n            default: _withCtx(() => [\n              _createVNode(_component_font_awesome_icon, { icon: \"trash-can\" })\n            ]),\n            _: 1\n          }, 8, [\"onClick\"]))\n        : _createCommentVNode(\"\", true)\n    ]),\n    (_ctx.loading)\n      ? (_openBlock(), _createElementBlock(\"div\", _hoisted_3, [\n          _createVNode(_component_el_skeleton, { rows: 5 })\n        ]))\n      : (!_ctx.historyList.length)\n        ? (_openBlock(), _createBlock(_component_el_empty, {\n            key: 1,\n            description: \"No recent items\",\n            \"image-size\": 150\n          }, {\n            image: _withCtx(() => [\n              _createVNode(_component_font_awesome_icon, {\n                icon: \"file-circle-exclamation\",\n                class: \"empty-icon\"\n              })\n            ]),\n            description: _withCtx(() => _cache[1] || (_cache[1] = [\n              _createElementVNode(\"p\", { class: \"empty-description\" }, \"No recent items\", -1),\n              _createElementVNode(\"p\", { class: \"empty-tip\" }, \"Recently opened plans will appear here\", -1)\n            ])),\n            _: 1\n          }))\n        : (_openBlock(), _createElementBlock(_Fragment, { key: 2 }, [\n            _withDirectives((_openBlock(), _createElementBlock(\"div\", _hoisted_4, [\n              (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(_ctx.displayedHistory, (item, index) => {\n                return (_openBlock(), _createElementBlock(\"div\", {\n                  key: item.id,\n                  class: _normalizeClass([\"history-item\", { 'history-item-alt': index % 2 === 1 }]),\n                  onClick: ($event: any) => (_ctx.handleOpenPlan(item.filePath))\n                }, [\n                  _createElementVNode(\"div\", _hoisted_6, [\n                    _createElementVNode(\"div\", _hoisted_7, [\n                      _createVNode(_component_el_tag, {\n                        size: \"small\",\n                        effect: \"plain\",\n                        class: \"plan-name\"\n                      }, {\n                        default: _withCtx(() => [\n                          _createTextVNode(_toDisplayString(item.planName), 1)\n                        ]),\n                        _: 2\n                      }, 1024)\n                    ]),\n                    _createElementVNode(\"div\", _hoisted_8, [\n                      _createVNode(_component_el_button, {\n                        type: \"text\",\n                        size: \"small\",\n                        onClick: _withModifiers(($event: any) => (_ctx.handleOpenFolder(item.filePath)), [\"stop\"]),\n                        title: \"Open in File Explorer\"\n                      }, {\n                        default: _withCtx(() => [\n                          _createVNode(_component_font_awesome_icon, { icon: \"folder-open\" })\n                        ]),\n                        _: 2\n                      }, 1032, [\"onClick\"])\n                    ]),\n                    _createElementVNode(\"div\", {\n                      class: \"item-path\",\n                      title: item.filePath\n                    }, [\n                      _createVNode(_component_font_awesome_icon, { icon: \"folder\" }),\n                      _createElementVNode(\"span\", _hoisted_10, _toDisplayString(item.filePath), 1)\n                    ], 8, _hoisted_9),\n                    _createElementVNode(\"div\", _hoisted_11, [\n                      _createVNode(_component_font_awesome_icon, { icon: \"clock\" }),\n                      _createTextVNode(\" \" + _toDisplayString(_ctx.formatDateTime(item.lastAccessTime)), 1)\n                    ])\n                  ])\n                ], 10, _hoisted_5))\n              }), 128))\n            ])), [\n              [_directive_loading, _ctx.loading]\n            ]),\n            (_ctx.historyList.length > _ctx.pageSize)\n              ? (_openBlock(), _createBlock(_component_el_pagination, {\n                  key: 0,\n                  layout: \"prev, pager, next\",\n                  total: _ctx.historyList.length,\n                  \"page-size\": _ctx.pageSize,\n                  \"current-page\": _ctx.currentPage,\n                  onCurrentChange: _ctx.handlePageChange,\n                  class: \"pagination\",\n                  background: \"\",\n                  \"hide-on-single-page\": \"\"\n                }, null, 8, [\"total\", \"page-size\", \"current-page\", \"onCurrentChange\"]))\n              : _createCommentVNode(\"\", true)\n          ], 64))\n  ]))\n}", "<template>\r\n  <div class=\"history-section\">\r\n    <div class=\"history-header\">\r\n      <div class=\"header-title\">\r\n        <h3>Recently Opened</h3>\r\n      </div>\r\n      <el-button\r\n        v-if=\"historyList.length > 0\"\r\n        type=\"text\"\r\n        size=\"small\"\r\n        @click=\"handleClearHistory\"\r\n        class=\"clear-btn\"\r\n        title=\"Clear history\"\r\n      >\r\n        <font-awesome-icon icon=\"trash-can\" />\r\n      </el-button>\r\n    </div>\r\n\r\n    <!-- 显示加载状态 -->\r\n    <div v-if=\"loading\" class=\"loading-container\">\r\n      <el-skeleton :rows=\"5\" />\r\n    </div>\r\n\r\n    <el-empty\r\n      v-else-if=\"!historyList.length\"\r\n      description=\"No recent items\"\r\n      :image-size=\"150\"\r\n    >\r\n      <template #image>\r\n        <font-awesome-icon icon=\"file-circle-exclamation\" class=\"empty-icon\" />\r\n      </template>\r\n      <template #description>\r\n        <p class=\"empty-description\">No recent items</p>\r\n        <p class=\"empty-tip\">Recently opened plans will appear here</p>\r\n      </template>\r\n    </el-empty>\r\n\r\n    <template v-else>\r\n      <div class=\"history-list\" v-loading=\"loading\">\r\n        <div\r\n          v-for=\"(item, index) in displayedHistory\"\r\n          :key=\"item.id\"\r\n          class=\"history-item\"\r\n          :class=\"{ 'history-item-alt': index % 2 === 1 }\"\r\n          @click=\"handleOpenPlan(item.filePath)\"\r\n        >\r\n          <div class=\"item-grid\">\r\n            <!-- 左上角: 计划名称 -->\r\n            <div class=\"item-name\">\r\n              <el-tag size=\"small\" effect=\"plain\" class=\"plan-name\">{{ item.planName }}</el-tag>\r\n            </div>\r\n\r\n            <!-- 右上角: 打开文件夹按钮 -->\r\n            <div class=\"item-action\">\r\n              <el-button\r\n                type=\"text\"\r\n                size=\"small\"\r\n                @click.stop=\"handleOpenFolder(item.filePath)\"\r\n                title=\"Open in File Explorer\"\r\n              >\r\n                <font-awesome-icon icon=\"folder-open\" />\r\n              </el-button>\r\n            </div>\r\n\r\n            <!-- 左下角: 文件路径 -->\r\n            <div class=\"item-path\" :title=\"item.filePath\">\r\n              <font-awesome-icon icon=\"folder\" />\r\n              <span class=\"file-path\">{{ item.filePath }}</span>\r\n            </div>\r\n\r\n            <!-- 右下角: 最近访问时间 -->\r\n            <div class=\"access-time\">\r\n              <font-awesome-icon icon=\"clock\" />\r\n              {{ formatDateTime(item.lastAccessTime) }}\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <el-pagination\r\n        v-if=\"historyList.length > pageSize\"\r\n        layout=\"prev, pager, next\"\r\n        :total=\"historyList.length\"\r\n        :page-size=\"pageSize\"\r\n        :current-page=\"currentPage\"\r\n        @current-change=\"handlePageChange\"\r\n        class=\"pagination\"\r\n        background\r\n        hide-on-single-page\r\n      />\r\n    </template>\r\n  </div>\r\n</template>\r\n\r\n<script lang=\"ts\">\r\nimport { defineComponent, ref, onMounted, computed } from 'vue';\r\nimport { testPlanService } from '@/services/testPlanService';\r\nimport { ElMessageBox } from 'element-plus';\r\nimport { explorerApi } from '@/api/explorerApi';\r\nimport { testPlanApi } from '@/api/testPlanApi';\r\nimport { testPlanHistoryApi } from '@/api/testPlanHistoryApi';\r\n\r\nexport default defineComponent({\r\n  name: 'TestPlanHistory',\r\n  emits: ['open-plan'],\r\n  setup(props, { emit, expose }) {\r\n    const state = testPlanService.getState();\r\n    const pageSize = 20;\r\n    const currentPage = ref(1);\r\n    const loading = ref(true); // 确保初始时为加载状态\r\n\r\n    const historyList = computed(() => state.recentPlans);\r\n\r\n    const displayedHistory = computed(() => {\r\n      const start = (currentPage.value - 1) * pageSize;\r\n      const end = start + pageSize;\r\n      return historyList.value.slice(start, end);\r\n    });\r\n\r\n    const fetchHistory = async () => {\r\n      loading.value = true; // 开始加载时设置loading为true\r\n      try {\r\n        await testPlanService.loadRecentPlans();\r\n        currentPage.value = 1; // 重置页码\r\n      } finally {\r\n        loading.value = false; // 加载完成后设置loading为false\r\n      }\r\n    };\r\n\r\n    const handlePageChange = (page: number) => {\r\n      currentPage.value = page;\r\n    };\r\n\r\n    const handleClearHistory = () => {\r\n      ElMessageBox.confirm(\r\n        'Are you sure you want to clear all recent items?',\r\n        'Warning',\r\n        {\r\n          confirmButtonText: 'Confirm',\r\n          cancelButtonText: 'Cancel',\r\n          type: 'warning'\r\n        }\r\n      ).then(async () => {\r\n        await testPlanService.clearHistory();\r\n      }).catch(() => {\r\n        // 用户取消操作，不需要做任何处理\r\n      });\r\n    };\r\n\r\n    const handleOpenPlan = async (path: string) => {\r\n      try {\r\n        // 先检查文件是否存在\r\n        const response = await testPlanApi.checkFileExists(path);\r\n\r\n        if (response.data.exists) {\r\n          // 文件存在，正常打开\r\n          emit('open-plan', path);\r\n        } else {\r\n          // 文件不存在，提示用户\r\n          ElMessageBox.confirm(\r\n            '测试计划文件不存在，是否从历史记录中删除该条目？',\r\n            '文件不存在',\r\n            {\r\n              confirmButtonText: '删除',\r\n              cancelButtonText: '取消',\r\n              type: 'warning'\r\n            }\r\n          ).then(async () => {\r\n            // 用户选择删除\r\n            await testPlanHistoryApi.deleteRecord(path);\r\n            // 刷新历史记录\r\n            fetchHistory();\r\n          }).catch(() => {\r\n            // 用户取消，不做任何操作\r\n          });\r\n        }\r\n      } catch (error) {\r\n        console.error('检查文件存在性失败:', error);\r\n      }\r\n    };\r\n\r\n    const handleOpenFolder = async (path: string) => {\r\n      try {\r\n        await explorerApi.openExplorer(path);\r\n      } catch (error) {\r\n        console.error('Failed to open folder', error);\r\n      }\r\n    };\r\n\r\n    const formatDateTime = (dateTimeStr: string) => {\r\n      const date = new Date(dateTimeStr);\r\n      return date.toLocaleString('en-US', {\r\n        year: 'numeric',\r\n        month: '2-digit',\r\n        day: '2-digit',\r\n        hour: '2-digit',\r\n        minute: '2-digit'\r\n      });\r\n    };\r\n\r\n    // 组件挂载时获取历史记录\r\n    onMounted(fetchHistory);\r\n\r\n    // 暴露刷新方法给父组件\r\n    expose({\r\n      refresh: fetchHistory\r\n    });\r\n\r\n    return {\r\n      historyList,\r\n      displayedHistory,\r\n      currentPage,\r\n      pageSize,\r\n      loading, // 返回loading变量\r\n      handleClearHistory,\r\n      handleOpenPlan,\r\n      handleOpenFolder,\r\n      formatDateTime,\r\n      handlePageChange\r\n    };\r\n  }\r\n});\r\n</script>\r\n\r\n<style scoped>\r\n.history-section {\r\n  display: flex;\r\n  flex-direction: column;\r\n  height: 100%;\r\n}\r\n\r\n.history-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 16px;\r\n  padding-bottom: 12px;\r\n  border-bottom: 1px solid var(--el-border-color-lighter);\r\n}\r\n\r\n.header-title {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n}\r\n\r\n.header-title h3 {\r\n  margin: 0;\r\n  font-size: 14px;\r\n  font-weight: 500;\r\n}\r\n\r\n.header-icon {\r\n  color: var(--el-color-primary);\r\n  font-size: 18px;\r\n}\r\n\r\n.clear-btn {\r\n  color: var(--el-text-color-primary);\r\n  padding: 4px;\r\n  font-size: 16px;\r\n}\r\n\r\n.empty-icon {\r\n  font-size: 60px;\r\n  color: #DDD;\r\n}\r\n\r\n.empty-description {\r\n  margin: 10px 0 0;\r\n  font-size: 16px;\r\n  color: var(--el-text-color-secondary);\r\n}\r\n\r\n.empty-tip {\r\n  margin: 5px 0 0;\r\n  font-size: 14px;\r\n  color: var(--el-text-color-placeholder);\r\n}\r\n\r\n.history-list {\r\n  flex: 1;\r\n  overflow-y: auto;\r\n}\r\n\r\n.history-item {\r\n  padding: 12px 16px;\r\n  margin-bottom: 8px;\r\n  border-radius: 6px;\r\n  background-color: var(--el-bg-color-page);\r\n  border-left: 3px solid var(--el-color-primary-light-5);\r\n  cursor: pointer;\r\n}\r\n\r\n.history-item:hover {\r\n  background-color: var(--el-color-primary-light-9);\r\n}\r\n\r\n.history-item-alt {\r\n  background-color: var(--el-fill-color-lighter);\r\n  border-left-color: var(--el-color-success-light-5);\r\n}\r\n\r\n.history-item-alt:hover {\r\n  background-color: var(--el-color-success-light-9);\r\n}\r\n\r\n.item-grid {\r\n  display: grid;\r\n  grid-template-columns: 1fr auto;\r\n  grid-template-rows: auto auto;\r\n  grid-gap: 8px;\r\n  width: 100%;\r\n}\r\n\r\n.item-name {\r\n  grid-column: 1;\r\n  grid-row: 1;\r\n}\r\n\r\n.item-action {\r\n  margin-right: -10px;\r\n  grid-column: 2;\r\n  grid-row: 1;\r\n  justify-self: end;\r\n}\r\n\r\n.item-path {\r\n  grid-column: 1;\r\n  grid-row: 2;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n  color: var(--el-text-color-secondary);\r\n  font-size: 13px;\r\n}\r\n\r\n.access-time {\r\n  grid-column: 2;\r\n  grid-row: 2;\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 4px;\r\n  font-size: 12px;\r\n  color: var(--el-text-color-secondary);\r\n  justify-self: end;\r\n}\r\n\r\n.plan-name {\r\n  font-weight: 500;\r\n  padding: 4px 8px;\r\n  max-width: 200px;\r\n  overflow: hidden;\r\n  white-space: nowrap;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n.file-path {\r\n  overflow: hidden;\r\n  white-space: nowrap;\r\n  text-overflow: ellipsis;\r\n}\r\n\r\n/* 移除不再需要的样式 */\r\n.item-content,\r\n.item-header,\r\n.item-actions {\r\n  display: none;\r\n}\r\n\r\n.pagination {\r\n  margin-top: 16px;\r\n  text-align: center;\r\n}\r\n\r\n/* 添加加载容器样式 */\r\n.loading-container {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  padding: 20px;\r\n  min-height: 200px;\r\n}\r\n\r\n</style>\r\n", "import { render } from \"./TestPlanHistory.vue?vue&type=template&id=d6b8a97a&scoped=true&ts=true\"\nimport script from \"./TestPlanHistory.vue?vue&type=script&lang=ts\"\nexport * from \"./TestPlanHistory.vue?vue&type=script&lang=ts\"\n\nimport \"./TestPlanHistory.vue?vue&type=style&index=0&id=d6b8a97a&scoped=true&lang=css\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-d6b8a97a\"]])\n\nexport default __exports__", "import { defineComponent as _defineComponent } from 'vue'\nimport { renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, createVNode as _createVNode } from \"vue\"\n\nconst _hoisted_1 = { class: \"guide-content\" }\nconst _hoisted_2 = { class: \"step-content\" }\nconst _hoisted_3 = { class: \"step-header\" }\nconst _hoisted_4 = { class: \"step-title\" }\nconst _hoisted_5 = { class: \"step-desc\" }\n\n\nexport default /*@__PURE__*/_defineComponent({\n  __name: 'TestPlanGuide',\n  setup(__props) {\n\r\n\r\nconst steps = [\r\n  {\r\n    title: 'Basic Setting',\r\n    description: 'Configure test plan name, description and select test suite type',\r\n  },\r\n  {\r\n    title: 'Hardware Setting',\r\n    description: 'Set up CAN/CANFD hardware type and configure baud rate parameters',\r\n  },\r\n  {\r\n    title: 'Interoperation',\r\n    description: 'Verify hardware connection and protocol compatibility',\r\n  },\r\n  {\r\n    title: 'Test Cases',\r\n    description: 'Select coverage intensity and generate test cases based on supported services',\r\n  },\r\n  {\r\n    title: 'Test Run',\r\n    description: 'Run test cases with start and stop control',\r\n  },\r\n  {\r\n    title: 'Report',\r\n    description: 'View test reports and analyze data with detailed results',\r\n  }\r\n]\r\n\nreturn (_ctx: any,_cache: any) => {\n  const _component_el_timeline_item = _resolveComponent(\"el-timeline-item\")!\n  const _component_el_timeline = _resolveComponent(\"el-timeline\")!\n\n  return (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [\n    _createVNode(_component_el_timeline, null, {\n      default: _withCtx(() => [\n        (_openBlock(), _createElementBlock(_Fragment, null, _renderList(steps, (step) => {\n          return _createVNode(_component_el_timeline_item, {\n            key: step.title,\n            type: 'info',\n            size: 'normal',\n            timestamp: ``\n          }, {\n            default: _withCtx(() => [\n              _createElementVNode(\"div\", _hoisted_2, [\n                _createElementVNode(\"div\", _hoisted_3, [\n                  _createElementVNode(\"span\", _hoisted_4, _toDisplayString(step.title), 1)\n                ]),\n                _createElementVNode(\"p\", _hoisted_5, _toDisplayString(step.description), 1)\n              ])\n            ]),\n            _: 2\n          }, 1024)\n        }), 64))\n      ]),\n      _: 1\n    })\n  ]))\n}\n}\n\n})", "<template>\r\n  <div class=\"guide-content\">\r\n    <el-timeline>\r\n      <el-timeline-item\r\n        v-for=\"step in steps\"\r\n        :key=\"step.title\"\r\n        :type=\"'info'\"\r\n        :size=\"'normal'\"\r\n        :timestamp=\"``\"\r\n      >\r\n        <div class=\"step-content\">\r\n          <div class=\"step-header\">\r\n            <span class=\"step-title\">{{ step.title }}</span>\r\n          </div>\r\n          <p class=\"step-desc\">{{ step.description }}</p>\r\n        </div>\r\n      </el-timeline-item>\r\n    </el-timeline>\r\n  </div>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\n\r\nconst steps = [\r\n  {\r\n    title: 'Basic Setting',\r\n    description: 'Configure test plan name, description and select test suite type',\r\n  },\r\n  {\r\n    title: 'Hardware Setting',\r\n    description: 'Set up CAN/CANFD hardware type and configure baud rate parameters',\r\n  },\r\n  {\r\n    title: 'Interoperation',\r\n    description: 'Verify hardware connection and protocol compatibility',\r\n  },\r\n  {\r\n    title: 'Test Cases',\r\n    description: 'Select coverage intensity and generate test cases based on supported services',\r\n  },\r\n  {\r\n    title: 'Test Run',\r\n    description: 'Run test cases with start and stop control',\r\n  },\r\n  {\r\n    title: 'Report',\r\n    description: 'View test reports and analyze data with detailed results',\r\n  }\r\n]\r\n</script>\r\n\r\n<style scoped>\r\n.guide-content {\r\n  padding: 10px 16px;\r\n  max-width: 800px;\r\n  margin: 0 auto;\r\n  max-height: 100vh;\r\n}\r\n\r\n:deep(.el-timeline) {\r\n  padding: 0 15px;\r\n}\r\n\r\n:deep(.el-timeline-item__node) {\r\n  background-color: var(--el-color-primary);\r\n  top: 10px;\r\n}\r\n\r\n:deep(.el-timeline-item__tail) {\r\n  top: 10px;\r\n}\r\n\r\n:deep(.el-timeline-item__node--primary) {\r\n  background-color: var(--el-color-primary);\r\n}\r\n\r\n:deep(.el-timeline-item__node--success) {\r\n  background-color: var(--el-color-success);\r\n}\r\n\r\n:deep(.el-timeline-item__timestamp) {\r\n  color: var(--el-text-color-secondary);\r\n  font-weight: bold;\r\n}\r\n\r\n:deep(.el-timeline-item__content) {\r\n  position: relative;\r\n  top: -4px; /* 微调内容垂直位置以保持对齐 */\r\n}\r\n\r\n:deep(.el-timeline-item) {\r\n  padding-bottom: 15px; /* 减小项目间距 */\r\n}\r\n\r\n.step-content {\r\n  background: var(--el-bg-color-page);\r\n  border-radius: 4px;\r\n  padding: 10px 20px;\r\n  margin-bottom: 10px;\r\n  box-shadow: var(--el-box-shadow-lighter);\r\n}\r\n\r\n.step-title {\r\n  font-size: 16px; /* 减小标题字体大小 */\r\n  font-weight: bold;\r\n  color: var(--el-color-primary);\r\n  margin-bottom: 5px; /* 减小底部外边距 */\r\n  display: block;\r\n}\r\n\r\n.step-desc {\r\n  color: var(--el-text-color-regular);\r\n  margin: 5px 0 0; /* 减小上方外边距 */\r\n  font-size: 14px;\r\n  line-height: 1.4; /* 稍微减小行高 */\r\n}\r\n\r\n:deep(.el-timeline-item:last-child .el-timeline-item__tail) {\r\n  display: none;\r\n}\r\n</style>\r\n", "import script from \"./TestPlanGuide.vue?vue&type=script&setup=true&lang=ts\"\nexport * from \"./TestPlanGuide.vue?vue&type=script&setup=true&lang=ts\"\n\nimport \"./TestPlanGuide.vue?vue&type=style&index=0&id=6570f07b&scoped=true&lang=css\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-6570f07b\"]])\n\nexport default __exports__", "import { render } from \"./HomeView.vue?vue&type=template&id=dc9ab15c&scoped=true&ts=true\"\nimport script from \"./HomeView.vue?vue&type=script&lang=ts\"\nexport * from \"./HomeView.vue?vue&type=script&lang=ts\"\n\nimport \"./HomeView.vue?vue&type=style&index=0&id=dc9ab15c&scoped=true&lang=css\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['render',render],['__scopeId',\"data-v-dc9ab15c\"]])\n\nexport default __exports__", "import { defineComponent as _defineComponent } from 'vue'\nimport { renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, unref as _unref, createVNode as _createVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, withModifiers as _withModifiers, normalizeClass as _normalizeClass, createBlock as _createBlock, createCommentVNode as _createCommentVNode } from \"vue\"\n\nconst _hoisted_1 = { class: \"test-suite-container\" }\nconst _hoisted_2 = { class: \"suites-list\" }\nconst _hoisted_3 = { class: \"suite-header\" }\nconst _hoisted_4 = { class: \"version\" }\nconst _hoisted_5 = { class: \"packages-list\" }\nconst _hoisted_6 = [\"onClick\"]\n\nimport { ref, onMounted } from 'vue'\r\nimport { ElMessage } from 'element-plus'\r\nimport { Document } from '@element-plus/icons-vue'\r\nimport XmlViewer from '@/components/TestSuite/XmlViewer.vue'\r\nimport { testSuiteApi, type TestSuite, type SequencePackage } from '@/api/testSuiteApi'\r\n\r\n\nexport default /*@__PURE__*/_defineComponent({\n  __name: 'TestSuiteView',\n  setup(__props) {\n\r\nconst testSuites = ref<TestSuite[]>([])\r\nconst selectedSuite = ref<TestSuite | null>(null)\r\nconst xmlContent = ref('')\r\nconst selectedPackageIndex = ref<number>(-1)\r\nconst selectedPackageName = ref('')\r\n\r\nconst loadTestSuites = async () => {\r\n  try {\r\n    const response = await testSuiteApi.getBuiltIn()\r\n    testSuites.value = response.data\r\n  } catch (error) {\r\n    ElMessage.error('Failed to load test suites')\r\n    console.error('Error loading test suites:', error)\r\n  }\r\n}\r\n\r\nconst handleSuiteClick = async (suite: TestSuite) => {\r\n  if (selectedSuite.value?.name === suite.name) return\r\n  selectedSuite.value = suite\r\n  selectedPackageIndex.value = -1\r\n  selectedPackageName.value = ''\r\n  xmlContent.value = ''\r\n}\r\n\r\nconst handlePackageClick = async (suite: TestSuite, index: number, sequencePackage: SequencePackage) => {\r\n  selectedSuite.value = suite\r\n  selectedPackageIndex.value = index\r\n  selectedPackageName.value = sequencePackage.name\r\n  try {\r\n    const response = await testSuiteApi.getBuiltInXml(suite.name, sequencePackage.name)\r\n    xmlContent.value = response.data\r\n  } catch (error) {\r\n    ElMessage.error('Failed to load XML content')\r\n    console.error('Error loading XML:', error)\r\n  }\r\n}\r\n\r\nonMounted(() => {\r\n  loadTestSuites()\r\n})\r\n\nreturn (_ctx: any,_cache: any) => {\n  const _component_el_icon = _resolveComponent(\"el-icon\")!\n  const _component_el_card = _resolveComponent(\"el-card\")!\n\n  return (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [\n    _createElementVNode(\"div\", _hoisted_2, [\n      (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(testSuites.value, (suite) => {\n        return (_openBlock(), _createBlock(_component_el_card, {\n          key: suite.name,\n          class: _normalizeClass([\"suite-card\", { 'active': selectedSuite.value?.name === suite.name }]),\n          onClick: ($event: any) => (handleSuiteClick(suite))\n        }, {\n          header: _withCtx(() => [\n            _createElementVNode(\"div\", _hoisted_3, [\n              _createElementVNode(\"h3\", null, _toDisplayString(suite.name), 1),\n              _createElementVNode(\"span\", _hoisted_4, \"v\" + _toDisplayString(suite.version), 1)\n            ])\n          ]),\n          default: _withCtx(() => [\n            _createElementVNode(\"div\", _hoisted_5, [\n              (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(suite.packages, (sequencePackage, index) => {\n                return (_openBlock(), _createElementBlock(\"div\", {\n                  key: index,\n                  class: _normalizeClass([\"package-item\", { 'active': selectedSuite.value?.name === suite.name && selectedPackageIndex.value === index }]),\n                  onClick: _withModifiers(($event: any) => (handlePackageClick(suite, index, sequencePackage)), [\"stop\"])\n                }, [\n                  _createVNode(_component_el_icon, null, {\n                    default: _withCtx(() => [\n                      _createVNode(_unref(Document))\n                    ]),\n                    _: 1\n                  }),\n                  _createElementVNode(\"span\", null, _toDisplayString(sequencePackage.name), 1)\n                ], 10, _hoisted_6))\n              }), 128))\n            ])\n          ]),\n          _: 2\n        }, 1032, [\"class\", \"onClick\"]))\n      }), 128))\n    ]),\n    (selectedSuite.value)\n      ? (_openBlock(), _createBlock(XmlViewer, {\n          key: 0,\n          title: `${selectedSuite.value.name} - ${selectedPackageName.value}`,\n          content: xmlContent.value\n        }, null, 8, [\"title\", \"content\"]))\n      : _createCommentVNode(\"\", true)\n  ]))\n}\n}\n\n})", "<template>\r\n  <div class=\"test-suite-container\">\r\n    <!-- 左侧测试套件列表 -->\r\n    <div class=\"suites-list\">\r\n      <el-card v-for=\"suite in testSuites\" \r\n               :key=\"suite.name\" \r\n               class=\"suite-card\"\r\n               :class=\"{ 'active': selectedSuite?.name === suite.name }\"\r\n               @click=\"handleSuiteClick(suite)\">\r\n        <template #header>\r\n          <div class=\"suite-header\">\r\n            <h3>{{ suite.name }}</h3>\r\n            <span class=\"version\">v{{ suite.version }}</span>\r\n          </div>\r\n        </template>\r\n        <div class=\"packages-list\">\r\n          <div v-for=\"(sequencePackage, index) in suite.packages\" \r\n               :key=\"index\" \r\n               class=\"package-item\"\r\n               :class=\"{ 'active': selectedSuite?.name === suite.name && selectedPackageIndex === index }\"\r\n               @click.stop=\"handlePackageClick(suite, index, sequencePackage)\">\r\n            <el-icon><Document /></el-icon>\r\n            <span>{{ sequencePackage.name }}</span>\r\n          </div>\r\n        </div>\r\n      </el-card>\r\n    </div>\r\n\r\n    <!-- 右侧XML查看器 -->\r\n    <XmlViewer \r\n      v-if=\"selectedSuite\"\r\n      :title=\"`${selectedSuite.name} - ${selectedPackageName}`\"\r\n      :content=\"xmlContent\"\r\n    />\r\n  </div>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { ref, onMounted } from 'vue'\r\nimport { ElMessage } from 'element-plus'\r\nimport { Document } from '@element-plus/icons-vue'\r\nimport XmlViewer from '@/components/TestSuite/XmlViewer.vue'\r\nimport { testSuiteApi, type TestSuite, type SequencePackage } from '@/api/testSuiteApi'\r\n\r\nconst testSuites = ref<TestSuite[]>([])\r\nconst selectedSuite = ref<TestSuite | null>(null)\r\nconst xmlContent = ref('')\r\nconst selectedPackageIndex = ref<number>(-1)\r\nconst selectedPackageName = ref('')\r\n\r\nconst loadTestSuites = async () => {\r\n  try {\r\n    const response = await testSuiteApi.getBuiltIn()\r\n    testSuites.value = response.data\r\n  } catch (error) {\r\n    ElMessage.error('Failed to load test suites')\r\n    console.error('Error loading test suites:', error)\r\n  }\r\n}\r\n\r\nconst handleSuiteClick = async (suite: TestSuite) => {\r\n  if (selectedSuite.value?.name === suite.name) return\r\n  selectedSuite.value = suite\r\n  selectedPackageIndex.value = -1\r\n  selectedPackageName.value = ''\r\n  xmlContent.value = ''\r\n}\r\n\r\nconst handlePackageClick = async (suite: TestSuite, index: number, sequencePackage: SequencePackage) => {\r\n  selectedSuite.value = suite\r\n  selectedPackageIndex.value = index\r\n  selectedPackageName.value = sequencePackage.name\r\n  try {\r\n    const response = await testSuiteApi.getBuiltInXml(suite.name, sequencePackage.name)\r\n    xmlContent.value = response.data\r\n  } catch (error) {\r\n    ElMessage.error('Failed to load XML content')\r\n    console.error('Error loading XML:', error)\r\n  }\r\n}\r\n\r\nonMounted(() => {\r\n  loadTestSuites()\r\n})\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.test-suite-container {\r\n  padding: 20px;\r\n  display: flex;\r\n  gap: 20px;\r\n  height: calc(100vh - 80px);\r\n}\r\n\r\n.suites-list {\r\n  width: 300px;\r\n  overflow-y: auto;\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 20px;\r\n}\r\n\r\n.suite-card {\r\n  cursor: pointer;\r\n  \r\n  &.active {\r\n    border-color: var(--el-color-primary);\r\n  }\r\n}\r\n\r\n.suite-header {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n\r\n  h3 {\r\n    margin: 0;\r\n    flex: 1;\r\n  }\r\n\r\n  .version {\r\n    color: var(--el-text-color-secondary);\r\n    font-size: 0.9em;\r\n  }\r\n}\r\n\r\n.packages-list {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 8px;\r\n}\r\n\r\n.package-item {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 8px;\r\n  padding: 8px;\r\n  border-radius: 4px;\r\n  \r\n  &:hover {\r\n    background-color: var(--el-fill-color-light);\r\n  }\r\n\r\n  .el-icon {\r\n    color: var(--el-text-color-secondary);\r\n  }\r\n  \r\n  &.active {\r\n    background-color: var(--el-color-primary-light-9);\r\n    color: var(--el-color-primary);\r\n    \r\n    .el-icon {\r\n      color: var(--el-color-primary);\r\n    }\r\n  }\r\n}\r\n</style>\r\n", "import script from \"./TestSuiteView.vue?vue&type=script&setup=true&lang=ts\"\nexport * from \"./TestSuiteView.vue?vue&type=script&setup=true&lang=ts\"\n\nimport \"./TestSuiteView.vue?vue&type=style&index=0&id=0f6f8041&scoped=true&lang=scss\"\n\nimport exportComponent from \"../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-0f6f8041\"]])\n\nexport default __exports__", "import { createRouter, create<PERSON>eb<PERSON><PERSON><PERSON>, RouteRecordRaw } from 'vue-router'\nimport HomeView from '../views/HomeView.vue'\nimport TestSuiteView from '@/views/TestSuiteView.vue'\nimport { testPlanService } from '@/services/testPlanService';\nimport { ElMessage } from 'element-plus';\n\nconst routes: Array<RouteRecordRaw> = [\n  {\n    path: '/',\n    name: 'home',\n    component: HomeView\n  },\n  {\n    path: '/test-plan',\n    name: 'test-plan',\n    component: () => import('../views/TestPlanView.vue'),\n    props: route => ({ path: route.query.path }),\n    children: [\n      {\n        path: '',\n        name: 'test-plan.basic-setting',\n        component: () => import('../views/testplan/BasicSetting.vue')\n      },\n      {\n        path: 'basic-settings',\n        name: 'test-plan.basic-settings',\n        component: () => import('../views/testplan/BasicSetting.vue')\n      },\n      {\n        path: 'hardware',\n        name: 'test-plan.hardware',\n        component: () => import('../views/testplan/HardwareSetting.vue')\n      },\n      {\n        path: 'case-setting',\n        name: 'test-plan.case-setting',\n        component: () => import('../views/testplan/CaseSetting.vue')\n      },\n      {\n        path: 'interoperation',\n        name: 'test-plan.interoperation',\n        component: () => import('../views/testplan/Interoperation.vue')\n      },\n      {\n        path: 'test-cases',\n        name: 'test-plan.test-cases',\n        component: () => import('../views/testplan/TestCases.vue')\n      },\n      {\n        path: 'test-run',\n        name: 'test-plan.test-run',\n        component: () => import('../views/testplan/TestRun.vue')\n      },\n      {\n        path: 'test-results',\n        name: 'test-plan.test-results',\n        component: () => import('../views/testplan/TestResults.vue')\n      },\n      {\n        path: 'sequence-setting',\n        name: 'test-plan.sequence-setting',\n        component: () => import('../views/testplan/SequenceSetting.vue')\n      }\n    ]\n  },\n  {\n    path: '/test-suite',\n    name: 'test-suite',\n    component: TestSuiteView\n  },\n  {\n    path: '/about',\n    name: 'about',\n    component: () => import('../views/AboutView.vue')\n  }\n]\n\nconst router = createRouter({\n  history: createWebHistory(),\n  routes\n})\n\n// 添加路由守卫\nrouter.beforeEach(async (to, from, next) => {\n  // 初始化路由\n  testPlanService.setRouter(router);\n  \n  // 访问测试计划相关页面时检查是否有当前测试计划\n  if (to.path.startsWith('/test-plan') || to.path.startsWith('/testplan')) {\n    const state = testPlanService.getState();\n    if (!state.currentPlan) {\n      const currentPlan = await testPlanService.getCurrentPlan();\n      if (!currentPlan) {\n        next('/');\n        ElMessage.warning(\"No test plan is currently open\");\n        return;\n      }\n    }\n  }\n  next();\n});\n\nexport default router\n", "import { createStore } from \"vuex\";\n\nexport default createStore({\n  state: {},\n  getters: {},\n  mutations: {},\n  actions: {},\n  modules: {},\n});\n", "import axios from 'axios';\nimport { ElMessage, ElMessageBox } from 'element-plus';\n\n// 格式化错误信息，显示所有层级的异常\nconst formatErrorMessage = (error: any): string => {\n  if (!error.response || !error.response.data) {\n    return error.message || 'Unknown error';\n  }\n\n  const errorData = error.response.data;\n  const errorMessages = [];\n\n  // 添加主异常信息\n  if (errorData.exceptionMessage) {\n    errorMessages.push(errorData.exceptionMessage);\n  }\n\n  // 递归添加所有内部异常信息\n  let currentException = errorData.innerException;\n  while (currentException) {\n    if (currentException.exceptionMessage) {\n      errorMessages.push(currentException.exceptionMessage);\n    }\n    currentException = currentException.innerException;\n  }\n\n  // 如果没有找到任何异常信息，返回通用错误消息\n  if (errorMessages.length === 0) {\n    return errorData.message || 'An error occurred';\n  }\n\n  // 返回所有异常信息，每个一行\n  return errorMessages.join('<br>');\n};\n\n// 显示详细错误信息\nconst showDetailedError = (error: any): void => {\n  if (!error.response || !error.response.data) {\n    ElMessage.error(error.message || 'Unknown error');\n    return;\n  }\n\n  // 获取格式化的错误信息\n  const errorMessage = formatErrorMessage(error);\n\n  // 使用对话框显示详细错误信息\n  ElMessageBox.alert(\n    errorMessage,\n    'Error',\n    {\n      confirmButtonText: 'OK',\n      dangerouslyUseHTMLString: true,\n      closeOnClickModal: true,  // 允许点击空白区域关闭\n      closeOnPressEscape: true, // 允许按ESC键关闭\n      showClose: true           // 显示右上角关闭按钮\n    }\n  );\n};\n\n// 检查是否为用户取消操作\nconst isUserCanceled = (error: any): boolean => {\n  // 检查错误响应数据\n  if (error.response && error.response.data) {\n    // 检查直接等于字符串的情况\n    if (error.response.data === 'UserCanceled') {\n      return true;\n    }\n\n    // 检查错误消息字段\n    if (error.response.data.message === 'UserCanceled') {\n      return true;\n    }\n\n    // 检查错误代码字段\n    if (error.response.data.errorCode === 'UserCanceled') {\n      return true;\n    }\n  }\n\n  return false;\n};\n\n// 设置响应拦截器\nexport const setupErrorHandler = (): void => {\n  axios.interceptors.response.use(\n    response => response,\n    error => {\n      // 检查是否为用户取消操作\n      if (isUserCanceled(error)) {\n        // 用户取消操作，显示信息提示而不是错误\n        ElMessage.info(\"Operation cancelled by user\");\n\n        // 继续抛出错误，以便调用者可以进行额外处理\n        return Promise.reject(error);\n      }\n\n      // 处理其他错误\n      showDetailedError(error);\n\n      // 继续抛出错误，以便调用者可以进行额外处理\n      return Promise.reject(error);\n    }\n  );\n};\n\nexport default setupErrorHandler;\n", "import { createApp } from 'vue'\nimport App from './App.vue'\nimport router from './router'\nimport store from './store'\nimport { appApi, type ErrorData } from './api/appApi' // 导入 appApi 而不是 axios\nimport { setupErrorHandler } from './utils/errorHandler' // 导入错误处理器\n\n// 引入 Element Plus\nimport ElementPlus from 'element-plus'\nimport 'element-plus/dist/index.css'\nimport zhCn from 'element-plus/dist/locale/zh-cn.mjs'\n\n// 设置 Element Plus 主题变量\nimport * as ElementPlusIconsVue from '@element-plus/icons-vue'\nimport './styles/element-variables.css' // 需要创建这个文件来自定义主题\n\n// 引入 FontAwesome\nimport { library } from '@fortawesome/fontawesome-svg-core'\nimport { FontAwesomeIcon } from '@fortawesome/vue-fontawesome'\nimport {\n  faCogs, faFolderOpen, faPlus, faFileAlt,\n  faHistory, faTrashCan, faFileExcel, faClock,\n  faFolder, faChartBar, faProjectDiagram,\n  faClockRotateLeft, faFileCircleExclamation,\n  faAngleDown, faAngleUp, faExpand, faCompress,\n  faUpRightAndDownLeftFromCenter, faDownLeftAndUpRightToCenter\n} from '@fortawesome/free-solid-svg-icons'\n\n// 添加需要使用的图标到库中\nlibrary.add(\n  faCogs, faFolderOpen, faPlus, faFileAlt,\n  faHistory, faTrashCan, faFileExcel, faClock,\n  faFolder, faChartBar, faProjectDiagram,\n  faClockRotateLeft, faFileCircleExclamation,\n  faAngleDown, faAngleUp, faExpand, faCompress,\n  faUpRightAndDownLeftFromCenter, faDownLeftAndUpRightToCenter\n)\n\nconst app = createApp(App)\n\n// 全局注册 FontAwesome 组件\napp.component('font-awesome-icon', FontAwesomeIcon)\n\n// 全局注册所有图标\nfor (const [key, component] of Object.entries(ElementPlusIconsVue)) {\n  app.component(key, component)\n}\n\n// 设置全局错误处理\nsetupErrorHandler()\n\napp.use(store)\n   .use(router)\n   .use(ElementPlus, {\n     locale: zhCn,\n     size: 'default'\n   })\n   .mount('#app')\n\n// 定义 sendError 类型\ntype SendErrorType = Error | unknown;\n\n// 全局异常处理\napp.config.errorHandler = (err: unknown, vm, info) => {\n  // 控制台输出错误\n  console.error(\"Vue 全局错误:\", err);\n\n  // 将错误发送到后端\n  const errorData: ErrorData = {\n    message: err instanceof Error ? err.message : String(err),\n    stack: err instanceof Error ? err.stack : \"无堆栈信息\",\n    vueHookInfo: info, // 更新字段名\n    url: window.location.href,\n  };\n\n  appApi.logError(errorData).catch((sendError: SendErrorType) => {\n    console.error(\"发送错误到服务器失败:\", sendError);\n  });\n};\n\n// 捕获未处理的Promise异常\nwindow.addEventListener(\"unhandledrejection\", (event) => {\n  const errorData: ErrorData = {\n    message:\n      event.reason instanceof Error\n        ? event.reason.message\n        : \"未处理的Promise异常\",\n    stack: event.reason instanceof Error ? event.reason.stack : \"无堆栈信息\",\n    url: window.location.href,\n    type: \"unhandledrejection\",\n  };\n\n  appApi.logError(errorData).catch((sendError: SendErrorType) => {\n    console.error(\"发送Promise错误到服务器失败:\", sendError);\n  });\n});\n\n// 捕获全局JS错误\nwindow.addEventListener(\"error\", (event) => {\n  // 过滤资源加载错误\n  if (event.message) {\n    const errorData: ErrorData = {\n      message: event.message,\n      codeInfo: `${event.filename}:${event.lineno}:${event.colno}`,\n      url: window.location.href,\n      type: \"global-error\",\n    };\n\n    appApi.logError(errorData).catch((sendError: SendErrorType) => {\n      console.error(\"发送全局错误到服务器失败:\", sendError);\n    });\n  }\n});\n", "import axios from 'axios'\r\nimport { USE_MOCK, mockApi } from '@/mock/mockApi'\r\n\r\n// 定义序列配置数据结构\r\nexport interface SequenceConfigData {\r\n  testSuiteName: string;\r\n  sequencePackageName: string;\r\n  sequencePackageXml?: string;\r\n}\r\n\r\nexport enum CommunicationType {\r\n  Can = 0,\r\n  Canfd = 1\r\n}\r\n\r\nconst BASE_URL = '/api/sequenceConfig'\r\n\r\nexport const sequenceApi = {\r\n  // 获取序列配置\r\n  getSequenceConfig: () => {\r\n    if (USE_MOCK) {\r\n      return mockApi.sequence.getSequenceConfig();\r\n    }\r\n    return axios.get<SequenceConfigData>(`${BASE_URL}`);\r\n  },\r\n\r\n  // 更新序列配置\r\n  updateSequenceConfig: (config: SequenceConfigData) => {\r\n    if (USE_MOCK) {\r\n      return mockApi.sequence.updateSequenceConfig(config);\r\n    }\r\n    return axios.post<SequenceConfigData>(`${BASE_URL}`, config);\r\n  }\r\n}\r\n\r\nexport default sequenceApi\r\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\tid: moduleId,\n\t\tloaded: false,\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n\t// Flag the module as loaded\n\tmodule.loaded = true;\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n// expose the modules object (__webpack_modules__)\n__webpack_require__.m = __webpack_modules__;\n\n", "var deferred = [];\n__webpack_require__.O = function(result, chunkIds, fn, priority) {\n\tif(chunkIds) {\n\t\tpriority = priority || 0;\n\t\tfor(var i = deferred.length; i > 0 && deferred[i - 1][2] > priority; i--) deferred[i] = deferred[i - 1];\n\t\tdeferred[i] = [chunkIds, fn, priority];\n\t\treturn;\n\t}\n\tvar notFulfilled = Infinity;\n\tfor (var i = 0; i < deferred.length; i++) {\n\t\tvar chunkIds = deferred[i][0];\n\t\tvar fn = deferred[i][1];\n\t\tvar priority = deferred[i][2];\n\t\tvar fulfilled = true;\n\t\tfor (var j = 0; j < chunkIds.length; j++) {\n\t\t\tif ((priority & 1 === 0 || notFulfilled >= priority) && Object.keys(__webpack_require__.O).every(function(key) { return __webpack_require__.O[key](chunkIds[j]); })) {\n\t\t\t\tchunkIds.splice(j--, 1);\n\t\t\t} else {\n\t\t\t\tfulfilled = false;\n\t\t\t\tif(priority < notFulfilled) notFulfilled = priority;\n\t\t\t}\n\t\t}\n\t\tif(fulfilled) {\n\t\t\tdeferred.splice(i--, 1)\n\t\t\tvar r = fn();\n\t\t\tif (r !== undefined) result = r;\n\t\t}\n\t}\n\treturn result;\n};", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = function(module) {\n\tvar getter = module && module.__esModule ?\n\t\tfunction() { return module['default']; } :\n\t\tfunction() { return module; };\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = function(exports, definition) {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.f = {};\n// This file contains only the entry chunk.\n// The chunk loading function for additional chunks\n__webpack_require__.e = function(chunkId) {\n\treturn Promise.all(Object.keys(__webpack_require__.f).reduce(function(promises, key) {\n\t\t__webpack_require__.f[key](chunkId, promises);\n\t\treturn promises;\n\t}, []));\n};", "// This function allow to reference async chunks\n__webpack_require__.u = function(chunkId) {\n\t// return url for filenames based on template\n\treturn \"js/\" + chunkId + \".\" + {\"67\":\"cd8395db\",\"112\":\"dc1abed5\",\"153\":\"8f9ab41e\",\"186\":\"602861f1\",\"295\":\"46f815ae\",\"351\":\"2e6bb7b3\",\"644\":\"4ba6c9bf\",\"766\":\"95a4e0c1\",\"828\":\"c5b13821\",\"852\":\"c05bc352\",\"929\":\"245410b5\"}[chunkId] + \".js\";\n};", "// This function allow to reference async chunks\n__webpack_require__.miniCssF = function(chunkId) {\n\t// return url for filenames based on template\n\treturn \"css/\" + chunkId + \".\" + {\"67\":\"d5944060\",\"112\":\"33e69a23\",\"153\":\"69e46bae\",\"186\":\"69bdf509\",\"295\":\"15161d40\",\"351\":\"e46682e2\",\"644\":\"8183bc07\",\"766\":\"1ce99f9b\",\"852\":\"7937723c\",\"929\":\"9b8195a7\"}[chunkId] + \".css\";\n};", "__webpack_require__.g = (function() {\n\tif (typeof globalThis === 'object') return globalThis;\n\ttry {\n\t\treturn this || new Function('return this')();\n\t} catch (e) {\n\t\tif (typeof window === 'object') return window;\n\t}\n})();", "__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }", "var inProgress = {};\nvar dataWebpackPrefix = \"fuzz-web:\";\n// loadScript function to load a script via script tag\n__webpack_require__.l = function(url, done, key, chunkId) {\n\tif(inProgress[url]) { inProgress[url].push(done); return; }\n\tvar script, needAttach;\n\tif(key !== undefined) {\n\t\tvar scripts = document.getElementsByTagName(\"script\");\n\t\tfor(var i = 0; i < scripts.length; i++) {\n\t\t\tvar s = scripts[i];\n\t\t\tif(s.getAttribute(\"src\") == url || s.getAttribute(\"data-webpack\") == dataWebpackPrefix + key) { script = s; break; }\n\t\t}\n\t}\n\tif(!script) {\n\t\tneedAttach = true;\n\t\tscript = document.createElement('script');\n\n\t\tscript.charset = 'utf-8';\n\t\tscript.timeout = 120;\n\t\tif (__webpack_require__.nc) {\n\t\t\tscript.setAttribute(\"nonce\", __webpack_require__.nc);\n\t\t}\n\t\tscript.setAttribute(\"data-webpack\", dataWebpackPrefix + key);\n\n\t\tscript.src = url;\n\t}\n\tinProgress[url] = [done];\n\tvar onScriptComplete = function(prev, event) {\n\t\t// avoid mem leaks in IE.\n\t\tscript.onerror = script.onload = null;\n\t\tclearTimeout(timeout);\n\t\tvar doneFns = inProgress[url];\n\t\tdelete inProgress[url];\n\t\tscript.parentNode && script.parentNode.removeChild(script);\n\t\tdoneFns && doneFns.forEach(function(fn) { return fn(event); });\n\t\tif(prev) return prev(event);\n\t}\n\tvar timeout = setTimeout(onScriptComplete.bind(null, undefined, { type: 'timeout', target: script }), 120000);\n\tscript.onerror = onScriptComplete.bind(null, script.onerror);\n\tscript.onload = onScriptComplete.bind(null, script.onload);\n\tneedAttach && document.head.appendChild(script);\n};", "// define __esModule on exports\n__webpack_require__.r = function(exports) {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "__webpack_require__.nmd = function(module) {\n\tmodule.paths = [];\n\tif (!module.children) module.children = [];\n\treturn module;\n};", "__webpack_require__.p = \"/\";", "if (typeof document === \"undefined\") return;\nvar createStylesheet = function(chunkId, fullhref, oldTag, resolve, reject) {\n\tvar linkTag = document.createElement(\"link\");\n\n\tlinkTag.rel = \"stylesheet\";\n\tlinkTag.type = \"text/css\";\n\tif (__webpack_require__.nc) {\n\t\tlinkTag.nonce = __webpack_require__.nc;\n\t}\n\tvar onLinkComplete = function(event) {\n\t\t// avoid mem leaks.\n\t\tlinkTag.onerror = linkTag.onload = null;\n\t\tif (event.type === 'load') {\n\t\t\tresolve();\n\t\t} else {\n\t\t\tvar errorType = event && event.type;\n\t\t\tvar realHref = event && event.target && event.target.href || fullhref;\n\t\t\tvar err = new Error(\"Loading CSS chunk \" + chunkId + \" failed.\\n(\" + errorType + \": \" + realHref + \")\");\n\t\t\terr.name = \"ChunkLoadError\";\n\t\t\terr.code = \"CSS_CHUNK_LOAD_FAILED\";\n\t\t\terr.type = errorType;\n\t\t\terr.request = realHref;\n\t\t\tif (linkTag.parentNode) linkTag.parentNode.removeChild(linkTag)\n\t\t\treject(err);\n\t\t}\n\t}\n\tlinkTag.onerror = linkTag.onload = onLinkComplete;\n\tlinkTag.href = fullhref;\n\n\n\tif (oldTag) {\n\t\toldTag.parentNode.insertBefore(linkTag, oldTag.nextSibling);\n\t} else {\n\t\tdocument.head.appendChild(linkTag);\n\t}\n\treturn linkTag;\n};\nvar findStylesheet = function(href, fullhref) {\n\tvar existingLinkTags = document.getElementsByTagName(\"link\");\n\tfor(var i = 0; i < existingLinkTags.length; i++) {\n\t\tvar tag = existingLinkTags[i];\n\t\tvar dataHref = tag.getAttribute(\"data-href\") || tag.getAttribute(\"href\");\n\t\tif(tag.rel === \"stylesheet\" && (dataHref === href || dataHref === fullhref)) return tag;\n\t}\n\tvar existingStyleTags = document.getElementsByTagName(\"style\");\n\tfor(var i = 0; i < existingStyleTags.length; i++) {\n\t\tvar tag = existingStyleTags[i];\n\t\tvar dataHref = tag.getAttribute(\"data-href\");\n\t\tif(dataHref === href || dataHref === fullhref) return tag;\n\t}\n};\nvar loadStylesheet = function(chunkId) {\n\treturn new Promise(function(resolve, reject) {\n\t\tvar href = __webpack_require__.miniCssF(chunkId);\n\t\tvar fullhref = __webpack_require__.p + href;\n\t\tif(findStylesheet(href, fullhref)) return resolve();\n\t\tcreateStylesheet(chunkId, fullhref, null, resolve, reject);\n\t});\n}\n// object to store loaded CSS chunks\nvar installedCssChunks = {\n\t524: 0\n};\n\n__webpack_require__.f.miniCss = function(chunkId, promises) {\n\tvar cssChunks = {\"67\":1,\"112\":1,\"153\":1,\"186\":1,\"295\":1,\"351\":1,\"644\":1,\"766\":1,\"852\":1,\"929\":1};\n\tif(installedCssChunks[chunkId]) promises.push(installedCssChunks[chunkId]);\n\telse if(installedCssChunks[chunkId] !== 0 && cssChunks[chunkId]) {\n\t\tpromises.push(installedCssChunks[chunkId] = loadStylesheet(chunkId).then(function() {\n\t\t\tinstalledCssChunks[chunkId] = 0;\n\t\t}, function(e) {\n\t\t\tdelete installedCssChunks[chunkId];\n\t\t\tthrow e;\n\t\t}));\n\t}\n};\n\n// no hmr\n\n// no prefetching\n\n// no preloaded", "// no baseURI\n\n// object to store loaded and loading chunks\n// undefined = chunk not loaded, null = chunk preloaded/prefetched\n// [resolve, reject, Promise] = chunk loading, 0 = chunk loaded\nvar installedChunks = {\n\t524: 0\n};\n\n__webpack_require__.f.j = function(chunkId, promises) {\n\t\t// JSONP chunk loading for javascript\n\t\tvar installedChunkData = __webpack_require__.o(installedChunks, chunkId) ? installedChunks[chunkId] : undefined;\n\t\tif(installedChunkData !== 0) { // 0 means \"already installed\".\n\n\t\t\t// a Promise means \"currently loading\".\n\t\t\tif(installedChunkData) {\n\t\t\t\tpromises.push(installedChunkData[2]);\n\t\t\t} else {\n\t\t\t\tif(true) { // all chunks have JS\n\t\t\t\t\t// setup Promise in chunk cache\n\t\t\t\t\tvar promise = new Promise(function(resolve, reject) { installedChunkData = installedChunks[chunkId] = [resolve, reject]; });\n\t\t\t\t\tpromises.push(installedChunkData[2] = promise);\n\n\t\t\t\t\t// start chunk loading\n\t\t\t\t\tvar url = __webpack_require__.p + __webpack_require__.u(chunkId);\n\t\t\t\t\t// create error before stack unwound to get useful stacktrace later\n\t\t\t\t\tvar error = new Error();\n\t\t\t\t\tvar loadingEnded = function(event) {\n\t\t\t\t\t\tif(__webpack_require__.o(installedChunks, chunkId)) {\n\t\t\t\t\t\t\tinstalledChunkData = installedChunks[chunkId];\n\t\t\t\t\t\t\tif(installedChunkData !== 0) installedChunks[chunkId] = undefined;\n\t\t\t\t\t\t\tif(installedChunkData) {\n\t\t\t\t\t\t\t\tvar errorType = event && (event.type === 'load' ? 'missing' : event.type);\n\t\t\t\t\t\t\t\tvar realSrc = event && event.target && event.target.src;\n\t\t\t\t\t\t\t\terror.message = 'Loading chunk ' + chunkId + ' failed.\\n(' + errorType + ': ' + realSrc + ')';\n\t\t\t\t\t\t\t\terror.name = 'ChunkLoadError';\n\t\t\t\t\t\t\t\terror.type = errorType;\n\t\t\t\t\t\t\t\terror.request = realSrc;\n\t\t\t\t\t\t\t\tinstalledChunkData[1](error);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t};\n\t\t\t\t\t__webpack_require__.l(url, loadingEnded, \"chunk-\" + chunkId, chunkId);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n};\n\n// no prefetching\n\n// no preloaded\n\n// no HMR\n\n// no HMR manifest\n\n__webpack_require__.O.j = function(chunkId) { return installedChunks[chunkId] === 0; };\n\n// install a JSONP callback for chunk loading\nvar webpackJsonpCallback = function(parentChunkLoadingFunction, data) {\n\tvar chunkIds = data[0];\n\tvar moreModules = data[1];\n\tvar runtime = data[2];\n\t// add \"moreModules\" to the modules object,\n\t// then flag all \"chunkIds\" as loaded and fire callback\n\tvar moduleId, chunkId, i = 0;\n\tif(chunkIds.some(function(id) { return installedChunks[id] !== 0; })) {\n\t\tfor(moduleId in moreModules) {\n\t\t\tif(__webpack_require__.o(moreModules, moduleId)) {\n\t\t\t\t__webpack_require__.m[moduleId] = moreModules[moduleId];\n\t\t\t}\n\t\t}\n\t\tif(runtime) var result = runtime(__webpack_require__);\n\t}\n\tif(parentChunkLoadingFunction) parentChunkLoadingFunction(data);\n\tfor(;i < chunkIds.length; i++) {\n\t\tchunkId = chunkIds[i];\n\t\tif(__webpack_require__.o(installedChunks, chunkId) && installedChunks[chunkId]) {\n\t\t\tinstalledChunks[chunkId][0]();\n\t\t}\n\t\tinstalledChunks[chunkId] = 0;\n\t}\n\treturn __webpack_require__.O(result);\n}\n\nvar chunkLoadingGlobal = self[\"webpackChunkfuzz_web\"] = self[\"webpackChunkfuzz_web\"] || [];\nchunkLoadingGlobal.forEach(webpackJsonpCallback.bind(null, 0));\nchunkLoadingGlobal.push = webpackJsonpCallback.bind(null, chunkLoadingGlobal.push.bind(chunkLoadingGlobal));", "// startup\n// Load entry module and return exports\n// This entry module depends on other loaded chunks and execution need to be delayed\nvar __webpack_exports__ = __webpack_require__.O(undefined, [504], function() { return __webpack_require__(7921); })\n__webpack_exports__ = __webpack_require__.O(__webpack_exports__);\n"], "names": ["_defineComponent", "__name", "props", "value", "language", "readOnly", "type", "Boolean", "emits", "setup", "__props", "emit", "__emit", "<PERSON><PERSON><PERSON><PERSON>", "ref", "view", "basicSetupExtensions", "lineNumbers", "highlightActiveLineGutter", "highlightSpecialChars", "history", "drawSelection", "dropCursor", "EditorState", "allowMultipleSelections", "of", "indentOnInput", "syntaxHighlighting", "defaultHighlightStyle", "fallback", "fold<PERSON>utter", "keymap", "defaultKeymap", "historyKeymap", "foldKeymap", "createEditor", "extensions", "xml", "Editor<PERSON><PERSON><PERSON>", "updateListener", "update", "do<PERSON><PERSON><PERSON><PERSON>", "state", "doc", "toString", "contentAttributes", "contenteditable", "class", "editable", "create", "parent", "onMounted", "watch", "newValue", "currentScrollPos", "scrollDOM", "scrollTop", "dispatch", "changes", "from", "to", "length", "insert", "setTimeout", "effects", "reconfigure", "onBeforeUnmount", "destroy", "_ctx", "_cache", "_openBlock", "_createElementBlock", "ref_key", "__exports__", "_hoisted_1", "_hoisted_2", "title", "content", "handleContentChange", "_createElementVNode", "_createVNode", "XmlEditor", "CoverageType", "ExecutionState", "isTesterCompleted", "snapshot", "processState", "Success", "Failure", "BASE_URL", "appApi", "getAppInfo", "USE_MOCK", "mockApi", "app", "axios", "get", "logError", "errorData", "post", "exit", "getLatestInteroperationCaseResults", "generateCases", "coverage", "sequenceNames", "request", "SequenceNames", "saveCases", "getSavedCases", "startTest", "stopTest", "pauseTest", "resumeTest", "getTestStatus", "getTestStatusPaged", "paged<PERSON><PERSON>y", "getTestResults", "getCases", "testResultId", "getCaseSteps", "caseResultId", "getCaseResult", "case", "deleteTestResult", "delete", "downloadHtmlReport", "url", "tester", "interoperationApi", "interoperation", "getStatus", "testSuiteApi", "getBuiltIn", "testSuite", "getBuiltInXml", "suiteName", "packageName", "getXml", "params", "reactive", "currentPlan", "recentPlans", "isLoading", "TestPlanServiceClass", "constructor", "_defineProperty", "setRouter", "router", "this", "getState", "readonly", "openFromExplorer", "response", "test<PERSON>lan<PERSON><PERSON>", "openInExplorer", "data", "ElMessage", "success", "navigateToTestPlan", "loadRecentPlans", "openFromPath", "path", "open", "createTestPlan", "testPlanData", "error", "console", "closeTestPlan", "close", "push", "test<PERSON>lan<PERSON><PERSON><PERSON><PERSON><PERSON>", "clearHistory", "clear", "updateBasicInfo", "description", "getCurrentPlan", "name", "testPlanService", "generateId", "Math", "random", "substring", "getCurrentISOTime", "Date", "toISOString", "getRandomPastTime", "now", "pastTime", "getTime", "floor", "testPlans", "manifest", "created", "modified", "config", "targetDevice", "protocol", "testCases", "testPlanHistoryItems", "id", "filePath", "planName", "lastAccessTime", "lastModified", "isDeleted", "errorLogs", "currentTestPlan", "mockTestPlanApi", "randomIndex", "<PERSON><PERSON><PERSON>", "updateTestPlanHistory", "mockSuccess", "historyItem", "find", "item", "plan", "matchingPlan", "p", "replace", "toLowerCase", "newPlan", "log", "undefined", "mockError", "checkFileExists", "exists", "existingItem", "sort", "a", "b", "mockTestPlanHistoryApi", "sortedHistory", "filter", "for<PERSON>ach", "deleteRecord", "record", "generateMockInteroperationResults", "results", "uniqueSequences", "successCount", "selectedSequences", "slice", "sequenceName", "index", "otherStates", "begin", "end", "sequenceId", "parameter", "detail", "generateMockTestCases", "caseCount", "High", "prefixGroups", "i", "prefixIndex", "prefix", "padStart", "Array", "fill", "map", "join", "generateMockTestResults", "creationTime", "totalCount", "failureCount", "resultFolderName", "testType", "completionTime", "generateMockCaseSteps", "steps", "stepCount", "timestamp", "stateOptions", "frameTimestamp", "progressInterval", "mockProcessState", "Pending", "mockCurrentOperation", "mockTestResult", "mockCaseResults", "startProgressUpdate", "clearInterval", "window", "setInterval", "Running", "currentIndex", "isSuccess", "nextIndex", "mockAppApi", "dataFolder", "logFolder", "coverageType", "actualCoverageType", "actualSequenceNames", "cases", "c", "savedCases", "Normal", "states", "Paused", "currentOperation", "testResult", "caseResults", "totalCases", "startIndex", "pageNumber", "pageSize", "endIndex", "min", "pagedItems", "pagedCaseResult", "items", "total", "Promise", "resolve", "mockExplorerApi", "selectFolder", "openExplorer", "mockTestSuites", "version", "packages", "sequences", "preamble", "onConnect", "mockSequenceXml", "mockTestSuiteApi", "suite", "s", "pkg", "mockDeviceChannels", "communicationType", "isConnected", "currentTestPlanConfig", "canConfig", "deviceChannelName", "dataBitrate", "canFdConfig", "arbitrationBitrate", "device", "mockHardwareApi", "getHardwareConfig", "deviceChannels", "testPlanConfig", "updateHardwareConfig", "updatedConfig", "currentSequenceConfig", "testSuiteName", "sequencePackageName", "sequencePackageXml", "mockSequenceApi", "getSequenceConfig", "updateSequenceConfig", "defaultTesterSnapshot", "crypto", "randomUUID", "mockStatus", "progressTimer", "generateTestCases", "stopProgressSimulation", "mockInteroperationApi", "message", "defaultConfig", "whiteList<PERSON>rames", "selectedNodeName", "enableNmWakeup", "nmWakeupId", "nmWakeupIsExt", "nmWakeupDlc", "nmWakeupData", "nmWakeupCommunicationType", "nmWakeupCycleMs", "nmWakeupDelayMs", "diagReqId", "diagReqIsExt", "diagResId", "diagTimeoutMs", "isDutMtuLessThan4096", "enableDiagFallbackRequest", "diagFallbackRequestPayload", "securityInfo", "hasDll", "dllFileName", "dllSize", "mockSingleCaseResult", "mockCaseApi", "getCaseConfig", "updateCaseConfig", "removeSecurityDll", "securityDllPath", "dllName", "split", "pop", "mockDllSize", "importDbc", "dlc", "isExt", "transmitter", "receivers", "nodeNames", "selectSecurityDll", "process", "delay", "ms", "then", "status", "statusText", "headers", "Error", "reject", "r", "v", "testPlan", "testPlanHistory", "explorer", "hardware", "sequence", "convertToHistoryItems", "histories", "getHistory", "convertedData", "<PERSON><PERSON><PERSON>", "render", "$props", "$setup", "$data", "$options", "_component_el_input", "_resolveComponent", "_component_el_form_item", "_component_el_button", "_component_el_form", "_component_el_dialog", "_createBlock", "modelValue", "dialogVisible", "$event", "width", "footer", "_withCtx", "onClick", "default", "_createTextVNode", "_", "handleCreateTestPlan", "model", "form", "label", "required", "placeholder", "folder", "handleSelectFolder", "defineComponent", "async", "warning", "show", "key", "_hoisted_3", "_hoisted_4", "useRouter", "route", "useRoute", "isNavigating", "isClosing", "isOnTestPlanPage", "computed", "startsWith", "hasOpenTestPlan", "handleReturnToPlan", "handleClosePlan", "result", "ElMessageBox", "confirm", "confirmButtonText", "cancelButtonText", "_component_el_icon", "_unref", "Document", "_toDisplayString", "_createCommentVNode", "size", "loading", "Back", "Close", "createDialog", "activeIndex", "handleOpen", "handleOpenFromHistory", "catch", "handleCreate", "handleTestPlanCreated", "handleExit", "info", "_component_el_menu_item", "_component_el_divider", "_component_el_sub_menu", "_component_el_menu", "mode", "HomeFilled", "_Fragment", "_renderList", "TestPlanStatusIndicator", "CreateTestPlan", "onCreated", "_component_Folder", "_component_Plus", "_component_test_plan_history", "_component_el_card", "_component_el_col", "_component_test_plan_guide", "_component_el_row", "_component_create_test_plan", "gutter", "span", "header", "handleOpenTestPlan", "showCreateDialog", "onOpenPlan", "_hoisted_5", "_hoisted_6", "_hoisted_7", "_hoisted_8", "_hoisted_9", "_hoisted_10", "_hoisted_11", "_component_font_awesome_icon", "_component_el_skeleton", "_component_el_empty", "_component_el_tag", "_component_el_pagination", "_directive_loading", "_resolveDirective", "historyList", "handleClearHistory", "icon", "rows", "_withDirectives", "displayedHistory", "_normalizeClass", "handleOpenPlan", "effect", "_withModifiers", "handleOpenFolder", "formatDateTime", "layout", "currentPage", "onCurrentChange", "handlePageChange", "background", "image", "expose", "start", "fetchHistory", "page", "dateTimeStr", "date", "toLocaleString", "year", "month", "day", "hour", "minute", "refresh", "_component_el_timeline_item", "_component_el_timeline", "step", "components", "TestPlanHistory", "TestPlanGuide", "Folder", "Plus", "historyComponent", "testSuites", "<PERSON><PERSON><PERSON><PERSON>", "xmlContent", "selectedPackageIndex", "selectedPackageName", "loadTestSuites", "handleSuiteClick", "handlePackageClick", "sequencePackage", "XmlViewer", "routes", "component", "HomeView", "query", "children", "TestSuiteView", "createRouter", "createWebHistory", "beforeEach", "next", "createStore", "getters", "mutations", "actions", "modules", "formatErrorMessage", "errorMessages", "exceptionMessage", "currentException", "innerException", "showDetailedError", "errorMessage", "alert", "dangerouslyUseHTMLString", "closeOnClickModal", "closeOnPressEscape", "showClose", "isUserCanceled", "errorCode", "setup<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "interceptors", "use", "library", "add", "faCogs", "faFolderOpen", "faPlus", "faFileAlt", "faHistory", "faTrashCan", "faFileExcel", "faClock", "faFolder", "faChartBar", "faProjectDiagram", "faClockRotateLeft", "faFileCircleExclamation", "faAngleDown", "faAngleUp", "faExpand", "faCompress", "faUpRightAndDownLeftFromCenter", "faDownLeftAndUpRightToCenter", "createApp", "App", "FontAwesomeIcon", "Object", "entries", "ElementPlusIconsVue", "store", "ElementPlus", "locale", "zhCn", "mount", "<PERSON><PERSON><PERSON><PERSON>", "err", "vm", "String", "stack", "vueHookInfo", "location", "href", "sendError", "addEventListener", "event", "reason", "codeInfo", "filename", "lineno", "colno", "CommunicationType", "sequenceApi", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "exports", "module", "loaded", "__webpack_modules__", "call", "m", "deferred", "O", "chunkIds", "fn", "priority", "notFulfilled", "Infinity", "fulfilled", "j", "keys", "every", "splice", "n", "getter", "__esModule", "d", "definition", "o", "defineProperty", "enumerable", "f", "e", "chunkId", "all", "reduce", "promises", "u", "miniCssF", "g", "globalThis", "Function", "obj", "prop", "prototype", "hasOwnProperty", "inProgress", "dataWebpackPrefix", "l", "done", "script", "<PERSON><PERSON><PERSON><PERSON>", "scripts", "document", "getElementsByTagName", "getAttribute", "createElement", "charset", "timeout", "nc", "setAttribute", "src", "onScriptComplete", "prev", "onerror", "onload", "clearTimeout", "doneFns", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "bind", "target", "head", "append<PERSON><PERSON><PERSON>", "Symbol", "toStringTag", "nmd", "paths", "createStylesheet", "fullhref", "oldTag", "linkTag", "rel", "nonce", "onLinkComplete", "errorType", "realHref", "code", "insertBefore", "nextS<PERSON>ling", "find<PERSON><PERSON><PERSON><PERSON><PERSON>", "existingLinkTags", "tag", "dataHref", "existingStyleTags", "loadStylesheet", "installedCssChunks", "miniCss", "cssChunks", "installedChunks", "installedChunkData", "promise", "loadingEnded", "realSrc", "webpackJsonpCallback", "parentChunkLoadingFunction", "moreModules", "runtime", "some", "chunkLoadingGlobal", "self", "__webpack_exports__"], "sourceRoot": ""}