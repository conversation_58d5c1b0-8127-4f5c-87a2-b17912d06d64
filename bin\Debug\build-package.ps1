﻿# 获取当前日期并格式化
$dateStr = Get-Date -Format "yyyyMMdd"
$zipFileName = "Fuzz_$dateStr.zip"
$sourceDirectory = ".\x64"
$targetZipPath = ".\$zipFileName"

# 1. 检查x64文件夹是否存在
if (-not (Test-Path $sourceDirectory)) {
    Write-Error "x64文件夹不存在！"
    exit 1
}

# 2. 删除指定类型的文件
Write-Host "正在删除不需要的文件..."
Get-ChildItem -Path $sourceDirectory -File | Where-Object {
    $_.Extension -in @(".config", ".pdb", ".xml")
} | Remove-Item -Force

# 3. 删除locales下的pak文件（排除en-US和zh-CN）
$localesPath = Join-Path $sourceDirectory "CefSharp\locales"
if (Test-Path $localesPath) {
    Get-ChildItem -Path $localesPath -Filter "*.pak" | Where-Object {
        $_.Name -notin @("en-US.pak", "zh-CN.pak")
    } | Remove-Item -Force
}

# 4. 创建zip文件
Write-Host "正在创建压缩文件..."
if (Test-Path $targetZipPath) {
    Remove-Item $targetZipPath -Force
}
Compress-Archive -Path "$sourceDirectory\*" -DestinationPath $targetZipPath

# 获取文件大小
$fileSize = (Get-Item $targetZipPath).Length
$fileSizeMB = [math]::Round($fileSize / 1MB, 2)

# 5. 显示信息并询问用户
Write-Host "`n生成的文件信息："
Write-Host "文件名: $zipFileName"
Write-Host "文件大小: $fileSizeMB MB"

$targetPath = "Z:\Fuzz_Build"
$confirmation = Read-Host "`n是否要将文件复制到 $targetPath ? (Y/N)"

# 6. 根据用户选择复制文件
if ($confirmation -eq 'Y' -or $confirmation -eq 'y') {
    if (Test-Path $targetPath) {
        # 获取目标文件路径
        $targetFilePath = Join-Path $targetPath $zipFileName
        
        # 如果目标文件已存在，自动添加后缀
        if (Test-Path $targetFilePath) {
            $counter = 1
            $fileNameWithoutExt = [System.IO.Path]::GetFileNameWithoutExtension($zipFileName)
            $extension = [System.IO.Path]::GetExtension($zipFileName)
            
            while (Test-Path $targetFilePath) {
                $newFileName = "${fileNameWithoutExt}_${counter}${extension}"
                $targetFilePath = Join-Path $targetPath $newFileName
                $counter++
            }
            
            Write-Host "`n目标文件已存在，将使用新文件名: $newFileName"
        }
        
        Copy-Item $targetZipPath -Destination $targetFilePath -Force
        Write-Host "`n文件已成功复制到 $targetFilePath"
    } else {
        Write-Error "目标路径 $targetPath 不存在！"
    }
} else {
    Write-Host "`n已取消复制操作"
}

Write-Host "`n脚本执行完成"