using Alsi.Fuzz.Core.Service.CaseFactory.Core;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Alsi.Fuzz.Core.Service.CaseFactory.Factories.Iso14229
{
    public abstract class CaseFactoryBase : ICaseFactory
    {
        public IsoType IsoType => IsoType.Iso14229;
        public abstract CaseMutation[] Generate(MutationOptions options);

        protected static CaseMutation CreateCaseMutation(string name, IEnumerable<byte> mutatePayload, int mutatePayloadLength)
        {
            var caseMutation = CaseMutation.Create(name)
                .MutatePayload(mutatePayload.ToArray())
                .MutatePayloadLength(mutatePayloadLength);
            return caseMutation;
        }

        protected static bool TryGetParameter2kFromXml(MutationOptions options, byte sid, byte subfunctionId, out byte[] parameter2k)
        {
            parameter2k = Array.Empty<byte>();
            var xmlService = options.XmlServices.FirstOrDefault(x => x.Id == sid && x.SubfunctionId == subfunctionId);
            if (xmlService == null)
            {
                return false;
            }
            parameter2k = xmlService.Parameter2k;
            return true;
        }

        private static Random random = new Random();
        protected static byte[] RandomBytes(int length)
        {
            if (length > 0)
            {
                var data2k = new byte[length];
                random.NextBytes(data2k);
                return data2k;
            }
            return Array.Empty<byte>();
        }
    }
}
