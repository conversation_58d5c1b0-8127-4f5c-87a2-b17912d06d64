using Alsi.App.Devices.Core.TransportLayer.Frames;
using System.Collections.Generic;
using System.Linq;

namespace Alsi.App.Devices.Core.TransportLayer
{
    public class ResponseStore
    {
        public SingleFrame SingleFrame { get; set; }
        public FirstFrame FirstFrame { get; set; }
        private List<ConsecutiveFrame> ConsecutiveFrames { get; } = new List<ConsecutiveFrame>();

        public void AddConsecutiveFrame(ConsecutiveFrame consecutiveFrame)
        {
            lock (ConsecutiveFrames)
            {
                ConsecutiveFrames.Add(consecutiveFrame);
            }
        }
        public void ClearConsecutiveFrame()
        {
            lock (ConsecutiveFrames)
            {
                ConsecutiveFrames.Clear();
            }
        }

        public ConsecutiveFrame[] GetConsecutiveFrames()
        {
            lock (ConsecutiveFrames)
            {
                return ConsecutiveFrames.ToArray();
            }
        }


        public bool IsFinished(out byte[] payload)
        {
            payload = new byte[0];
            // 单帧传输是否结束
            if (SingleFrame != null)
            {
                payload = SingleFrame.Payload;
                return true;
            }

            // 多帧传输是否结束
            if (FirstFrame != null)
            {
                var consecutiveFrames = GetConsecutiveFrames();
                var currentPayloadLength = FirstFrame.Payload.Length + consecutiveFrames.Sum(x => x.Payload.Length);
                if (currentPayloadLength >= FirstFrame.TotalLength)
                {
                    var bytes = new List<byte>(FirstFrame.Payload.Length);
                    bytes.AddRange(FirstFrame.Payload);
                    bytes.AddRange(consecutiveFrames.SelectMany(x => x.Payload));
                    payload = bytes.Take(FirstFrame.TotalLength).ToArray();
                    return true;
                }
            }

            return false;
        }
    }
}
