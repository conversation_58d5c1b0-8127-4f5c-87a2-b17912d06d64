<template>
  <div v-if="hasOpenTestPlan" class="test-plan-indicator">
    <div class="plan-info">
      <el-icon><Document /></el-icon>
      <span class="plan-name" :title="currentPlan.path">{{ currentPlan.manifest.name }}</span>
    </div>
    <div class="plan-actions">
      <el-button
        v-if="!isOnTestPlanPage"
        type="primary"
        size="small"
        @click="handleReturnToPlan"
        :loading="isNavigating"
      >
        <el-icon><Back /></el-icon>
        Back
      </el-button>
      <el-button
        type="danger"
        size="small"
        @click="handleClosePlan"
        :loading="isClosing"
      >
        <el-icon><Close /></el-icon>
        Close
      </el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { ElMessageBox, ElMessage } from 'element-plus';
import { Document, Back, Close } from '@element-plus/icons-vue';
import { testPlanService } from '@/services/testPlanService';

const router = useRouter();
const route = useRoute();
const state = testPlanService.getState();
const isNavigating = ref(false);
const isClosing = ref(false);

// Check if currently on test plan page
const isOnTestPlanPage = computed(() => route.path.startsWith('/test-plan'));

// Check if a test plan is currently open
const hasOpenTestPlan = computed(() => !!state.currentPlan);
const currentPlan = computed(() => state.currentPlan);

// Return to test plan page
const handleReturnToPlan = async () => {
  if (!hasOpenTestPlan.value) return;

  isNavigating.value = true;
  try {
    await router.push('/test-plan');
  } catch (error) {
    console.error('Failed to navigate to test plan page:', error);
    ElMessage.error('Failed to return to test plan');
  } finally {
    isNavigating.value = false;
  }
};

// 关闭测试计划
const handleClosePlan = async () => {
  if (!hasOpenTestPlan.value) return;

  try {
    const result = await ElMessageBox.confirm(
      'Are you sure you want to close the current test plan? Unsaved changes may be lost.',
      'Close Test Plan',
      {
        confirmButtonText: 'OK',
        cancelButtonText: 'Cancel',
        type: 'warning'
      }
    );

    if (result === 'confirm') {
      isClosing.value = true;
      await testPlanService.closeTestPlan();
    }
  } catch (error) {
    // User canceled the operation, do nothing
  } finally {
    isClosing.value = false;
  }
};
</script>

<style scoped>
.test-plan-indicator {
  display: flex;
  align-items: center;
  padding: 0 15px;
  margin-left: 10px;
  height: 40px;
  border-left: 1px solid var(--el-border-color-light);
  border-bottom: 1px solid var(--el-menu-border-color);
}

.plan-info {
  display: flex;
  align-items: center;
  margin-right: 15px;
  color: var(--el-text-color-secondary);
}

.plan-name {
  margin-left: 5px;
  font-weight: 500;
  max-width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: var(--el-text-color-primary);
}

.plan-actions {
  display: flex;
  gap: 8px;
}
</style>
