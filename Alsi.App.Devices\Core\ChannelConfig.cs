﻿namespace Alsi.App.Devices.Core
{
    public class ChannelConfig
    {
        public ChannelConfig()
        {
        }

        public ChannelConfig(CommunicationType communicationType, int canFdArbitrationBitRate, int canFdDataBitRate, int canBitrate)
        {
            CommunicationType = communicationType;
            CanFdArbitrationBitRate = canFdArbitrationBitRate;
            CanFdDataBitRate = canFdDataBitRate;
            CanBitrate = canBitrate;
        }

        public CommunicationType CommunicationType { get; set; } = CommunicationType.Can;
        public int CanFdArbitrationBitRate { get; set; } = 500_000;
        public int CanFdDataBitRate { get; set; } = 2000_000;
        public int CanBitrate { get; set; } = 500_000;

        public override string ToString()
        {
            return $"{nameof(CommunicationType)}={CommunicationType};" +
                $" {nameof(CanFdArbitrationBitRate)}={CanFdArbitrationBitRate};" +
                $" {nameof(CanFdDataBitRate)}={CanFdDataBitRate};" +
                $" {nameof(CanBitrate)}={CanBitrate};";
        }
    }
}
