﻿using System;
using System.Linq;
using System.Runtime.InteropServices;
using TsMaster;

namespace Alsi.App.Devices.TsLibCan
{
    public static class TsLibApi
    {
        private static bool isInitialized;

        private static TTSCANConnectedCallback_Win32 connectedCallBack = new TTSCANConnectedCallback_Win32(TsCanConnectedCallBack);
        private static TTSCANConnectedCallback_Win32 disconnectedCallBack = new TTSCANConnectedCallback_Win32(TsCanDisconnectedCallBack);

        public static void Initialize()
        {
            if (isInitialized)
            {
                return;
            }

            isInitialized = true;

            TsCANApi.initialize_lib_tscan(true, true, false);

            uint ret = TsCANApi.tscan_register_event_connected(connectedCallBack);
            CheckRet(ret, nameof(TsCANApi.tscan_register_event_connected));

            ret = TsCANApi.tscan_register_event_disconnected(disconnectedCallBack);
            CheckRet(ret, nameof(TsCANApi.tscan_register_event_disconnected));
        }

        public static void Release()
        {
            if (!isInitialized)
            {
                return;
            }

            isInitialized = false;

            uint ret = TsCANApi.tscan_unregister_event_connected(connectedCallBack);
            CheckRet(ret, nameof(TsCANApi.tscan_unregister_event_connected));

            ret = TsCANApi.tscan_unregister_event_disconnected(disconnectedCallBack);
            CheckRet(ret, nameof(TsCANApi.tscan_unregister_event_disconnected));

            TsCANApi.finalize_lib_tscan();
        }

        // LIN 总线连接回调
        private static void TsCanConnectedCallBack(IntPtr ADevicehandle)
        {
            AppEnv.Logger.Info($"[Tosun] connected -> {ADevicehandle}");
        }

        // LIN 总线断开连接回调
        private static void TsCanDisconnectedCallBack(IntPtr ADevicehandle)
        {
            AppEnv.Logger.Info($"[Tosun] disconnected -> {ADevicehandle}");
        }

        public static int GetChannelCount(string product)
        {
            if (product.ToUpper().Split(new[] { ' ' }).Contains("LIN"))
            {
                return TosunConsts.LinChannels.ContainsKey(product) ? TosunConsts.LinChannels[product] : 1;
            }
            else if (product.ToUpper().Contains("CANFD"))
            {
                return TosunConsts.CanChannels.ContainsKey(product) ? TosunConsts.CanChannels[product] : 1;
            }
            else
            {
                return TosunConsts.CanChannels.ContainsKey(product) ? TosunConsts.CanChannels[product] : 1;
            }           
        }

        /// <summary>
        /// 检查 TS LIB API 返回结果是否正确
        /// </summary>
        public static void CheckRet(uint ret, string action, bool writeSuccessLog = true)
        {
            if (ret == (uint)TSCAN_DEF.IDX_ERR_OK)
            {
                if (writeSuccessLog)
                {
                    AppEnv.Logger.Debug($"[Tosun] {action} successfully.");
                }
            }
            else
            {
                var message = $"[Tosun] {action} failed, ret={(TSCAN_DEF)ret}({ret})";

                IntPtr ptr = IntPtr.Zero;
                var retGetErrorDescption = TsCANApi.tscan_get_error_description(ret, ref ptr);
                if (retGetErrorDescption == (uint)TSCAN_DEF.IDX_ERR_OK
                    && ptr != IntPtr.Zero)
                {
                    message += ", desc=" + Marshal.PtrToStringAnsi(ptr);
                }

                AppEnv.Logger.Error(message);
            }
        }
    }
}
