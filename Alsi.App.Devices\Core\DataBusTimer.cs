using System;
using System.Diagnostics;

namespace Alsi.App.Devices.Core
{
    public class DataBusTimer
    {
        public void Start()
        {
            maxTimestamp = 0;
            DeviationUs = 0;
            Stopwatch.Restart();
        }

        public void Stop()
        {
            Stopwatch.Stop();
        }

        private Stopwatch Stopwatch { get; set; } = new Stopwatch();

        /// <summary>
        /// 硬件时间戳来源通道，时间仅和一个通道进行同步
        /// </summary>
        public int? HardwareTimeBaseChannel { get; set; }

        public void SyncTimeBaseDeviation(ulong timestamp, int channel)
        {
            // 使用收到的第一帧数据所在通道，作为基础时间通道
            HardwareTimeBaseChannel = channel;

            // 如当前通道，是时间戳基准通道，使用硬件时间戳，同步当前总线时间戳
            var hardwareTimestamp = timestamp;
            if (hardwareTimestamp > long.MaxValue)
            {
                return;
            }

            var ms = Stopwatch.Elapsed.TotalMilliseconds;
            DeviationUs = (long)hardwareTimestamp - (long)TimestampUtils.GetMicroseconds(ms);
        }

        private ulong maxTimestamp;
        private long DeviationUs { get; set; }
        public ulong Timestamp
        {
            get
            {
                var ms = Stopwatch.Elapsed.TotalMilliseconds;
                var timestamp = (ulong)((long)TimestampUtils.GetMicroseconds(ms) + DeviationUs);
                timestamp = Math.Max(maxTimestamp, timestamp);
                maxTimestamp = timestamp;
                return timestamp;
            }
        }
    }
}
