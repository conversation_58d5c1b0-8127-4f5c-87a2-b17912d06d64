using Alsi.Common.Utils;
using System;

namespace Alsi.App.Devices.Core
{
    [Serializable]
    public class CanFrame
    {
        public CanFrame()
        {
        }
        public CanFrame(
            int id, byte[] data, ulong timeUS, byte dlc, byte chn, byte properties, bool isCanfd, bool isExt)
        {
            Id = id;
            Data = data;
            TimeUS = timeUS;
            Dlc = dlc;
            Channel = chn;
            Properties = properties;
            IsCanFd = isCanfd;
            IsExt = isExt;
        }

        public int Id { get; set; }
        public byte[] Data { get; set; } = new byte[8];
        public ulong TimeUS { get; set; }
        public byte Dlc { get; set; }
        public byte Channel { get; set; }
        public byte Properties { get; set; }

        public bool IsBrs { get; set; }
        public bool IsEsi { get; set; }

        public bool IsCanFd { get; set; } = false;

        // 参考: 005_TAB\2、技术资料\tosun\TSMaster-API\TSMaster API\tsmaster_sdk_csharp_api\TSMaster_SDK_DotNet_cn.pdf
        //
        // FProperties：CAN 属性定义：该参数默认为 0，共八个 bits，每一个位的定义如下：
        // Bit  意义
        // 0    0:  Rx 接收报文；1：Tx 发送报文
        // 1    0：data frame 数据帧；1：remote frame 远程帧
        // 2    0：std frame 标准帧；1：extended frame 扩展帧
        // 3-5  Reserved
        // 6    0：不记录；1：已经被记录
        // 7    Reserved
        private bool GetBit(byte mask) => (Properties & mask) != 0;
        private void SetBit(byte mask, bool value)
        {
            if (value)
                Properties |= mask;  // 设置位
            else
                Properties &= (byte)~mask;  // 清除位
        }

        public bool IsRx
        {
            get => !IsTx;
            set => IsTx = !value;
        }

        public bool IsTx
        {
            get => GetBit(0b0000_0001);
            set => SetBit(0b0000_0001, value);
        }

        public bool Rtr
        {
            get => GetBit(0b0000_0010);
            set => SetBit(0b0000_0010, value);
        }

        public bool IsExt
        {
            get => GetBit(0b0000_0100);
            set => SetBit(0b0000_0100, value);
        }

        public bool IsRecorded
        {
            get => GetBit(0b0100_0000);
            set => SetBit(0b0100_0000, value);
        }

        public bool IsErrorFrame => Id == ErrorFrameId;

        public const int ErrorFrameId = -1;

        public override string ToString()
        {
            return $"{TimestampUtils.GetString(TimeUS)} {Id:X} {(IsTx ? "Tx" : "Rx")} DLC={Dlc} RTR={Rtr} Ext={IsExt} {Data.ToHex()}";
        }
    }
}
