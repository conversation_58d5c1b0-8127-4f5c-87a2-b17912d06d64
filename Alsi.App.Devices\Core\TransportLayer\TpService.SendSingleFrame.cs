using Alsi.App.Devices.Core.TransportLayer.Frames;
using Alsi.Common.Utils.Autosar;

namespace Alsi.App.Devices.Core.TransportLayer
{
    public partial class TpService
    {
        /// <summary>
        /// 发送单帧
        /// </summary>
        public bool SendSingleFrame(Request request)
        {
            if (SingleFrame.TryBuild(request.Payload, request.PayloadLength, request.IsCanfd, out var singleFrame))
            {
                // [发送方] 单帧（SF）
                if (request.IsCanfd) //canfd方式发送数据
                {
                    HandleRequest(request.RequestId,
                        (byte)DlcUtils.GetDlc(singleFrame.Data.Length < 8 ? 8 : singleFrame.Data.Length),
                        singleFrame.Data, request.IsCanfd, request.RequestIsExt);
                }
                else
                {
                    HandleRequest(request.RequestId, 8, singleFrame.Data,
                        request.IsCanfd, request.RequestIsExt);
                }

                Context.Recorder.Add(singleFrame);

                return true;
            }

            return false;
        }
    }
}
