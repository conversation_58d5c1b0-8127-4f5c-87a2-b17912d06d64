using Alsi.App.Devices.Core;
using Alsi.Common.Utils.Autosar;
using Alsi.Fuzz.Core.Service.CaseFactory.Core;
using System;
using System.Collections.Generic;

namespace Alsi.Fuzz.Core.Service.CaseFactory.Factories.Iso11898
{
    public class G21_CaseFactory : ICaseFactory
    {
        public IsoType IsoType => IsoType.Iso11898;

        public CaseMutation[] Generate(MutationOptions options)
        {
            var list = new List<CaseMutation>();
            if (options.CommunicationType != CommunicationType.Can)
            {
                return list.ToArray();
            }

            var random = options.Random;

            // G211
            for (var i = 0; i <= 7; i++)
            {
                foreach (var item in new byte[] { 0, 1, 0xFE, 0xFF })
                {
                    var id = i * 100 + item;
                    var bytes = new byte[0];
                    var mutation = CaseMutation.Create($"G211-ID_{id:X}")
                        .MutateId(id)
                        .MutateDlc(0)
                        .MutateRtr(0)
                        .MutateExt(false)
                        .MutateData(bytes);
                    list.Add(mutation);
                }
            }

            // G212
            var idG212 = options.Random.Next(0, 0x800);
            var mutationG212 = CaseMutation.Create($"G212-ID_{idG212:X}")
                .MutateId(idG212)
                .MutateDlc(0)
                .MutateRtr(1)
                .MutateExt(false)
                .MutateData(Array.Empty<byte>());
            list.Add(mutationG212);

            // G213
            for (var i = 0; i <= 7; i++)
            {
                foreach (var item in new byte[] { 0, 1, 0xFE, 0xFF })
                {
                    var id = i * 100 + item;
                    var dlc = random.Next(1, 9);
                    var bytes = new byte[DlcUtils.GetDataLength(dlc)];
                    random.NextBytes(bytes);
                    var mutation = CaseMutation.Create($"G213-ID_{id:X}")
                        .MutateId(id)
                        .MutateDlc(dlc)
                        .MutateRtr(0)
                        .MutateExt(false)
                        .MutateData(bytes);
                    list.Add(mutation);
                }
            }

            // G214
            var idG214 = random.Next(0, 0x800);
            var mutationG214 = CaseMutation.Create($"G214-ID_{idG214:X}")
                .MutateId(idG214)
                .MutateDlc(random.Next(1, 9))
                .MutateRtr(1)
                .MutateExt(false)
                .MutateData(Array.Empty<byte>());
            list.Add(mutationG214);

            // G215
            var idG215 = random.Next(0, 0x800);
            var bytes8 = new byte[8];
            random.NextBytes(bytes8);
            var mutationG215 = CaseMutation.Create($"G215-ID_{idG215:X}")
                .MutateId(idG215)
                .MutateDlc(random.Next(9, 16))
                .MutateRtr(0)
                .MutateExt(false)
                .MutateData(bytes8);
            list.Add(mutationG215);

            // G216
            var idG216 = random.Next(0, 0x800);
            var mutationG216 = CaseMutation.Create($"G216-ID_{idG216:X}")
                .MutateId(idG216)
                .MutateDlc(random.Next(9, 16))
                .MutateRtr(1)
                .MutateExt(false)
                .MutateData(Array.Empty<byte>());
            list.Add(mutationG216);

            // G217
            foreach (var id in new int[] { 0x0, 0x1, 0xFF, 0x1FF, 0x1FFF, 0x1FFFF, 0x1FFFFF, 0x1FFFFF, 0x1FFFFFF, 0xFFFFFFF, 0x10000000, 0x10000001, 0x1FFFFFFE, 0x1FFFFFFF })
            {
                var bytes = new byte[0];
                var mutation = CaseMutation.Create($"G217-ID_{id:X}")
                    .MutateId(id)
                    .MutateDlc(0)
                    .MutateRtr(0)
                    .MutateExt(true)
                    .MutateData(bytes);
                list.Add(mutation);
            }

            // G218
            var idG218 = random.Next(0, 0x20000000);
            var mutationG218 = CaseMutation.Create($"G218-ID_{idG218:X}")
                .MutateId(idG218)
                .MutateDlc(random.Next(1, 9))
                .MutateRtr(1)
                .MutateExt(true)
                .MutateData(Array.Empty<byte>());
            list.Add(mutationG218);

            // G219
            foreach (var id in new int[] { 0x0, 0x1, 0xFF, 0x1FF, 0x1FFF, 0x1FFFF, 0x1FFFFF, 0x1FFFFF, 0x1FFFFFF, 0xFFFFFFF, 0x10000000, 0x10000001, 0x1FFFFFFE, 0x1FFFFFFF })
            {
                var dlc = random.Next(1, 9);
                var bytes = new byte[DlcUtils.GetDataLength(dlc)];
                random.NextBytes(bytes);
                var mutation = CaseMutation.Create($"G219-ID_{id:X}")
                    .MutateId(id)
                    .MutateDlc(dlc)
                    .MutateRtr(0)
                    .MutateExt(true)
                    .MutateData(bytes);
                list.Add(mutation);
            }

            // G21A
            var idG21A = random.Next(0, 0x20000000);
            var mutationG21A = CaseMutation.Create($"G21A-ID_{idG21A:X}")
                .MutateId(idG21A)
                .MutateDlc(random.Next(1, 9))
                .MutateRtr(1)
                .MutateExt(true)
                .MutateData(Array.Empty<byte>());
            list.Add(mutationG21A);

            // G21B
            var idG21B = random.Next(0, 0x20000000);
            var bytesG21B = new byte[8];
            random.NextBytes(bytesG21B);
            var mutationG21B = CaseMutation.Create($"G21B-ID_{idG21B:X}")
                .MutateId(idG21B)
                .MutateDlc(random.Next(9, 16))
                .MutateRtr(0)
                .MutateExt(true)
                .MutateData(bytesG21B);
            list.Add(mutationG21B);

            // G21C
            var idG21C = random.Next(0, 0x20000000);
            var mutationG21C = CaseMutation.Create($"G21C-ID_{idG21C:X}")
                .MutateId(idG21C)
                .MutateDlc(random.Next(9, 16))
                .MutateRtr(1)
                .MutateExt(true)
                .MutateData(Array.Empty<byte>());
            list.Add(mutationG21C);

            return list.ToArray();
        }
    }
}
