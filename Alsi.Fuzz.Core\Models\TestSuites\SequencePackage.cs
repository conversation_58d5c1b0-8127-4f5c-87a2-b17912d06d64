using System.Collections.Generic;
using System.Xml.Serialization;

namespace Alsi.Fuzz.Core.Models.TestSuites
{
    [XmlRoot("package")]
    public class SequencePackage
    {
        [XmlAttribute("name")]
        public string Name { get; set; }

        [XmlElement("sequence")]
        public List<Sequence> Sequences { get; set; } = new List<Sequence>();

        [XmlElement("set-var")]
        public List<SetVar> SetVars { get; set; } = new List<SetVar>();
    }
}
