{"version": 3, "file": "js/112.dc1abed5.js", "mappings": "qUA8DA,MAAMA,EAAW,kBAEJC,EAAU,CACrBC,aAAAA,GACE,OAAIC,EAAAA,GACKC,EAAAA,GAAQC,KAAKH,gBAEfI,EAAAA,EAAMC,IAAI,GAAGP,IACtB,EAEAQ,gBAAAA,CAAiBC,GACf,OAAIN,EAAAA,GACKC,EAAAA,GAAQC,KAAKG,iBAAiBC,GAEhCH,EAAAA,EAAMI,KAAK,GAAGV,WAAmBS,EAC1C,EAEAE,SAAAA,GACE,OAAIR,EAAAA,GACKC,EAAAA,GAAQC,KAAKM,YAEfL,EAAAA,EAAMC,IAAI,GAAGP,eACtB,EAGAY,iBAAAA,GACE,OAAIT,EAAAA,GACKC,EAAAA,GAAQC,KAAKO,oBAEfN,EAAAA,EAAMI,KAAK,GAAGV,wBACvB,GCzFF,MAAMa,EAAa,CAAEC,MAAO,0BACtBC,EAAa,CAAED,MAAO,uBACtBE,EAAa,CAAEF,MAAO,gBACtBG,EAAa,CAAEH,MAAO,gBACtBI,EAAa,CAAEJ,MAAO,kBACtBK,EAAa,CACjBC,IAAK,EACLN,MAAO,eAEHO,EAAa,CACjBD,IAAK,EACLN,MAAO,2BAEHQ,EAAa,CAAER,MAAO,kBACtBS,EAAa,CAAET,MAAO,iBACtBU,EAAc,CAAEV,MAAO,kBACvBW,EAAc,CAAEX,MAAO,WACvBY,EAAc,CAAEZ,MAAO,WACvBa,EAAc,CAAEb,MAAO,WACvBc,EAAc,CAAEd,MAAO,kBACvBe,EAAc,CAAEf,MAAO,wBACvBgB,EAAc,CAAEhB,MAAO,oBACvBiB,EAAc,CAClBX,IAAK,EACLN,MAAO,qBAEHkB,EAAc,CAClBZ,IAAK,EACLN,MAAO,qBAEHmB,EAAc,CAAEnB,MAAO,0BAQ7B,OAA4BoB,EAAAA,EAAAA,IAAiB,CAC3CC,OAAQ,cACRC,KAAAA,CAAMC,GCuMR,MAAMC,GAAUC,EAAAA,EAAAA,KAAI,GACd9B,GAAS8B,EAAAA,EAAAA,IAAmB,CAChCC,gBAAiB,GACjBC,gBAAgB,EAChBC,WAAY,KACZC,eAAe,EACfC,YAAa,EACbC,aAAc,CAAC,GAAM,GAAM,IAAM,IAAM,IAAM,IAAM,IAAM,KACzDC,0BAA2B,MAC3BC,gBAAiB,IACjBC,gBAAiB,IACjBC,UAAW,KACXC,cAAc,EACdC,UAAW,KACXC,cAAe,IACfC,sBAAsB,EACtBC,2BAA2B,EAC3BC,2BAA4B,CAAC,GAAM,GACnCC,aAAc,CACZC,QAAQ,EACRC,iBAAaC,EACbC,QAAS,KAKPC,GAAYtB,EAAAA,EAAAA,IAAc,IAC1BuB,GAAevB,EAAAA,EAAAA,IAAY,IAC3BwB,GAAYxB,EAAAA,EAAAA,IAAsB,IAGlCyB,GAAiBC,EAAAA,EAAAA,KAAS,IACzBH,EAAaI,MAEXH,EAAUG,MAAMC,QAAOC,GAE5BA,EAAMC,cAAgBP,EAAaI,OACnCE,EAAME,UAAUC,SAAST,EAAaI,SALR,KAS5BM,GAAcjC,EAAAA,EAAAA,IAAI,CAAC,OAAQ,WAAY,MAAO,aAE9CkC,GAAkBlC,EAAAA,EAAAA,IAAI,IACtBmC,GAAmBnC,EAAAA,EAAAA,IAAI,IACvBoC,GAAoBpC,EAAAA,EAAAA,IAAI,IACxBqC,GAAiBrC,EAAAA,EAAAA,IAAI,IACrBsC,GAAiBtC,EAAAA,EAAAA,IAAI,IACrBuC,GAAkCvC,EAAAA,EAAAA,IAAI,IAGtCwC,GAAoBd,EAAAA,EAAAA,IAAS,CACjC1D,IAAKA,IAC+C,QAA3CE,EAAOyD,MAAMpB,0BAAsC,YAAc,cAE1EkC,IAAMd,IACJzD,EAAOyD,MAAMpB,0BAAsC,cAAVoB,EAAwB,MAAQ,OAAO,IAI9Ee,GAAkB1C,EAAAA,EAAAA,IAAmB,MACrC2C,GAAkB3C,EAAAA,EAAAA,IAAmB,MACrC4C,GAAkB5C,EAAAA,EAAAA,KAAI,GAEtB6C,EAA+B,CACnC5C,gBAAiB,GACjBC,gBAAgB,EAChBC,WAAY,KACZC,eAAe,EACfC,YAAa,EACbC,aAAc,CAAC,GAAM,GAAM,IAAM,IAAM,IAAM,IAAM,IAAM,KACzDC,0BAA2B,MAC3BC,gBAAiB,IACjBC,gBAAiB,IACjBC,UAAW,KACXC,cAAc,EACdC,UAAW,KACXC,cAAe,IACfC,sBAAsB,EACtBC,2BAA2B,EAC3BC,2BAA4B,CAAC,GAAM,GACnCC,aAAc,CACZC,QAAQ,EACRC,iBAAaC,EACbC,QAAS,IAIPyB,EAAoBC,GACjBA,EAAIC,KAAIC,GAAK,KAAOA,EAAEC,SAAS,IAAIC,gBAAeC,KAAK,KAc1DC,EAA0B1B,IAC9B,IAEE,MAAM2B,EAAa3B,EAAM4B,QAAQ,OAAQ,IACnCC,EAAKC,SAASH,EAAY,IAChC,GAAII,MAAMF,GAER,YADAG,EAAAA,GAAUC,MAAM,qBAGlB1F,EAAOyD,MAAMxB,WAAaqD,EAE1BtB,EAAgBP,MAAQ,KAAO6B,EAAGN,SAAS,IAAIC,a,CAC/C,MAAOS,GACPD,EAAAA,GAAUC,MAAM,sB,GAIdC,EAA2BlC,IAC/B,IAEE,MAAM2B,EAAa3B,EAAM4B,QAAQ,OAAQ,IACnCO,EAAML,SAASH,EAAY,IACjC,GAAII,MAAMI,GAER,YADAH,EAAAA,GAAUC,MAAM,sBAGlB,GAAIE,EAAM,GAAKA,EAAM,GAEnB,YADAH,EAAAA,GAAUI,QAAQ,kCAGpB7F,EAAOyD,MAAMtB,YAAcyD,EAE3B3B,EAAiBR,MAAQ,KAAOmC,EAAIZ,SAAS,IAAIC,a,CACjD,MAAOS,GACPD,EAAAA,GAAUC,MAAM,sB,GAIdI,EAA4BrC,IAChC,IACE,MAAMsC,EAAQtC,EAAMuC,MAAM,KACvBlB,KAAImB,GAAKA,EAAEC,SACXxC,QAAOuC,GAAKA,IACZnB,KAAImB,GAAKV,SAASU,EAAG,MAExB,GAAIF,EAAMI,OAAS,EAEjB,YADAV,EAAAA,GAAUI,QAAQ,2BAGpB,GAAIE,EAAMK,MAAKC,GAAKb,MAAMa,IAAMA,EAAI,GAAKA,EAAI,MAE3C,YADAZ,EAAAA,GAAUI,QAAQ,2CAGpB7F,EAAOyD,MAAMrB,aAAe2D,C,CAC5B,MAAOL,GACPD,EAAAA,GAAUC,MAAM,uB,GAIdY,EAAyB7C,IAC7B,IAEE,MAAM2B,EAAa3B,EAAM4B,QAAQ,OAAQ,IACnCC,EAAKC,SAASH,EAAY,IAChC,GAAII,MAAMF,GAER,YADAG,EAAAA,GAAUC,MAAM,qBAGlB1F,EAAOyD,MAAMjB,UAAY8C,EAEzBnB,EAAeV,MAAQ,KAAO6B,EAAGN,SAAS,IAAIC,a,CAC9C,MAAOS,GACPD,EAAAA,GAAUC,MAAM,sB,GAIda,EAAyB9C,IAC7B,IAEE,MAAM2B,EAAa3B,EAAM4B,QAAQ,OAAQ,IACnCC,EAAKC,SAASH,EAAY,IAChC,GAAII,MAAMF,GAER,YADAG,EAAAA,GAAUC,MAAM,qBAGlB1F,EAAOyD,MAAMf,UAAY4C,EAEzBlB,EAAeX,MAAQ,KAAO6B,EAAGN,SAAS,IAAIC,a,CAC9C,MAAOS,GACPD,EAAAA,GAAUC,MAAM,sB,GAIdc,EAA0C/C,IAC9C,IACE,MAAMsC,EAAQtC,EAAMuC,MAAM,KACvBlB,KAAImB,GAAKA,EAAEC,SACXxC,QAAOuC,GAAKA,IACZnB,KAAImB,GAAKV,SAASU,EAAG,MAExB,GAAIF,EAAMK,MAAKC,GAAKb,MAAMa,IAAMA,EAAI,GAAKA,EAAI,MAE3C,YADAZ,EAAAA,GAAUI,QAAQ,2CAGpB7F,EAAOyD,MAAMX,2BAA6BiD,C,CAC1C,MAAOL,GACPD,EAAAA,GAAUC,MAAM,uB,GAIde,EAAiBA,KACrBzG,EAAOyD,MAAQiD,KAAKC,MAAMD,KAAKE,UAAUjC,IACzCX,EAAgBP,MAAQ,KAAOkB,EAAc1C,WAAW+C,SAAS,IAAIC,cACrEhB,EAAiBR,MAAQ,KAAOkB,EAAcxC,YAAY6C,SAAS,IAAIC,cACvEf,EAAkBT,MAAQmB,EAAiBD,EAAcvC,cACzD+B,EAAeV,MAAQ,KAAOkB,EAAcnC,UAAUwC,SAAS,IAAIC,cACnEb,EAAeX,MAAQ,KAAOkB,EAAcjC,UAAUsC,SAAS,IAAIC,cACnEZ,EAAgCZ,MAAQmB,EAAiBD,EAAc7B,4BACvE0B,EAAgBf,MAAQ,KACxBgB,EAAgBhB,MAAQ,KACxBiB,EAAgBjB,OAAQ,EAGxBL,EAAUK,MAAQ,GAClBJ,EAAaI,MAAQ,GACrBH,EAAUG,MAAQ,EAAE,EAGhBoD,EAAaC,UACjBjF,EAAQ4B,OAAQ,EAChB,IACE,MAAMsD,QAAiBvH,EAAQC,gBAmC/B,GAlCAO,EAAOyD,MAAQsD,EAASC,KACxBhD,EAAgBP,MAAQ,KAAOsD,EAASC,KAAK/E,WAAW+C,SAAS,IAAIC,cACrEf,EAAkBT,MAAQmB,EAAiBmC,EAASC,KAAK5E,cACzD+B,EAAeV,MAAQ,KAAOsD,EAASC,KAAKxE,UAAUwC,SAAS,IAAIC,cACnEb,EAAeX,MAAQ,KAAOsD,EAASC,KAAKtE,UAAUsC,SAAS,IAAIC,cAG/D8B,EAASC,KAAKlE,2BAChBuB,EAAgCZ,MAAQmB,EAAiBmC,EAASC,KAAKlE,4BAEvEuB,EAAgCZ,MAAQ,iBAINP,IAAhC6D,EAASC,KAAK9E,gBAChBlC,EAAOyD,MAAMvB,eAAgB,QAEGgB,IAA9B6D,EAASC,KAAK7E,cAChBnC,EAAOyD,MAAMtB,YAAc,QAEMe,IAA/B6D,EAASC,KAAKvE,eAChBzC,EAAOyD,MAAMhB,cAAe,QAEkBS,IAA5C6D,EAASC,KAAKnE,4BAChB7C,EAAOyD,MAAMZ,2BAA4B,QAEMK,IAA7C6D,EAASC,KAAKlE,6BAChB9C,EAAOyD,MAAMX,2BAA6B,CAAC,GAAM,IAInDmB,EAAiBR,MAAQ,KAAOzD,EAAOyD,MAAMtB,YAAY6C,SAAS,IAAIC,cAGlE8B,EAASC,KAAKjF,gBAAgBoE,OAAS,EAAG,CAC5C,MAAMc,EAASF,EAASC,KAAKjF,gBAC7BuB,EAAUG,MAAQwD,EAGlB,MAAMC,EAAQ,IAAIC,IAClBF,EAAOG,SAAQzD,IACTA,EAAMC,aAAasD,EAAMG,IAAI1D,EAAMC,aACvCD,EAAME,WAAWuD,SAAQE,GAAKJ,EAAMG,IAAIC,IAAG,IAG7ClE,EAAUK,MAAQ8D,MAAMC,KAAKN,GAGzBH,EAASC,KAAKS,kBAAoBrE,EAAUK,MAAMK,SAASiD,EAASC,KAAKS,kBAC3EpE,EAAaI,MAAQsD,EAASC,KAAKS,iBAGD,IAA3BrE,EAAUK,MAAM0C,SACvB9C,EAAaI,MAAQL,EAAUK,MAAM,G,EAGzC,MAAOiC,GACPD,EAAAA,GAAUC,MAAM,+B,CAChB,QACA7D,EAAQ4B,OAAQ,C,GAKdiE,EAAoBjE,IAEnBA,GACHgC,EAAAA,GAAUI,QAAQ,6B,EAIhB8B,GAAab,UACjBjF,EAAQ4B,OAAQ,EAChB,IAEE,MAAMmE,EAAa,IAAK5H,EAAOyD,OAG/BmE,EAAW7F,gBAAkBuB,EAAUG,MAGvCmE,EAAWH,iBAAmBpE,EAAaI,MAEvCe,EAAgBf,QAClBmE,EAAWC,gBAAkBrD,EAAgBf,OAG3CiB,EAAgBjB,QAClBmE,EAAWE,mBAAoB,GAGjC,MAAMC,QAAevI,EAAQO,iBAAiB6H,GAC9C5H,EAAOyD,MAAQsE,EAAOf,KAEtBxC,EAAgBf,MAAQ,KACxBgB,EAAgBhB,MAAQ,KACxBiB,EAAgBjB,OAAQ,EAExBgC,EAAAA,GAAUuC,QAAQ,kB,CAClB,MAAOtC,GACPD,EAAAA,GAAUC,MAAM,c,CAChB,QACA7D,EAAQ4B,OAAQ,C,GAIdwE,IAAgBnG,EAAAA,EAAAA,KAAI,GAEpBoG,GAAkBpB,UACtBmB,GAAcxE,OAAQ,EACtB,IACE,MAAMsD,QAAiBvH,EAAQU,YAG/BoD,EAAUG,MAAQsD,EAASC,KAAKjF,gBAChCqB,EAAUK,MAAQsD,EAASC,KAAK5D,UAGhCC,EAAaI,MAAQ,GAGrBzD,EAAOyD,MAAM1B,gBAAkB,GAE/B0D,EAAAA,GAAUuC,QAAQ,8D,CAClB,MAAOtC,GACP,GAA6B,iBAAzBA,EAAMqB,UAAUC,KAClB,OAEFvB,EAAAA,GAAUC,MAA+B,sBAAzBA,EAAMqB,UAAUC,KAC5B,0BACA,4B,CACJ,QACAiB,GAAcxE,OAAQ,C,GAIpB0E,GAAkBpC,IACtB,IAAKA,EAAO,MAAO,MAEnB,MAAMqC,EAAQ,CAAC,IAAK,KAAM,KAAM,MAChC,IAAIC,EAAOtC,EACPuC,EAAY,EAEhB,MAAOD,GAAQ,MAAQC,EAAYF,EAAMjC,OAAS,EAChDkC,GAAQ,KACRC,IAGF,MAAO,GAAGD,EAAKE,QAAQ,MAAMH,EAAME,IAAY,EAG3CE,GAAY1B,UAChB,IACE,MAAMC,QAAiBvH,EAAQW,oBACzBsI,EAAU1B,EAASC,KAAK0B,KAC9BlE,EAAgBf,MAAQgF,EACxBhE,EAAgBhB,MAAQgF,EAAQzC,MAAM,MAAM2C,OAASF,C,CACrD,MAAO/C,GACP,GAA6B,iBAAzBA,EAAMqB,UAAUC,KAClB,OAEFvB,EAAAA,GAAUC,MAA+B,sBAAzBA,EAAMqB,UAAUC,KAC5B,0BACA,4B,GAIF4B,GAAYA,KAChBlE,EAAgBjB,OAAQ,CAAI,EAGxBoF,IAAqBrF,EAAAA,EAAAA,KAAS,IAC9BiB,EAAgBhB,MACXgB,EAAgBhB,MAGrBiB,EAAgBjB,MACX,iCAGFzD,EAAOyD,MAAMV,cAAcC,OAC9B,GAAGhD,EAAOyD,MAAMV,aAAaE,gBAAgBkF,GAAenI,EAAOyD,MAAMV,aAAaI,YACtF,KAMA2F,GAAYA,KAChB/E,EAAYN,MAAQ,CAAC,OAAQ,WAAY,MAAO,WAAW,EAIvDsF,GAAcA,KAClBhF,EAAYN,MAAQ,EAAE,ED9LxB,OCiMAuF,EAAAA,EAAAA,KAAU,KACRnC,GAAY,IDlMP,CAACoC,EAAUC,KAChB,MAAMC,GAA+BC,EAAAA,EAAAA,IAAkB,qBACjDC,GAAuBD,EAAAA,EAAAA,IAAkB,aACzCE,GAA0BF,EAAAA,EAAAA,IAAkB,gBAC5CG,GAAuBH,EAAAA,EAAAA,IAAkB,aACzCI,GAAuBJ,EAAAA,EAAAA,IAAkB,aACzCK,IAA6BL,EAAAA,EAAAA,IAAkB,mBAC/CM,IAAsBN,EAAAA,EAAAA,IAAkB,YACxCO,IAAqBP,EAAAA,EAAAA,IAAkB,WACvCQ,IAA8BR,EAAAA,EAAAA,IAAkB,oBAChDS,IAAuBT,EAAAA,EAAAA,IAAkB,aACzCU,IAAsBV,EAAAA,EAAAA,IAAkB,YACxCW,IAA6BX,EAAAA,EAAAA,IAAkB,mBAC/CY,IAAyBZ,EAAAA,EAAAA,IAAkB,eAC3Ca,IAAqBC,EAAAA,EAAAA,IAAkB,WAE7C,OAAOC,EAAAA,EAAAA,MAAiBC,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAoB,MAAOjK,EAAY,EAC3EkK,EAAAA,EAAAA,IAAoB,MAAOhK,EAAY,EACrCgK,EAAAA,EAAAA,IAAoB,MAAO/J,EAAY,EACrCgK,EAAAA,EAAAA,IAAalB,EAAsB,CACjCmB,QAAS1B,GACT2B,KAAM,UACNpC,KAAM,QACNhI,MAAO,iBACN,CACDqK,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBJ,EAAAA,EAAAA,IAAapB,EAA8B,CAAEyB,KAAM,uCACnD1B,EAAO,MAAQA,EAAO,KAAMoB,EAAAA,EAAAA,IAAoB,OAAQ,CAAEjK,MAAO,eAAiB,cAAe,OAEnGwK,EAAG,KAELN,EAAAA,EAAAA,IAAalB,EAAsB,CACjCmB,QAASzB,GACT0B,KAAM,UACNpC,KAAM,QACNhI,MAAO,mBACN,CACDqK,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBJ,EAAAA,EAAAA,IAAapB,EAA8B,CAAEyB,KAAM,qCACnD1B,EAAO,MAAQA,EAAO,KAAMoB,EAAAA,EAAAA,IAAoB,OAAQ,CAAEjK,MAAO,eAAiB,gBAAiB,OAErGwK,EAAG,SAITP,EAAAA,EAAAA,IAAoB,MAAO9J,EAAY,EACrC+J,EAAAA,EAAAA,IAAaP,GAAwB,CACnCc,WAAY/G,EAAYN,MACxB,sBAAuByF,EAAO,MAAQA,EAAO,IAAO6B,GAAkBhH,EAAaN,MAAQsH,IAC1F,CACDL,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBL,EAAAA,EAAAA,IAAoB,MAAO7J,EAAY,EACrC8J,EAAAA,EAAAA,IAAaX,GAA6B,CACxCoB,MAAO,eACPC,KAAM,OACN5K,MAAO,eACN,CACDqK,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBJ,EAAAA,EAAAA,IAAaZ,GAAoB,CAC/BuB,MAAOlL,EAAOyD,MACd,cAAe,QACf,iBAAkB,OACjB,CACDiH,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBJ,EAAAA,EAAAA,IAAajB,EAAyB,KAAM,CAC1CoB,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBJ,EAAAA,EAAAA,IAAalB,EAAsB,CACjCmB,QAAStC,GACTrG,QAASoG,GAAcxE,MACvBgH,KAAM,UACNpC,KAAM,QACN8C,MAAO,CAAC,gBAAgB,SACvB,CACDT,SAASC,EAAAA,EAAAA,KAAS,IAAMzB,EAAO,MAAQA,EAAO,IAAM,EAClDkC,EAAAA,EAAAA,IAAiB,oBAEnBP,EAAG,GACF,EAAG,CAAC,eAETA,EAAG,IAEJzH,EAAUK,MAAM0C,OAAS,IACrBiE,EAAAA,EAAAA,OAAciB,EAAAA,EAAAA,IAAa/B,EAAyB,CACnD3I,IAAK,EACL2K,MAAO,oBACN,CACDZ,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBJ,EAAAA,EAAAA,IAAaf,EAAsB,CACjCsB,WAAYzH,EAAaI,MACzB,sBAAuByF,EAAO,KAAOA,EAAO,GAAM6B,GAAkB1H,EAAcI,MAAQsH,GAC1FQ,YAAa,oBACbJ,MAAO,CAAC,MAAQ,QAChBK,SAAU9D,GACT,CACDgD,SAASC,EAAAA,EAAAA,KAAS,IAAM,GACrBP,EAAAA,EAAAA,KAAW,IAAOC,EAAAA,EAAAA,IAAoBoB,EAAAA,GAAW,MAAMC,EAAAA,EAAAA,IAAYtI,EAAUK,OAAQkI,KAC5EvB,EAAAA,EAAAA,OAAciB,EAAAA,EAAAA,IAAa9B,EAAsB,CACvD5I,IAAKgL,EACLL,MAAOK,EACPlI,MAAOkI,GACN,KAAM,EAAG,CAAC,QAAS,aACpB,SAENd,EAAG,GACF,EAAG,CAAC,kBAETA,EAAG,MAELe,EAAAA,EAAAA,IAAoB,IAAI,IAC5BrB,EAAAA,EAAAA,IAAajB,EAAyB,CACpCgC,MAAO,sBAAsB/H,EAAeE,MAAM0C,WACjD,CACDuE,SAASC,EAAAA,EAAAA,KAAS,IAAM,CACM,IAA3BrH,EAAUG,MAAM0C,SACZiE,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAoB,MAAO3J,EAAY,8EACpD2C,EAAaI,QAEZ2G,EAAAA,EAAAA,OAAciB,EAAAA,EAAAA,IAAa3B,GAAqB,CAC/C/I,IAAK,EACLqG,KAAMzD,EAAeE,MACrB0H,MAAO,CAAC,MAAQ,QAChB,aAAc,IACdU,OAAQ,IACP,CACDnB,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBJ,EAAAA,EAAAA,IAAad,GAA4B,CACvC6B,MAAO,KACP,YAAa,OACZ,CACDZ,SAASC,EAAAA,EAAAA,KAAS,EAAEmB,SAAS,EAC3BV,EAAAA,EAAAA,IAAiB,OAAQW,EAAAA,EAAAA,IAAiBD,EAAIxG,GAAGN,SAAS,IAAIC,cAAc+G,SAAS,EAAG,MAAO,MAEjGnB,EAAG,KAELN,EAAAA,EAAAA,IAAad,GAA4B,CACvCwC,KAAM,OACNX,MAAO,OACP,YAAa,SAEff,EAAAA,EAAAA,IAAad,GAA4B,CACvC6B,MAAO,MACP,YAAa,MACZ,CACDZ,SAASC,EAAAA,EAAAA,KAAS,EAAEmB,SAAS,EAC3BV,EAAAA,EAAAA,IAAiB,OAAQW,EAAAA,EAAAA,IAAiBD,EAAIlG,IAAIZ,SAAS,IAAIC,eAAgB,MAEjF4F,EAAG,KAELN,EAAAA,EAAAA,IAAad,GAA4B,CACvC6B,MAAO,WACP,YAAa,OACZ,CACDZ,SAASC,EAAAA,EAAAA,KAAS,EAAEmB,SAAS,EAC3BV,EAAAA,EAAAA,KAAiBW,EAAAA,EAAAA,IAAiBD,EAAII,MAAQ,eAAiB,gBAAiB,MAElFrB,EAAG,KAELN,EAAAA,EAAAA,IAAad,GAA4B,CACvCwC,KAAM,cACNX,MAAO,cACP,YAAa,SAEff,EAAAA,EAAAA,IAAad,GAA4B,CACvC6B,MAAO,YACP,YAAa,OACZ,CACDZ,SAASC,EAAAA,EAAAA,KAAS,EAAEmB,SAAS,EAC3BV,EAAAA,EAAAA,KAAiBW,EAAAA,EAAAA,IAAiBD,EAAIjI,UAAUqB,KAAK,OAAQ,MAE/D2F,EAAG,OAGPA,EAAG,GACF,EAAG,CAAC,YAzDNT,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAoB,MAAOzJ,EAAY,2DA2D9DiK,EAAG,GACF,EAAG,CAAC,aAETA,EAAG,GACF,EAAG,CAAC,aAETA,EAAG,OAGPP,EAAAA,EAAAA,IAAoB,MAAOzJ,EAAY,EACrC0J,EAAAA,EAAAA,IAAaX,GAA6B,CACxCoB,MAAO,qBACPC,KAAM,WACN5K,MAAO,eACN,CACDqK,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBJ,EAAAA,EAAAA,IAAaZ,GAAoB,CAC/BuB,MAAOlL,EAAOyD,MACd,cAAe,QACf,iBAAkB,OACjB,CACDiH,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBJ,EAAAA,EAAAA,IAAajB,EAAyB,CAAEgC,MAAO,qBAAuB,CACpEZ,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBJ,EAAAA,EAAAA,IAAaV,GAAsB,CACjCiB,WAAY9K,EAAOyD,MAAMzB,eACzB,sBAAuBkH,EAAO,KAAOA,EAAO,GAAM6B,GAAkB/K,EAAOyD,MAAMzB,eAAkB+I,IAClG,KAAM,EAAG,CAAC,kBAEfF,EAAG,KAELP,EAAAA,EAAAA,IAAoB,MAAO,CACzBjK,OAAO8L,EAAAA,EAAAA,IAAgB,CAAE,yBAA0BnM,EAAOyD,MAAMzB,kBAC/D,EACDuI,EAAAA,EAAAA,IAAab,GAAqB,CAChC1C,KAAM,CAAC,CAAC,GACR6E,OAAQ,GACRV,MAAO,CAAC,MAAQ,OAAO,gBAAgB,QACvC,eAAe,EACf9K,MAAO,YACN,CACDqK,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBJ,EAAAA,EAAAA,IAAad,GAA4B,CACvC6B,MAAO,KACPc,MAAO,OACN,CACD1B,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBJ,EAAAA,EAAAA,IAAaT,GAAqB,CAChCgB,WAAY9G,EAAgBP,MAC5B,sBAAuByF,EAAO,KAAOA,EAAO,GAAM6B,GAAkB/G,EAAiBP,MAAQsH,GAC7FQ,YAAa,8BACbC,SAAUrG,EACVkH,UAAWrM,EAAOyD,MAAMzB,gBACvB,KAAM,EAAG,CAAC,aAAc,gBAE7B6I,EAAG,KAELN,EAAAA,EAAAA,IAAad,GAA4B,CACvC6B,MAAO,WACPc,MAAO,OACN,CACD1B,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBJ,EAAAA,EAAAA,IAAaV,GAAsB,CACjCiB,WAAY9K,EAAOyD,MAAMvB,cACzB,sBAAuBgH,EAAO,KAAOA,EAAO,GAAM6B,GAAkB/K,EAAOyD,MAAMvB,cAAiB6I,GAClGsB,UAAWrM,EAAOyD,MAAMzB,gBACvB,KAAM,EAAG,CAAC,aAAc,gBAE7B6I,EAAG,KAELN,EAAAA,EAAAA,IAAad,GAA4B,CACvC6B,MAAO,aACPc,MAAO,OACN,CACD1B,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBJ,EAAAA,EAAAA,IAAaf,EAAsB,CACjCsB,WAAYxG,EAAkBb,MAC9B,sBAAuByF,EAAO,KAAOA,EAAO,GAAM6B,GAAkBzG,EAAmBb,MAAQsH,GAC/FsB,UAAWrM,EAAOyD,MAAMzB,gBACvB,CACD0I,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBJ,EAAAA,EAAAA,IAAahB,EAAsB,CACjC9F,MAAO,YACP6H,MAAO,eAETf,EAAAA,EAAAA,IAAahB,EAAsB,CACjC9F,MAAO,cACP6H,MAAO,mBAGXT,EAAG,GACF,EAAG,CAAC,aAAc,gBAEvBA,EAAG,KAELN,EAAAA,EAAAA,IAAad,GAA4B,CACvC6B,MAAO,MACPc,MAAO,OACN,CACD1B,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBJ,EAAAA,EAAAA,IAAaT,GAAqB,CAChCgB,WAAY7G,EAAiBR,MAC7B,sBAAuByF,EAAO,KAAOA,EAAO,GAAM6B,GAAkB9G,EAAkBR,MAAQsH,GAC9FQ,YAAa,+BACbC,SAAU7F,EACV0G,UAAWrM,EAAOyD,MAAMzB,gBACvB,KAAM,EAAG,CAAC,aAAc,gBAE7B6I,EAAG,KAELN,EAAAA,EAAAA,IAAad,GAA4B,CAAE6B,MAAO,QAAU,CAC1DZ,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBJ,EAAAA,EAAAA,IAAaT,GAAqB,CAChCgB,WAAY5G,EAAkBT,MAC9B,sBAAuByF,EAAO,KAAOA,EAAO,GAAM6B,GAAkB7G,EAAmBT,MAAQsH,GAC/FQ,YAAa,mEACbC,SAAU1F,EACVuG,UAAWrM,EAAOyD,MAAMzB,gBACvB,KAAM,EAAG,CAAC,aAAc,gBAE7B6I,EAAG,OAGPA,EAAG,KAELP,EAAAA,EAAAA,IAAoB,MAAOxJ,EAAY,EACrCyJ,EAAAA,EAAAA,IAAajB,EAAyB,CACpCgC,MAAO,aACPjL,MAAO,kBACN,CACDqK,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBJ,EAAAA,EAAAA,IAAaR,GAA4B,CACvCe,WAAY9K,EAAOyD,MAAMnB,gBACzB,sBAAuB4G,EAAO,KAAOA,EAAO,GAAM6B,GAAkB/K,EAAOyD,MAAMnB,gBAAmByI,GACpGuB,IAAK,EACLC,IAAK,IACLpB,MAAO,CAAC,MAAQ,SAChBkB,UAAWrM,EAAOyD,MAAMzB,gBACvB,KAAM,EAAG,CAAC,aAAc,gBAE7B6I,EAAG,KAELN,EAAAA,EAAAA,IAAajB,EAAyB,CACpCgC,MAAO,qBACPjL,MAAO,kBACN,CACDqK,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBJ,EAAAA,EAAAA,IAAaR,GAA4B,CACvCe,WAAY9K,EAAOyD,MAAMlB,gBACzB,sBAAuB2G,EAAO,KAAOA,EAAO,GAAM6B,GAAkB/K,EAAOyD,MAAMlB,gBAAmBwI,GACpGuB,IAAK,EACLC,IAAK,IACLpB,MAAO,CAAC,MAAQ,SAChBkB,UAAWrM,EAAOyD,MAAMzB,gBACvB,KAAM,EAAG,CAAC,aAAc,gBAE7B6I,EAAG,OAGN,MAELA,EAAG,GACF,EAAG,CAAC,aAETA,EAAG,OAGPP,EAAAA,EAAAA,IAAoB,MAAOvJ,EAAa,EACtCwJ,EAAAA,EAAAA,IAAaX,GAA6B,CACxCoB,MAAO,cACPC,KAAM,MACN5K,MAAO,eACN,CACDqK,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBJ,EAAAA,EAAAA,IAAaZ,GAAoB,CAC/BuB,MAAOlL,EAAOyD,MACd,cAAe,QACf,iBAAkB,OACjB,CACDiH,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBL,EAAAA,EAAAA,IAAoB,MAAOtJ,EAAa,EACtCuJ,EAAAA,EAAAA,IAAajB,EAAyB,CACpCgC,MAAO,wBACPjL,MAAO,YACN,CACDqK,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBJ,EAAAA,EAAAA,IAAaT,GAAqB,CAChCgB,WAAY3G,EAAeV,MAC3B,sBAAuByF,EAAO,KAAOA,EAAO,GAAM6B,GAAkB5G,EAAgBV,MAAQsH,GAC5FQ,YAAa,8BACbC,SAAUlF,GACT,KAAM,EAAG,CAAC,kBAEfuE,EAAG,KAELN,EAAAA,EAAAA,IAAajB,EAAyB,CACpCgC,MAAO,WACPjL,MAAO,YACN,CACDqK,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBJ,EAAAA,EAAAA,IAAaV,GAAsB,CACjCiB,WAAY9K,EAAOyD,MAAMhB,aACzB,sBAAuByG,EAAO,MAAQA,EAAO,IAAO6B,GAAkB/K,EAAOyD,MAAMhB,aAAgBsI,IAClG,KAAM,EAAG,CAAC,kBAEfF,EAAG,KAELN,EAAAA,EAAAA,IAAajB,EAAyB,CACpCgC,MAAO,yBACPjL,MAAO,YACN,CACDqK,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBJ,EAAAA,EAAAA,IAAaT,GAAqB,CAChCgB,WAAY1G,EAAeX,MAC3B,sBAAuByF,EAAO,MAAQA,EAAO,IAAO6B,GAAkB3G,EAAgBX,MAAQsH,GAC9FQ,YAAa,8BACbC,SAAUjF,GACT,KAAM,EAAG,CAAC,kBAEfsE,EAAG,OAGPP,EAAAA,EAAAA,IAAoB,MAAOrJ,EAAa,EACtCsJ,EAAAA,EAAAA,IAAajB,EAAyB,CACpCgC,MAAO,0BACPjL,MAAO,YACN,CACDqK,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBJ,EAAAA,EAAAA,IAAaR,GAA4B,CACvCe,WAAY9K,EAAOyD,MAAMd,cACzB,sBAAuBuG,EAAO,MAAQA,EAAO,IAAO6B,GAAkB/K,EAAOyD,MAAMd,cAAiBoI,GACpGuB,IAAK,EACLC,IAAK,IACLpB,MAAO,CAAC,MAAQ,UACf,KAAM,EAAG,CAAC,kBAEfN,EAAG,KAELN,EAAAA,EAAAA,IAAajB,EAAyB,CACpCgC,MAAO,uBACPjL,MAAO,YACN,CACDqK,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBJ,EAAAA,EAAAA,IAAaV,GAAsB,CACjCiB,WAAY9K,EAAOyD,MAAMb,qBACzB,sBAAuBsG,EAAO,MAAQA,EAAO,IAAO6B,GAAkB/K,EAAOyD,MAAMb,qBAAwBmI,IAC1G,KAAM,EAAG,CAAC,kBAEfF,EAAG,OAGPP,EAAAA,EAAAA,IAAoB,MAAOpJ,EAAa,EACtCqJ,EAAAA,EAAAA,IAAajB,EAAyB,CACpCgC,MAAO,0BACPjL,MAAO,YACN,CACDqK,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBJ,EAAAA,EAAAA,IAAaV,GAAsB,CACjCiB,WAAY9K,EAAOyD,MAAMZ,0BACzB,sBAAuBqG,EAAO,MAAQA,EAAO,IAAO6B,GAAkB/K,EAAOyD,MAAMZ,0BAA6BkI,IAC/G,KAAM,EAAG,CAAC,kBAEfF,EAAG,KAELN,EAAAA,EAAAA,IAAajB,EAAyB,CACpCgC,MAAO,2BACPjL,MAAO,YACN,CACDqK,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBJ,EAAAA,EAAAA,IAAaT,GAAqB,CAChCgB,WAAYzG,EAAgCZ,MAC5C,sBAAuByF,EAAO,MAAQA,EAAO,IAAO6B,GAAkB1G,EAAiCZ,MAAQsH,GAC/GQ,YAAa,qCACbC,SAAUhF,EACV6F,UAAWrM,EAAOyD,MAAMZ,2BACvB,KAAM,EAAG,CAAC,aAAc,gBAE7BgI,EAAG,SAITA,EAAG,GACF,EAAG,CAAC,aAETA,EAAG,OAGPP,EAAAA,EAAAA,IAAoB,MAAOnJ,EAAa,EACtCoJ,EAAAA,EAAAA,IAAaX,GAA6B,CACxCoB,MAAO,0BACPC,KAAM,WACN5K,MAAO,eACN,CACDqK,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBJ,EAAAA,EAAAA,IAAaZ,GAAoB,CAC/BuB,MAAOlL,EAAOyD,MACd,cAAe,QACf,iBAAkB,OACjB,CACDiH,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBJ,EAAAA,EAAAA,IAAajB,EAAyB,CAAEgC,MAAO,uBAAyB,CACtEZ,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBL,EAAAA,EAAAA,IAAoB,MAAOlJ,EAAa,EACtCmJ,EAAAA,EAAAA,IAAaT,GAAqB,CAChCgB,WAAYjC,GAAmBpF,MAC/B,sBAAuByF,EAAO,MAAQA,EAAO,IAAO6B,GAAkBlC,GAAoBpF,MAAQsH,GAClGQ,YAAa,kBACbiB,SAAU,GACVnM,MAAO,sBACN,KAAM,EAAG,CAAC,gBACbiK,EAAAA,EAAAA,IAAoB,MAAOjJ,EAAa,CACpCrB,EAAOyD,MAAMV,cAAcC,QAQzB4I,EAAAA,EAAAA,IAAoB,IAAI,KAPvBxB,EAAAA,EAAAA,OAAciB,EAAAA,EAAAA,IAAahC,EAAsB,CAChD1I,IAAK,EACL6J,QAAShC,GACTiC,KAAM,UACNG,MAAM6B,EAAAA,EAAAA,IAAOC,EAAAA,MACb1B,MAAO,cACN,KAAM,EAAG,CAAC,UAEhBhL,EAAOyD,MAAMV,cAAcC,SACvBoH,EAAAA,EAAAA,OAAciB,EAAAA,EAAAA,IAAahC,EAAsB,CAChD1I,IAAK,EACL6J,QAAS5B,GACT6B,KAAM,SACNG,MAAM6B,EAAAA,EAAAA,IAAOE,EAAAA,QACb3B,MAAO,cACN,KAAM,EAAG,CAAC,WACbY,EAAAA,EAAAA,IAAoB,IAAI,OAG/BnH,EAAgBhB,QACZ2G,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAoB,MAAO/I,EAAa,0CACvDsK,EAAAA,EAAAA,IAAoB,IAAI,GAC3BlH,EAAgBjB,QACZ2G,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAoB,MAAO9I,EAAa,oDACvDqK,EAAAA,EAAAA,IAAoB,IAAI,MAE9Bf,EAAG,OAGPA,EAAG,GACF,EAAG,CAAC,aAETA,EAAG,SAITA,EAAG,GACF,EAAG,CAAC,kBAETP,EAAAA,EAAAA,IAAoB,MAAO9I,EAAa,EACtC+I,EAAAA,EAAAA,IAAalB,EAAsB,CACjCoB,KAAM,UACND,QAAS7C,IACR,CACD+C,SAASC,EAAAA,EAAAA,KAAS,IAAMzB,EAAO,MAAQA,EAAO,IAAM,EAClDkC,EAAAA,EAAAA,IAAiB,YAEnBP,EAAG,KAELN,EAAAA,EAAAA,IAAalB,EAAsB,CACjCmB,QAAS/D,EACT0E,MAAO,CAAC,cAAc,SACrB,CACDT,SAASC,EAAAA,EAAAA,KAAS,IAAMzB,EAAO,MAAQA,EAAO,IAAM,EAClDkC,EAAAA,EAAAA,IAAiB,aAEnBP,EAAG,SAGJ,CACH,CAACZ,GAAoBpI,EAAQ4B,QAC7B,CAEJ,I,UEjgCA,MAAMmJ,GAA2B,OAAgB,EAAQ,CAAC,CAAC,YAAY,qBAEvE,O", "sources": ["webpack://fuzz-web/./src/api/caseApi.ts", "webpack://fuzz-web/./src/views/testplan/CaseSetting.vue?b1e6", "webpack://fuzz-web/./src/views/testplan/CaseSetting.vue", "webpack://fuzz-web/./src/views/testplan/CaseSetting.vue?4fa3"], "sourcesContent": ["import axios, { AxiosResponse } from 'axios';\r\nimport { USE_MOCK, mockApi } from '@/mock/mockApi';\r\nimport { CaseResult } from '@/api/interoperationApi';\r\n\r\n// 修改 WhiteListFrame 接口，添加 transmitter、receivers 和 IsExt\r\nexport interface WhiteListFrame {\r\n  id: number;\r\n  name: string;\r\n  dlc: number;\r\n  isExt: boolean;\r\n  transmitter: string;\r\n  receivers: string[];\r\n}\r\n\r\n// 安全配置信息接口\r\nexport interface SecurityConfigInfo {\r\n  hasDll: boolean;\r\n  dllFileName?: string;\r\n  dllSize?: number;\r\n}\r\n\r\nexport interface CaseConfigDto {\r\n  // 基本属性 - 移除 whiteListIds, dlc, limitValues\r\n  whiteListFrames: WhiteListFrame[]; // 新增：用于替代 whiteListIds 和 dlc\r\n  selectedNodeName?: string;        // 新增：保存选中的目标节点名称\r\n\r\n  // NM唤醒相关配置\r\n  enableNmWakeup: boolean;\r\n  nmWakeupId: number;\r\n  nmWakeupIsExt: boolean;\r\n  nmWakeupDlc: number;\r\n  nmWakeupData: number[];\r\n  nmWakeupCommunicationType: string;\r\n  nmWakeupCycleMs: number;\r\n  nmWakeupDelayMs: number;\r\n  // 诊断通信相关配置\r\n  diagReqId: number;\r\n  diagReqIsExt: boolean;\r\n  diagResId: number;\r\n  diagTimeoutMs: number;\r\n  isDutMtuLessThan4096?: boolean;   // 新增：DUT MTU小于4096的标识\r\n  enableDiagFallbackRequest?: boolean; // 新增：当诊断请求无响应时，是否发送其它诊断请求\r\n  diagFallbackRequestPayload?: number[]; // 新增：备用诊断请求数据\r\n\r\n  // 安全配置相关\r\n  securityInfo?: SecurityConfigInfo;  // 显示用\r\n  securityDllPath?: string;           // 新选择的DLL路径\r\n  removeSecurityDll?: boolean;        // 是否移除现有DLL\r\n}\r\n\r\n// 向后兼容的类型别名，保持接口一致性\r\nexport type CaseConfig = CaseConfigDto;\r\n\r\nexport interface CaseConfigFromDbc {\r\n  whiteListFrames: WhiteListFrame[]; // 帧列表\r\n  nodeNames: string[];               // 新增：节点名称列表\r\n}\r\n\r\nexport interface SecurityDllPathResponse {\r\n  path: string;\r\n}\r\n\r\nconst BASE_URL = '/api/caseconfig';\r\n\r\nexport const caseApi = {\r\n  getCaseConfig(): Promise<AxiosResponse<CaseConfigDto>> {\r\n    if (USE_MOCK) {\r\n      return mockApi.case.getCaseConfig();\r\n    }\r\n    return axios.get(`${BASE_URL}`);\r\n  },\r\n\r\n  updateCaseConfig(config: CaseConfigDto): Promise<AxiosResponse<CaseConfigDto>> {\r\n    if (USE_MOCK) {\r\n      return mockApi.case.updateCaseConfig(config);\r\n    }\r\n    return axios.post(`${BASE_URL}/update`, config);\r\n  },\r\n\r\n  importDbc(): Promise<AxiosResponse<CaseConfigFromDbc>> {\r\n    if (USE_MOCK) {\r\n      return mockApi.case.importDbc();\r\n    }\r\n    return axios.get(`${BASE_URL}/import-dbc`);\r\n  },\r\n\r\n  // 新增：选择安全DLL文件\r\n  selectSecurityDll(): Promise<AxiosResponse<SecurityDllPathResponse>> {\r\n    if (USE_MOCK) {\r\n      return mockApi.case.selectSecurityDll();\r\n    }\r\n    return axios.post(`${BASE_URL}/select-security-dll`);\r\n  }\r\n};\r\n\r\nexport default caseApi;\r\n", "import { defineComponent as _defineComponent } from 'vue'\nimport { resolveComponent as _resolveComponent, createVNode as _createVNode, createElementVNode as _createElementVNode, withCtx as _withCtx, createTextVNode as _createTextVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, createB<PERSON> as _createBlock, createCommentVNode as _createCommentVNode, toDisplayString as _toDisplayString, normalizeClass as _normalizeClass, unref as _unref, resolveDirective as _resolveDirective, withDirectives as _withDirectives } from \"vue\"\n\nconst _hoisted_1 = { class: \"case-setting-container\" }\nconst _hoisted_2 = { class: \"toolbar top-toolbar\" }\nconst _hoisted_3 = { class: \"toolbar-left\" }\nconst _hoisted_4 = { class: \"content-area\" }\nconst _hoisted_5 = { class: \"card-container\" }\nconst _hoisted_6 = {\n  key: 0,\n  class: \"import-note\"\n}\nconst _hoisted_7 = {\n  key: 1,\n  class: \"node-selection-required\"\n}\nconst _hoisted_8 = { class: \"card-container\" }\nconst _hoisted_9 = { class: \"nm-wakeup-row\" }\nconst _hoisted_10 = { class: \"card-container\" }\nconst _hoisted_11 = { class: \"uds-row\" }\nconst _hoisted_12 = { class: \"uds-row\" }\nconst _hoisted_13 = { class: \"uds-row\" }\nconst _hoisted_14 = { class: \"card-container\" }\nconst _hoisted_15 = { class: \"security-input-group\" }\nconst _hoisted_16 = { class: \"security-buttons\" }\nconst _hoisted_17 = {\n  key: 0,\n  class: \"selected-dll-info\"\n}\nconst _hoisted_18 = {\n  key: 1,\n  class: \"selected-dll-info\"\n}\nconst _hoisted_19 = { class: \"toolbar bottom-toolbar\" }\n\nimport { ref, onMounted, computed } from 'vue';\r\nimport { ElMessage } from 'element-plus';\r\nimport { Delete, Plus, ArrowDown, ArrowUp } from '@element-plus/icons-vue';\r\nimport { caseApi, CaseConfigDto, WhiteListFrame } from '@/api/caseApi';\r\n\r\n\nexport default /*@__PURE__*/_defineComponent({\n  __name: 'CaseSetting',\n  setup(__props) {\n\r\nconst loading = ref(false);\r\nconst config = ref<CaseConfigDto>({\r\n  whiteListFrames: [],\r\n  enableNmWakeup: true,\r\n  nmWakeupId: 0x53F,\r\n  nmWakeupIsExt: false,\r\n  nmWakeupDlc: 8,\r\n  nmWakeupData: [0x3F, 0x50, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF],\r\n  nmWakeupCommunicationType: 'Can',\r\n  nmWakeupCycleMs: 100,\r\n  nmWakeupDelayMs: 2000,\r\n  diagReqId: 0x731,\r\n  diagReqIsExt: false,\r\n  diagResId: 0x631,\r\n  diagTimeoutMs: 500,\r\n  isDutMtuLessThan4096: false,\r\n  enableDiagFallbackRequest: false,\r\n  diagFallbackRequestPayload: [0x10, 0x01],\r\n  securityInfo: {\r\n    hasDll: false,\r\n    dllFileName: undefined,\r\n    dllSize: 0\r\n  }\r\n});\r\n\r\n// 新增变量\r\nconst nodeNames = ref<string[]>([]);\r\nconst selectedNode = ref<string>('');\r\nconst allFrames = ref<WhiteListFrame[]>([]);\r\n\r\n// 根据选定节点过滤帧\r\nconst filteredFrames = computed(() => {\r\n  if (!selectedNode.value) return [];\r\n\r\n  return allFrames.value.filter(frame =>\r\n    // 选中的节点是发送者或接收者之一\r\n    frame.transmitter === selectedNode.value ||\r\n    frame.receivers.includes(selectedNode.value)\r\n  );\r\n});\r\n\r\nconst activeNames = ref(['case', 'nmWakeup', 'uds', 'security']); // 默认展开所有分组\r\n\r\nconst nmWakeupIdInput = ref('');\r\nconst nmWakeupDlcInput = ref('');\r\nconst nmWakeupDataInput = ref('');\r\nconst diagReqIdInput = ref('');\r\nconst diagResIdInput = ref('');\r\nconst diagFallbackRequestPayloadInput = ref('');\r\n\r\n// 用于显示和选择 Frame Type\r\nconst nmWakeupFrameType = computed({\r\n  get: () => {\r\n    return config.value.nmWakeupCommunicationType === 'Can' ? 'CAN Frame' : 'CANFD Frame';\r\n  },\r\n  set: (value: string) => {\r\n    config.value.nmWakeupCommunicationType = value === 'CAN Frame' ? 'Can' : 'Canfd';\r\n  }\r\n});\r\n\r\nconst selectedDllPath = ref<string | null>(null);\r\nconst selectedDllName = ref<string | null>(null);\r\nconst shouldRemoveDll = ref(false);\r\n\r\nconst defaultConfig: CaseConfigDto = {\r\n  whiteListFrames: [], // 修改为空数组\r\n  enableNmWakeup: true,\r\n  nmWakeupId: 0x53F,\r\n  nmWakeupIsExt: false,\r\n  nmWakeupDlc: 8,\r\n  nmWakeupData: [0x3F, 0x50, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF],\r\n  nmWakeupCommunicationType: 'Can',\r\n  nmWakeupCycleMs: 100,\r\n  nmWakeupDelayMs: 2000,\r\n  diagReqId: 0x731,\r\n  diagReqIsExt: false,\r\n  diagResId: 0x631,\r\n  diagTimeoutMs: 500,\r\n  isDutMtuLessThan4096: false,\r\n  enableDiagFallbackRequest: false,\r\n  diagFallbackRequestPayload: [0x10, 0x01],\r\n  securityInfo: {\r\n    hasDll: false,\r\n    dllFileName: undefined,\r\n    dllSize: 0\r\n  }\r\n};\r\n\r\nconst arrayToHexString = (arr: number[]) => {\r\n  return arr.map(n => '0x' + n.toString(16).toUpperCase()).join(',');\r\n};\r\n\r\nconst parseHexString = (str: string): number[] => {\r\n  try {\r\n    return str.split(',')\r\n      .map(s => s.trim())\r\n      .filter(s => s)\r\n      .map(s => parseInt(s, 16));\r\n  } catch {\r\n    return [];\r\n  }\r\n};\r\n\r\nconst handleNmWakeupIdChange = (value: string) => {\r\n  try {\r\n    // 移除可能的 0x 前缀\r\n    const cleanValue = value.replace(/^0x/i, '');\r\n    const id = parseInt(cleanValue, 16);\r\n    if (isNaN(id)) {\r\n      ElMessage.error('Invalid ID format');\r\n      return;\r\n    }\r\n    config.value.nmWakeupId = id;\r\n    // 确保显示带有 0x 前缀\r\n    nmWakeupIdInput.value = '0x' + id.toString(16).toUpperCase();\r\n  } catch (error) {\r\n    ElMessage.error('Invalid input value');\r\n  }\r\n};\r\n\r\nconst handleNmWakeupDlcChange = (value: string) => {\r\n  try {\r\n    // 移除可能的 0x 前缀\r\n    const cleanValue = value.replace(/^0x/i, '');\r\n    const dlc = parseInt(cleanValue, 16);\r\n    if (isNaN(dlc)) {\r\n      ElMessage.error('Invalid DLC format');\r\n      return;\r\n    }\r\n    if (dlc < 0 || dlc > 64) {\r\n      ElMessage.warning('DLC should be between 0 and 64');\r\n      return;\r\n    }\r\n    config.value.nmWakeupDlc = dlc;\r\n    // 确保显示带有 0x 前缀\r\n    nmWakeupDlcInput.value = '0x' + dlc.toString(16).toUpperCase();\r\n  } catch (error) {\r\n    ElMessage.error('Invalid input value');\r\n  }\r\n};\r\n\r\nconst handleNmWakeupDataChange = (value: string) => {\r\n  try {\r\n    const bytes = value.split(',')\r\n      .map(s => s.trim())\r\n      .filter(s => s)\r\n      .map(s => parseInt(s, 16));\r\n\r\n    if (bytes.length > 8) {\r\n      ElMessage.warning('Maximum 8 bytes allowed');\r\n      return;\r\n    }\r\n    if (bytes.some(b => isNaN(b) || b < 0 || b > 0xFF)) {\r\n      ElMessage.warning('Each byte must be between 0x00 and 0xFF');\r\n      return;\r\n    }\r\n    config.value.nmWakeupData = bytes;\r\n  } catch (error) {\r\n    ElMessage.error('Invalid input format');\r\n  }\r\n};\r\n\r\nconst handleDiagReqIdChange = (value: string) => {\r\n  try {\r\n    // 移除可能的 0x 前缀\r\n    const cleanValue = value.replace(/^0x/i, '');\r\n    const id = parseInt(cleanValue, 16);\r\n    if (isNaN(id)) {\r\n      ElMessage.error('Invalid ID format');\r\n      return;\r\n    }\r\n    config.value.diagReqId = id;\r\n    // 确保显示带有 0x 前缀\r\n    diagReqIdInput.value = '0x' + id.toString(16).toUpperCase();\r\n  } catch (error) {\r\n    ElMessage.error('Invalid input value');\r\n  }\r\n};\r\n\r\nconst handleDiagResIdChange = (value: string) => {\r\n  try {\r\n    // 移除可能的 0x 前缀\r\n    const cleanValue = value.replace(/^0x/i, '');\r\n    const id = parseInt(cleanValue, 16);\r\n    if (isNaN(id)) {\r\n      ElMessage.error('Invalid ID format');\r\n      return;\r\n    }\r\n    config.value.diagResId = id;\r\n    // 确保显示带有 0x 前缀\r\n    diagResIdInput.value = '0x' + id.toString(16).toUpperCase();\r\n  } catch (error) {\r\n    ElMessage.error('Invalid input value');\r\n  }\r\n};\r\n\r\nconst handleDiagFallbackRequestPayloadChange = (value: string) => {\r\n  try {\r\n    const bytes = value.split(',')\r\n      .map(s => s.trim())\r\n      .filter(s => s)\r\n      .map(s => parseInt(s, 16));\r\n\r\n    if (bytes.some(b => isNaN(b) || b < 0 || b > 0xFF)) {\r\n      ElMessage.warning('Each byte must be between 0x00 and 0xFF');\r\n      return;\r\n    }\r\n    config.value.diagFallbackRequestPayload = bytes;\r\n  } catch (error) {\r\n    ElMessage.error('Invalid input format');\r\n  }\r\n};\r\n\r\nconst resetToDefault = () => {\r\n  config.value = JSON.parse(JSON.stringify(defaultConfig));\r\n  nmWakeupIdInput.value = '0x' + defaultConfig.nmWakeupId.toString(16).toUpperCase();\r\n  nmWakeupDlcInput.value = '0x' + defaultConfig.nmWakeupDlc.toString(16).toUpperCase();\r\n  nmWakeupDataInput.value = arrayToHexString(defaultConfig.nmWakeupData);\r\n  diagReqIdInput.value = '0x' + defaultConfig.diagReqId.toString(16).toUpperCase();\r\n  diagResIdInput.value = '0x' + defaultConfig.diagResId.toString(16).toUpperCase();\r\n  diagFallbackRequestPayloadInput.value = arrayToHexString(defaultConfig.diagFallbackRequestPayload);\r\n  selectedDllPath.value = null;\r\n  selectedDllName.value = null;\r\n  shouldRemoveDll.value = false;\r\n\r\n  // 重置节点和帧相关状态\r\n  nodeNames.value = [];\r\n  selectedNode.value = '';\r\n  allFrames.value = [];\r\n};\r\n\r\nconst loadConfig = async () => {\r\n  loading.value = true;\r\n  try {\r\n    const response = await caseApi.getCaseConfig();\r\n    config.value = response.data;\r\n    nmWakeupIdInput.value = '0x' + response.data.nmWakeupId.toString(16).toUpperCase();\r\n    nmWakeupDataInput.value = arrayToHexString(response.data.nmWakeupData);\r\n    diagReqIdInput.value = '0x' + response.data.diagReqId.toString(16).toUpperCase();\r\n    diagResIdInput.value = '0x' + response.data.diagResId.toString(16).toUpperCase();\r\n\r\n    // 设置备用诊断请求数据\r\n    if (response.data.diagFallbackRequestPayload) {\r\n      diagFallbackRequestPayloadInput.value = arrayToHexString(response.data.diagFallbackRequestPayload);\r\n    } else {\r\n      diagFallbackRequestPayloadInput.value = '0x10,0x01';\r\n    }\r\n\r\n    // 确保新增字段有默认值\r\n    if (response.data.nmWakeupIsExt === undefined) {\r\n      config.value.nmWakeupIsExt = false;\r\n    }\r\n    if (response.data.nmWakeupDlc === undefined) {\r\n      config.value.nmWakeupDlc = 8;\r\n    }\r\n    if (response.data.diagReqIsExt === undefined) {\r\n      config.value.diagReqIsExt = false;\r\n    }\r\n    if (response.data.enableDiagFallbackRequest === undefined) {\r\n      config.value.enableDiagFallbackRequest = false;\r\n    }\r\n    if (response.data.diagFallbackRequestPayload === undefined) {\r\n      config.value.diagFallbackRequestPayload = [0x10, 0x01];\r\n    }\r\n\r\n    // 设置 DLC 输入框的值\r\n    nmWakeupDlcInput.value = '0x' + config.value.nmWakeupDlc.toString(16).toUpperCase();\r\n\r\n    // 如果已存在帧，尝试提取节点列表\r\n    if (response.data.whiteListFrames.length > 0) {\r\n      const frames = response.data.whiteListFrames;\r\n      allFrames.value = frames;\r\n\r\n      // 提取所有唯一的节点名\r\n      const nodes = new Set<string>();\r\n      frames.forEach(frame => {\r\n        if (frame.transmitter) nodes.add(frame.transmitter);\r\n        frame.receivers?.forEach(r => nodes.add(r));\r\n      });\r\n\r\n      nodeNames.value = Array.from(nodes);\r\n\r\n      // 如果保存了选中的节点，恢复选中状态\r\n      if (response.data.selectedNodeName && nodeNames.value.includes(response.data.selectedNodeName)) {\r\n        selectedNode.value = response.data.selectedNodeName;\r\n      }\r\n      // 否则，如果只有一个节点，自动选择\r\n      else if (nodeNames.value.length === 1) {\r\n        selectedNode.value = nodeNames.value[0];\r\n      }\r\n    }\r\n  } catch (error) {\r\n    ElMessage.error('Failed to load configuration');\r\n  } finally {\r\n    loading.value = false;\r\n  }\r\n};\r\n\r\n// 处理节点改变\r\nconst handleNodeChange = (value: string) => {\r\n  // 更新过滤后的帧列表 (通过 computed 自动实现)\r\n  if (!value) {\r\n    ElMessage.warning('Please select a target ECU');\r\n  }\r\n};\r\n\r\nconst handleSave = async () => {\r\n  loading.value = true;\r\n  try {\r\n    // 准备提交数据\r\n    const submitData = { ...config.value };\r\n\r\n    // 修改: 保存所有帧数据，而不只是筛选后的数据\r\n    submitData.whiteListFrames = allFrames.value;\r\n\r\n    // 保存选中的节点名称\r\n    submitData.selectedNodeName = selectedNode.value;\r\n\r\n    if (selectedDllPath.value) {\r\n      submitData.securityDllPath = selectedDllPath.value;\r\n    }\r\n\r\n    if (shouldRemoveDll.value) {\r\n      submitData.removeSecurityDll = true;\r\n    }\r\n\r\n    const result = await caseApi.updateCaseConfig(submitData);\r\n    config.value = result.data;\r\n\r\n    selectedDllPath.value = null;\r\n    selectedDllName.value = null;\r\n    shouldRemoveDll.value = false;\r\n\r\n    ElMessage.success('Save successful');\r\n  } catch (error) {\r\n    ElMessage.error('Save failed');\r\n  } finally {\r\n    loading.value = false;\r\n  }\r\n};\r\n\r\nconst importLoading = ref(false);\r\n\r\nconst handleImportDbc = async () => {\r\n  importLoading.value = true;\r\n  try {\r\n    const response = await caseApi.importDbc();\r\n\r\n    // 保存所有帧和节点信息\r\n    allFrames.value = response.data.whiteListFrames;\r\n    nodeNames.value = response.data.nodeNames;\r\n\r\n    // 重置选择的节点\r\n    selectedNode.value = '';\r\n\r\n    // 清空当前已保存的帧列表，等待用户选择节点\r\n    config.value.whiteListFrames = [];\r\n\r\n    ElMessage.success('DBC file imported successfully. Please select a target ECU.');\r\n  } catch (error: any) {\r\n    if (error.response?.data === 'UserCanceled') {\r\n      return;\r\n    }\r\n    ElMessage.error(error.response?.data === 'InvalidFileFormat'\r\n      ? 'Invalid DBC file format'\r\n      : 'Failed to import DBC file');\r\n  } finally {\r\n    importLoading.value = false;\r\n  }\r\n};\r\n\r\nconst formatFileSize = (bytes?: number): string => {\r\n  if (!bytes) return '0 B';\r\n\r\n  const units = ['B', 'KB', 'MB', 'GB'];\r\n  let size = bytes;\r\n  let unitIndex = 0;\r\n\r\n  while (size >= 1024 && unitIndex < units.length - 1) {\r\n    size /= 1024;\r\n    unitIndex++;\r\n  }\r\n\r\n  return `${size.toFixed(2)} ${units[unitIndex]}`;\r\n};\r\n\r\nconst selectDll = async () => {\r\n  try {\r\n    const response = await caseApi.selectSecurityDll();\r\n    const dllPath = response.data.path;\r\n    selectedDllPath.value = dllPath;\r\n    selectedDllName.value = dllPath.split('\\\\').pop() || dllPath;\r\n  } catch (error: any) {\r\n    if (error.response?.data === 'UserCanceled') {\r\n      return;\r\n    }\r\n    ElMessage.error(error.response?.data === 'InvalidFileFormat'\r\n      ? 'Invalid DLL file format'\r\n      : 'Failed to select DLL file');\r\n  }\r\n};\r\n\r\nconst removeDll = () => {\r\n  shouldRemoveDll.value = true;\r\n};\r\n\r\nconst securityDllDisplay = computed(() => {\r\n  if (selectedDllName.value) {\r\n    return selectedDllName.value;\r\n  }\r\n\r\n  if (shouldRemoveDll.value) {\r\n    return 'DLL will be removed after save';\r\n  }\r\n\r\n  return config.value.securityInfo?.hasDll\r\n    ? `${config.value.securityInfo.dllFileName} (${formatFileSize(config.value.securityInfo.dllSize)})`\r\n    : '';\r\n});\r\n\r\n\r\n\r\n// 展开全部面板\r\nconst expandAll = () => {\r\n  activeNames.value = ['case', 'nmWakeup', 'uds', 'security'];\r\n};\r\n\r\n// 收起全部面板\r\nconst collapseAll = () => {\r\n  activeNames.value = [];\r\n};\r\n\r\nonMounted(() => {\r\n  loadConfig();\r\n});\r\n\nreturn (_ctx: any,_cache: any) => {\n  const _component_font_awesome_icon = _resolveComponent(\"font-awesome-icon\")!\n  const _component_el_button = _resolveComponent(\"el-button\")!\n  const _component_el_form_item = _resolveComponent(\"el-form-item\")!\n  const _component_el_option = _resolveComponent(\"el-option\")!\n  const _component_el_select = _resolveComponent(\"el-select\")!\n  const _component_el_table_column = _resolveComponent(\"el-table-column\")!\n  const _component_el_table = _resolveComponent(\"el-table\")!\n  const _component_el_form = _resolveComponent(\"el-form\")!\n  const _component_el_collapse_item = _resolveComponent(\"el-collapse-item\")!\n  const _component_el_switch = _resolveComponent(\"el-switch\")!\n  const _component_el_input = _resolveComponent(\"el-input\")!\n  const _component_el_input_number = _resolveComponent(\"el-input-number\")!\n  const _component_el_collapse = _resolveComponent(\"el-collapse\")!\n  const _directive_loading = _resolveDirective(\"loading\")!\n\n  return _withDirectives((_openBlock(), _createElementBlock(\"div\", _hoisted_1, [\n    _createElementVNode(\"div\", _hoisted_2, [\n      _createElementVNode(\"div\", _hoisted_3, [\n        _createVNode(_component_el_button, {\n          onClick: expandAll,\n          type: \"primary\",\n          size: \"small\",\n          class: \"expand-button\"\n        }, {\n          default: _withCtx(() => [\n            _createVNode(_component_font_awesome_icon, { icon: \"up-right-and-down-left-from-center\" }),\n            _cache[18] || (_cache[18] = _createElementVNode(\"span\", { class: \"button-text\" }, \"Expand All\", -1))\n          ]),\n          _: 1\n        }),\n        _createVNode(_component_el_button, {\n          onClick: collapseAll,\n          type: \"primary\",\n          size: \"small\",\n          class: \"collapse-button\"\n        }, {\n          default: _withCtx(() => [\n            _createVNode(_component_font_awesome_icon, { icon: \"down-left-and-up-right-to-center\" }),\n            _cache[19] || (_cache[19] = _createElementVNode(\"span\", { class: \"button-text\" }, \"Collapse All\", -1))\n          ]),\n          _: 1\n        })\n      ])\n    ]),\n    _createElementVNode(\"div\", _hoisted_4, [\n      _createVNode(_component_el_collapse, {\n        modelValue: activeNames.value,\n        \"onUpdate:modelValue\": _cache[17] || (_cache[17] = ($event: any) => ((activeNames).value = $event))\n      }, {\n        default: _withCtx(() => [\n          _createElementVNode(\"div\", _hoisted_5, [\n            _createVNode(_component_el_collapse_item, {\n              title: \"Case Setting\",\n              name: \"case\",\n              class: \"custom-card\"\n            }, {\n              default: _withCtx(() => [\n                _createVNode(_component_el_form, {\n                  model: config.value,\n                  \"label-width\": \"160px\",\n                  \"label-position\": \"top\"\n                }, {\n                  default: _withCtx(() => [\n                    _createVNode(_component_el_form_item, null, {\n                      default: _withCtx(() => [\n                        _createVNode(_component_el_button, {\n                          onClick: handleImportDbc,\n                          loading: importLoading.value,\n                          type: \"primary\",\n                          size: \"small\",\n                          style: {\"margin-bottom\":\"15px\"}\n                        }, {\n                          default: _withCtx(() => _cache[20] || (_cache[20] = [\n                            _createTextVNode(\" Import DBC \")\n                          ])),\n                          _: 1\n                        }, 8, [\"loading\"])\n                      ]),\n                      _: 1\n                    }),\n                    (nodeNames.value.length > 0)\n                      ? (_openBlock(), _createBlock(_component_el_form_item, {\n                          key: 0,\n                          label: \"Target ECU (DUT)\"\n                        }, {\n                          default: _withCtx(() => [\n                            _createVNode(_component_el_select, {\n                              modelValue: selectedNode.value,\n                              \"onUpdate:modelValue\": _cache[0] || (_cache[0] = ($event: any) => ((selectedNode).value = $event)),\n                              placeholder: \"Select target ECU\",\n                              style: {\"width\":\"100%\"},\n                              onChange: handleNodeChange\n                            }, {\n                              default: _withCtx(() => [\n                                (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(nodeNames.value, (node) => {\n                                  return (_openBlock(), _createBlock(_component_el_option, {\n                                    key: node,\n                                    label: node,\n                                    value: node\n                                  }, null, 8, [\"label\", \"value\"]))\n                                }), 128))\n                              ]),\n                              _: 1\n                            }, 8, [\"modelValue\"])\n                          ]),\n                          _: 1\n                        }))\n                      : _createCommentVNode(\"\", true),\n                    _createVNode(_component_el_form_item, {\n                      label: `White List Frames (${filteredFrames.value.length})`\n                    }, {\n                      default: _withCtx(() => [\n                        (allFrames.value.length === 0)\n                          ? (_openBlock(), _createElementBlock(\"div\", _hoisted_6, \" No frames imported. Please use \\\"Import DBC\\\" button above to add frames. \"))\n                          : (!selectedNode.value)\n                            ? (_openBlock(), _createElementBlock(\"div\", _hoisted_7, \" Please select a target ECU to view related frames \"))\n                            : (_openBlock(), _createBlock(_component_el_table, {\n                                key: 2,\n                                data: filteredFrames.value,\n                                style: {\"width\":\"100%\"},\n                                \"max-height\": 400,\n                                border: \"\"\n                              }, {\n                                default: _withCtx(() => [\n                                  _createVNode(_component_el_table_column, {\n                                    label: \"ID\",\n                                    \"min-width\": \"120\"\n                                  }, {\n                                    default: _withCtx(({row}) => [\n                                      _createTextVNode(\" 0x\" + _toDisplayString(row.id.toString(16).toUpperCase().padStart(3, '0')), 1)\n                                    ]),\n                                    _: 1\n                                  }),\n                                  _createVNode(_component_el_table_column, {\n                                    prop: \"name\",\n                                    label: \"Name\",\n                                    \"min-width\": \"180\"\n                                  }),\n                                  _createVNode(_component_el_table_column, {\n                                    label: \"DLC\",\n                                    \"min-width\": \"80\"\n                                  }, {\n                                    default: _withCtx(({row}) => [\n                                      _createTextVNode(\" 0x\" + _toDisplayString(row.dlc.toString(16).toUpperCase()), 1)\n                                    ]),\n                                    _: 1\n                                  }),\n                                  _createVNode(_component_el_table_column, {\n                                    label: \"Ext Flag\",\n                                    \"min-width\": \"120\"\n                                  }, {\n                                    default: _withCtx(({row}) => [\n                                      _createTextVNode(_toDisplayString(row.isExt ? 'CAN Extended' : 'CAN Standard'), 1)\n                                    ]),\n                                    _: 1\n                                  }),\n                                  _createVNode(_component_el_table_column, {\n                                    prop: \"transmitter\",\n                                    label: \"Transmitter\",\n                                    \"min-width\": \"120\"\n                                  }),\n                                  _createVNode(_component_el_table_column, {\n                                    label: \"Receivers\",\n                                    \"min-width\": \"180\"\n                                  }, {\n                                    default: _withCtx(({row}) => [\n                                      _createTextVNode(_toDisplayString(row.receivers.join(', ')), 1)\n                                    ]),\n                                    _: 1\n                                  })\n                                ]),\n                                _: 1\n                              }, 8, [\"data\"]))\n                      ]),\n                      _: 1\n                    }, 8, [\"label\"])\n                  ]),\n                  _: 1\n                }, 8, [\"model\"])\n              ]),\n              _: 1\n            })\n          ]),\n          _createElementVNode(\"div\", _hoisted_8, [\n            _createVNode(_component_el_collapse_item, {\n              title: \"NM Wake Up Setting\",\n              name: \"nmWakeup\",\n              class: \"custom-card\"\n            }, {\n              default: _withCtx(() => [\n                _createVNode(_component_el_form, {\n                  model: config.value,\n                  \"label-width\": \"160px\",\n                  \"label-position\": \"top\"\n                }, {\n                  default: _withCtx(() => [\n                    _createVNode(_component_el_form_item, { label: \"Enable NM Wake Up\" }, {\n                      default: _withCtx(() => [\n                        _createVNode(_component_el_switch, {\n                          modelValue: config.value.enableNmWakeup,\n                          \"onUpdate:modelValue\": _cache[1] || (_cache[1] = ($event: any) => ((config.value.enableNmWakeup) = $event))\n                        }, null, 8, [\"modelValue\"])\n                      ]),\n                      _: 1\n                    }),\n                    _createElementVNode(\"div\", {\n                      class: _normalizeClass({ 'disabled-form-content': !config.value.enableNmWakeup })\n                    }, [\n                      _createVNode(_component_el_table, {\n                        data: [{}],\n                        border: \"\",\n                        style: {\"width\":\"100%\",\"margin-bottom\":\"15px\"},\n                        \"show-header\": true,\n                        class: \"nm-table\"\n                      }, {\n                        default: _withCtx(() => [\n                          _createVNode(_component_el_table_column, {\n                            label: \"ID\",\n                            width: \"180\"\n                          }, {\n                            default: _withCtx(() => [\n                              _createVNode(_component_el_input, {\n                                modelValue: nmWakeupIdInput.value,\n                                \"onUpdate:modelValue\": _cache[2] || (_cache[2] = ($event: any) => ((nmWakeupIdInput).value = $event)),\n                                placeholder: \"Enter ID in hex (e.g.: 53F)\",\n                                onChange: handleNmWakeupIdChange,\n                                disabled: !config.value.enableNmWakeup\n                              }, null, 8, [\"modelValue\", \"disabled\"])\n                            ]),\n                            _: 1\n                          }),\n                          _createVNode(_component_el_table_column, {\n                            label: \"Ext Flag\",\n                            width: \"100\"\n                          }, {\n                            default: _withCtx(() => [\n                              _createVNode(_component_el_switch, {\n                                modelValue: config.value.nmWakeupIsExt,\n                                \"onUpdate:modelValue\": _cache[3] || (_cache[3] = ($event: any) => ((config.value.nmWakeupIsExt) = $event)),\n                                disabled: !config.value.enableNmWakeup\n                              }, null, 8, [\"modelValue\", \"disabled\"])\n                            ]),\n                            _: 1\n                          }),\n                          _createVNode(_component_el_table_column, {\n                            label: \"Frame Type\",\n                            width: \"200\"\n                          }, {\n                            default: _withCtx(() => [\n                              _createVNode(_component_el_select, {\n                                modelValue: nmWakeupFrameType.value,\n                                \"onUpdate:modelValue\": _cache[4] || (_cache[4] = ($event: any) => ((nmWakeupFrameType).value = $event)),\n                                disabled: !config.value.enableNmWakeup\n                              }, {\n                                default: _withCtx(() => [\n                                  _createVNode(_component_el_option, {\n                                    value: \"CAN Frame\",\n                                    label: \"CAN Frame\"\n                                  }),\n                                  _createVNode(_component_el_option, {\n                                    value: \"CANFD Frame\",\n                                    label: \"CANFD Frame\"\n                                  })\n                                ]),\n                                _: 1\n                              }, 8, [\"modelValue\", \"disabled\"])\n                            ]),\n                            _: 1\n                          }),\n                          _createVNode(_component_el_table_column, {\n                            label: \"DLC\",\n                            width: \"150\"\n                          }, {\n                            default: _withCtx(() => [\n                              _createVNode(_component_el_input, {\n                                modelValue: nmWakeupDlcInput.value,\n                                \"onUpdate:modelValue\": _cache[5] || (_cache[5] = ($event: any) => ((nmWakeupDlcInput).value = $event)),\n                                placeholder: \"Enter DLC in hex (e.g.: 0x8)\",\n                                onChange: handleNmWakeupDlcChange,\n                                disabled: !config.value.enableNmWakeup\n                              }, null, 8, [\"modelValue\", \"disabled\"])\n                            ]),\n                            _: 1\n                          }),\n                          _createVNode(_component_el_table_column, { label: \"Data\" }, {\n                            default: _withCtx(() => [\n                              _createVNode(_component_el_input, {\n                                modelValue: nmWakeupDataInput.value,\n                                \"onUpdate:modelValue\": _cache[6] || (_cache[6] = ($event: any) => ((nmWakeupDataInput).value = $event)),\n                                placeholder: \"Enter data bytes (e.g.: 0x3F,0x50,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF)\",\n                                onChange: handleNmWakeupDataChange,\n                                disabled: !config.value.enableNmWakeup\n                              }, null, 8, [\"modelValue\", \"disabled\"])\n                            ]),\n                            _: 1\n                          })\n                        ]),\n                        _: 1\n                      }),\n                      _createElementVNode(\"div\", _hoisted_9, [\n                        _createVNode(_component_el_form_item, {\n                          label: \"Cycle (ms)\",\n                          class: \"nm-wakeup-item\"\n                        }, {\n                          default: _withCtx(() => [\n                            _createVNode(_component_el_input_number, {\n                              modelValue: config.value.nmWakeupCycleMs,\n                              \"onUpdate:modelValue\": _cache[7] || (_cache[7] = ($event: any) => ((config.value.nmWakeupCycleMs) = $event)),\n                              min: 1,\n                              max: 60000,\n                              style: {\"width\":\"200px\"},\n                              disabled: !config.value.enableNmWakeup\n                            }, null, 8, [\"modelValue\", \"disabled\"])\n                          ]),\n                          _: 1\n                        }),\n                        _createVNode(_component_el_form_item, {\n                          label: \"Wake Up Delay (ms)\",\n                          class: \"nm-wakeup-item\"\n                        }, {\n                          default: _withCtx(() => [\n                            _createVNode(_component_el_input_number, {\n                              modelValue: config.value.nmWakeupDelayMs,\n                              \"onUpdate:modelValue\": _cache[8] || (_cache[8] = ($event: any) => ((config.value.nmWakeupDelayMs) = $event)),\n                              min: 0,\n                              max: 60000,\n                              style: {\"width\":\"200px\"},\n                              disabled: !config.value.enableNmWakeup\n                            }, null, 8, [\"modelValue\", \"disabled\"])\n                          ]),\n                          _: 1\n                        })\n                      ])\n                    ], 2)\n                  ]),\n                  _: 1\n                }, 8, [\"model\"])\n              ]),\n              _: 1\n            })\n          ]),\n          _createElementVNode(\"div\", _hoisted_10, [\n            _createVNode(_component_el_collapse_item, {\n              title: \"UDS Setting\",\n              name: \"uds\",\n              class: \"custom-card\"\n            }, {\n              default: _withCtx(() => [\n                _createVNode(_component_el_form, {\n                  model: config.value,\n                  \"label-width\": \"160px\",\n                  \"label-position\": \"top\"\n                }, {\n                  default: _withCtx(() => [\n                    _createElementVNode(\"div\", _hoisted_11, [\n                      _createVNode(_component_el_form_item, {\n                        label: \"Diagnostic Request ID\",\n                        class: \"uds-item\"\n                      }, {\n                        default: _withCtx(() => [\n                          _createVNode(_component_el_input, {\n                            modelValue: diagReqIdInput.value,\n                            \"onUpdate:modelValue\": _cache[9] || (_cache[9] = ($event: any) => ((diagReqIdInput).value = $event)),\n                            placeholder: \"Enter ID in hex (e.g.: 731)\",\n                            onChange: handleDiagReqIdChange\n                          }, null, 8, [\"modelValue\"])\n                        ]),\n                        _: 1\n                      }),\n                      _createVNode(_component_el_form_item, {\n                        label: \"Ext Flag\",\n                        class: \"uds-item\"\n                      }, {\n                        default: _withCtx(() => [\n                          _createVNode(_component_el_switch, {\n                            modelValue: config.value.diagReqIsExt,\n                            \"onUpdate:modelValue\": _cache[10] || (_cache[10] = ($event: any) => ((config.value.diagReqIsExt) = $event))\n                          }, null, 8, [\"modelValue\"])\n                        ]),\n                        _: 1\n                      }),\n                      _createVNode(_component_el_form_item, {\n                        label: \"Diagnostic Response ID\",\n                        class: \"uds-item\"\n                      }, {\n                        default: _withCtx(() => [\n                          _createVNode(_component_el_input, {\n                            modelValue: diagResIdInput.value,\n                            \"onUpdate:modelValue\": _cache[11] || (_cache[11] = ($event: any) => ((diagResIdInput).value = $event)),\n                            placeholder: \"Enter ID in hex (e.g.: 631)\",\n                            onChange: handleDiagResIdChange\n                          }, null, 8, [\"modelValue\"])\n                        ]),\n                        _: 1\n                      })\n                    ]),\n                    _createElementVNode(\"div\", _hoisted_12, [\n                      _createVNode(_component_el_form_item, {\n                        label: \"Diagnostic Timeout (ms)\",\n                        class: \"uds-item\"\n                      }, {\n                        default: _withCtx(() => [\n                          _createVNode(_component_el_input_number, {\n                            modelValue: config.value.diagTimeoutMs,\n                            \"onUpdate:modelValue\": _cache[12] || (_cache[12] = ($event: any) => ((config.value.diagTimeoutMs) = $event)),\n                            min: 1,\n                            max: 60000,\n                            style: {\"width\":\"200px\"}\n                          }, null, 8, [\"modelValue\"])\n                        ]),\n                        _: 1\n                      }),\n                      _createVNode(_component_el_form_item, {\n                        label: \"DUT MTU < 4096 bytes\",\n                        class: \"uds-item\"\n                      }, {\n                        default: _withCtx(() => [\n                          _createVNode(_component_el_switch, {\n                            modelValue: config.value.isDutMtuLessThan4096,\n                            \"onUpdate:modelValue\": _cache[13] || (_cache[13] = ($event: any) => ((config.value.isDutMtuLessThan4096) = $event))\n                          }, null, 8, [\"modelValue\"])\n                        ]),\n                        _: 1\n                      })\n                    ]),\n                    _createElementVNode(\"div\", _hoisted_13, [\n                      _createVNode(_component_el_form_item, {\n                        label: \"Enable Fallback Request\",\n                        class: \"uds-item\"\n                      }, {\n                        default: _withCtx(() => [\n                          _createVNode(_component_el_switch, {\n                            modelValue: config.value.enableDiagFallbackRequest,\n                            \"onUpdate:modelValue\": _cache[14] || (_cache[14] = ($event: any) => ((config.value.enableDiagFallbackRequest) = $event))\n                          }, null, 8, [\"modelValue\"])\n                        ]),\n                        _: 1\n                      }),\n                      _createVNode(_component_el_form_item, {\n                        label: \"Fallback Request Payload\",\n                        class: \"uds-item\"\n                      }, {\n                        default: _withCtx(() => [\n                          _createVNode(_component_el_input, {\n                            modelValue: diagFallbackRequestPayloadInput.value,\n                            \"onUpdate:modelValue\": _cache[15] || (_cache[15] = ($event: any) => ((diagFallbackRequestPayloadInput).value = $event)),\n                            placeholder: \"Enter data bytes (e.g.: 0x10,0x01)\",\n                            onChange: handleDiagFallbackRequestPayloadChange,\n                            disabled: !config.value.enableDiagFallbackRequest\n                          }, null, 8, [\"modelValue\", \"disabled\"])\n                        ]),\n                        _: 1\n                      })\n                    ])\n                  ]),\n                  _: 1\n                }, 8, [\"model\"])\n              ]),\n              _: 1\n            })\n          ]),\n          _createElementVNode(\"div\", _hoisted_14, [\n            _createVNode(_component_el_collapse_item, {\n              title: \"Security Access Setting\",\n              name: \"security\",\n              class: \"custom-card\"\n            }, {\n              default: _withCtx(() => [\n                _createVNode(_component_el_form, {\n                  model: config.value,\n                  \"label-width\": \"160px\",\n                  \"label-position\": \"top\"\n                }, {\n                  default: _withCtx(() => [\n                    _createVNode(_component_el_form_item, { label: \"Security Access Dll\" }, {\n                      default: _withCtx(() => [\n                        _createElementVNode(\"div\", _hoisted_15, [\n                          _createVNode(_component_el_input, {\n                            modelValue: securityDllDisplay.value,\n                            \"onUpdate:modelValue\": _cache[16] || (_cache[16] = ($event: any) => ((securityDllDisplay).value = $event)),\n                            placeholder: \"No Dll selected\",\n                            readonly: \"\",\n                            class: \"security-dll-input\"\n                          }, null, 8, [\"modelValue\"]),\n                          _createElementVNode(\"div\", _hoisted_16, [\n                            (!config.value.securityInfo?.hasDll)\n                              ? (_openBlock(), _createBlock(_component_el_button, {\n                                  key: 0,\n                                  onClick: selectDll,\n                                  type: \"primary\",\n                                  icon: _unref(Plus),\n                                  title: \"Select DLL\"\n                                }, null, 8, [\"icon\"]))\n                              : _createCommentVNode(\"\", true),\n                            (config.value.securityInfo?.hasDll)\n                              ? (_openBlock(), _createBlock(_component_el_button, {\n                                  key: 1,\n                                  onClick: removeDll,\n                                  type: \"danger\",\n                                  icon: _unref(Delete),\n                                  title: \"Remove DLL\"\n                                }, null, 8, [\"icon\"]))\n                              : _createCommentVNode(\"\", true)\n                          ])\n                        ]),\n                        (selectedDllName.value)\n                          ? (_openBlock(), _createElementBlock(\"div\", _hoisted_17, \" DLL selected. Click Save to apply. \"))\n                          : _createCommentVNode(\"\", true),\n                        (shouldRemoveDll.value)\n                          ? (_openBlock(), _createElementBlock(\"div\", _hoisted_18, \" DLL marked for removal. Click Save to apply. \"))\n                          : _createCommentVNode(\"\", true)\n                      ]),\n                      _: 1\n                    })\n                  ]),\n                  _: 1\n                }, 8, [\"model\"])\n              ]),\n              _: 1\n            })\n          ])\n        ]),\n        _: 1\n      }, 8, [\"modelValue\"])\n    ]),\n    _createElementVNode(\"div\", _hoisted_19, [\n      _createVNode(_component_el_button, {\n        type: \"primary\",\n        onClick: handleSave\n      }, {\n        default: _withCtx(() => _cache[21] || (_cache[21] = [\n          _createTextVNode(\"Save\")\n        ])),\n        _: 1\n      }),\n      _createVNode(_component_el_button, {\n        onClick: resetToDefault,\n        style: {\"margin-left\":\"10px\"}\n      }, {\n        default: _withCtx(() => _cache[22] || (_cache[22] = [\n          _createTextVNode(\"Reset\")\n        ])),\n        _: 1\n      })\n    ])\n  ])), [\n    [_directive_loading, loading.value]\n  ])\n}\n}\n\n})", "<template>\r\n  <div class=\"case-setting-container\" v-loading=\"loading\">\r\n    <!-- Top Toolbar -->\r\n    <div class=\"toolbar top-toolbar\">\r\n      <div class=\"toolbar-left\">\r\n        <!-- 展开/收起全部按钮 -->\r\n        <el-button @click=\"expandAll\" type=\"primary\" size=\"small\" class=\"expand-button\">\r\n          <font-awesome-icon icon=\"up-right-and-down-left-from-center\" /><span class=\"button-text\">Expand All</span>\r\n        </el-button>\r\n        <el-button @click=\"collapseAll\" type=\"primary\" size=\"small\" class=\"collapse-button\">\r\n          <font-awesome-icon icon=\"down-left-and-up-right-to-center\" /><span class=\"button-text\">Collapse All</span>\r\n        </el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- Content Area -->\r\n    <div class=\"content-area\">\r\n      <el-collapse v-model=\"activeNames\">\r\n        <div class=\"card-container\">\r\n          <el-collapse-item title=\"Case Setting\" name=\"case\" class=\"custom-card\">\r\n            <el-form :model=\"config\" label-width=\"160px\" label-position=\"top\">\r\n              <!-- Import DBC 按钮 -->\r\n              <el-form-item>\r\n                <el-button @click=\"handleImportDbc\" :loading=\"importLoading\" type=\"primary\" size=\"small\" style=\"margin-bottom: 15px;\">\r\n                  Import DBC\r\n                </el-button>\r\n              </el-form-item>\r\n\r\n              <!-- 节点选择 -->\r\n              <el-form-item label=\"Target ECU (DUT)\" v-if=\"nodeNames.length > 0\">\r\n                <el-select\r\n                  v-model=\"selectedNode\"\r\n                  placeholder=\"Select target ECU\"\r\n                  style=\"width: 100%;\"\r\n                  @change=\"handleNodeChange\"\r\n                >\r\n                  <el-option\r\n                    v-for=\"node in nodeNames\"\r\n                    :key=\"node\"\r\n                    :label=\"node\"\r\n                    :value=\"node\"\r\n                  />\r\n                </el-select>\r\n              </el-form-item>\r\n\r\n              <el-form-item :label=\"`White List Frames (${filteredFrames.length})`\">\r\n                <div class=\"import-note\" v-if=\"allFrames.length === 0\">\r\n                  No frames imported. Please use \"Import DBC\" button above to add frames.\r\n                </div>\r\n                <div v-else-if=\"!selectedNode\" class=\"node-selection-required\">\r\n                  Please select a target ECU to view related frames\r\n                </div>\r\n                <el-table\r\n                  :data=\"filteredFrames\"\r\n                  style=\"width: 100%\"\r\n                  v-else\r\n                  :max-height=\"400\"\r\n                  border\r\n                >\r\n                  <el-table-column label=\"ID\" min-width=\"120\">\r\n                    <template #default=\"{row}\">\r\n                      0x{{ row.id.toString(16).toUpperCase().padStart(3, '0') }}\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column prop=\"name\" label=\"Name\" min-width=\"180\" />\r\n                  <el-table-column label=\"DLC\" min-width=\"80\">\r\n                    <template #default=\"{row}\">\r\n                      0x{{ row.dlc.toString(16).toUpperCase() }}\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column label=\"Ext Flag\" min-width=\"120\">\r\n                    <template #default=\"{row}\">\r\n                      {{ row.isExt ? 'CAN Extended' : 'CAN Standard' }}\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column prop=\"transmitter\" label=\"Transmitter\" min-width=\"120\" />\r\n                  <el-table-column label=\"Receivers\" min-width=\"180\">\r\n                    <template #default=\"{row}\">\r\n                      {{ row.receivers.join(', ') }}\r\n                    </template>\r\n                  </el-table-column>\r\n                </el-table>\r\n              </el-form-item>\r\n            </el-form>\r\n          </el-collapse-item>\r\n        </div>\r\n\r\n        <div class=\"card-container\">\r\n          <el-collapse-item title=\"NM Wake Up Setting\" name=\"nmWakeup\" class=\"custom-card\">\r\n            <el-form :model=\"config\" label-width=\"160px\" label-position=\"top\">\r\n              <!-- 第一行：Enable NM Wake Up -->\r\n              <el-form-item label=\"Enable NM Wake Up\">\r\n                <el-switch v-model=\"config.enableNmWakeup\" />\r\n              </el-form-item>\r\n\r\n              <div :class=\"{ 'disabled-form-content': !config.enableNmWakeup }\">\r\n                <!-- 表格布局：ID、IsExtFlag、Frame Type、DLC、Data -->\r\n                <el-table :data=\"[{}]\" border style=\"width: 100%; margin-bottom: 15px;\" :show-header=\"true\" class=\"nm-table\">\r\n                  <el-table-column label=\"ID\" width=\"180\">\r\n                    <template #default>\r\n                      <el-input v-model=\"nmWakeupIdInput\" placeholder=\"Enter ID in hex (e.g.: 53F)\"\r\n                        @change=\"handleNmWakeupIdChange\" :disabled=\"!config.enableNmWakeup\" />\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column label=\"Ext Flag\" width=\"100\">\r\n                    <template #default>\r\n                      <el-switch v-model=\"config.nmWakeupIsExt\" :disabled=\"!config.enableNmWakeup\" />\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column label=\"Frame Type\" width=\"200\">\r\n                    <template #default>\r\n                      <el-select\r\n                        v-model=\"nmWakeupFrameType\"\r\n                        :disabled=\"!config.enableNmWakeup\">\r\n                        <el-option value=\"CAN Frame\" label=\"CAN Frame\" />\r\n                        <el-option value=\"CANFD Frame\" label=\"CANFD Frame\" />\r\n                      </el-select>\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column label=\"DLC\" width=\"150\">\r\n                    <template #default>\r\n                      <el-input v-model=\"nmWakeupDlcInput\" placeholder=\"Enter DLC in hex (e.g.: 0x8)\"\r\n                        @change=\"handleNmWakeupDlcChange\" :disabled=\"!config.enableNmWakeup\" />\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column label=\"Data\">\r\n                    <template #default>\r\n                      <el-input v-model=\"nmWakeupDataInput\"\r\n                        placeholder=\"Enter data bytes (e.g.: 0x3F,0x50,0xFF,0xFF,0xFF,0xFF,0xFF,0xFF)\"\r\n                        @change=\"handleNmWakeupDataChange\" :disabled=\"!config.enableNmWakeup\" />\r\n                    </template>\r\n                  </el-table-column>\r\n                </el-table>\r\n\r\n                <!-- 第三行：Cycle、Wake Up Delay -->\r\n                <div class=\"nm-wakeup-row\">\r\n                  <!-- Cycle -->\r\n                  <el-form-item label=\"Cycle (ms)\" class=\"nm-wakeup-item\">\r\n                    <el-input-number v-model=\"config.nmWakeupCycleMs\" :min=\"1\" :max=\"60000\" style=\"width: 200px\"\r\n                      :disabled=\"!config.enableNmWakeup\" />\r\n                  </el-form-item>\r\n\r\n                  <!-- Wake Up Delay -->\r\n                  <el-form-item label=\"Wake Up Delay (ms)\" class=\"nm-wakeup-item\">\r\n                    <el-input-number v-model=\"config.nmWakeupDelayMs\" :min=\"0\" :max=\"60000\" style=\"width: 200px\"\r\n                      :disabled=\"!config.enableNmWakeup\" />\r\n                  </el-form-item>\r\n                </div>\r\n              </div>\r\n            </el-form>\r\n          </el-collapse-item>\r\n        </div>\r\n\r\n        <div class=\"card-container\">\r\n          <el-collapse-item title=\"UDS Setting\" name=\"uds\" class=\"custom-card\">\r\n            <el-form :model=\"config\" label-width=\"160px\" label-position=\"top\">\r\n              <!-- 第一行：请求ID、Is Ext Flag、响应ID -->\r\n              <div class=\"uds-row\">\r\n                <el-form-item label=\"Diagnostic Request ID\" class=\"uds-item\">\r\n                  <el-input v-model=\"diagReqIdInput\" placeholder=\"Enter ID in hex (e.g.: 731)\"\r\n                    @change=\"handleDiagReqIdChange\" />\r\n                </el-form-item>\r\n\r\n                <el-form-item label=\"Ext Flag\" class=\"uds-item\">\r\n                  <el-switch v-model=\"config.diagReqIsExt\" />\r\n                </el-form-item>\r\n\r\n                <el-form-item label=\"Diagnostic Response ID\" class=\"uds-item\">\r\n                  <el-input v-model=\"diagResIdInput\" placeholder=\"Enter ID in hex (e.g.: 631)\"\r\n                    @change=\"handleDiagResIdChange\" />\r\n                </el-form-item>\r\n              </div>\r\n\r\n              <!-- 第二行：超时和 MTU 设置 -->\r\n              <div class=\"uds-row\">\r\n                <el-form-item label=\"Diagnostic Timeout (ms)\" class=\"uds-item\">\r\n                  <el-input-number v-model=\"config.diagTimeoutMs\" :min=\"1\" :max=\"60000\" style=\"width: 200px\" />\r\n                </el-form-item>\r\n\r\n                <el-form-item label=\"DUT MTU < 4096 bytes\" class=\"uds-item\">\r\n                  <el-switch v-model=\"config.isDutMtuLessThan4096\" />\r\n                </el-form-item>\r\n              </div>\r\n\r\n              <!-- 第三行：备用诊断请求设置 -->\r\n              <div class=\"uds-row\">\r\n                <el-form-item label=\"Enable Fallback Request\" class=\"uds-item\">\r\n                  <el-switch v-model=\"config.enableDiagFallbackRequest\" />\r\n                </el-form-item>\r\n\r\n                <el-form-item label=\"Fallback Request Payload\" class=\"uds-item\">\r\n                  <el-input v-model=\"diagFallbackRequestPayloadInput\"\r\n                    placeholder=\"Enter data bytes (e.g.: 0x10,0x01)\"\r\n                    @change=\"handleDiagFallbackRequestPayloadChange\"\r\n                    :disabled=\"!config.enableDiagFallbackRequest\" />\r\n                </el-form-item>\r\n              </div>\r\n            </el-form>\r\n          </el-collapse-item>\r\n        </div>\r\n\r\n        <div class=\"card-container\">\r\n          <el-collapse-item title=\"Security Access Setting\" name=\"security\" class=\"custom-card\">\r\n            <el-form :model=\"config\" label-width=\"160px\" label-position=\"top\">\r\n              <el-form-item label=\"Security Access Dll\">\r\n                <div class=\"security-input-group\">\r\n                  <el-input v-model=\"securityDllDisplay\" placeholder=\"No Dll selected\" readonly\r\n                    class=\"security-dll-input\"></el-input>\r\n                  <div class=\"security-buttons\">\r\n                    <el-button v-if=\"!config.securityInfo?.hasDll\" @click=\"selectDll\" type=\"primary\" :icon=\"Plus\"\r\n                      title=\"Select DLL\" />\r\n                    <el-button v-if=\"config.securityInfo?.hasDll\" @click=\"removeDll\" type=\"danger\" :icon=\"Delete\"\r\n                      title=\"Remove DLL\" />\r\n                  </div>\r\n                </div>\r\n                <div v-if=\"selectedDllName\" class=\"selected-dll-info\">\r\n                  DLL selected. Click Save to apply.\r\n                </div>\r\n                <div v-if=\"shouldRemoveDll\" class=\"selected-dll-info\">\r\n                  DLL marked for removal. Click Save to apply.\r\n                </div>\r\n              </el-form-item>\r\n            </el-form>\r\n          </el-collapse-item>\r\n        </div>\r\n      </el-collapse>\r\n    </div>\r\n\r\n    <!-- Bottom Toolbar -->\r\n    <div class=\"toolbar bottom-toolbar\">\r\n      <el-button type=\"primary\" @click=\"handleSave\">Save</el-button>\r\n      <el-button @click=\"resetToDefault\" style=\"margin-left: 10px;\">Reset</el-button>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { ref, onMounted, computed } from 'vue';\r\nimport { ElMessage } from 'element-plus';\r\nimport { Delete, Plus, ArrowDown, ArrowUp } from '@element-plus/icons-vue';\r\nimport { caseApi, CaseConfigDto, WhiteListFrame } from '@/api/caseApi';\r\n\r\nconst loading = ref(false);\r\nconst config = ref<CaseConfigDto>({\r\n  whiteListFrames: [],\r\n  enableNmWakeup: true,\r\n  nmWakeupId: 0x53F,\r\n  nmWakeupIsExt: false,\r\n  nmWakeupDlc: 8,\r\n  nmWakeupData: [0x3F, 0x50, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF],\r\n  nmWakeupCommunicationType: 'Can',\r\n  nmWakeupCycleMs: 100,\r\n  nmWakeupDelayMs: 2000,\r\n  diagReqId: 0x731,\r\n  diagReqIsExt: false,\r\n  diagResId: 0x631,\r\n  diagTimeoutMs: 500,\r\n  isDutMtuLessThan4096: false,\r\n  enableDiagFallbackRequest: false,\r\n  diagFallbackRequestPayload: [0x10, 0x01],\r\n  securityInfo: {\r\n    hasDll: false,\r\n    dllFileName: undefined,\r\n    dllSize: 0\r\n  }\r\n});\r\n\r\n// 新增变量\r\nconst nodeNames = ref<string[]>([]);\r\nconst selectedNode = ref<string>('');\r\nconst allFrames = ref<WhiteListFrame[]>([]);\r\n\r\n// 根据选定节点过滤帧\r\nconst filteredFrames = computed(() => {\r\n  if (!selectedNode.value) return [];\r\n\r\n  return allFrames.value.filter(frame =>\r\n    // 选中的节点是发送者或接收者之一\r\n    frame.transmitter === selectedNode.value ||\r\n    frame.receivers.includes(selectedNode.value)\r\n  );\r\n});\r\n\r\nconst activeNames = ref(['case', 'nmWakeup', 'uds', 'security']); // 默认展开所有分组\r\n\r\nconst nmWakeupIdInput = ref('');\r\nconst nmWakeupDlcInput = ref('');\r\nconst nmWakeupDataInput = ref('');\r\nconst diagReqIdInput = ref('');\r\nconst diagResIdInput = ref('');\r\nconst diagFallbackRequestPayloadInput = ref('');\r\n\r\n// 用于显示和选择 Frame Type\r\nconst nmWakeupFrameType = computed({\r\n  get: () => {\r\n    return config.value.nmWakeupCommunicationType === 'Can' ? 'CAN Frame' : 'CANFD Frame';\r\n  },\r\n  set: (value: string) => {\r\n    config.value.nmWakeupCommunicationType = value === 'CAN Frame' ? 'Can' : 'Canfd';\r\n  }\r\n});\r\n\r\nconst selectedDllPath = ref<string | null>(null);\r\nconst selectedDllName = ref<string | null>(null);\r\nconst shouldRemoveDll = ref(false);\r\n\r\nconst defaultConfig: CaseConfigDto = {\r\n  whiteListFrames: [], // 修改为空数组\r\n  enableNmWakeup: true,\r\n  nmWakeupId: 0x53F,\r\n  nmWakeupIsExt: false,\r\n  nmWakeupDlc: 8,\r\n  nmWakeupData: [0x3F, 0x50, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF, 0xFF],\r\n  nmWakeupCommunicationType: 'Can',\r\n  nmWakeupCycleMs: 100,\r\n  nmWakeupDelayMs: 2000,\r\n  diagReqId: 0x731,\r\n  diagReqIsExt: false,\r\n  diagResId: 0x631,\r\n  diagTimeoutMs: 500,\r\n  isDutMtuLessThan4096: false,\r\n  enableDiagFallbackRequest: false,\r\n  diagFallbackRequestPayload: [0x10, 0x01],\r\n  securityInfo: {\r\n    hasDll: false,\r\n    dllFileName: undefined,\r\n    dllSize: 0\r\n  }\r\n};\r\n\r\nconst arrayToHexString = (arr: number[]) => {\r\n  return arr.map(n => '0x' + n.toString(16).toUpperCase()).join(',');\r\n};\r\n\r\nconst parseHexString = (str: string): number[] => {\r\n  try {\r\n    return str.split(',')\r\n      .map(s => s.trim())\r\n      .filter(s => s)\r\n      .map(s => parseInt(s, 16));\r\n  } catch {\r\n    return [];\r\n  }\r\n};\r\n\r\nconst handleNmWakeupIdChange = (value: string) => {\r\n  try {\r\n    // 移除可能的 0x 前缀\r\n    const cleanValue = value.replace(/^0x/i, '');\r\n    const id = parseInt(cleanValue, 16);\r\n    if (isNaN(id)) {\r\n      ElMessage.error('Invalid ID format');\r\n      return;\r\n    }\r\n    config.value.nmWakeupId = id;\r\n    // 确保显示带有 0x 前缀\r\n    nmWakeupIdInput.value = '0x' + id.toString(16).toUpperCase();\r\n  } catch (error) {\r\n    ElMessage.error('Invalid input value');\r\n  }\r\n};\r\n\r\nconst handleNmWakeupDlcChange = (value: string) => {\r\n  try {\r\n    // 移除可能的 0x 前缀\r\n    const cleanValue = value.replace(/^0x/i, '');\r\n    const dlc = parseInt(cleanValue, 16);\r\n    if (isNaN(dlc)) {\r\n      ElMessage.error('Invalid DLC format');\r\n      return;\r\n    }\r\n    if (dlc < 0 || dlc > 64) {\r\n      ElMessage.warning('DLC should be between 0 and 64');\r\n      return;\r\n    }\r\n    config.value.nmWakeupDlc = dlc;\r\n    // 确保显示带有 0x 前缀\r\n    nmWakeupDlcInput.value = '0x' + dlc.toString(16).toUpperCase();\r\n  } catch (error) {\r\n    ElMessage.error('Invalid input value');\r\n  }\r\n};\r\n\r\nconst handleNmWakeupDataChange = (value: string) => {\r\n  try {\r\n    const bytes = value.split(',')\r\n      .map(s => s.trim())\r\n      .filter(s => s)\r\n      .map(s => parseInt(s, 16));\r\n\r\n    if (bytes.length > 8) {\r\n      ElMessage.warning('Maximum 8 bytes allowed');\r\n      return;\r\n    }\r\n    if (bytes.some(b => isNaN(b) || b < 0 || b > 0xFF)) {\r\n      ElMessage.warning('Each byte must be between 0x00 and 0xFF');\r\n      return;\r\n    }\r\n    config.value.nmWakeupData = bytes;\r\n  } catch (error) {\r\n    ElMessage.error('Invalid input format');\r\n  }\r\n};\r\n\r\nconst handleDiagReqIdChange = (value: string) => {\r\n  try {\r\n    // 移除可能的 0x 前缀\r\n    const cleanValue = value.replace(/^0x/i, '');\r\n    const id = parseInt(cleanValue, 16);\r\n    if (isNaN(id)) {\r\n      ElMessage.error('Invalid ID format');\r\n      return;\r\n    }\r\n    config.value.diagReqId = id;\r\n    // 确保显示带有 0x 前缀\r\n    diagReqIdInput.value = '0x' + id.toString(16).toUpperCase();\r\n  } catch (error) {\r\n    ElMessage.error('Invalid input value');\r\n  }\r\n};\r\n\r\nconst handleDiagResIdChange = (value: string) => {\r\n  try {\r\n    // 移除可能的 0x 前缀\r\n    const cleanValue = value.replace(/^0x/i, '');\r\n    const id = parseInt(cleanValue, 16);\r\n    if (isNaN(id)) {\r\n      ElMessage.error('Invalid ID format');\r\n      return;\r\n    }\r\n    config.value.diagResId = id;\r\n    // 确保显示带有 0x 前缀\r\n    diagResIdInput.value = '0x' + id.toString(16).toUpperCase();\r\n  } catch (error) {\r\n    ElMessage.error('Invalid input value');\r\n  }\r\n};\r\n\r\nconst handleDiagFallbackRequestPayloadChange = (value: string) => {\r\n  try {\r\n    const bytes = value.split(',')\r\n      .map(s => s.trim())\r\n      .filter(s => s)\r\n      .map(s => parseInt(s, 16));\r\n\r\n    if (bytes.some(b => isNaN(b) || b < 0 || b > 0xFF)) {\r\n      ElMessage.warning('Each byte must be between 0x00 and 0xFF');\r\n      return;\r\n    }\r\n    config.value.diagFallbackRequestPayload = bytes;\r\n  } catch (error) {\r\n    ElMessage.error('Invalid input format');\r\n  }\r\n};\r\n\r\nconst resetToDefault = () => {\r\n  config.value = JSON.parse(JSON.stringify(defaultConfig));\r\n  nmWakeupIdInput.value = '0x' + defaultConfig.nmWakeupId.toString(16).toUpperCase();\r\n  nmWakeupDlcInput.value = '0x' + defaultConfig.nmWakeupDlc.toString(16).toUpperCase();\r\n  nmWakeupDataInput.value = arrayToHexString(defaultConfig.nmWakeupData);\r\n  diagReqIdInput.value = '0x' + defaultConfig.diagReqId.toString(16).toUpperCase();\r\n  diagResIdInput.value = '0x' + defaultConfig.diagResId.toString(16).toUpperCase();\r\n  diagFallbackRequestPayloadInput.value = arrayToHexString(defaultConfig.diagFallbackRequestPayload);\r\n  selectedDllPath.value = null;\r\n  selectedDllName.value = null;\r\n  shouldRemoveDll.value = false;\r\n\r\n  // 重置节点和帧相关状态\r\n  nodeNames.value = [];\r\n  selectedNode.value = '';\r\n  allFrames.value = [];\r\n};\r\n\r\nconst loadConfig = async () => {\r\n  loading.value = true;\r\n  try {\r\n    const response = await caseApi.getCaseConfig();\r\n    config.value = response.data;\r\n    nmWakeupIdInput.value = '0x' + response.data.nmWakeupId.toString(16).toUpperCase();\r\n    nmWakeupDataInput.value = arrayToHexString(response.data.nmWakeupData);\r\n    diagReqIdInput.value = '0x' + response.data.diagReqId.toString(16).toUpperCase();\r\n    diagResIdInput.value = '0x' + response.data.diagResId.toString(16).toUpperCase();\r\n\r\n    // 设置备用诊断请求数据\r\n    if (response.data.diagFallbackRequestPayload) {\r\n      diagFallbackRequestPayloadInput.value = arrayToHexString(response.data.diagFallbackRequestPayload);\r\n    } else {\r\n      diagFallbackRequestPayloadInput.value = '0x10,0x01';\r\n    }\r\n\r\n    // 确保新增字段有默认值\r\n    if (response.data.nmWakeupIsExt === undefined) {\r\n      config.value.nmWakeupIsExt = false;\r\n    }\r\n    if (response.data.nmWakeupDlc === undefined) {\r\n      config.value.nmWakeupDlc = 8;\r\n    }\r\n    if (response.data.diagReqIsExt === undefined) {\r\n      config.value.diagReqIsExt = false;\r\n    }\r\n    if (response.data.enableDiagFallbackRequest === undefined) {\r\n      config.value.enableDiagFallbackRequest = false;\r\n    }\r\n    if (response.data.diagFallbackRequestPayload === undefined) {\r\n      config.value.diagFallbackRequestPayload = [0x10, 0x01];\r\n    }\r\n\r\n    // 设置 DLC 输入框的值\r\n    nmWakeupDlcInput.value = '0x' + config.value.nmWakeupDlc.toString(16).toUpperCase();\r\n\r\n    // 如果已存在帧，尝试提取节点列表\r\n    if (response.data.whiteListFrames.length > 0) {\r\n      const frames = response.data.whiteListFrames;\r\n      allFrames.value = frames;\r\n\r\n      // 提取所有唯一的节点名\r\n      const nodes = new Set<string>();\r\n      frames.forEach(frame => {\r\n        if (frame.transmitter) nodes.add(frame.transmitter);\r\n        frame.receivers?.forEach(r => nodes.add(r));\r\n      });\r\n\r\n      nodeNames.value = Array.from(nodes);\r\n\r\n      // 如果保存了选中的节点，恢复选中状态\r\n      if (response.data.selectedNodeName && nodeNames.value.includes(response.data.selectedNodeName)) {\r\n        selectedNode.value = response.data.selectedNodeName;\r\n      }\r\n      // 否则，如果只有一个节点，自动选择\r\n      else if (nodeNames.value.length === 1) {\r\n        selectedNode.value = nodeNames.value[0];\r\n      }\r\n    }\r\n  } catch (error) {\r\n    ElMessage.error('Failed to load configuration');\r\n  } finally {\r\n    loading.value = false;\r\n  }\r\n};\r\n\r\n// 处理节点改变\r\nconst handleNodeChange = (value: string) => {\r\n  // 更新过滤后的帧列表 (通过 computed 自动实现)\r\n  if (!value) {\r\n    ElMessage.warning('Please select a target ECU');\r\n  }\r\n};\r\n\r\nconst handleSave = async () => {\r\n  loading.value = true;\r\n  try {\r\n    // 准备提交数据\r\n    const submitData = { ...config.value };\r\n\r\n    // 修改: 保存所有帧数据，而不只是筛选后的数据\r\n    submitData.whiteListFrames = allFrames.value;\r\n\r\n    // 保存选中的节点名称\r\n    submitData.selectedNodeName = selectedNode.value;\r\n\r\n    if (selectedDllPath.value) {\r\n      submitData.securityDllPath = selectedDllPath.value;\r\n    }\r\n\r\n    if (shouldRemoveDll.value) {\r\n      submitData.removeSecurityDll = true;\r\n    }\r\n\r\n    const result = await caseApi.updateCaseConfig(submitData);\r\n    config.value = result.data;\r\n\r\n    selectedDllPath.value = null;\r\n    selectedDllName.value = null;\r\n    shouldRemoveDll.value = false;\r\n\r\n    ElMessage.success('Save successful');\r\n  } catch (error) {\r\n    ElMessage.error('Save failed');\r\n  } finally {\r\n    loading.value = false;\r\n  }\r\n};\r\n\r\nconst importLoading = ref(false);\r\n\r\nconst handleImportDbc = async () => {\r\n  importLoading.value = true;\r\n  try {\r\n    const response = await caseApi.importDbc();\r\n\r\n    // 保存所有帧和节点信息\r\n    allFrames.value = response.data.whiteListFrames;\r\n    nodeNames.value = response.data.nodeNames;\r\n\r\n    // 重置选择的节点\r\n    selectedNode.value = '';\r\n\r\n    // 清空当前已保存的帧列表，等待用户选择节点\r\n    config.value.whiteListFrames = [];\r\n\r\n    ElMessage.success('DBC file imported successfully. Please select a target ECU.');\r\n  } catch (error: any) {\r\n    if (error.response?.data === 'UserCanceled') {\r\n      return;\r\n    }\r\n    ElMessage.error(error.response?.data === 'InvalidFileFormat'\r\n      ? 'Invalid DBC file format'\r\n      : 'Failed to import DBC file');\r\n  } finally {\r\n    importLoading.value = false;\r\n  }\r\n};\r\n\r\nconst formatFileSize = (bytes?: number): string => {\r\n  if (!bytes) return '0 B';\r\n\r\n  const units = ['B', 'KB', 'MB', 'GB'];\r\n  let size = bytes;\r\n  let unitIndex = 0;\r\n\r\n  while (size >= 1024 && unitIndex < units.length - 1) {\r\n    size /= 1024;\r\n    unitIndex++;\r\n  }\r\n\r\n  return `${size.toFixed(2)} ${units[unitIndex]}`;\r\n};\r\n\r\nconst selectDll = async () => {\r\n  try {\r\n    const response = await caseApi.selectSecurityDll();\r\n    const dllPath = response.data.path;\r\n    selectedDllPath.value = dllPath;\r\n    selectedDllName.value = dllPath.split('\\\\').pop() || dllPath;\r\n  } catch (error: any) {\r\n    if (error.response?.data === 'UserCanceled') {\r\n      return;\r\n    }\r\n    ElMessage.error(error.response?.data === 'InvalidFileFormat'\r\n      ? 'Invalid DLL file format'\r\n      : 'Failed to select DLL file');\r\n  }\r\n};\r\n\r\nconst removeDll = () => {\r\n  shouldRemoveDll.value = true;\r\n};\r\n\r\nconst securityDllDisplay = computed(() => {\r\n  if (selectedDllName.value) {\r\n    return selectedDllName.value;\r\n  }\r\n\r\n  if (shouldRemoveDll.value) {\r\n    return 'DLL will be removed after save';\r\n  }\r\n\r\n  return config.value.securityInfo?.hasDll\r\n    ? `${config.value.securityInfo.dllFileName} (${formatFileSize(config.value.securityInfo.dllSize)})`\r\n    : '';\r\n});\r\n\r\n\r\n\r\n// 展开全部面板\r\nconst expandAll = () => {\r\n  activeNames.value = ['case', 'nmWakeup', 'uds', 'security'];\r\n};\r\n\r\n// 收起全部面板\r\nconst collapseAll = () => {\r\n  activeNames.value = [];\r\n};\r\n\r\nonMounted(() => {\r\n  loadConfig();\r\n});\r\n</script>\r\n\r\n<style scoped>\r\n.case-setting-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  flex: 1;\r\n  background-color: #ffffff;\r\n  border-radius: 8px;\r\n  padding: 0 10px;\r\n}\r\n\r\n.toolbar {\r\n  padding: 15px 0;\r\n  display: flex;\r\n  background-color: #ffffff;\r\n  z-index: 10;\r\n}\r\n\r\n.top-toolbar {\r\n  border-bottom: 1px solid #e4e7ed;\r\n  position: sticky;\r\n  top: 0;\r\n}\r\n\r\n.bottom-toolbar {\r\n  border-top: 1px solid #e4e7ed;\r\n  position: sticky;\r\n  bottom: 0;\r\n}\r\n\r\n.content-area {\r\n  flex: 1;\r\n  overflow-y: auto;\r\n  padding: 10px 0;\r\n}\r\n\r\n/* 卡片容器样式 */\r\n.card-container {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n/* 自定义卡片样式 */\r\n.custom-card {\r\n  border: 1px solid #e4e7ed;\r\n  border-radius: 4px;\r\n  overflow: hidden;\r\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);\r\n  background-color: #fff;\r\n}\r\n\r\n:deep(.el-collapse-item__header) {\r\n  font-size: 16px;\r\n  font-weight: bold;\r\n  background-color: #f5f7fa;\r\n  padding: 12px 15px;\r\n  border-bottom: 1px solid #e4e7ed;\r\n  transition: background-color 0.3s;\r\n}\r\n\r\n:deep(.el-collapse) {\r\n  border: none;\r\n}\r\n\r\n:deep(.el-collapse-item__wrap) {\r\n  padding: 15px;\r\n  border: none;\r\n  background-color: #ffffff;\r\n}\r\n\r\n.disabled-form-content {\r\n  opacity: 0.6;\r\n  pointer-events: none;\r\n}\r\n\r\n.disabled-form-content :deep(.el-input__wrapper),\r\n.disabled-form-content :deep(.el-input-number__wrapper),\r\n.disabled-form-content :deep(.el-select) {\r\n  cursor: not-allowed;\r\n}\r\n\r\n.security-config {\r\n  padding: 10px;\r\n}\r\n\r\n.dll-info {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.security-input-group {\r\n  display: flex;\r\n  align-items: center;\r\n}\r\n\r\n.security-dll-input {\r\n  width: 400px;\r\n}\r\n\r\n.security-buttons {\r\n  margin-left: 10px;\r\n  display: flex;\r\n  gap: 5px;\r\n}\r\n\r\n.selected-dll-info {\r\n  margin-left: 10px;\r\n  margin-top: 5px;\r\n  font-size: 12px;\r\n  color: var(--el-color-warning);\r\n}\r\n\r\n.warning-text {\r\n  color: #E6A23C;\r\n}\r\n\r\n.node-selection-required {\r\n  color: #E6A23C;\r\n  text-align: center;\r\n  padding: 20px;\r\n  border: 1px dashed #dcdfe6;\r\n  border-radius: 4px;\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.import-note {\r\n  color: #909399;\r\n  font-style: italic;\r\n  text-align: center;\r\n}\r\n\r\n.white-list-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  width: 100%;\r\n}\r\n\r\n.toolbar-left {\r\n  display: flex;\r\n  gap: 15px;\r\n}\r\n\r\n.expand-button,\r\n.collapse-button {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 5px;\r\n}\r\n\r\n.button-text {\r\n  margin-left: 8px; /* 增加图标和文本之间的水平距离 */\r\n}\r\n\r\n/* NM 唤醒表格样式 */\r\n.nm-table {\r\n  margin-bottom: 15px;\r\n}\r\n\r\n:deep(.nm-table .el-table__header) {\r\n  font-weight: bold;\r\n}\r\n\r\n:deep(.nm-table .el-table__cell) {\r\n  padding: 8px;\r\n}\r\n\r\n:deep(.nm-table .el-input__wrapper),\r\n:deep(.nm-table .el-select) {\r\n  width: 100%;\r\n}\r\n\r\n/* NM Wake Up 设置的行布局 */\r\n.nm-wakeup-row {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 15px;\r\n  margin-bottom: 10px;\r\n  justify-content: flex-start; /* 左对齐 */\r\n}\r\n\r\n.nm-wakeup-item {\r\n  flex: 0 0 auto; /* 不自动拉伸，保持原始大小 */\r\n}\r\n\r\n/* ID、Is Ext Flag、Frame Type、DLC 的宽度 */\r\n.nm-wakeup-item:nth-child(1) {\r\n  width: 180px;\r\n}\r\n\r\n.nm-wakeup-item:nth-child(2) {\r\n  width: 80px;\r\n}\r\n\r\n.nm-wakeup-item:nth-child(3) {\r\n  width: 200px;\r\n}\r\n\r\n.nm-wakeup-item:nth-child(4) {\r\n  width: 150px;\r\n}\r\n\r\n/* Data 字段随页面大小变化 */\r\n.nm-wakeup-item:nth-child(5) {\r\n  flex: 1 1 auto; /* 可以拉伸和收缩 */\r\n  min-width: 300px;\r\n}\r\n\r\n/* 第三行的元素左对齐 */\r\n.nm-wakeup-row:nth-child(2) .nm-wakeup-item {\r\n  width: 180px;\r\n}\r\n\r\n/* UDS Setting 设置的行布局 */\r\n.uds-row {\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  gap: 15px;\r\n  margin-bottom: 10px;\r\n  justify-content: flex-start; /* 左对齐 */\r\n}\r\n\r\n.uds-item {\r\n  flex: 0 0 auto; /* 不自动拉伸，保持原始大小 */\r\n  min-width: 150px;\r\n}\r\n\r\n/* 请求ID、Is Ext Flag、响应ID 的宽度 */\r\n.uds-row:first-child .uds-item:nth-child(1) {\r\n  width: 200px;\r\n}\r\n\r\n.uds-row:first-child .uds-item:nth-child(2) {\r\n  width: 100px;\r\n}\r\n\r\n.uds-row:first-child .uds-item:nth-child(3) {\r\n  width: 200px;\r\n}\r\n\r\n/* 超时和 MTU 设置的宽度 */\r\n.uds-row:nth-child(2) .uds-item {\r\n  width: 200px;\r\n}\r\n\r\n\r\n</style>\r\n", "import script from \"./CaseSetting.vue?vue&type=script&setup=true&lang=ts\"\nexport * from \"./CaseSetting.vue?vue&type=script&setup=true&lang=ts\"\n\nimport \"./CaseSetting.vue?vue&type=style&index=0&id=58369462&scoped=true&lang=css\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-58369462\"]])\n\nexport default __exports__"], "names": ["BASE_URL", "caseApi", "getCaseConfig", "USE_MOCK", "mockApi", "case", "axios", "get", "updateCaseConfig", "config", "post", "importDbc", "selectSecurityDll", "_hoisted_1", "class", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_hoisted_5", "_hoisted_6", "key", "_hoisted_7", "_hoisted_8", "_hoisted_9", "_hoisted_10", "_hoisted_11", "_hoisted_12", "_hoisted_13", "_hoisted_14", "_hoisted_15", "_hoisted_16", "_hoisted_17", "_hoisted_18", "_hoisted_19", "_defineComponent", "__name", "setup", "__props", "loading", "ref", "whiteList<PERSON>rames", "enableNmWakeup", "nmWakeupId", "nmWakeupIsExt", "nmWakeupDlc", "nmWakeupData", "nmWakeupCommunicationType", "nmWakeupCycleMs", "nmWakeupDelayMs", "diagReqId", "diagReqIsExt", "diagResId", "diagTimeoutMs", "isDutMtuLessThan4096", "enableDiagFallbackRequest", "diagFallbackRequestPayload", "securityInfo", "hasDll", "dllFileName", "undefined", "dllSize", "nodeNames", "selectedNode", "allFrames", "filteredFrames", "computed", "value", "filter", "frame", "transmitter", "receivers", "includes", "activeNames", "nmWakeupIdInput", "nmWakeupDlcInput", "nmWakeupDataInput", "diagReqIdInput", "diagResIdInput", "diagFallbackRequestPayloadInput", "nmWakeupFrameType", "set", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectedDllName", "shouldRemoveDll", "defaultConfig", "arrayToHexString", "arr", "map", "n", "toString", "toUpperCase", "join", "handleNmWakeupIdChange", "cleanValue", "replace", "id", "parseInt", "isNaN", "ElMessage", "error", "handleNmWakeupDlcChange", "dlc", "warning", "handleNmWakeupDataChange", "bytes", "split", "s", "trim", "length", "some", "b", "handleDiagReqIdChange", "handleDiagResIdChange", "handleDiagFallbackRequestPayloadChange", "resetToDefault", "JSON", "parse", "stringify", "loadConfig", "async", "response", "data", "frames", "nodes", "Set", "for<PERSON>ach", "add", "r", "Array", "from", "selectedNodeName", "handleNodeChange", "handleSave", "submitData", "securityDllPath", "removeSecurityDll", "result", "success", "importLoading", "handleImportDbc", "formatFileSize", "units", "size", "unitIndex", "toFixed", "selectDll", "dll<PERSON><PERSON>", "path", "pop", "removeDll", "securityDllDisplay", "expandAll", "collapseAll", "onMounted", "_ctx", "_cache", "_component_font_awesome_icon", "_resolveComponent", "_component_el_button", "_component_el_form_item", "_component_el_option", "_component_el_select", "_component_el_table_column", "_component_el_table", "_component_el_form", "_component_el_collapse_item", "_component_el_switch", "_component_el_input", "_component_el_input_number", "_component_el_collapse", "_directive_loading", "_resolveDirective", "_withDirectives", "_openBlock", "_createElementBlock", "_createElementVNode", "_createVNode", "onClick", "type", "default", "_withCtx", "icon", "_", "modelValue", "$event", "title", "name", "model", "style", "_createTextVNode", "_createBlock", "label", "placeholder", "onChange", "_Fragment", "_renderList", "node", "_createCommentVNode", "border", "row", "_toDisplayString", "padStart", "prop", "isExt", "_normalizeClass", "width", "disabled", "min", "max", "readonly", "_unref", "Plus", "Delete", "__exports__"], "sourceRoot": ""}