using Alsi.App.Devices.TsLibCan;
using Alsi.App.Devices.Vector;
using System.Xml.Serialization;

namespace Alsi.App.Devices.Core
{
    public class DeviceChannel
    {
        /// <summary>
        /// 设备通道的名称（唯一）
        /// </summary>
        [XmlIgnore]
        public string Name => $"{DeviceType} {DeviceSerial}";

        /// <summary>
        /// 通道
        /// </summary>
        [XmlAttribute]
        public byte Channel { get; set; } = 1;

        /// <summary>
        /// 制造商
        /// </summary>
        [XmlAttribute]
        public Manufacturer Manufacturer { get; set; }

        /// <summary>
        /// 通信类型
        /// </summary>
        [XmlAttribute]
        public CommunicationType CommunicationType { get; set; }

        /// <summary>
        /// 设备类型 VN1630 tc1001等
        /// </summary>
        [XmlAttribute]
        public string DeviceType { get; set; }

        /// <summary>
        /// 设备厂商 Vector Tosun
        /// </summary>
        [XmlAttribute]
        public string DeviceFactory { get; set; }

        /// <summary>
        /// 设备序列号
        /// </summary>
        [XmlAttribute]
        public string DeviceSerial { get; set; }

        /// <summary>
        /// 同星通道信息
        /// </summary>
        public TsLibCanChannelInfo TsLibCanChannelInfo { get; set; } = null;

        /// <summary>
        /// Vector设备信息
        /// </summary>
        public VectorInfo VectorInfo { get; set; } = null;

        public override string ToString()
        {
            return Name;
        }
    }
}
