"use strict";(self["webpackChunkfuzz_web"]=self["webpackChunkfuzz_web"]||[]).push([[644],{8644:function(e,a,t){t.r(a),t.d(a,{default:function(){return w}});var i=t(6768),l=t(4232),n=t(144),s=t(7477),o=t(2616);const c={class:"basic-setting-container"},u={class:"plan-name-section"},d={class:"plan-title"},r={class:"title-with-refresh"},v={class:"plan-meta"},p={class:"description-section"},f={class:"section-header"},k={key:0,class:"description-content"},m={key:0},g={key:1,class:"description-edit"},y={class:"edit-actions"},_={class:"additional-info-section"},b={class:"path-display"};var h=(0,i.pM)({__name:"BasicSetting",setup(e){const a=o.f.getState(),t=(0,i.EW)((()=>a.currentPlan)),h=(0,i.EW)((()=>a.isLoading)),C=(0,n.KR)(!1),L=(0,n.KR)(""),w=(0,n.KR)(!1),F=async()=>{await o.f.getCurrentPlan()},E=e=>{if(!e)return"-";const a=e instanceof Date?e:new Date(e);return a.toLocaleString()},W=()=>{L.value=t.value?.manifest.description||"",C.value=!0},z=()=>{C.value=!1},X=async()=>{if(t.value){w.value=!0;try{await o.f.updateBasicInfo(L.value),C.value=!1}finally{w.value=!1}}},R=()=>{F()};return(0,i.sV)((()=>{F()})),(e,a)=>{const o=(0,i.g2)("el-button"),F=(0,i.g2)("el-tag"),K=(0,i.g2)("el-skeleton"),S=(0,i.g2)("el-empty"),V=(0,i.g2)("el-input"),x=(0,i.g2)("el-tooltip"),D=(0,i.g2)("el-descriptions-item"),I=(0,i.g2)("el-descriptions"),P=(0,i.gN)("loading");return(0,i.bo)(((0,i.uX)(),(0,i.CE)("div",c,[(0,i.Lk)("div",u,[(0,i.Lk)("div",d,[(0,i.Lk)("div",r,[(0,i.Lk)("h2",null,(0,l.v_)(t.value?.manifest.name),1),(0,i.bF)(o,{type:"primary",onClick:R,icon:(0,n.R1)(s.Refresh),circle:"",size:"small"},null,8,["icon"])])]),(0,i.Lk)("div",v,[(0,i.bF)(F,{size:"small"},{default:(0,i.k6)((()=>[(0,i.eW)("Created: "+(0,l.v_)(E(t.value?.manifest.created)),1)])),_:1}),(0,i.bF)(F,{size:"small",type:"info"},{default:(0,i.k6)((()=>[(0,i.eW)("Modified: "+(0,l.v_)(E(t.value?.manifest.modified)),1)])),_:1})])]),(0,i.Lk)("div",p,[(0,i.Lk)("div",f,[a[2]||(a[2]=(0,i.Lk)("h4",null,"Description",-1)),C.value?(0,i.Q3)("",!0):((0,i.uX)(),(0,i.Wv)(o,{key:0,type:"primary",text:"",size:"small",onClick:W,icon:(0,n.R1)(s.Edit)},{default:(0,i.k6)((()=>a[1]||(a[1]=[(0,i.eW)(" Edit ")]))),_:1},8,["icon"]))]),h.value?((0,i.uX)(),(0,i.Wv)(K,{key:0,rows:3,animated:""})):((0,i.uX)(),(0,i.CE)(i.FK,{key:1},[C.value?((0,i.uX)(),(0,i.CE)("div",g,[(0,i.bF)(V,{modelValue:L.value,"onUpdate:modelValue":a[0]||(a[0]=e=>L.value=e),type:"textarea",rows:3,placeholder:"Enter test plan description",maxlength:"500","show-word-limit":""},null,8,["modelValue"]),(0,i.Lk)("div",y,[(0,i.bF)(o,{type:"primary",onClick:X,loading:w.value},{default:(0,i.k6)((()=>a[3]||(a[3]=[(0,i.eW)(" Save ")]))),_:1},8,["loading"]),(0,i.bF)(o,{onClick:z},{default:(0,i.k6)((()=>a[4]||(a[4]=[(0,i.eW)("Cancel")]))),_:1})])])):((0,i.uX)(),(0,i.CE)("div",k,[t.value?.manifest.description?((0,i.uX)(),(0,i.CE)("p",m,(0,l.v_)(t.value?.manifest.description),1)):((0,i.uX)(),(0,i.Wv)(S,{key:1,description:"No description","image-size":100}))]))],64))]),(0,i.Lk)("div",_,[a[5]||(a[5]=(0,i.Lk)("h4",null,"Additional Information",-1)),(0,i.bF)(I,{border:"",column:1},{default:(0,i.k6)((()=>[(0,i.bF)(D,{label:"File Path"},{default:(0,i.k6)((()=>[(0,i.bF)(x,{content:t.value?.path,placement:"top"},{default:(0,i.k6)((()=>[(0,i.Lk)("div",b,(0,l.v_)(t.value?.path),1)])),_:1},8,["content"])])),_:1})])),_:1})])])),[[P,h.value]])}}}),C=t(1241);const L=(0,C.A)(h,[["__scopeId","data-v-6ed11c04"]]);var w=L}}]);
//# sourceMappingURL=644.4ba6c9bf.js.map