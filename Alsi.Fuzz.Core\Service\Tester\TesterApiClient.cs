using Alsi.Fuzz.Core.Contracts.Tester;
using Alsi.Fuzz.Core.Models.TestSuites;
using Alsi.Fuzz.Core.Service.Results;
using Alsi.Fuzz.Core.Utils;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;

namespace Alsi.Fuzz.Core.Service.Tester
{
    /// <summary>
    /// Tester进程API客户端实现
    /// </summary>
    public class TesterApiClient : ITesterApiClient
    {
        private readonly HttpClient _httpClient;
        private readonly int _port;
        private bool _disposed = false;

        public TesterApiClient(int port = 5000, string hostname = "localhost", int timeoutMs = 10_000)
        {
            _port = port;
            _httpClient = new HttpClient
            {
                BaseAddress = new Uri($"http://{hostname}:{port}/"),
                Timeout = TimeSpan.FromMilliseconds(timeoutMs)
            };
        }

        /// <summary>
        /// 获取Tester是否正在运行
        /// </summary>
        public async Task<bool> IsRunningAsync()
        {
            try
            {
                var response = await _httpClient.GetAsync("api/tester");
                if (!response.IsSuccessStatusCode)
                {
                    return false;
                }

                var content = await response.Content.ReadAsStringAsync();
                var result = JsonConvert.DeserializeAnonymousType(content, new { IsRunning = false });
                return result?.IsRunning ?? false;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"获取Tester状态失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 请求Tester进程退出
        /// </summary>
        public async Task<ApiResponse> ExitAsync()
        {
            var response = await _httpClient.PostAsync("api/tester/exit", null);
            return await HandleResponseAsync(response);
        }

        public async Task<ApiResponse> PauseAsync()
        {
            var response = await _httpClient.PostAsync("api/tester/pause", null);
            return await HandleResponseAsync(response);
        }

        public async Task<ApiResponse> ResumeAsync()
        {
            var response = await _httpClient.PostAsync("api/tester/resume", null);
            return await HandleResponseAsync(response);
        }

        /// <summary>
        /// 检查Tester API是否可访问
        /// </summary>
        public async Task<ApiResponse> PingAsync()
        {
            var response = await _httpClient.GetAsync("api/tester");
            return await HandleResponseAsync(response);
        }

        public async Task<TesterSnapshot> GetTesterSnapshotAsync()
        {
            // 第一次查询获取全部结果
            var pagedQuery = new PagedQuery { PageNumber = 1, PageSize = int.MaxValue };
            var snapshotResponse = await GetTesterSnapshotPagedAsync(pagedQuery);
            
            if (snapshotResponse == null)
            {
                return null;
            }
            
            // 转换为原始TesterSnapshot格式
            var snapshot = new TesterSnapshot
            {
                ProcessState = snapshotResponse.ProcessState,
                CurrentOperation = snapshotResponse.CurrentOperation,
                TestResult = snapshotResponse.TestResult,
                CaseResults = snapshotResponse.PagedCaseResult?.Items ?? new CaseResult[0]
            };
            
            return snapshot;
        }

        public async Task<TesterSnapshotResponse> GetTesterSnapshotPagedAsync(PagedQuery pagedQuery)
        {
            var content = new StringContent(JsonConvert.SerializeObject(pagedQuery), System.Text.Encoding.UTF8, "application/json");
            var response = await _httpClient.PostAsync("api/tester/snapshot", content);
            
            if (!response.IsSuccessStatusCode)
            {
                return null;
            }
            
            var json = await response.Content.ReadAsStringAsync();
            var snapshotResponse = JsonConvert.DeserializeObject<TesterSnapshotResponse>(json);
            return snapshotResponse;
        }

        public async Task StartAsync(TestResult testResult)
        {
            var currentPlan = TestPlanManager.Instance.GetCurrentPlan();
            var selectedSequence = currentPlan?.Config?.SequenceConfigList.FirstOrDefault(x => x.IsSelected);
            if (selectedSequence == null)
            {
                throw new Exception("无法启动互操作测试: 当前测试计划或序列配置为空");
            }

            // 获取序列配置
            if (string.IsNullOrWhiteSpace(selectedSequence.TestSuiteName) ||
                string.IsNullOrWhiteSpace(selectedSequence.SequencePackageName))
            {
                throw new Exception("无法启动互操作测试: 测试套件或序列包名称为空");
            }


            var selectedDeviceChannelName = currentPlan.Config.HardwareConfig?.GetSelectedDeviceChannelName();
            if (string.IsNullOrWhiteSpace(selectedDeviceChannelName))
            {
                throw new Exception("无法启动互操作测试: 未选择设备通道");
            }

            var builtInTestSuiteService = new BuiltInTestSuiteService();
            var testSuite = builtInTestSuiteService.GetByName(selectedSequence.TestSuiteName);
            var package = testSuite.Packages.FirstOrDefault(x => x.Name == selectedSequence.SequencePackageName);
            if (package == null)
            {
                throw new Exception($"无法启动互操作测试: 找不到序列包 {selectedSequence.SequencePackageName}");
            }

            var securityAccessDllBytes = currentPlan.Config.SecurityConfig.DllBytes;
            SecurityAccessUtils.PrepareDll(securityAccessDllBytes);

            var sequencePackageXml = selectedSequence.SequencePackageXml;

            var request = new ExecutionRequest
            {
                TestResult = testResult,
                HardwareConfig = currentPlan.Config.HardwareConfig,
                CaseConfig = currentPlan.Config.CaseConfig,
                SequencePackageXml = sequencePackageXml
            };
            var requestBody = JsonConvert.SerializeObject(request);

            var content = new StringContent(requestBody, System.Text.Encoding.UTF8, "application/json");
            await _httpClient.PostAsync("api/tester/execute", content);
        }

        /// <summary>
        /// 启动互操作测试
        /// </summary>
        public async Task<ApiResponse> StartInteroperationTestAsync()
        {
            // 从TestPlanManager获取当前测试计划
            var currentPlan = TestPlanManager.Instance.GetCurrentPlan();
            var selectedSequence = currentPlan?.Config?.SequenceConfigList.FirstOrDefault(x => x.IsSelected);
            if (selectedSequence == null)
            {
                throw new Exception("无法启动互操作测试: 当前测试计划或序列配置为空");
            }

            // 获取序列配置
            if (string.IsNullOrWhiteSpace(selectedSequence.TestSuiteName) ||
                string.IsNullOrWhiteSpace(selectedSequence.SequencePackageName))
            {
                throw new Exception("无法启动互操作测试: 测试套件或序列包名称为空");
            }

            var selectedDeviceChannelName = currentPlan.Config.HardwareConfig?.GetSelectedDeviceChannelName();
            if (string.IsNullOrWhiteSpace(selectedDeviceChannelName))
            {
                throw new Exception("无法启动互操作测试: 未选择设备通道");
            }

            var sequencePackageXml = selectedSequence.SequencePackageXml;

            var readerService = new TestResultReaderService();
            var testPlanName = Path.GetFileNameWithoutExtension(TestPlanManager.Instance.GetCurrentPlanPath());
            var testResult = readerService.CreateTestResult(testPlanName, TestType.Interoperation);

            var package = SequencePackageUtils.LoadFromString(sequencePackageXml);
            CreateCaseResults(testResult, package);

            var securityAccessDllBytes = currentPlan.Config.SecurityConfig.DllBytes;
            SecurityAccessUtils.PrepareDll(securityAccessDllBytes);

            var request = new ExecutionRequest
            {
                TestResult = testResult,
                HardwareConfig = currentPlan.Config.HardwareConfig,
                CaseConfig = currentPlan.Config.CaseConfig,
                SequencePackageXml = sequencePackageXml
            };
            var requestBody = JsonConvert.SerializeObject(request);

            var content = new StringContent(requestBody, System.Text.Encoding.UTF8, "application/json");
            var response = await _httpClient.PostAsync("api/tester/execute", content);
            return await HandleResponseAsync(response);
        }

        public async Task<ApiResponse> StopAsync()
        {
            var response = await _httpClient.PostAsync("api/tester/stop", null);
            return await HandleResponseAsync(response);
        }

        // 初始化用例
        private static void CreateCaseResults(TestResult testResult, SequencePackage sequencePackage)
        {
            var count = 1;
            var caseResults = new List<CaseResult>();
            foreach (var sequence in sequencePackage.Sequences)
            {
                var caseResult = new CaseResult
                {
                    Id = count,
                    TestResultId = testResult.Id,
                    SequenceId = Guid.Empty,
                    SequenceName = sequence.Name,
                    Parameter = string.Empty,
                    State = ExecutionState.Pending,
                    Begin = null,
                    End = null,
                    Detail = string.Empty
                };
                ++count;
                caseResults.Add(caseResult);
            }

            var writerService = new TestResultWriterService();
            try
            {
                writerService.Begin(testResult);
                writerService.BatchAddCaseResult(caseResults.ToArray());
            }
            finally
            {
                writerService.End();
            }
        }

        public async Task<ApiResponse> HandleResponseAsync(HttpResponseMessage response)
        {
            if (response.IsSuccessStatusCode)
            {
                return new ApiResponse(true, string.Empty);
            }
            else
            {
                var json = await response.Content.ReadAsStringAsync();
                var result = JsonConvert.DeserializeAnonymousType(json, new { message = string.Empty });
                return new ApiResponse(false, result.message);
            }
        }

        #region IDisposable Support
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    _httpClient?.Dispose();
                }
                _disposed = true;
            }
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }
        #endregion
    }
}
