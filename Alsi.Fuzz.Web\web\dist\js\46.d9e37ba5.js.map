{"version": 3, "file": "js/46.d9e37ba5.js", "mappings": "gLAOA,GAA4BA,EAAAA,EAAAA,IAAiB,CAC3CC,OAAQ,eACRC,MAAO,CACLC,MAAO,CAAC,GAEVC,KAAAA,CAAMC,GCFR,MAAMH,EAAQG,EAIRC,GAAUC,EAAAA,EAAAA,KAAoD,KAClE,OAAQL,EAAMC,OACZ,KAAKK,EAAAA,GAAeC,QAClB,MAAO,UACT,KAAKD,EAAAA,GAAeE,QAClB,MAAO,UACT,KAAKF,EAAAA,GAAeG,QAClB,MAAO,SACT,KAAKH,EAAAA,GAAeI,QACpB,QACE,MAAO,O,IAIPC,EAAoBV,IACxB,OAAQA,GACN,KAAKK,EAAAA,GAAeE,QAClB,MAAO,UACT,KAAKF,EAAAA,GAAeI,QAClB,MAAO,UACT,KAAKJ,EAAAA,GAAeC,QAClB,MAAO,SACT,KAAKD,EAAAA,GAAeG,QAClB,MAAO,SACT,QACE,MAAO,U,EAIPG,GAAYP,EAAAA,EAAAA,KAAS,IAClBM,EAAiBX,EAAMC,SDKhC,MAAO,CAACY,EAAUC,KAChB,MAAMC,GAAoBC,EAAAA,EAAAA,IAAkB,UAE5C,OAAQC,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAaH,EAAmB,CACpDI,KAAMf,EAAQgB,MACdC,KAAM,QACNC,MAAO,CAAC,YAAY,SACnB,CACDC,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBC,EAAAA,EAAAA,KAAiBC,EAAAA,EAAAA,IAAiBd,EAAUQ,OAAQ,MAEtDO,EAAG,GACF,EAAG,CAAC,QAAQ,CAEjB,IE5DA,MAAMC,EAAc,EAEpB,O;;;;;;;;ACGA,MAAMC,EAYJC,WAAAA,CAAYC,GACVC,KAAKC,UAAYF,EAAQE,UACzBD,KAAKE,MAAQH,EAAQG,OAAS,GAC9BF,KAAKG,WAAaJ,EAAQI,YAAc,GACxCH,KAAKI,WAAaL,EAAQK,YAAc,GACxCJ,KAAKK,iBAAmBN,EAAQO,WAChCN,KAAKO,mBAAqBR,EAAQS,aAClCR,KAAKS,UAAYV,EAAQU,WAAa,OAEtCT,KAAKU,kBAAoB,EACzBV,KAAKW,gBAAkB,EACvBX,KAAKY,gBAAkB,KACvBZ,KAAKa,eAAiB,KACtBb,KAAKc,iBAAmB,KACxBd,KAAKe,YAAcf,KAAKE,MAAMc,OAAShB,KAAKG,WAC5CH,KAAKiB,YAAc,EAGfjB,KAAKe,YAAcf,KAAKS,YAC1BT,KAAKiB,YAAcjB,KAAKS,UAAYT,KAAKe,aAG3Cf,KAAKkB,YACT,CAMEA,UAAAA,GAiBE,GAfAlB,KAAKC,UAAUkB,UAAY,GAG3BnB,KAAKY,gBAAkBQ,SAASC,cAAc,OAE9CC,OAAOC,OAAOvB,KAAKY,gBAAgBtB,MAAO,CACxCkC,KAAM,IACNC,SAAU,OACVC,SAAU,WACVC,UAAW,IACXC,OAAQ,OACRC,UAAW,eAIT7B,KAAKO,mBAAoB,CAC3B,MAAMuB,EAAS9B,KAAKO,qBAChBuB,GACF9B,KAAKY,gBAAgBmB,YAAYD,EAEzC,CAGI9B,KAAKa,eAAiBO,SAASC,cAAc,OAE7CC,OAAOC,OAAOvB,KAAKa,eAAevB,MAAO,CACvCoC,SAAU,WACVM,MAAO,SAIT,MAAMC,EAAejC,KAAKe,YAAcf,KAAKiB,YAC7CjB,KAAKa,eAAevB,MAAMsC,OAAS,GAAGK,MAGtCjC,KAAKc,iBAAmBM,SAASC,cAAc,OAE/CC,OAAOC,OAAOvB,KAAKc,iBAAiBxB,MAAO,CACzCoC,SAAU,WACVM,MAAO,OACPE,KAAM,MAIRlC,KAAKY,gBAAgBuB,iBAAiB,SAAUnC,KAAKoC,aAAaC,KAAKrC,OAGvEA,KAAKa,eAAekB,YAAY/B,KAAKc,kBACrCd,KAAKY,gBAAgBmB,YAAY/B,KAAKa,gBACtCb,KAAKC,UAAU8B,YAAY/B,KAAKY,iBAGhCZ,KAAKsC,mBAAmB,EAAGC,KAAKC,IAAI,IAAKxC,KAAKE,MAAMc,QACxD,CAMEoB,YAAAA,GACE,MAAMK,EAAYzC,KAAKY,gBAAgB6B,UACjCC,EAAkB1C,KAAKY,gBAAgB+B,aAGvCC,EAAgBH,EAAYzC,KAAKiB,YAGjC4B,EAAaN,KAAKO,IAAI,EAAGP,KAAKQ,MAAMH,EAAgB5C,KAAKG,YAAcH,KAAKI,YAC5E4C,EAAWT,KAAKC,IACpBxC,KAAKE,MAAMc,OACXuB,KAAKU,MAAML,EAAgBF,EAAkB1C,KAAKiB,aAAejB,KAAKG,YAAcH,KAAKI,YAIvFyC,IAAe7C,KAAKU,mBAAqBsC,IAAahD,KAAKW,kBAC7DX,KAAKsC,mBAAmBO,EAAYG,GACpChD,KAAKU,kBAAoBmC,EACzB7C,KAAKW,gBAAkBqC,EAE7B,CAQEV,kBAAAA,CAAmBO,EAAYG,GAE7BhD,KAAKc,iBAAiBK,UAAY,GAGlCnB,KAAKc,iBAAiBxB,MAAM4D,UAAY,cAAcL,EAAa7C,KAAKG,WAAaH,KAAKiB,iBAG1F,IAAK,IAAIkC,EAAIN,EAAYM,EAAIH,EAAUG,IAAK,CAC1C,MAAMC,EAAOpD,KAAKE,MAAMiD,GAExB,GAAInD,KAAKK,iBAAkB,CAEzB,MAAMgD,EAAcrD,KAAKK,iBAAiB+C,EAAMD,GAC5CE,IAEFA,EAAY/D,MAAMsC,OAAY5B,KAAKG,WAAaH,KAAKiB,YAA1B,KAC3BoC,EAAY/D,MAAMuC,UAAY,aAC9BwB,EAAY/D,MAAM0C,MAAQ,OAE1BhC,KAAKc,iBAAiBiB,YAAYsB,GAE5C,KAAa,CAEL,MAAMC,EAAMlC,SAASC,cAAc,OACnCC,OAAOC,OAAO+B,EAAIhE,MAAO,CACvBsC,OAAW5B,KAAKG,WAAaH,KAAKiB,YAA1B,KACRe,MAAO,OACPH,UAAW,aACX0B,QAAS,MACTC,aAAc,mBAEhBF,EAAIG,YAAcC,KAAKC,UAAUP,GACjCpD,KAAKc,iBAAiBiB,YAAYuB,EAC1C,CACA,CACA,CAOEM,WAAAA,CAAY1D,GACVF,KAAKE,MAAQA,GAAS,GACtBF,KAAKe,YAAcf,KAAKE,MAAMc,OAAShB,KAAKG,WAG5CH,KAAKiB,YAAc,EACfjB,KAAKe,YAAcf,KAAKS,YAC1BT,KAAKiB,YAAcjB,KAAKS,UAAYT,KAAKe,aAIvCf,KAAKa,iBACPb,KAAKa,eAAevB,MAAMsC,OAAY5B,KAAKe,YAAcf,KAAKiB,YAA3B,MAGrCjB,KAAKU,kBAAoB,EACzBV,KAAKW,gBAAkB,EAGvBX,KAAKoC,cACT,CAOEyB,aAAAA,CAAcC,GACRA,GAAS,GAAKA,EAAQ9D,KAAKE,MAAMc,SAEnChB,KAAKY,gBAAgB6B,UAAYqB,EAAQ9D,KAAKG,WAAaH,KAAKiB,YAEtE,CAME8C,OAAAA,GACM/D,KAAKY,iBACPZ,KAAKY,gBAAgBoD,oBAAoB,SAAUhE,KAAKoC,cAEtDpC,KAAKC,YACPD,KAAKC,UAAUkB,UAAY,IAE7BnB,KAAKE,MAAQ,KACbF,KAAKC,UAAY,KACjBD,KAAKY,gBAAkB,KACvBZ,KAAKa,eAAiB,KACtBb,KAAKc,iBAAmB,IAC5B,CAMEmD,OAAAA,GACEjE,KAAKoC,cACT,CAOE8B,kBAAAA,GACE,OAAOlE,KAAKY,eAChB;;;;;;GC3OsB,qBAAXuD,SACTA,OAAOC,UAAY,CACjBvE,kB,cCZJ,MAAMwE,EAAa,CAAEC,MAAO,kCCuBtBC,EAAc,GACdC,EAAc,GACdC,EAAgB,GDbtB,OAA4B3G,EAAAA,EAAAA,IAAiB,CAC3CC,OAAQ,WACRC,MAAO,CACL0G,MAAO,CAAC,GAEVC,MAAO,CAAC,eACRzG,KAAAA,CAAMC,GAAgByG,KAAMC,ICP9B,MAAM7G,EAAQG,EAIRyG,EAAOC,EAKPC,GAAiBC,EAAAA,EAAAA,IAAwB,MACzCC,GAAkBD,EAAAA,EAAAA,IAAwB,MAChD,IAAIE,EAAqB,KAMzB,MAAMC,EAAoBA,KACxB,IAAKF,EAAgB5F,MAAO,OAG5B4F,EAAgB5F,MAAM+B,UAAY,GAGlC,MAAMW,EAASV,SAASC,cAAc,OACtCS,EAAOqD,UAAY,aACnBrD,EAAOxC,MAAMsC,OAAS,GAAG6C,MAGzB,MAAMW,EAAWhE,SAASC,cAAc,OACxC+D,EAAS3B,YAAc,KACvB2B,EAASD,UAAY,iBACrBrD,EAAOC,YAAYqD,GAGnB,MAAMC,EAAajE,SAASC,cAAc,OAC1CgE,EAAW5B,YAAc,OACzB4B,EAAWF,UAAY,mBACvBrD,EAAOC,YAAYsD,GAGnB,MAAMC,EAAclE,SAASC,cAAc,OAC3CiE,EAAY7B,YAAc,YAC1B6B,EAAYH,UAAY,oBACxBrD,EAAOC,YAAYuD,GAGnB,MAAMC,EAAenE,SAASC,cAAc,OAC5CkE,EAAa9B,YAAc,SAC3B8B,EAAaJ,UAAY,qBACzBrD,EAAOC,YAAYwD,GAGnB,MAAMC,EAAepE,SAASC,cAAc,OAC5CmE,EAAa/B,YAAc,SAC3B+B,EAAaL,UAAY,qBACzBrD,EAAOC,YAAYyD,GAGnBR,EAAgB5F,MAAM2C,YAAYD,EAAO,EAIrC2D,EAAoBA,KACnBX,EAAe1F,OAAUpB,EAAM0G,MAAM1D,SAG1CkE,IAGID,GACFA,EAAclB,UAGhBkB,EAAgB,IAAIpF,EAAc,CAChCI,UAAW6E,EAAe1F,MAC1Bc,MAAOlC,EAAM0G,MACbvE,WAAYoE,EACZnE,WAAYoE,EACZlE,WAAYA,CAAC8C,EAAkBU,KAE7B,MAAM4B,EAAMtE,SAASC,cAAc,OACnCqE,EAAIP,UAAY,WAChBO,EAAIC,QAAU,IAAMf,EAAK,cAAexB,GAGxCsC,EAAIpG,MAAMsC,OAAS,GAAG2C,MACtBmB,EAAIpG,MAAMsG,WAAa,GAAGrB,MAC1BmB,EAAIpG,MAAMkE,aAAeM,IAAU9F,EAAM0G,MAAM1D,OAAS,EAAI,OAAS,oBACrE0E,EAAIpG,MAAMuG,gBAAkBzC,EAAK0C,GAAK,IAAM,EAAI,OAAS,UAGzDJ,EAAIK,YAAc,KAChBL,EAAIpG,MAAMuG,gBAAkB,SAAS,EAEvCH,EAAIM,WAAa,KACfN,EAAIpG,MAAMuG,gBAAkBzC,EAAK0C,GAAK,IAAM,EAAI,OAAS,UAEzDJ,EAAIpG,MAAMkE,aAAeM,IAAU9F,EAAM0G,MAAM1D,OAAS,EAAI,OAAS,mBAAmB,EAI1F,MAAMiF,EAAS7E,SAASC,cAAc,OACtC4E,EAAOd,UAAY,mBAGnB,MAAMe,EAAQ9E,SAASC,cAAc,OACrC6E,EAAMzC,YAAc,IAAIL,EAAK0C,KAC7BI,EAAMf,UAAY,eAClBc,EAAOlE,YAAYmE,GAGnB,MAAMC,EAAU/E,SAASC,cAAc,OACvC8E,EAAQ1C,YAAcL,EAAKgD,KAC3BD,EAAQhB,UAAY,iBACpBc,EAAOlE,YAAYoE,GAGnB,MAAME,EAAWjF,SAASC,cAAc,OACxCgF,EAAS5C,YAAcL,EAAKkD,UAC5BD,EAASE,MAAQnD,EAAKkD,UACtBD,EAASlB,UAAY,kBACrBc,EAAOlE,YAAYsE,GAGnB,MAAMG,EAAYpF,SAASC,cAAc,OACzCmF,EAAU/C,YAAcL,EAAKqD,QAAU,IACvCD,EAAUD,MAAQnD,EAAKqD,QAAU,GACjCD,EAAUrB,UAAY,mBACtBc,EAAOlE,YAAYyE,GAGnB,MAAME,EAAYtF,SAASC,cAAc,OACzCqF,EAAUvB,UAAY,mBAGtB,MAAM/G,EAAUuI,EAAiBvD,EAAKnF,OAChC2I,EAAUC,EAAczD,EAAKnF,OAE7B6I,EAAQ1F,SAASC,cAAc,QASrC,OARAyF,EAAM3B,UAAY,kBAAkB/G,kCACpC0I,EAAMrD,YAAcmD,EAEpBF,EAAU3E,YAAY+E,GACtBb,EAAOlE,YAAY2E,GAEnBhB,EAAI3D,YAAYkE,GAETP,CAAG,IAEZ,EAIEqB,EAAsBA,KACtB9B,EACFA,EAAcrB,YAAY5F,EAAM0G,QAEhCsC,EAAAA,EAAAA,KAAS,KACPvB,GAAmB,G,EAMnBkB,EAAoB1I,IACxB,OAAQA,GACN,KAAKK,EAAAA,GAAeC,QAClB,MAAO,UACT,KAAKD,EAAAA,GAAeE,QAClB,MAAO,UACT,KAAKF,EAAAA,GAAeG,QAClB,MAAO,SACT,KAAKH,EAAAA,GAAeI,QACpB,QACE,MAAO,O,EAKPmI,EAAiB5I,IACrB,OAAQA,GACN,KAAKK,EAAAA,GAAeC,QAClB,MAAO,SACT,KAAKD,EAAAA,GAAeG,QAClB,MAAO,SACT,KAAKH,EAAAA,GAAeE,QAClB,MAAO,UACT,KAAKF,EAAAA,GAAeI,QAClB,MAAO,UACT,QACE,MAAO,U,ED0Bb,OCrBAuI,EAAAA,EAAAA,KAAM,IAAMjJ,EAAM0G,QAAO,MACvBsC,EAAAA,EAAAA,KAAS,KACPD,GAAqB,GACrB,GACD,CAAEG,MAAM,KAGXC,EAAAA,EAAAA,KAAU,MACRH,EAAAA,EAAAA,KAAS,KACPvB,GAAmB,GACnB,KAIJ2B,EAAAA,EAAAA,KAAY,KACNnC,IACFA,EAAclB,UACdkB,EAAgB,K,IDIb,CAACpG,EAAUC,MACRG,EAAAA,EAAAA,OAAcoI,EAAAA,EAAAA,IAAoB,MAAOhD,EAAY,EAC3DiD,EAAAA,EAAAA,IAAoB,MAAO,CACzBhD,MAAO,eACPiD,QAAS,kBACTxC,IAAKC,GACJ,KAAM,MACTsC,EAAAA,EAAAA,IAAoB,MAAO,CACzBC,QAAS,iBACTxC,IAAKD,EACLR,MAAO,iBACN,KAAM,OAGb,I,UE/OA,MAAM1E,GAA2B,OAAgB,EAAQ,CAAC,CAAC,YAAY,qBAEvE,O,4GCDO,MAAM4H,EAAuBvJ,IAClC,OAAQA,GACN,KAAKK,EAAAA,GAAeC,QAClB,MAAO,UACT,KAAKD,EAAAA,GAAeE,QAClB,MAAO,UACT,KAAKF,EAAAA,GAAeG,QAClB,MAAO,SACT,KAAKH,EAAAA,GAAeI,QACpB,QACE,MAAO,O,gBCdb,MAAM2F,EAAa,CACjBoD,IAAK,EACLnD,MAAO,WAEHoD,EAAa,CACjBD,IAAK,EACLnD,MAAO,uBAEHqD,EAAa,CAAErD,MAAO,cACtBsD,EAAa,CAAEtD,MAAO,aACtBuD,EAAa,CAAEvD,MAAO,aACtBwD,EAAa,CAAExD,MAAO,SACtByD,EAAa,CAAEzD,MAAO,aACtB0D,EAAa,CAAE1D,MAAO,SACtB2D,EAAa,CAAE3D,MAAO,aACtB4D,EAAc,CAAE5D,MAAO,SACvB6D,EAAc,CAAE7D,MAAO,aACvB8D,EAAc,CAAE9D,MAAO,yBACvB+D,EAAc,CAClBZ,IAAK,EACLnD,MAAO,wBAEHgE,EAAc,CAAC,SACfC,EAAc,CAClBd,IAAK,EACLnD,MAAO,wBAEHkE,EAAc,CAAC,SACfC,EAAc,CAAEnE,MAAO,iBACvBoE,EAAc,CAClBjB,IAAK,EACLnD,MAAO,YAEHqE,EAAc,CAAErE,MAAO,gBACvBsE,EAAc,CAAEtE,MAAO,YACvBuE,EAAc,CAAEvE,MAAO,aACvBwE,EAAc,CAAExE,MAAO,kBACvByE,EAAc,CAAC,SACfC,EAAc,CAAC,SACfC,EAAc,CAAE3E,MAAO,cACvB4E,EAAc,CAAE5E,MAAO,iBAW7B,OAA4BxG,EAAAA,EAAAA,IAAiB,CAC3CC,OAAQ,mBACRC,MAAO,CACLmL,QAAS,CAAEhK,KAAMiK,SACjBC,aAAc,CAAC,EACfC,aAAc,CAAC,GAEjB3E,MAAO,CAAC,iBAAkB,SAC1BzG,KAAAA,CAAMC,GAAgByG,KAAMC,ICyB9B,MAAM7G,EAAQG,EAMRyG,EAAOC,EAKP0E,GAAgBxE,EAAAA,EAAAA,IAAI/G,EAAMmL,SAC1BK,GAAWzE,EAAAA,EAAAA,IAAuB,MAClC0E,GAAQ1E,EAAAA,EAAAA,IAAgB,IACxB2E,GAAU3E,EAAAA,EAAAA,KAAI,IAEpBkC,EAAAA,EAAAA,KAAM,IAAMjJ,EAAMmL,UAAUQ,IAC1BJ,EAAcnK,MAAQuK,EAClBA,GAAY3L,EAAMqL,cAAgBrL,EAAMsL,cAC1CM,G,KAIJ3C,EAAAA,EAAAA,KAAM,IAAMsC,EAAcnK,QAAQuK,IAChC/E,EAAK,iBAAkB+E,GAClBA,GAAU/E,EAAK,QAAQ,IAG9B,MAAMgF,EAAeC,UACnB,GAAK7L,EAAMqL,cAAiBrL,EAAMsL,aAAlC,CAKAI,EAAQtK,OAAQ,EAChB,IAEE,MAAO0K,EAAcC,SAAuBC,QAAQC,IAAI,CACtDC,EAAAA,GAAOC,cAAcnM,EAAMqL,aAAcrL,EAAMsL,cAC/CY,EAAAA,GAAOE,aAAapM,EAAMqL,aAAcrL,EAAMsL,gBAGhDE,EAASpK,MAAQ0K,EAAaO,KAC9BZ,EAAMrK,MAAQ2K,EAAcM,I,CAC5B,MAAOC,GACPC,QAAQD,MAAM,4BAA6BA,GAC3CE,EAAAA,GAAUF,MAAM,8B,CAChB,QACAZ,EAAQtK,OAAQ,C,OAlBhBoL,EAAAA,GAAUC,QAAQ,8B,EAsBhBC,EAAcA,KAClBnB,EAAcnK,OAAQ,EAEtBoK,EAASpK,MAAQ,KACjBqK,EAAMrK,MAAQ,EAAE,EAIZuL,EAAkBC,IACtB,IAAKA,EAAY,MAAO,MACxB,IACE,MAAMC,EAAO,IAAIC,KAAKF,GACtB,OAAOC,EAAKE,gB,CACZ,MAAOC,GACP,OAAOJ,C,GAuBLK,EAAsBC,IAC1B,IAAKA,GAAiC,IAAjBA,EAAoB,MAAO,MAGhD,MAAMC,EAAUD,EAAe,IAE/B,MAAO,GAAGC,EAAQC,QAAQ,IAAI,EDpBhC,OCwBAjE,EAAAA,EAAAA,KAAU,KACJoC,EAAcnK,OAASpB,EAAMqL,cAAgBrL,EAAMsL,cACrDM,G,ID1BG,CAAC/K,EAAUC,KAChB,MAAMuM,GAAyBrM,EAAAA,EAAAA,IAAkB,eAC3CsM,GAAsBtM,EAAAA,EAAAA,IAAkB,YACxCuM,GAA8BvM,EAAAA,EAAAA,IAAkB,oBAChDwM,GAAyBxM,EAAAA,EAAAA,IAAkB,eAC3CyM,GAAuBzM,EAAAA,EAAAA,IAAkB,aACzC0M,GAAuB1M,EAAAA,EAAAA,IAAkB,aAE/C,OAAQC,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAawM,EAAsB,CACvDC,WAAYpC,EAAcnK,MAC1B,sBAAuBN,EAAO,KAAOA,EAAO,GAAM8M,GAAkBrC,EAAenK,MAAQwM,GAC3FrF,MAAO,GAAGiD,EAASpK,OAAOgH,MAAQoD,EAASpK,OAAOyM,cAAgB,KAClE7J,MAAO,MACP,mBAAoB,IACnB,CACD8J,QAAQtM,EAAAA,EAAAA,KAAS,IAAM,EACrB8H,EAAAA,EAAAA,IAAoB,OAAQ4B,EAAa,EACvC6C,EAAAA,EAAAA,IAAaN,EAAsB,CAAEO,QAAStB,GAAe,CAC3DnL,SAASC,EAAAA,EAAAA,KAAS,IAAMV,EAAO,KAAOA,EAAO,GAAK,EAChDW,EAAAA,EAAAA,IAAiB,aAEnBE,EAAG,SAITJ,SAASC,EAAAA,EAAAA,KAAS,IAAM,CACrBkK,EAAQtK,QACJH,EAAAA,EAAAA,OAAcoI,EAAAA,EAAAA,IAAoB,MAAOhD,EAAY,EACpD0H,EAAAA,EAAAA,IAAaV,EAAwB,CACnCY,KAAM,GACNC,SAAU,UAGbjN,EAAAA,EAAAA,OAAcoI,EAAAA,EAAAA,IAAoB,MAAOK,EAAY,EACpDJ,EAAAA,EAAAA,IAAoB,MAAOK,EAAY,CACrC7I,EAAO,KAAOA,EAAO,IAAKwI,EAAAA,EAAAA,IAAoB,KAAM,KAAM,eAAgB,KAC1EA,EAAAA,EAAAA,IAAoB,MAAOM,EAAY,EACrCN,EAAAA,EAAAA,IAAoB,MAAOO,EAAY,CACrC/I,EAAO,KAAOA,EAAO,IAAKwI,EAAAA,EAAAA,IAAoB,MAAO,CAAEhD,MAAO,SAAW,cAAe,KACxFgD,EAAAA,EAAAA,IAAoB,MAAOQ,GAAYpI,EAAAA,EAAAA,IAAiB8J,EAASpK,OAAOgH,MAAO,MAEjFkB,EAAAA,EAAAA,IAAoB,MAAOS,EAAY,CACrCjJ,EAAO,KAAOA,EAAO,IAAKwI,EAAAA,EAAAA,IAAoB,MAAO,CAAEhD,MAAO,SAAW,kBAAmB,KAC5FgD,EAAAA,EAAAA,IAAoB,MAAOU,GAAYtI,EAAAA,EAAAA,IAAiB8J,EAASpK,OAAOyM,cAAe,MAEzFvE,EAAAA,EAAAA,IAAoB,MAAOW,EAAY,CACrCnJ,EAAO,KAAOA,EAAO,IAAKwI,EAAAA,EAAAA,IAAoB,MAAO,CAAEhD,MAAO,SAAW,eAAgB,KACzFgD,EAAAA,EAAAA,IAAoB,MAAOY,GAAaxI,EAAAA,EAAAA,IAAiBiL,EAAenB,EAASpK,OAAO+M,QAAS,MAEnG7E,EAAAA,EAAAA,IAAoB,MAAOa,EAAa,CACtCrJ,EAAO,KAAOA,EAAO,IAAKwI,EAAAA,EAAAA,IAAoB,MAAO,CAAEhD,MAAO,SAAW,sBAAuB,KAChGgD,EAAAA,EAAAA,IAAoB,MAAOc,EAAa,EACtC3I,EAAAA,EAAAA,KAAiBC,EAAAA,EAAAA,IAAiBiL,EAAenB,EAASpK,OAAOgN,MAAQ,IAAK,IAC9EL,EAAAA,EAAAA,IAAaM,EAAAA,EAAc,CACzBpO,MAAOuL,EAASpK,OAAOnB,OAAS,GAChCqG,MAAO,cACN,KAAM,EAAG,CAAC,cAGhBkF,EAASpK,OAAOkH,YACZrH,EAAAA,EAAAA,OAAcoI,EAAAA,EAAAA,IAAoB,MAAOgB,EAAa,CACrDvJ,EAAO,KAAOA,EAAO,IAAKwI,EAAAA,EAAAA,IAAoB,MAAO,CAAEhD,MAAO,SAAW,cAAe,KACxFgD,EAAAA,EAAAA,IAAoB,MAAO,CACzBhD,MAAO,QACPiC,MAAOiD,EAASpK,OAAOkH,YACtB5G,EAAAA,EAAAA,IAAiB8J,EAASpK,OAAOkH,WAAY,EAAGgC,OAErDgE,EAAAA,EAAAA,IAAoB,IAAI,GAC3B9C,EAASpK,OAAOqH,SACZxH,EAAAA,EAAAA,OAAcoI,EAAAA,EAAAA,IAAoB,MAAOkB,EAAa,CACrDzJ,EAAO,KAAOA,EAAO,IAAKwI,EAAAA,EAAAA,IAAoB,MAAO,CAAEhD,MAAO,SAAW,WAAY,KACrFgD,EAAAA,EAAAA,IAAoB,MAAO,CACzBhD,MAAO,QACPiC,MAAOiD,EAASpK,OAAOqH,SACtB/G,EAAAA,EAAAA,IAAiB8J,EAASpK,MAAMqH,QAAS,EAAG+B,OAEjD8D,EAAAA,EAAAA,IAAoB,IAAI,QAGhChF,EAAAA,EAAAA,IAAoB,MAAOmB,EAAa,CACtC3J,EAAO,KAAOA,EAAO,IAAKwI,EAAAA,EAAAA,IAAoB,KAAM,KAAM,SAAU,IAC5C,IAAvBmC,EAAMrK,MAAM4B,SACR/B,EAAAA,EAAAA,OAAcoI,EAAAA,EAAAA,IAAoB,MAAOqB,EAAa,EACrDqD,EAAAA,EAAAA,IAAaT,EAAqB,CAAEiB,YAAa,4BAElDtN,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAasM,EAAwB,CAAE/D,IAAK,GAAK,CAC9DlI,SAASC,EAAAA,EAAAA,KAAS,IAAM,GACrBP,EAAAA,EAAAA,KAAW,IAAOoI,EAAAA,EAAAA,IAAoBmF,EAAAA,GAAW,MAAMC,EAAAA,EAAAA,IAAYhD,EAAMrK,OAAQsN,KACxEzN,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAaqM,EAA6B,CAC9D9D,IAAKiF,EAAK5G,GACV3G,MAAMwN,EAAAA,EAAAA,IAAOnF,EAAPmF,CAA4BD,EAAKzO,OACvC2O,OAAuB,YAAfF,EAAKzO,OACZ,CACDsB,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtB8H,EAAAA,EAAAA,IAAoB,MAAOqB,EAAa,EACtCrB,EAAAA,EAAAA,IAAoB,MAAOsB,EAAa,EACtCtB,EAAAA,EAAAA,IAAoB,MAAOuB,EAAa,EACtCvB,EAAAA,EAAAA,IAAoB,OAAQwB,GAAapJ,EAAAA,EAAAA,IAAiBuL,EAAmByB,EAAKG,YAAa,IAC/FvF,EAAAA,EAAAA,IAAoB,OAAQ,CAC1BhD,MAAO,YACPiC,MAAOmG,EAAKtG,OACX1G,EAAAA,EAAAA,IAAiBgN,EAAKtG,MAAO,EAAG2C,GAClC2D,EAAKjG,SACDxH,EAAAA,EAAAA,OAAcoI,EAAAA,EAAAA,IAAoB,OAAQ,CACzCI,IAAK,EACLlB,MAAOmG,EAAKjG,OACZnC,MAAO,uBACN5E,EAAAA,EAAAA,IAAiBgN,EAAKjG,QAAS,EAAGuC,KACrCsD,EAAAA,EAAAA,IAAoB,IAAI,MAE9BhF,EAAAA,EAAAA,IAAoB,MAAO2B,EAAa,EACtC8C,EAAAA,EAAAA,IAAaM,EAAAA,EAAc,CACzBpO,MAAOyO,EAAKzO,OACX,KAAM,EAAG,CAAC,mBAKrB0B,EAAG,GACF,KAAM,CAAC,OAAQ,cAChB,SAENA,EAAG,aAKnBA,EAAG,GACF,EAAG,CAAC,aAAc,SAAS,CAEhC,I,UE9RA,MAAMC,GAA2B,OAAgB,EAAQ,CAAC,CAAC,YAAY,qBAEvE,O", "sources": ["webpack://fuzz-web/./src/components/common/CaseStateTag.vue?c0c7", "webpack://fuzz-web/./src/components/common/CaseStateTag.vue", "webpack://fuzz-web/./src/components/common/CaseStateTag.vue?4980", "webpack://fuzz-web/../src/virtual-scroll.js", "webpack://fuzz-web/../src/index.js", "webpack://fuzz-web/./src/components/test/CaseList.vue?a767", "webpack://fuzz-web/./src/components/test/CaseList.vue", "webpack://fuzz-web/./src/components/test/CaseList.vue?5681", "webpack://fuzz-web/./src/utils/status.ts", "webpack://fuzz-web/./src/components/test/CaseDetailDialog.vue?5717", "webpack://fuzz-web/./src/components/test/CaseDetailDialog.vue", "webpack://fuzz-web/./src/components/test/CaseDetailDialog.vue?ca65"], "sourcesContent": ["import { defineComponent as _defineComponent } from 'vue'\nimport { toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, openBlock as _openBlock, createBlock as _createBlock } from \"vue\"\n\nimport { computed } from 'vue';\r\nimport { ExecutionState } from '@/api/appApi';\r\n\r\n\nexport default /*@__PURE__*/_defineComponent({\n  __name: 'CaseStateTag',\n  props: {\n    state: {}\n  },\n  setup(__props: any) {\n\r\nconst props = __props;\r\n\r\nconst tagType = computed<'success' | 'warning' | 'danger' | 'info'>(() => {\r\n  switch (props.state) {\r\n    case ExecutionState.Success:\r\n      return 'success';\r\n    case ExecutionState.Running:\r\n      return 'warning';\r\n    case ExecutionState.Failure:\r\n      return 'danger';\r\n    case ExecutionState.Pending:\r\n    default:\r\n      return 'info';\r\n  }\r\n});\r\n\r\nconst getCaseStateName = (state: string): 'Not Run' | 'Running' | 'Passed' | 'Failed' | 'Unknown' => {\r\n  switch (state) {\r\n    case ExecutionState.Running:\r\n      return 'Running';\r\n    case ExecutionState.Pending:\r\n      return 'Not Run';\r\n    case ExecutionState.Success:\r\n      return 'Passed';\r\n    case ExecutionState.Failure:\r\n      return 'Failed';\r\n    default:\r\n      return 'Unknown';\r\n  }\r\n};\r\n\r\nconst stateName = computed(() => {\r\n  return getCaseStateName(props.state);\r\n});\r\n\nreturn (_ctx: any,_cache: any) => {\n  const _component_el_tag = _resolveComponent(\"el-tag\")!\n\n  return (_openBlock(), _createBlock(_component_el_tag, {\n    type: tagType.value,\n    size: \"small\",\n    style: {\"min-width\":\"60px\"}\n  }, {\n    default: _withCtx(() => [\n      _createTextVNode(_toDisplayString(stateName.value), 1)\n    ]),\n    _: 1\n  }, 8, [\"type\"]))\n}\n}\n\n})", "<template>\r\n  <el-tag :type=\"tagType\" size=\"small\" style=\"min-width: 60px;\">\r\n    {{ stateName }}\r\n  </el-tag>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { computed, defineProps } from 'vue';\r\nimport { ExecutionState } from '@/api/appApi';\r\n\r\nconst props = defineProps<{\r\n  state: string;\r\n}>();\r\n\r\nconst tagType = computed<'success' | 'warning' | 'danger' | 'info'>(() => {\r\n  switch (props.state) {\r\n    case ExecutionState.Success:\r\n      return 'success';\r\n    case ExecutionState.Running:\r\n      return 'warning';\r\n    case ExecutionState.Failure:\r\n      return 'danger';\r\n    case ExecutionState.Pending:\r\n    default:\r\n      return 'info';\r\n  }\r\n});\r\n\r\nconst getCaseStateName = (state: string): 'Not Run' | 'Running' | 'Passed' | 'Failed' | 'Unknown' => {\r\n  switch (state) {\r\n    case ExecutionState.Running:\r\n      return 'Running';\r\n    case ExecutionState.Pending:\r\n      return 'Not Run';\r\n    case ExecutionState.Success:\r\n      return 'Passed';\r\n    case ExecutionState.Failure:\r\n      return 'Failed';\r\n    default:\r\n      return 'Unknown';\r\n  }\r\n};\r\n\r\nconst stateName = computed(() => {\r\n  return getCaseStateName(props.state);\r\n});\r\n</script>\r\n", "import script from \"./CaseStateTag.vue?vue&type=script&setup=true&lang=ts\"\nexport * from \"./CaseStateTag.vue?vue&type=script&setup=true&lang=ts\"\n\nconst __exports__ = script;\n\nexport default __exports__", "/**\r\n * js-booster - High-performance frontend library\r\n * VirtualScroll - Virtual scrolling implementation\r\n * @version 1.1.1\r\n * <AUTHOR>\r\n * @license MIT\r\n */\r\n\r\nclass VirtualScroll {\r\n  /**\r\n   * Create a virtual scroll instance\r\n   * @param {Object} options Configuration options\r\n   * @param {HTMLElement} options.container Scroll container element\r\n   * @param {Array} options.items Data items to display\r\n   * @param {number} [options.itemHeight=20] Height of each list item (pixels)\r\n   * @param {number} [options.bufferSize=10] Number of buffer items outside the visible area\r\n   * @param {Function} [options.renderItem] Custom item rendering function\r\n   * @param {Function} [options.renderHeader] Custom header rendering function\r\n   * @param {number} [options.maxHeight=26840000] Maximum height in pixels for the content wrapper\r\n   */\r\n  constructor(options) {\r\n    this.container = options.container;\r\n    this.items = options.items || [];\r\n    this.itemHeight = options.itemHeight || 20;\r\n    this.bufferSize = options.bufferSize || 10;\r\n    this.customRenderItem = options.renderItem;\r\n    this.customRenderHeader = options.renderHeader;\r\n    this.maxHeight = options.maxHeight || 26840000; // Add maximum height limit to prevent DOM height overflow\r\n\r\n    this.visibleStartIndex = 0;\r\n    this.visibleEndIndex = 0;\r\n    this.scrollContainer = null;\r\n    this.contentWrapper = null;\r\n    this.contentContainer = null;\r\n    this.totalHeight = this.items.length * this.itemHeight;\r\n    this.heightScale = 1; // Height scaling factor\r\n\r\n    // If total height exceeds maximum height, calculate scaling factor\r\n    if (this.totalHeight > this.maxHeight) {\r\n      this.heightScale = this.maxHeight / this.totalHeight;\r\n    }\r\n\r\n    this.initialize();\r\n  }\r\n\r\n  /**\r\n   * Initialize virtual scroll component\r\n   * @private\r\n   */\r\n  initialize() {\r\n    // Clear container\r\n    this.container.innerHTML = '';\r\n\r\n    // Create scroll container\r\n    this.scrollContainer = document.createElement('div');\r\n    // Add inline styles\r\n    Object.assign(this.scrollContainer.style, {\r\n      flex: '1',\r\n      overflow: 'auto',\r\n      position: 'relative',\r\n      minHeight: '0',\r\n      height: '100%',\r\n      boxSizing: 'border-box'\r\n    });\r\n\r\n    // If there's a custom header render function, render the header\r\n    if (this.customRenderHeader) {\r\n      const header = this.customRenderHeader();\r\n      if (header) {\r\n        this.scrollContainer.appendChild(header);\r\n      }\r\n    }\r\n\r\n    // Create content wrapper\r\n    this.contentWrapper = document.createElement('div');\r\n    // Add inline styles\r\n    Object.assign(this.contentWrapper.style, {\r\n      position: 'relative',\r\n      width: '100%'\r\n    });\r\n\r\n    // Use scaled height to ensure it doesn't exceed browser limits\r\n    const scaledHeight = this.totalHeight * this.heightScale;\r\n    this.contentWrapper.style.height = `${scaledHeight}px`;\r\n\r\n    // Create content container\r\n    this.contentContainer = document.createElement('div');\r\n    // Add inline styles\r\n    Object.assign(this.contentContainer.style, {\r\n      position: 'absolute',\r\n      width: '100%',\r\n      left: '0'\r\n    });\r\n\r\n    // Add scroll event listener\r\n    this.scrollContainer.addEventListener('scroll', this.handleScroll.bind(this));\r\n\r\n    // Assemble DOM\r\n    this.contentWrapper.appendChild(this.contentContainer);\r\n    this.scrollContainer.appendChild(this.contentWrapper);\r\n    this.container.appendChild(this.scrollContainer);\r\n\r\n    // Render initial visible items\r\n    this.renderVisibleItems(0, Math.min(100, this.items.length));\r\n  }\r\n\r\n  /**\r\n   * Handle scroll event\r\n   * @private\r\n   */\r\n  handleScroll() {\r\n    const scrollTop = this.scrollContainer.scrollTop;\r\n    const containerHeight = this.scrollContainer.clientHeight;\r\n\r\n    // Consider scaling factor in calculations\r\n    const realScrollTop = scrollTop / this.heightScale;\r\n\r\n    // Calculate visible range\r\n    const startIndex = Math.max(0, Math.floor(realScrollTop / this.itemHeight) - this.bufferSize);\r\n    const endIndex = Math.min(\r\n      this.items.length,\r\n      Math.ceil((realScrollTop + containerHeight / this.heightScale) / this.itemHeight) + this.bufferSize\r\n    );\r\n\r\n    // Only update when visible range changes\r\n    if (startIndex !== this.visibleStartIndex || endIndex !== this.visibleEndIndex) {\r\n      this.renderVisibleItems(startIndex, endIndex);\r\n      this.visibleStartIndex = startIndex;\r\n      this.visibleEndIndex = endIndex;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Render visible items\r\n   * @param {number} startIndex Start index\r\n   * @param {number} endIndex End index\r\n   * @private\r\n   */\r\n  renderVisibleItems(startIndex, endIndex) {\r\n    // Clear content container\r\n    this.contentContainer.innerHTML = '';\r\n\r\n    // Set position considering scaling factor\r\n    this.contentContainer.style.transform = `translateY(${startIndex * this.itemHeight * this.heightScale}px)`;\r\n\r\n    // Render visible items\r\n    for (let i = startIndex; i < endIndex; i++) {\r\n      const item = this.items[i];\r\n\r\n      if (this.customRenderItem) {\r\n        // Use custom render function\r\n        const itemElement = this.customRenderItem(item, i);\r\n        if (itemElement) {\r\n          // Only set necessary height styles, other styles are determined by the caller\r\n          itemElement.style.height = `${this.itemHeight * this.heightScale}px`;\r\n          itemElement.style.boxSizing = 'border-box';\r\n          itemElement.style.width = '100%';\r\n\r\n          this.contentContainer.appendChild(itemElement);\r\n        }\r\n      } else {\r\n        // Use default rendering - very simple default implementation\r\n        const row = document.createElement('div');\r\n        Object.assign(row.style, {\r\n          height: `${this.itemHeight * this.heightScale}px`,\r\n          width: '100%',\r\n          boxSizing: 'border-box',\r\n          padding: '8px',\r\n          borderBottom: '1px solid #eee'\r\n        });\r\n        row.textContent = JSON.stringify(item);\r\n        this.contentContainer.appendChild(row);\r\n      }\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Update data items and re-render\r\n   * @param {Array} items New data items array\r\n   * @public\r\n   */\r\n  updateItems(items) {\r\n    this.items = items || [];\r\n    this.totalHeight = this.items.length * this.itemHeight;\r\n\r\n    // Recalculate scaling factor\r\n    this.heightScale = 1;\r\n    if (this.totalHeight > this.maxHeight) {\r\n      this.heightScale = this.maxHeight / this.totalHeight;\r\n    }\r\n\r\n    // Ensure height is set correctly\r\n    if (this.contentWrapper) {\r\n      this.contentWrapper.style.height = `${this.totalHeight * this.heightScale}px`;\r\n    }\r\n\r\n    this.visibleStartIndex = 0;\r\n    this.visibleEndIndex = 0;\r\n\r\n    // Force recalculation of visible items\r\n    this.handleScroll();\r\n  }\r\n\r\n  /**\r\n   * Scroll to specified index\r\n   * @param {number} index Index of the item to scroll to\r\n   * @public\r\n   */\r\n  scrollToIndex(index) {\r\n    if (index >= 0 && index < this.items.length) {\r\n      // Apply scaling factor when scrolling\r\n      this.scrollContainer.scrollTop = index * this.itemHeight * this.heightScale;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Destroy component, remove event listeners, etc.\r\n   * @public\r\n   */\r\n  destroy() {\r\n    if (this.scrollContainer) {\r\n      this.scrollContainer.removeEventListener('scroll', this.handleScroll);\r\n    }\r\n    if (this.container) {\r\n      this.container.innerHTML = '';\r\n    }\r\n    this.items = null;\r\n    this.container = null;\r\n    this.scrollContainer = null;\r\n    this.contentWrapper = null;\r\n    this.contentContainer = null;\r\n  }\r\n\r\n  /**\r\n   * Refresh virtual scroll, re-render current visible items\r\n   * @public\r\n   */\r\n  refresh() {\r\n    this.handleScroll();\r\n  }\r\n\r\n  /**\r\n   * Get scroll container element\r\n   * @returns {HTMLElement} Scroll container element\r\n   * @public\r\n   */\r\n  getScrollContainer() {\r\n    return this.scrollContainer;\r\n  }\r\n}\r\n\r\n// Export VirtualScroll class\r\nexport { VirtualScroll };\r\n", "/**\r\n * js-booster - High-performance frontend library\r\n * @version 1.1.1\r\n * <AUTHOR>\r\n * @license MIT\r\n */\r\n\r\nimport { VirtualScroll } from './virtual-scroll';\r\n\r\n// Export all components\r\nexport { VirtualScroll };\r\n\r\n// If in browser environment, add to global object\r\nif (typeof window !== 'undefined') {\r\n  window.JsBooster = {\r\n    VirtualScroll\r\n  };\r\n}\r\n", "import { defineComponent as _defineComponent } from 'vue'\nimport { createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock } from \"vue\"\n\nconst _hoisted_1 = { class: \"cases-list-container case-list\" }\n\nimport { ref, onMounted, onUnmounted, watch, nextTick } from 'vue';\nimport { VirtualScroll } from 'js-booster';\nimport { CaseResult, ExecutionState } from '@/api/interoperationApi';\n\nconst ITEM_HEIGHT = 36; // 行高\nconst BUFFER_SIZE = 20; // 缓冲区大小\nconst HEADER_HEIGHT = 40; // 表头高度\n\n// 渲染固定表头\n\nexport default /*@__PURE__*/_defineComponent({\n  __name: 'CaseList',\n  props: {\n    cases: {}\n  },\n  emits: [\"view-detail\"],\n  setup(__props: any, { emit: __emit }) {\n\nconst props = __props;\n\nconst emit = __emit;\n\n// 虚拟滚动相关\nconst casesContainer = ref<HTMLElement | null>(null);\nconst headerContainer = ref<HTMLElement | null>(null);\nlet virtualScroll: any = null;\nconst renderFixedHeader = () => {\n  if (!headerContainer.value) return;\n\n  // 清空表头容器\n  headerContainer.value.innerHTML = '';\n\n  // 创建表头\n  const header = document.createElement('div');\n  header.className = 'header-row';\n  header.style.height = `${HEADER_HEIGHT}px`; // 动态高度\n\n  // ID 列\n  const idHeader = document.createElement('div');\n  idHeader.textContent = 'ID';\n  idHeader.className = 'header-cell-id';\n  header.appendChild(idHeader);\n\n  // 名称列\n  const nameHeader = document.createElement('div');\n  nameHeader.textContent = 'Name';\n  nameHeader.className = 'header-cell-name';\n  header.appendChild(nameHeader);\n\n  // 参数列\n  const paramHeader = document.createElement('div');\n  paramHeader.textContent = 'Parameter';\n  paramHeader.className = 'header-cell-param';\n  header.appendChild(paramHeader);\n\n  // 详情列\n  const detailHeader = document.createElement('div');\n  detailHeader.textContent = 'Detail';\n  detailHeader.className = 'header-cell-detail';\n  header.appendChild(detailHeader);\n\n  // 状态列\n  const statusHeader = document.createElement('div');\n  statusHeader.textContent = 'Status';\n  statusHeader.className = 'header-cell-status';\n  header.appendChild(statusHeader);\n\n  // 添加到表头容器\n  headerContainer.value.appendChild(header);\n};\n\n// 初始化虚拟滚动\nconst initVirtualScroll = () => {\n  if (!casesContainer.value || !props.cases.length) return;\n\n  // 渲染固定表头\n  renderFixedHeader();\n\n  // 如果已经存在虚拟滚动实例，先销毁\n  if (virtualScroll) {\n    virtualScroll.destroy();\n  }\n\n  virtualScroll = new VirtualScroll({\n    container: casesContainer.value,\n    items: props.cases,\n    itemHeight: ITEM_HEIGHT,\n    bufferSize: BUFFER_SIZE,\n    renderItem: (item: CaseResult, index: number) => {\n      // 创建主容器\n      const div = document.createElement('div');\n      div.className = 'case-row';\n      div.onclick = () => emit('view-detail', item);\n\n      // 设置动态样式\n      div.style.height = `${ITEM_HEIGHT}px`;\n      div.style.lineHeight = `${ITEM_HEIGHT}px`;\n      div.style.borderBottom = index === props.cases.length - 1 ? 'none' : '1px solid #ebeef5';\n      div.style.backgroundColor = item.id % 2 === 0 ? '#fff' : '#fafafa';\n\n      // 添加悬停效果\n      div.onmouseover = () => {\n        div.style.backgroundColor = '#f5f7fa';\n      };\n      div.onmouseout = () => {\n        div.style.backgroundColor = item.id % 2 === 0 ? '#fff' : '#fafafa';\n        // 确保鼠标移出时保持最后一个项目没有底部边框\n        div.style.borderBottom = index === props.cases.length - 1 ? 'none' : '1px solid #ebeef5';\n      };\n\n      // 创建行内容\n      const rowDiv = document.createElement('div');\n      rowDiv.className = 'case-row-content';\n\n      // ID\n      const idDiv = document.createElement('div');\n      idDiv.textContent = `#${item.id}`;\n      idDiv.className = 'case-cell-id';\n      rowDiv.appendChild(idDiv);\n\n      // 名称\n      const nameDiv = document.createElement('div');\n      nameDiv.textContent = item.name;\n      nameDiv.className = 'case-cell-name';\n      rowDiv.appendChild(nameDiv);\n\n      // 参数\n      const paramDiv = document.createElement('div');\n      paramDiv.textContent = item.parameter;\n      paramDiv.title = item.parameter;\n      paramDiv.className = 'case-cell-param';\n      rowDiv.appendChild(paramDiv);\n\n      // 详情列\n      const detailDiv = document.createElement('div');\n      detailDiv.textContent = item.detail || '-';\n      detailDiv.title = item.detail || '';\n      detailDiv.className = 'case-cell-detail';\n      rowDiv.appendChild(detailDiv);\n\n      // 状态\n      const statusDiv = document.createElement('div');\n      statusDiv.className = 'case-cell-status';\n\n      // 创建状态标签\n      const tagType = getStatusTagType(item.state);\n      const tagText = getStatusText(item.state);\n\n      const tagEl = document.createElement('span');\n      tagEl.className = `el-tag el-tag--${tagType} el-tag--small case-status-tag`;\n      tagEl.textContent = tagText;\n\n      statusDiv.appendChild(tagEl);\n      rowDiv.appendChild(statusDiv);\n\n      div.appendChild(rowDiv);\n\n      return div;\n    }\n  });\n};\n\n// 更新虚拟滚动数据\nconst updateVirtualScroll = () => {\n  if (virtualScroll) {\n    virtualScroll.updateItems(props.cases);\n  } else {\n    nextTick(() => {\n      initVirtualScroll();\n    });\n  }\n};\n\n// 获取状态对应的标签类型\nconst getStatusTagType = (state: string): string => {\n  switch (state) {\n    case ExecutionState.Success:\n      return 'success';\n    case ExecutionState.Running:\n      return 'warning';\n    case ExecutionState.Failure:\n      return 'danger';\n    case ExecutionState.Pending:\n    default:\n      return 'info';\n  }\n};\n\n// 获取状态的文本描述\nconst getStatusText = (state: string): string => {\n  switch (state) {\n    case ExecutionState.Success:\n      return 'Passed';\n    case ExecutionState.Failure:\n      return 'Failed';\n    case ExecutionState.Running:\n      return 'Running';\n    case ExecutionState.Pending:\n      return 'Pending';\n    default:\n      return 'Unknown';\n  }\n};\n\n// 监听 cases 变化，更新虚拟滚动\nwatch(() => props.cases, () => {\n  nextTick(() => {\n    updateVirtualScroll();\n  });\n}, { deep: true });\n\n// 组件挂载时初始化虚拟滚动\nonMounted(() => {\n  nextTick(() => {\n    initVirtualScroll();\n  });\n});\n\n// 组件卸载时销毁虚拟滚动\nonUnmounted(() => {\n  if (virtualScroll) {\n    virtualScroll.destroy();\n    virtualScroll = null;\n  }\n});\n\nreturn (_ctx: any,_cache: any) => {\n  return (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [\n    _createElementVNode(\"div\", {\n      class: \"cases-header\",\n      ref_key: \"headerContainer\",\n      ref: headerContainer\n    }, null, 512),\n    _createElementVNode(\"div\", {\n      ref_key: \"casesContainer\",\n      ref: casesContainer,\n      class: \"cases-content\"\n    }, null, 512)\n  ]))\n}\n}\n\n})", "<template>\n  <div class=\"cases-list-container case-list\">\n    <!-- 固定表头 -->\n    <div class=\"cases-header\" ref=\"headerContainer\"></div>\n    <!-- 虚拟滚动内容容器 -->\n    <div ref=\"casesContainer\" class=\"cases-content\"></div>\n  </div>\n</template>\n\n<script setup lang=\"ts\">\nimport { ref, onMounted, onUnmounted, watch, nextTick, defineProps, defineEmits } from 'vue';\nimport { VirtualScroll } from 'js-booster';\nimport { CaseResult, ExecutionState } from '@/api/interoperationApi';\n\nconst props = defineProps<{\n  cases: CaseResult[];\n}>();\n\nconst emit = defineEmits<{\n  (e: 'view-detail', caseResult: CaseResult): void;\n}>();\n\n// 虚拟滚动相关\nconst casesContainer = ref<HTMLElement | null>(null);\nconst headerContainer = ref<HTMLElement | null>(null);\nlet virtualScroll: any = null;\nconst ITEM_HEIGHT = 36; // 行高\nconst BUFFER_SIZE = 20; // 缓冲区大小\nconst HEADER_HEIGHT = 40; // 表头高度\n\n// 渲染固定表头\nconst renderFixedHeader = () => {\n  if (!headerContainer.value) return;\n\n  // 清空表头容器\n  headerContainer.value.innerHTML = '';\n\n  // 创建表头\n  const header = document.createElement('div');\n  header.className = 'header-row';\n  header.style.height = `${HEADER_HEIGHT}px`; // 动态高度\n\n  // ID 列\n  const idHeader = document.createElement('div');\n  idHeader.textContent = 'ID';\n  idHeader.className = 'header-cell-id';\n  header.appendChild(idHeader);\n\n  // 名称列\n  const nameHeader = document.createElement('div');\n  nameHeader.textContent = 'Name';\n  nameHeader.className = 'header-cell-name';\n  header.appendChild(nameHeader);\n\n  // 参数列\n  const paramHeader = document.createElement('div');\n  paramHeader.textContent = 'Parameter';\n  paramHeader.className = 'header-cell-param';\n  header.appendChild(paramHeader);\n\n  // 详情列\n  const detailHeader = document.createElement('div');\n  detailHeader.textContent = 'Detail';\n  detailHeader.className = 'header-cell-detail';\n  header.appendChild(detailHeader);\n\n  // 状态列\n  const statusHeader = document.createElement('div');\n  statusHeader.textContent = 'Status';\n  statusHeader.className = 'header-cell-status';\n  header.appendChild(statusHeader);\n\n  // 添加到表头容器\n  headerContainer.value.appendChild(header);\n};\n\n// 初始化虚拟滚动\nconst initVirtualScroll = () => {\n  if (!casesContainer.value || !props.cases.length) return;\n\n  // 渲染固定表头\n  renderFixedHeader();\n\n  // 如果已经存在虚拟滚动实例，先销毁\n  if (virtualScroll) {\n    virtualScroll.destroy();\n  }\n\n  virtualScroll = new VirtualScroll({\n    container: casesContainer.value,\n    items: props.cases,\n    itemHeight: ITEM_HEIGHT,\n    bufferSize: BUFFER_SIZE,\n    renderItem: (item: CaseResult, index: number) => {\n      // 创建主容器\n      const div = document.createElement('div');\n      div.className = 'case-row';\n      div.onclick = () => emit('view-detail', item);\n\n      // 设置动态样式\n      div.style.height = `${ITEM_HEIGHT}px`;\n      div.style.lineHeight = `${ITEM_HEIGHT}px`;\n      div.style.borderBottom = index === props.cases.length - 1 ? 'none' : '1px solid #ebeef5';\n      div.style.backgroundColor = item.id % 2 === 0 ? '#fff' : '#fafafa';\n\n      // 添加悬停效果\n      div.onmouseover = () => {\n        div.style.backgroundColor = '#f5f7fa';\n      };\n      div.onmouseout = () => {\n        div.style.backgroundColor = item.id % 2 === 0 ? '#fff' : '#fafafa';\n        // 确保鼠标移出时保持最后一个项目没有底部边框\n        div.style.borderBottom = index === props.cases.length - 1 ? 'none' : '1px solid #ebeef5';\n      };\n\n      // 创建行内容\n      const rowDiv = document.createElement('div');\n      rowDiv.className = 'case-row-content';\n\n      // ID\n      const idDiv = document.createElement('div');\n      idDiv.textContent = `#${item.id}`;\n      idDiv.className = 'case-cell-id';\n      rowDiv.appendChild(idDiv);\n\n      // 名称\n      const nameDiv = document.createElement('div');\n      nameDiv.textContent = item.name;\n      nameDiv.className = 'case-cell-name';\n      rowDiv.appendChild(nameDiv);\n\n      // 参数\n      const paramDiv = document.createElement('div');\n      paramDiv.textContent = item.parameter;\n      paramDiv.title = item.parameter;\n      paramDiv.className = 'case-cell-param';\n      rowDiv.appendChild(paramDiv);\n\n      // 详情列\n      const detailDiv = document.createElement('div');\n      detailDiv.textContent = item.detail || '-';\n      detailDiv.title = item.detail || '';\n      detailDiv.className = 'case-cell-detail';\n      rowDiv.appendChild(detailDiv);\n\n      // 状态\n      const statusDiv = document.createElement('div');\n      statusDiv.className = 'case-cell-status';\n\n      // 创建状态标签\n      const tagType = getStatusTagType(item.state);\n      const tagText = getStatusText(item.state);\n\n      const tagEl = document.createElement('span');\n      tagEl.className = `el-tag el-tag--${tagType} el-tag--small case-status-tag`;\n      tagEl.textContent = tagText;\n\n      statusDiv.appendChild(tagEl);\n      rowDiv.appendChild(statusDiv);\n\n      div.appendChild(rowDiv);\n\n      return div;\n    }\n  });\n};\n\n// 更新虚拟滚动数据\nconst updateVirtualScroll = () => {\n  if (virtualScroll) {\n    virtualScroll.updateItems(props.cases);\n  } else {\n    nextTick(() => {\n      initVirtualScroll();\n    });\n  }\n};\n\n// 获取状态对应的标签类型\nconst getStatusTagType = (state: string): string => {\n  switch (state) {\n    case ExecutionState.Success:\n      return 'success';\n    case ExecutionState.Running:\n      return 'warning';\n    case ExecutionState.Failure:\n      return 'danger';\n    case ExecutionState.Pending:\n    default:\n      return 'info';\n  }\n};\n\n// 获取状态的文本描述\nconst getStatusText = (state: string): string => {\n  switch (state) {\n    case ExecutionState.Success:\n      return 'Passed';\n    case ExecutionState.Failure:\n      return 'Failed';\n    case ExecutionState.Running:\n      return 'Running';\n    case ExecutionState.Pending:\n      return 'Pending';\n    default:\n      return 'Unknown';\n  }\n};\n\n// 监听 cases 变化，更新虚拟滚动\nwatch(() => props.cases, () => {\n  nextTick(() => {\n    updateVirtualScroll();\n  });\n}, { deep: true });\n\n// 组件挂载时初始化虚拟滚动\nonMounted(() => {\n  nextTick(() => {\n    initVirtualScroll();\n  });\n});\n\n// 组件卸载时销毁虚拟滚动\nonUnmounted(() => {\n  if (virtualScroll) {\n    virtualScroll.destroy();\n    virtualScroll = null;\n  }\n});\n</script>\n\n<style scoped lang=\"scss\">\n.cases-list-container {\n  height: 100%;\n  display: flex;\n  flex-direction: column;\n  position: relative;\n  font-size: 13px;\n}\n\n.cases-header {\n  position: sticky;\n  top: 0;\n  z-index: 10;\n  background-color: #f5f7fa;\n}\n\n.cases-content {\n  flex: 1;\n  overflow: auto;\n  position: relative;\n}\n</style>\n", "import script from \"./CaseList.vue?vue&type=script&setup=true&lang=ts\"\nexport * from \"./CaseList.vue?vue&type=script&setup=true&lang=ts\"\n\nimport \"./CaseList.vue?vue&type=style&index=0&id=207f960e&scoped=true&lang=scss\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-207f960e\"]])\n\nexport default __exports__", "import { ExecutionState } from '@/api/appApi';\r\n\r\n/**\r\n * 获取执行状态对应的时间线项类型\r\n * @param state 执行状态\r\n * @returns 时间线项类型\r\n */\r\nexport const getTimelineItemType = (state: string): 'primary' | 'success' | 'warning' | 'danger' | 'info' => {\r\n  switch (state) {\r\n    case ExecutionState.Success:\r\n      return 'success';\r\n    case ExecutionState.Running:\r\n      return 'primary';\r\n    case ExecutionState.Failure:\r\n      return 'danger';\r\n    case ExecutionState.Pending:\r\n    default:\r\n      return 'info';\r\n  }\r\n};", "import { defineComponent as _defineComponent } from 'vue'\nimport { resolveComponent as _resolveComponent, createVNode as _createVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode, createElementVNode as _createElementVNode, toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, renderList as _renderList, Fragment as _Fragment, unref as _unref, withCtx as _withCtx, createBlock as _createBlock } from \"vue\"\n\nconst _hoisted_1 = {\n  key: 0,\n  class: \"loading\"\n}\nconst _hoisted_2 = {\n  key: 1,\n  class: \"case-detail-content\"\n}\nconst _hoisted_3 = { class: \"basic-info\" }\nconst _hoisted_4 = { class: \"info-grid\" }\nconst _hoisted_5 = { class: \"info-item\" }\nconst _hoisted_6 = { class: \"value\" }\nconst _hoisted_7 = { class: \"info-item\" }\nconst _hoisted_8 = { class: \"value\" }\nconst _hoisted_9 = { class: \"info-item\" }\nconst _hoisted_10 = { class: \"value\" }\nconst _hoisted_11 = { class: \"info-item\" }\nconst _hoisted_12 = { class: \"value status-combined\" }\nconst _hoisted_13 = {\n  key: 0,\n  class: \"info-item full-width\"\n}\nconst _hoisted_14 = [\"title\"]\nconst _hoisted_15 = {\n  key: 1,\n  class: \"info-item full-width\"\n}\nconst _hoisted_16 = [\"title\"]\nconst _hoisted_17 = { class: \"steps-section\" }\nconst _hoisted_18 = {\n  key: 0,\n  class: \"no-steps\"\n}\nconst _hoisted_19 = { class: \"step-content\" }\nconst _hoisted_20 = { class: \"step-row\" }\nconst _hoisted_21 = { class: \"step-left\" }\nconst _hoisted_22 = { class: \"step-timestamp\" }\nconst _hoisted_23 = [\"title\"]\nconst _hoisted_24 = [\"title\"]\nconst _hoisted_25 = { class: \"step-right\" }\nconst _hoisted_26 = { class: \"dialog-footer\" }\n\nimport { ref, watch, onMounted } from 'vue';\r\nimport { ElMessage } from 'element-plus';\r\nimport { CaseResult } from '@/api/interoperationApi';\r\nimport { appApi, CaseStep } from '@/api/appApi';\r\nimport { caseApi } from '@/api/caseApi';\r\nimport { getTimelineItemType } from '@/utils/status';\r\nimport CaseStateTag from '@/components/common/CaseStateTag.vue';\r\n\r\n\nexport default /*@__PURE__*/_defineComponent({\n  __name: 'CaseDetailDialog',\n  props: {\n    visible: { type: Boolean },\n    testResultId: {},\n    caseResultId: {}\n  },\n  emits: [\"update:visible\", \"close\"],\n  setup(__props: any, { emit: __emit }) {\n\r\nconst props = __props;\r\n\r\nconst emit = __emit;\r\n\r\nconst dialogVisible = ref(props.visible);\r\nconst caseData = ref<CaseResult | null>(null);\r\nconst steps = ref<CaseStep[]>([]);\r\nconst loading = ref(false);\r\n\r\nwatch(() => props.visible, (newValue) => {\r\n  dialogVisible.value = newValue;\r\n  if (newValue && props.testResultId && props.caseResultId) {\r\n    loadCaseData();\r\n  }\r\n});\r\n\r\nwatch(() => dialogVisible.value, (newValue) => {\r\n  emit('update:visible', newValue);\r\n  if (!newValue) emit('close');\r\n});\r\n\r\nconst loadCaseData = async () => {\r\n  if (!props.testResultId || !props.caseResultId) {\r\n    ElMessage.warning('Missing required parameters');\r\n    return;\r\n  }\r\n\r\n  loading.value = true;\r\n  try {\r\n    // 并行加载用例数据和步骤数据\r\n    const [caseResponse, stepsResponse] = await Promise.all([\r\n      appApi.getCaseResult(props.testResultId, props.caseResultId),\r\n      appApi.getCaseSteps(props.testResultId, props.caseResultId)\r\n    ]);\r\n\r\n    caseData.value = caseResponse.data;\r\n    steps.value = stepsResponse.data;\r\n  } catch (error) {\r\n    console.error('Failed to load case data:', error);\r\n    ElMessage.error('Failed to load case details');\r\n  } finally {\r\n    loading.value = false;\r\n  }\r\n};\r\n\r\nconst closeDialog = () => {\r\n  dialogVisible.value = false;\r\n  // 清空数据\r\n  caseData.value = null;\r\n  steps.value = [];\r\n};\r\n\r\n// 格式化方法保持不变\r\nconst formatDateTime = (dateString?: string | null) => {\r\n  if (!dateString) return 'N/A';\r\n  try {\r\n    const date = new Date(dateString);\r\n    return date.toLocaleString();\r\n  } catch (e) {\r\n    return dateString;\r\n  }\r\n};\r\n\r\nconst formatTime = (dateString: string) => {\r\n  try {\r\n    const date = new Date(dateString);\r\n    return date.toLocaleTimeString();\r\n  } catch (e) {\r\n    return dateString;\r\n  }\r\n};\r\n\r\nconst formatTimestamp = (timestamp: number) => {\r\n  try {\r\n    const date = new Date(timestamp);\r\n    return date.toLocaleString();\r\n  } catch (e) {\r\n    return timestamp.toString();\r\n  }\r\n};\r\n\r\n// 添加新的微秒格式化方法\r\nconst formatMicroseconds = (microseconds: number): string => {\r\n  if (!microseconds && microseconds !== 0) return 'N/A';\r\n\r\n  // 转换为秒并保留6位小数\r\n  const seconds = microseconds / 1000000;\r\n  // 使用toFixed(6)确保始终有6位小数\r\n  return `${seconds.toFixed(6)}`;\r\n};\r\n\r\n// 组件挂载时，如果对话框是可见的且有必要参数，则加载数据\r\nonMounted(() => {\r\n  if (dialogVisible.value && props.testResultId && props.caseResultId) {\r\n    loadCaseData();\r\n  }\r\n});\r\n\nreturn (_ctx: any,_cache: any) => {\n  const _component_el_skeleton = _resolveComponent(\"el-skeleton\")!\n  const _component_el_empty = _resolveComponent(\"el-empty\")!\n  const _component_el_timeline_item = _resolveComponent(\"el-timeline-item\")!\n  const _component_el_timeline = _resolveComponent(\"el-timeline\")!\n  const _component_el_button = _resolveComponent(\"el-button\")!\n  const _component_el_dialog = _resolveComponent(\"el-dialog\")!\n\n  return (_openBlock(), _createBlock(_component_el_dialog, {\n    modelValue: dialogVisible.value,\n    \"onUpdate:modelValue\": _cache[0] || (_cache[0] = ($event: any) => ((dialogVisible).value = $event)),\n    title: `${caseData.value?.name || caseData.value?.sequenceName || ''}`,\n    width: \"60%\",\n    \"destroy-on-close\": \"\"\n  }, {\n    footer: _withCtx(() => [\n      _createElementVNode(\"span\", _hoisted_26, [\n        _createVNode(_component_el_button, { onClick: closeDialog }, {\n          default: _withCtx(() => _cache[9] || (_cache[9] = [\n            _createTextVNode(\"Close\")\n          ])),\n          _: 1\n        })\n      ])\n    ]),\n    default: _withCtx(() => [\n      (loading.value)\n        ? (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [\n            _createVNode(_component_el_skeleton, {\n              rows: 10,\n              animated: \"\"\n            })\n          ]))\n        : (_openBlock(), _createElementBlock(\"div\", _hoisted_2, [\n            _createElementVNode(\"div\", _hoisted_3, [\n              _cache[7] || (_cache[7] = _createElementVNode(\"h4\", null, \"Information\", -1)),\n              _createElementVNode(\"div\", _hoisted_4, [\n                _createElementVNode(\"div\", _hoisted_5, [\n                  _cache[1] || (_cache[1] = _createElementVNode(\"div\", { class: \"label\" }, \"Case Name:\", -1)),\n                  _createElementVNode(\"div\", _hoisted_6, _toDisplayString(caseData.value?.name), 1)\n                ]),\n                _createElementVNode(\"div\", _hoisted_7, [\n                  _cache[2] || (_cache[2] = _createElementVNode(\"div\", { class: \"label\" }, \"Sequence Name:\", -1)),\n                  _createElementVNode(\"div\", _hoisted_8, _toDisplayString(caseData.value?.sequenceName), 1)\n                ]),\n                _createElementVNode(\"div\", _hoisted_9, [\n                  _cache[3] || (_cache[3] = _createElementVNode(\"div\", { class: \"label\" }, \"Start Time:\", -1)),\n                  _createElementVNode(\"div\", _hoisted_10, _toDisplayString(formatDateTime(caseData.value?.begin)), 1)\n                ]),\n                _createElementVNode(\"div\", _hoisted_11, [\n                  _cache[4] || (_cache[4] = _createElementVNode(\"div\", { class: \"label\" }, \"End Time / Status:\", -1)),\n                  _createElementVNode(\"div\", _hoisted_12, [\n                    _createTextVNode(_toDisplayString(formatDateTime(caseData.value?.end)) + \" \", 1),\n                    _createVNode(CaseStateTag, {\n                      state: caseData.value?.state || '',\n                      class: \"status-tag\"\n                    }, null, 8, [\"state\"])\n                  ])\n                ]),\n                (caseData.value?.parameter)\n                  ? (_openBlock(), _createElementBlock(\"div\", _hoisted_13, [\n                      _cache[5] || (_cache[5] = _createElementVNode(\"div\", { class: \"label\" }, \"Parameter:\", -1)),\n                      _createElementVNode(\"div\", {\n                        class: \"value\",\n                        title: caseData.value?.parameter\n                      }, _toDisplayString(caseData.value?.parameter), 9, _hoisted_14)\n                    ]))\n                  : _createCommentVNode(\"\", true),\n                (caseData.value?.detail)\n                  ? (_openBlock(), _createElementBlock(\"div\", _hoisted_15, [\n                      _cache[6] || (_cache[6] = _createElementVNode(\"div\", { class: \"label\" }, \"Detail:\", -1)),\n                      _createElementVNode(\"div\", {\n                        class: \"value\",\n                        title: caseData.value?.detail\n                      }, _toDisplayString(caseData.value.detail), 9, _hoisted_16)\n                    ]))\n                  : _createCommentVNode(\"\", true)\n              ])\n            ]),\n            _createElementVNode(\"div\", _hoisted_17, [\n              _cache[8] || (_cache[8] = _createElementVNode(\"h4\", null, \"Steps\", -1)),\n              (steps.value.length === 0)\n                ? (_openBlock(), _createElementBlock(\"div\", _hoisted_18, [\n                    _createVNode(_component_el_empty, { description: \"No steps available\" })\n                  ]))\n                : (_openBlock(), _createBlock(_component_el_timeline, { key: 1 }, {\n                    default: _withCtx(() => [\n                      (_openBlock(true), _createElementBlock(_Fragment, null, _renderList(steps.value, (step) => {\n                        return (_openBlock(), _createBlock(_component_el_timeline_item, {\n                          key: step.id,\n                          type: _unref(getTimelineItemType)(step.state),\n                          hollow: step.state !== 'Success'\n                        }, {\n                          default: _withCtx(() => [\n                            _createElementVNode(\"div\", _hoisted_19, [\n                              _createElementVNode(\"div\", _hoisted_20, [\n                                _createElementVNode(\"div\", _hoisted_21, [\n                                  _createElementVNode(\"span\", _hoisted_22, _toDisplayString(formatMicroseconds(step.timestamp)), 1),\n                                  _createElementVNode(\"span\", {\n                                    class: \"step-name\",\n                                    title: step.name\n                                  }, _toDisplayString(step.name), 9, _hoisted_23),\n                                  (step.detail)\n                                    ? (_openBlock(), _createElementBlock(\"span\", {\n                                        key: 0,\n                                        title: step.detail,\n                                        class: \"step-detail-inline\"\n                                      }, _toDisplayString(step.detail), 9, _hoisted_24))\n                                    : _createCommentVNode(\"\", true)\n                                ]),\n                                _createElementVNode(\"div\", _hoisted_25, [\n                                  _createVNode(CaseStateTag, {\n                                    state: step.state\n                                  }, null, 8, [\"state\"])\n                                ])\n                              ])\n                            ])\n                          ]),\n                          _: 2\n                        }, 1032, [\"type\", \"hollow\"]))\n                      }), 128))\n                    ]),\n                    _: 1\n                  }))\n            ])\n          ]))\n    ]),\n    _: 1\n  }, 8, [\"modelValue\", \"title\"]))\n}\n}\n\n})", "<template>\r\n  <el-dialog v-model=\"dialogVisible\" :title=\"`${caseData?.name || caseData?.sequenceName || ''}`\" width=\"60%\"\r\n    destroy-on-close>\r\n    <div v-if=\"loading\" class=\"loading\">\r\n      <el-skeleton :rows=\"10\" animated />\r\n    </div>\r\n\r\n    <div v-else class=\"case-detail-content\">\r\n      <!-- 基本信息区域 -->\r\n      <div class=\"basic-info\">\r\n        <h4>Information</h4>\r\n        <div class=\"info-grid\">\r\n          <div class=\"info-item\">\r\n            <div class=\"label\">Case Name:</div>\r\n            <div class=\"value\">{{ caseData?.name }}</div>\r\n          </div>\r\n          <div class=\"info-item\">\r\n            <div class=\"label\">Sequence Name:</div>\r\n            <div class=\"value\">{{ caseData?.sequenceName }}</div>\r\n          </div>\r\n          <div class=\"info-item\">\r\n            <div class=\"label\">Start Time:</div>\r\n            <div class=\"value\">{{ formatDateTime(caseData?.begin) }}</div>\r\n          </div>\r\n          <div class=\"info-item\">\r\n            <div class=\"label\">End Time / Status:</div>\r\n            <div class=\"value status-combined\">\r\n              {{ formatDateTime(caseData?.end) }}\r\n              <CaseStateTag :state=\"caseData?.state || ''\" class=\"status-tag\" />\r\n            </div>\r\n          </div>\r\n          <div v-if=\"caseData?.parameter\" class=\"info-item full-width\">\r\n            <div class=\"label\">Parameter:</div>\r\n            <div class=\"value\" :title=\"caseData?.parameter\">{{ caseData?.parameter }}</div>\r\n          </div>\r\n          <div v-if=\"caseData?.detail\" class=\"info-item full-width\">\r\n            <div class=\"label\">Detail:</div>\r\n            <div class=\"value\" :title=\"caseData?.detail\">{{ caseData.detail }}</div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      <!-- 步骤列表区域 -->\r\n      <div class=\"steps-section\">\r\n        <h4>Steps</h4>\r\n\r\n        <div v-if=\"steps.length === 0\" class=\"no-steps\">\r\n          <el-empty description=\"No steps available\" />\r\n        </div>\r\n\r\n        <el-timeline v-else>\r\n          <el-timeline-item v-for=\"step in steps\" :key=\"step.id\" :type=\"getTimelineItemType(step.state)\"\r\n            :hollow=\"step.state !== 'Success'\">\r\n            <div class=\"step-content\">\r\n              <div class=\"step-row\">\r\n                <div class=\"step-left\">\r\n                  <span class=\"step-timestamp\">{{ formatMicroseconds(step.timestamp) }}</span>\r\n                  <span class=\"step-name\" :title=\"step.name\" >{{ step.name }}</span>\r\n                  <span v-if=\"step.detail\" :title=\"step.detail\" class=\"step-detail-inline\">{{ step.detail }}</span>\r\n                </div>\r\n                <div class=\"step-right\">\r\n                  <CaseStateTag :state=\"step.state\" />\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </el-timeline-item>\r\n        </el-timeline>\r\n      </div>\r\n    </div>\r\n\r\n    <template #footer>\r\n      <span class=\"dialog-footer\">\r\n        <el-button @click=\"closeDialog\">Close</el-button>\r\n      </span>\r\n    </template>\r\n  </el-dialog>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { ref, watch, defineProps, defineEmits, onMounted } from 'vue';\r\nimport { ElMessage } from 'element-plus';\r\nimport { CaseResult } from '@/api/interoperationApi';\r\nimport { appApi, CaseStep } from '@/api/appApi';\r\nimport { caseApi } from '@/api/caseApi';\r\nimport { getTimelineItemType } from '@/utils/status';\r\nimport CaseStateTag from '@/components/common/CaseStateTag.vue';\r\n\r\nconst props = defineProps<{\r\n  visible: boolean;\r\n  testResultId?: string | null;\r\n  caseResultId?: number | null;\r\n}>();\r\n\r\nconst emit = defineEmits<{\r\n  (e: 'update:visible', value: boolean): void;\r\n  (e: 'close'): void;\r\n}>();\r\n\r\nconst dialogVisible = ref(props.visible);\r\nconst caseData = ref<CaseResult | null>(null);\r\nconst steps = ref<CaseStep[]>([]);\r\nconst loading = ref(false);\r\n\r\nwatch(() => props.visible, (newValue) => {\r\n  dialogVisible.value = newValue;\r\n  if (newValue && props.testResultId && props.caseResultId) {\r\n    loadCaseData();\r\n  }\r\n});\r\n\r\nwatch(() => dialogVisible.value, (newValue) => {\r\n  emit('update:visible', newValue);\r\n  if (!newValue) emit('close');\r\n});\r\n\r\nconst loadCaseData = async () => {\r\n  if (!props.testResultId || !props.caseResultId) {\r\n    ElMessage.warning('Missing required parameters');\r\n    return;\r\n  }\r\n\r\n  loading.value = true;\r\n  try {\r\n    // 并行加载用例数据和步骤数据\r\n    const [caseResponse, stepsResponse] = await Promise.all([\r\n      appApi.getCaseResult(props.testResultId, props.caseResultId),\r\n      appApi.getCaseSteps(props.testResultId, props.caseResultId)\r\n    ]);\r\n\r\n    caseData.value = caseResponse.data;\r\n    steps.value = stepsResponse.data;\r\n  } catch (error) {\r\n    console.error('Failed to load case data:', error);\r\n    ElMessage.error('Failed to load case details');\r\n  } finally {\r\n    loading.value = false;\r\n  }\r\n};\r\n\r\nconst closeDialog = () => {\r\n  dialogVisible.value = false;\r\n  // 清空数据\r\n  caseData.value = null;\r\n  steps.value = [];\r\n};\r\n\r\n// 格式化方法保持不变\r\nconst formatDateTime = (dateString?: string | null) => {\r\n  if (!dateString) return 'N/A';\r\n  try {\r\n    const date = new Date(dateString);\r\n    return date.toLocaleString();\r\n  } catch (e) {\r\n    return dateString;\r\n  }\r\n};\r\n\r\nconst formatTime = (dateString: string) => {\r\n  try {\r\n    const date = new Date(dateString);\r\n    return date.toLocaleTimeString();\r\n  } catch (e) {\r\n    return dateString;\r\n  }\r\n};\r\n\r\nconst formatTimestamp = (timestamp: number) => {\r\n  try {\r\n    const date = new Date(timestamp);\r\n    return date.toLocaleString();\r\n  } catch (e) {\r\n    return timestamp.toString();\r\n  }\r\n};\r\n\r\n// 添加新的微秒格式化方法\r\nconst formatMicroseconds = (microseconds: number): string => {\r\n  if (!microseconds && microseconds !== 0) return 'N/A';\r\n\r\n  // 转换为秒并保留6位小数\r\n  const seconds = microseconds / 1000000;\r\n  // 使用toFixed(6)确保始终有6位小数\r\n  return `${seconds.toFixed(6)}`;\r\n};\r\n\r\n// 组件挂载时，如果对话框是可见的且有必要参数，则加载数据\r\nonMounted(() => {\r\n  if (dialogVisible.value && props.testResultId && props.caseResultId) {\r\n    loadCaseData();\r\n  }\r\n});\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.loading {\r\n  min-height: 200px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n}\r\n\r\n.case-detail-content {\r\n  padding: 0 20px;\r\n}\r\n\r\n.basic-info {\r\n  margin-bottom: 20px;\r\n\r\n  h4 {\r\n    margin-top: 0;\r\n    margin-bottom: 16px;\r\n    font-size: 16px;\r\n    color: #303133;\r\n    border-bottom: 1px solid #ebeef5;\r\n    padding-bottom: 8px;\r\n  }\r\n\r\n  .info-grid {\r\n    display: grid;\r\n    grid-template-columns: 1fr 1fr;\r\n    gap: 12px 24px;\r\n\r\n    .info-item {\r\n      .label {\r\n        font-size: 13px;\r\n        color: #909399;\r\n        margin-bottom: 4px;\r\n      }\r\n\r\n      .value {\r\n        font-size: 14px;\r\n        color: #303133;\r\n        word-break: break-word;\r\n\r\n        &.status-combined {\r\n          display: flex;\r\n          align-items: center;\r\n          \r\n          .status-tag {\r\n            margin-left: 12px;\r\n          }\r\n        }\r\n      }\r\n\r\n      &.full-width {\r\n        grid-column: span 2;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.steps-section {\r\n  h4 {\r\n    margin-top: 20px;\r\n    margin-bottom: 16px;\r\n    font-size: 16px;\r\n    color: #303133;\r\n    border-bottom: 1px solid #ebeef5;\r\n    padding-bottom: 8px;\r\n  }\r\n\r\n  .no-steps {\r\n    padding: 20px 0;\r\n  }\r\n\r\n  .step-content {\r\n    font-size: 13px;\r\n\r\n    .step-row {\r\n      display: flex;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n      width: 100%;\r\n      padding: 8px 4px;\r\n      transition: background-color 0.2s;\r\n    }\r\n\r\n    .step-left {\r\n      display: flex;\r\n      align-items: center;\r\n      flex: 1;\r\n      min-width: 0; // 防止flex子元素溢出\r\n    }\r\n\r\n    .step-right {\r\n      margin-left: 10px;\r\n      flex-shrink: 0;\r\n    }\r\n\r\n    .step-timestamp {\r\n      min-width: 80px;\r\n      display: inline-block;\r\n      color: #606266;\r\n      margin-right: 12px;\r\n      flex-shrink: 0;\r\n    }\r\n\r\n    .step-name {\r\n      min-width: 80px;\r\n      font-weight: 500;\r\n      display: inline-block;\r\n      color: #303133;\r\n      margin-right: 8px;\r\n      overflow: hidden;\r\n      text-overflow: ellipsis;\r\n      white-space: nowrap;\r\n      max-width: 200px; /* 限制最大宽度，可根据实际情况调整 */\r\n    }\r\n\r\n    .step-detail-inline {\r\n      color: #606266;\r\n      overflow: hidden;\r\n      text-overflow: ellipsis;\r\n      white-space: nowrap;\r\n    }\r\n  }\r\n\r\n  .el-timeline-item:nth-child(odd) .step-row {\r\n    background-color: #f5f5fa;\r\n  }\r\n\r\n  .el-timeline-item:nth-child(even) .step-row {\r\n    background-color: #f5f5fa;\r\n  }\r\n\r\n  :deep(.el-timeline-item__node) {\r\n    margin-top: 10px;\r\n  }\r\n\r\n  :deep(.el-timeline-item__tail) {\r\n    margin-top: 10px;\r\n  }\r\n}\r\n</style>\r\n", "import script from \"./CaseDetailDialog.vue?vue&type=script&setup=true&lang=ts\"\nexport * from \"./CaseDetailDialog.vue?vue&type=script&setup=true&lang=ts\"\n\nimport \"./CaseDetailDialog.vue?vue&type=style&index=0&id=a931aa76&scoped=true&lang=scss\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-a931aa76\"]])\n\nexport default __exports__"], "names": ["_defineComponent", "__name", "props", "state", "setup", "__props", "tagType", "computed", "ExecutionState", "Success", "Running", "Failure", "Pending", "getCaseStateName", "stateName", "_ctx", "_cache", "_component_el_tag", "_resolveComponent", "_openBlock", "_createBlock", "type", "value", "size", "style", "default", "_withCtx", "_createTextVNode", "_toDisplayString", "_", "__exports__", "VirtualScroll", "constructor", "options", "this", "container", "items", "itemHeight", "bufferSize", "customRenderItem", "renderItem", "customRenderHeader", "renderHeader", "maxHeight", "visibleStartIndex", "visibleEndIndex", "scrollContainer", "contentWrapper", "contentContainer", "totalHeight", "length", "heightScale", "initialize", "innerHTML", "document", "createElement", "Object", "assign", "flex", "overflow", "position", "minHeight", "height", "boxSizing", "header", "append<PERSON><PERSON><PERSON>", "width", "scaledHeight", "left", "addEventListener", "handleScroll", "bind", "renderVisibleItems", "Math", "min", "scrollTop", "containerHeight", "clientHeight", "realScrollTop", "startIndex", "max", "floor", "endIndex", "ceil", "transform", "i", "item", "itemElement", "row", "padding", "borderBottom", "textContent", "JSON", "stringify", "updateItems", "scrollToIndex", "index", "destroy", "removeEventListener", "refresh", "getScrollContainer", "window", "JsBooster", "_hoisted_1", "class", "ITEM_HEIGHT", "BUFFER_SIZE", "HEADER_HEIGHT", "cases", "emits", "emit", "__emit", "casesContainer", "ref", "headerContainer", "virtualScroll", "renderFixedHeader", "className", "idHeader", "nameHeader", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "statusH<PERSON>er", "initVirtualScroll", "div", "onclick", "lineHeight", "backgroundColor", "id", "on<PERSON><PERSON>ver", "onmouseout", "rowDiv", "idDiv", "nameDiv", "name", "paramDiv", "parameter", "title", "detailDiv", "detail", "statusDiv", "getStatusTagType", "tagText", "getStatusText", "tagEl", "updateVirtualScroll", "nextTick", "watch", "deep", "onMounted", "onUnmounted", "_createElementBlock", "_createElementVNode", "ref_key", "getTimelineItemType", "key", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_hoisted_5", "_hoisted_6", "_hoisted_7", "_hoisted_8", "_hoisted_9", "_hoisted_10", "_hoisted_11", "_hoisted_12", "_hoisted_13", "_hoisted_14", "_hoisted_15", "_hoisted_16", "_hoisted_17", "_hoisted_18", "_hoisted_19", "_hoisted_20", "_hoisted_21", "_hoisted_22", "_hoisted_23", "_hoisted_24", "_hoisted_25", "_hoisted_26", "visible", "Boolean", "testResultId", "caseResultId", "dialogVisible", "caseData", "steps", "loading", "newValue", "loadCaseData", "async", "caseResponse", "stepsResponse", "Promise", "all", "appApi", "getCaseResult", "getCaseSteps", "data", "error", "console", "ElMessage", "warning", "closeDialog", "formatDateTime", "dateString", "date", "Date", "toLocaleString", "e", "formatMicroseconds", "microseconds", "seconds", "toFixed", "_component_el_skeleton", "_component_el_empty", "_component_el_timeline_item", "_component_el_timeline", "_component_el_button", "_component_el_dialog", "modelValue", "$event", "sequenceName", "footer", "_createVNode", "onClick", "rows", "animated", "begin", "end", "CaseStateTag", "_createCommentVNode", "description", "_Fragment", "_renderList", "step", "_unref", "hollow", "timestamp"], "sourceRoot": ""}