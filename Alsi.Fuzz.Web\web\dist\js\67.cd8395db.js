"use strict";(self["webpackChunkfuzz_web"]=self["webpackChunkfuzz_web"]||[]).push([[67],{6741:function(e,a,s){s.d(a,{A:function(){return m}});var t=s(6768),l=s(144),n=s(4232),u=s(7477),i=s(1021),o=(0,t.pM)({__name:"TestStateTag",props:{state:{}},setup(e){const a=e,s=(0,t.EW)((()=>{switch(a.state){case i.si.Success:return"success";case i.si.Running:return"warning";case i.si.Failure:return"danger";case i.si.Pending:default:return"info"}})),l=e=>{switch(e){case i.si.Running:return"Running";case i.si.Pending:return"Not Run";case i.si.Success:return"Completed";case i.si.Failure:return"Faulted";case i.si.Paused:return"Paused";default:return"Unknown"}},u=(0,t.EW)((()=>l(a.state)));return(e,a)=>{const l=(0,t.g2)("el-tag");return(0,t.uX)(),(0,t.Wv)(l,{type:s.value,size:"small"},{default:(0,t.k6)((()=>[(0,t.eW)((0,n.v_)(u.value),1)])),_:1},8,["type"])}}});const c=o;var r=c;const d={key:0,class:"test-monitor"},v={class:"status-area"},g={class:"compact-status"},p={class:"status-header-inline"},k={key:0,class:"stats-row"},f={class:"stat-item success"},h={class:"stat-item failure"},R={class:"stat-item total"};var y=(0,t.pM)({__name:"TestMonitor",props:{runStatus:{},visible:{type:Boolean}},setup(e){const a=e,s=(0,t.EW)((()=>a.runStatus.testResult?.totalCount||0)),i=(0,t.EW)((()=>(a.runStatus.testResult?.successCount||0)+(a.runStatus.testResult?.failureCount||0)));return(e,a)=>{const o=(0,t.g2)("el-icon"),c=(0,t.g2)("el-progress");return e.visible?((0,t.uX)(),(0,t.CE)("div",d,[(0,t.Lk)("div",v,[(0,t.Lk)("div",g,[(0,t.Lk)("div",p,[s.value>0?((0,t.uX)(),(0,t.CE)("div",k,[(0,t.Lk)("div",f,[(0,t.bF)(o,null,{default:(0,t.k6)((()=>[(0,t.bF)((0,l.R1)(u.CircleCheckFilled))])),_:1}),(0,t.Lk)("span",null,(0,n.v_)(e.runStatus.testResult?.successCount||0),1)]),(0,t.Lk)("div",h,[(0,t.bF)(o,null,{default:(0,t.k6)((()=>[(0,t.bF)((0,l.R1)(u.CircleCloseFilled))])),_:1}),(0,t.Lk)("span",null,(0,n.v_)(e.runStatus.testResult?.failureCount||0),1)]),(0,t.Lk)("div",R,[(0,t.bF)(o,null,{default:(0,t.k6)((()=>[(0,t.bF)((0,l.R1)(u.InfoFilled))])),_:1}),(0,t.Lk)("span",null,(0,n.v_)(s.value||0),1)])])):(0,t.Q3)("",!0),(0,t.bF)(r,{state:e.runStatus.processState},null,8,["state"])]),(0,t.bF)(c,{percentage:s.value>0?Math.round(i.value/s.value*100):0,"stroke-width":8},null,8,["percentage"])])])])):(0,t.Q3)("",!0)}}}),b=s(1241);const C=(0,b.A)(y,[["__scopeId","data-v-ebdce83c"]]);var m=C},8763:function(e,a,s){s.r(a),s.d(a,{default:function(){return _}});s(4114),s(8111),s(7588);var t=s(6768),l=s(4232),n=s(144),u=s(1219),i=s(1021),o=s(6741),c=s(7823),r=s(9386);const d={class:"test-run-container"},v={class:"toolbar"},g={class:"action-buttons"},p={class:"content-area"},k={class:"test-cases-panel"},f={class:"card-header"},h={key:0,class:"case-count"},R={key:0,class:"loading-container"},y={key:1,class:"empty-container"},b={key:2,class:"case-list-wrapper"},C=50;var m=(0,t.pM)({__name:"TestRun",setup(e){const a=(0,n.KR)([]),s=(0,n.KR)(!0),m=(0,n.KR)(!1),w=(0,n.KR)(!1),F=(0,n.KR)(!1),_=(0,n.KR)(!1),S=(0,n.KR)(!1),x=(0,n.KR)({processState:i.si.Pending,currentOperation:"",testResult:{id:"",resultFolderName:"",testType:"",creationTime:"",totalCount:0,successCount:0,failureCount:0},caseResults:[]}),T=(0,n.KR)(""),E=(0,n.KR)(!1),I=(0,n.KR)(null),L=(0,n.KR)(null),W=(0,n.KR)(!0),K=(0,n.IJ)([]);let Q=0;const z=(0,n.KR)(new Map),P=(0,n.KR)(0);let X=null,M=null;const G=(0,t.EW)((()=>x.value.processState===i.si.Running)),A=(0,t.EW)((()=>x.value.processState===i.si.Paused)),N=(0,t.EW)((()=>(P.value,console.log("displayedCases = computed(() => begin."),(G.value||A.value)&&K.value?.length>0?(console.log("displayedCases = computed(() => end - running cases."),K.value):(console.log("displayedCases = computed(() => end - saved cases."),a.value)))),O=async()=>{s.value=!0;try{const e=await i.GQ.getSavedCases();a.value=e.data}catch(e){console.error("获取保存的测试用例失败:",e),u.nk.error("Failed to fetch saved test cases")}finally{s.value=!1}},D=()=>{O()},U=async()=>{if(0!==a.value.length){m.value=!0;try{await i.GQ.startTest(),S.value=!0,u.nk.success("Test execution started"),await j(),$()}catch(e){console.error("启动测试失败:",e),u.nk.error("Failed to start test execution")}finally{m.value=!1}}else u.nk.warning("No test cases available to execute")},V=async()=>{F.value=!0;try{await i.GQ.pauseTest(),u.nk.success("Test execution paused"),await j()}catch(e){console.error("暂停测试失败:",e),u.nk.error("Failed to pause test execution")}finally{F.value=!1}},B=async()=>{_.value=!0;try{await i.GQ.resumeTest(),u.nk.success("Test execution resumed"),await j()}catch(e){console.error("恢复测试失败:",e),u.nk.error("Failed to resume test execution")}finally{_.value=!1}},J=async()=>{w.value=!0;try{await i.GQ.stopTest(),u.nk.success("Test execution stopped"),await j()}catch(e){console.error("停止测试失败:",e),u.nk.error("Failed to stop test execution")}finally{w.value=!1}},j=async()=>{try{let e;if(W.value)e=await i.GQ.getTestStatus(),W.value=!1,x.value=e.data,K.value=e.data.caseResults||[],H(),Q=q(K.value);else{const a=Math.floor(Q/100)+1;e=await i.GQ.getTestStatusPaged({pageNumber:a,pageSize:100}),x.value.processState=e.data.processState,x.value.currentOperation=e.data.currentOperation,e.data.pagedCaseResult?.items&&(Z(e.data.pagedCaseResult.items),Q=q(K.value))}T.value=(new Date).toLocaleString(),(0,i.xh)(x.value)&&M&&(D(),ee())}catch(e){console.error("获取测试状态失败:",e)}},q=e=>{console.log("findFirstPendingIndex begin");for(let a=0;a<e.length;a++)if(e[a].state===i.si.Pending)return console.log("findFirstPendingIndex end"),a;return console.log("findFirstPendingIndex end"),e.length},H=()=>{console.log("rebuildIndexMap begin"),z.value.clear(),K.value.forEach(((e,a)=>{z.value.set(e.id,a)})),console.log("rebuildIndexMap end, map size:",z.value.size)},Y=()=>{X&&clearTimeout(X),X=window.setTimeout((()=>{(0,n.mu)(K),P.value++,X=null}),C)},Z=e=>{if(console.log("updateCaseResults begin, updating",e.length,"cases"),!K.value||0===K.value.length)return K.value=e,H(),Y(),void console.log("updateCaseResults end - initial load");const a=[];let s=0,t=0;for(const l of e){const e=z.value.get(l.id);if(void 0!==e&&e<K.value.length)if(K.value[e].id===l.id)a.push({index:e,case:l}),s++;else{console.warn("Index map inconsistency detected, rebuilding..."),H();const e=z.value.get(l.id);void 0!==e&&e<K.value.length?(a.push({index:e,case:l}),s++):t++}else t++}a.forEach((({index:e,case:a})=>{K.value[e]=a})),a.length>0&&Y(),console.log("updateCaseResults end - map hits:",s,"misses:",t,"updates:",a.length)},$=()=>{ee(),W.value=!0,z.value.clear(),M=window.setInterval(j,300)},ee=()=>{M&&(clearInterval(M),M=null)},ae=e=>{L.value=e.testResultId,I.value=e.id,E.value=!0},se=()=>{E.value=!1,I.value=null};return(0,t.sV)((()=>{O(),j().then((()=>{G.value&&(S.value=!0,$())}))})),(0,t.hi)((()=>{ee()})),(e,n)=>{const u=(0,t.g2)("el-button"),i=(0,t.g2)("el-skeleton"),C=(0,t.g2)("el-empty"),T=(0,t.g2)("el-card");return(0,t.uX)(),(0,t.CE)("div",d,[(0,t.Lk)("div",v,[n[5]||(n[5]=(0,t.Lk)("h3",null,"Test Execution",-1)),(0,t.Lk)("div",g,[G.value||A.value?(0,t.Q3)("",!0):((0,t.uX)(),(0,t.Wv)(u,{key:0,type:"success",size:"small",loading:m.value,onClick:U,disabled:0===a.value.length},{default:(0,t.k6)((()=>n[1]||(n[1]=[(0,t.eW)(" Start ")]))),_:1},8,["loading","disabled"])),G.value&&!A.value?((0,t.uX)(),(0,t.Wv)(u,{key:1,type:"warning",size:"small",loading:F.value,onClick:V},{default:(0,t.k6)((()=>n[2]||(n[2]=[(0,t.eW)(" Pause ")]))),_:1},8,["loading"])):(0,t.Q3)("",!0),A.value?((0,t.uX)(),(0,t.Wv)(u,{key:2,type:"info",size:"small",loading:_.value,onClick:B},{default:(0,t.k6)((()=>n[3]||(n[3]=[(0,t.eW)(" Resume ")]))),_:1},8,["loading"])):(0,t.Q3)("",!0),G.value||A.value?((0,t.uX)(),(0,t.Wv)(u,{key:3,type:"danger",size:"small",loading:w.value,onClick:J},{default:(0,t.k6)((()=>n[4]||(n[4]=[(0,t.eW)(" Stop ")]))),_:1},8,["loading"])):(0,t.Q3)("",!0)])]),(0,t.bF)(o.A,{"run-status":x.value,visible:S.value},null,8,["run-status","visible"]),(0,t.Lk)("div",p,[(0,t.Lk)("div",k,[(0,t.bF)(T,{shadow:"never",class:"test-cases-card"},{header:(0,t.k6)((()=>[(0,t.Lk)("div",f,[n[6]||(n[6]=(0,t.Lk)("span",null,"Test Cases",-1)),N.value.length>0?((0,t.uX)(),(0,t.CE)("span",h,(0,l.v_)(N.value.length)+" cases",1)):(0,t.Q3)("",!0)])])),default:(0,t.k6)((()=>[s.value?((0,t.uX)(),(0,t.CE)("div",R,[(0,t.bF)(i,{rows:10,animated:""})])):0===N.value.length?((0,t.uX)(),(0,t.CE)("div",y,[(0,t.bF)(C,{description:"No test cases found"})])):((0,t.uX)(),(0,t.CE)("div",b,[(0,t.bF)(r.A,{cases:N.value,onViewDetail:ae},null,8,["cases"])]))])),_:1})])]),(0,t.bF)(c.A,{visible:E.value,"onUpdate:visible":n[0]||(n[0]=e=>E.value=e),testResultId:L.value,caseResultId:I.value,onClose:se},null,8,["visible","testResultId","caseResultId"])])}}}),w=s(1241);const F=(0,w.A)(m,[["__scopeId","data-v-47d5a254"]]);var _=F}}]);
//# sourceMappingURL=67.cd8395db.js.map