<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Newtonsoft.Json.Bson</name>
    </assembly>
    <members>
        <member name="T:Newtonsoft.Json.Bson.BsonDataObjectId">
            <summary>
            Represents a BSON Oid (object id).
            </summary>
        </member>
        <member name="P:Newtonsoft.Json.Bson.BsonDataObjectId.Value">
            <summary>
            Gets or sets the value of the Oid.
            </summary>
            <value>The value of the Oid.</value>
        </member>
        <member name="M:Newtonsoft.Json.Bson.BsonDataObjectId.#ctor(System.Byte[])">
            <summary>
            Initializes a new instance of the <see cref="T:Newtonsoft.Json.Bson.BsonDataObjectId"/> class.
            </summary>
            <param name="value">The Oid value.</param>
        </member>
        <member name="T:Newtonsoft.Json.Bson.BsonDataReader">
            <summary>
            Represents a reader that provides fast, non-cached, forward-only access to serialized BSON data.
            </summary>
        </member>
        <member name="M:Newtonsoft.Json.Bson.BsonDataReader.ReadAsync(System.Threading.CancellationToken)">
            <summary>
            Asynchronously reads the next JSON token from the source.
            </summary>
            <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task`1" /> that represents the asynchronous read. The <see cref="P:System.Threading.Tasks.Task`1.Result" />
            property returns <c>true</c> if the next token was read successfully; <c>false</c> if there are no more tokens to read.</returns>
            <remarks>Derived classes must override this method to get asynchronous behaviour. Otherwise it will
            execute synchronously, returning an already-completed task. Asynchronous behaviour is also not available when the
            constructor was passed an instance of type derived from <see cref="T:System.IO.BinaryReader"/>.</remarks>
        </member>
        <member name="M:Newtonsoft.Json.Bson.BsonDataReader.ReadAsBooleanAsync(System.Threading.CancellationToken)">
            <summary>
            Asynchronously reads the next JSON token from the source as a <see cref="T:System.Nullable`1" /> of <see cref="T:System.Boolean" />.
            </summary>
            <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task`1" /> that represents the asynchronous read. The <see cref="P:System.Threading.Tasks.Task`1.Result" />
            property returns the <see cref="T:System.Nullable`1" /> of <see cref="T:System.Boolean" />. This result will be <c>null</c> at the end of an array.</returns>
            <remarks>Derived classes must override this method to get asynchronous behaviour. Otherwise it will
            execute synchronously, returning an already-completed task. Asynchronous behaviour is also not available when the
            constructor was passed an instance of type derived from <see cref="T:System.IO.BinaryReader"/>.</remarks>
        </member>
        <member name="M:Newtonsoft.Json.Bson.BsonDataReader.ReadAsBytesAsync(System.Threading.CancellationToken)">
            <summary>
            Asynchronously reads the next JSON token from the source as a <see cref="T:System.Byte" />[].
            </summary>
            <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task`1" /> that represents the asynchronous read. The <see cref="P:System.Threading.Tasks.Task`1.Result" />
            property returns the <see cref="T:System.Byte" />[]. This result will be <c>null</c> at the end of an array.</returns>
            <remarks>Derived classes must override this method to get asynchronous behaviour. Otherwise it will
            execute synchronously, returning an already-completed task. Asynchronous behaviour is also not available when the
            constructor was passed an instance of type derived from <see cref="T:System.IO.BinaryReader"/>.</remarks>
        </member>
        <member name="M:Newtonsoft.Json.Bson.BsonDataReader.ReadAsDateTimeAsync(System.Threading.CancellationToken)">
            <summary>
            Asynchronously reads the next JSON token from the source as a <see cref="T:System.Nullable`1" /> of <see cref="T:System.DateTime" />.
            </summary>
            <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task`1" /> that represents the asynchronous read. The <see cref="P:System.Threading.Tasks.Task`1.Result" />
            property returns the <see cref="T:System.Nullable`1" /> of <see cref="T:System.DateTime" />. This result will be <c>null</c> at the end of an array.</returns>
            <remarks>Derived classes must override this method to get asynchronous behaviour. Otherwise it will
            execute synchronously, returning an already-completed task. Asynchronous behaviour is also not available when the
            constructor was passed an instance of type derived from <see cref="T:System.IO.BinaryReader"/>.</remarks>
        </member>
        <member name="M:Newtonsoft.Json.Bson.BsonDataReader.ReadAsDateTimeOffsetAsync(System.Threading.CancellationToken)">
            <summary>
            Asynchronously reads the next JSON token from the source as a <see cref="T:System.Nullable`1" /> of <see cref="T:System.DateTimeOffset" />.
            </summary>
            <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task`1" /> that represents the asynchronous read. The <see cref="P:System.Threading.Tasks.Task`1.Result" />
            property returns the <see cref="T:System.Nullable`1" /> of <see cref="T:System.DateTimeOffset" />. This result will be <c>null</c> at the end of an array.</returns>
            <remarks>Derived classes must override this method to get asynchronous behaviour. Otherwise it will
            execute synchronously, returning an already-completed task. Asynchronous behaviour is also not available when the
            constructor was passed an instance of type derived from <see cref="T:System.IO.BinaryReader"/>.</remarks>
        </member>
        <member name="M:Newtonsoft.Json.Bson.BsonDataReader.ReadAsDecimalAsync(System.Threading.CancellationToken)">
            <summary>
            Asynchronously reads the next JSON token from the source as a <see cref="T:System.Nullable`1" /> of <see cref="T:System.Decimal" />.
            </summary>
            <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task`1" /> that represents the asynchronous read. The <see cref="P:System.Threading.Tasks.Task`1.Result" />
            property returns the <see cref="T:System.Nullable`1" /> of <see cref="T:System.Decimal" />. This result will be <c>null</c> at the end of an array.</returns>
            <remarks>Derived classes must override this method to get asynchronous behaviour. Otherwise it will
            execute synchronously, returning an already-completed task. Asynchronous behaviour is also not available when the
            constructor was passed an instance of type derived from <see cref="T:System.IO.BinaryReader"/>.</remarks>
        </member>
        <member name="M:Newtonsoft.Json.Bson.BsonDataReader.ReadAsDoubleAsync(System.Threading.CancellationToken)">
            <summary>
            Asynchronously reads the next JSON token from the source as a <see cref="T:System.Nullable`1" /> of <see cref="T:System.Double" />.
            </summary>
            <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task`1" /> that represents the asynchronous read. The <see cref="P:System.Threading.Tasks.Task`1.Result" />
            property returns the <see cref="T:System.Nullable`1" /> of <see cref="T:System.Double" />. This result will be <c>null</c> at the end of an array.</returns>
            <remarks>Derived classes must override this method to get asynchronous behaviour. Otherwise it will
            execute synchronously, returning an already-completed task. Asynchronous behaviour is also not available when the
            constructor was passed an instance of type derived from <see cref="T:System.IO.BinaryReader"/>.</remarks>
        </member>
        <member name="M:Newtonsoft.Json.Bson.BsonDataReader.ReadAsInt32Async(System.Threading.CancellationToken)">
            <summary>
            Asynchronously reads the next JSON token from the source as a <see cref="T:System.Nullable`1" /> of <see cref="T:System.Int32" />.
            </summary>
            <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task`1" /> that represents the asynchronous read. The <see cref="P:System.Threading.Tasks.Task`1.Result" />
            property returns the <see cref="T:System.Nullable`1" /> of <see cref="T:System.Int32" />. This result will be <c>null</c> at the end of an array.</returns>
            <remarks>Derived classes must override this method to get asynchronous behaviour. Otherwise it will
            execute synchronously, returning an already-completed task. Asynchronous behaviour is also not available when the
            constructor was passed an instance of type derived from <see cref="T:System.IO.BinaryReader"/>.</remarks>
        </member>
        <member name="M:Newtonsoft.Json.Bson.BsonDataReader.ReadAsStringAsync(System.Threading.CancellationToken)">
            <summary>
            Asynchronously reads the next JSON token from the source as a <see cref="T:System.String" />.
            </summary>
            <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None" />.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task`1" /> that represents the asynchronous read. The <see cref="P:System.Threading.Tasks.Task`1.Result" />
            property returns the <see cref="T:System.String" />. This result will be <c>null</c> at the end of an array.</returns>
            <remarks>Derived classes must override this method to get asynchronous behaviour. Otherwise it will
            execute synchronously, returning an already-completed task. Asynchronous behaviour is also not available when the
            constructor was passed an instance of type derived from <see cref="T:System.IO.BinaryReader"/>.</remarks>
        </member>
        <member name="P:Newtonsoft.Json.Bson.BsonDataReader.JsonNet35BinaryCompatibility">
            <summary>
            Gets or sets a value indicating whether binary data reading should be compatible with incorrect Json.NET 3.5 written binary.
            </summary>
            <value>
            	<c>true</c> if binary data reading will be compatible with incorrect Json.NET 3.5 written binary; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:Newtonsoft.Json.Bson.BsonDataReader.ReadRootValueAsArray">
            <summary>
            Gets or sets a value indicating whether the root object will be read as a JSON array.
            </summary>
            <value>
            	<c>true</c> if the root object will be read as a JSON array; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:Newtonsoft.Json.Bson.BsonDataReader.DateTimeKindHandling">
            <summary>
            Gets or sets the <see cref="T:System.DateTimeKind" /> used when reading <see cref="T:System.DateTime"/> values from BSON.
            </summary>
            <value>The <see cref="T:System.DateTimeKind" /> used when reading <see cref="T:System.DateTime"/> values from BSON.</value>
        </member>
        <member name="M:Newtonsoft.Json.Bson.BsonDataReader.#ctor(System.IO.Stream)">
            <summary>
            Initializes a new instance of the <see cref="T:Newtonsoft.Json.Bson.BsonDataReader"/> class.
            </summary>
            <param name="stream">The <see cref="T:System.IO.Stream"/> containing the BSON data to read.</param>
        </member>
        <member name="M:Newtonsoft.Json.Bson.BsonDataReader.#ctor(System.IO.BinaryReader)">
            <summary>
            Initializes a new instance of the <see cref="T:Newtonsoft.Json.Bson.BsonDataReader"/> class.
            </summary>
            <param name="reader">The <see cref="T:System.IO.BinaryReader"/> containing the BSON data to read.</param>
        </member>
        <member name="M:Newtonsoft.Json.Bson.BsonDataReader.#ctor(System.IO.Stream,System.Boolean,System.DateTimeKind)">
            <summary>
            Initializes a new instance of the <see cref="T:Newtonsoft.Json.Bson.BsonDataReader"/> class.
            </summary>
            <param name="stream">The <see cref="T:System.IO.Stream"/> containing the BSON data to read.</param>
            <param name="readRootValueAsArray">if set to <c>true</c> the root object will be read as a JSON array.</param>
            <param name="dateTimeKindHandling">The <see cref="T:System.DateTimeKind" /> used when reading <see cref="T:System.DateTime"/> values from BSON.</param>
        </member>
        <member name="M:Newtonsoft.Json.Bson.BsonDataReader.#ctor(System.IO.BinaryReader,System.Boolean,System.DateTimeKind)">
            <summary>
            Initializes a new instance of the <see cref="T:Newtonsoft.Json.Bson.BsonDataReader"/> class.
            </summary>
            <param name="reader">The <see cref="T:System.IO.BinaryReader"/> containing the BSON data to read.</param>
            <param name="readRootValueAsArray">if set to <c>true</c> the root object will be read as a JSON array.</param>
            <param name="dateTimeKindHandling">The <see cref="T:System.DateTimeKind" /> used when reading <see cref="T:System.DateTime"/> values from BSON.</param>
        </member>
        <member name="M:Newtonsoft.Json.Bson.BsonDataReader.Read">
            <summary>
            Reads the next JSON token from the underlying <see cref="T:System.IO.Stream"/>.
            </summary>
            <returns>
            <c>true</c> if the next token was read successfully; <c>false</c> if there are no more tokens to read.
            </returns>
        </member>
        <member name="M:Newtonsoft.Json.Bson.BsonDataReader.Close">
            <summary>
            Changes the reader's state to <see cref="F:Newtonsoft.Json.JsonReader.State.Closed"/>.
            If <see cref="P:Newtonsoft.Json.JsonReader.CloseInput"/> is set to <c>true</c>, the underlying <see cref="T:System.IO.Stream"/> is also closed.
            </summary>
        </member>
        <member name="T:Newtonsoft.Json.Bson.BsonDataWriter">
            <summary>
            Represents a writer that provides a fast, non-cached, forward-only way of generating BSON data.
            </summary>
        </member>
        <member name="M:Newtonsoft.Json.Bson.BsonDataWriter.FlushAsync(System.Threading.CancellationToken)">
            <summary>
            Asynchronously flushes whatever is in the buffer to the destination and also flushes the destination.
            </summary>
            <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None"/>.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation.</returns>
            <remarks>Because BSON documents are written as a single unit, only <see cref="M:Newtonsoft.Json.Bson.BsonDataWriter.FlushAsync(System.Threading.CancellationToken)"/>,
            <see cref="M:Newtonsoft.Json.Bson.BsonDataWriter.CloseAsync(System.Threading.CancellationToken)"/> and the final <see cref="M:Newtonsoft.Json.Bson.BsonDataWriter.WriteEndAsync(System.Threading.CancellationToken)"/>,
            <see cref="M:Newtonsoft.Json.Bson.BsonDataWriter.WriteEndArrayAsync(System.Threading.CancellationToken)"/> or <see cref="M:Newtonsoft.Json.Bson.BsonDataWriter.WriteEndObjectAsync(System.Threading.CancellationToken)"/>
            that finishes writing the document will write asynchronously. Derived classes will not write asynchronously.</remarks>
        </member>
        <member name="M:Newtonsoft.Json.Bson.BsonDataWriter.WriteEndAsync(System.Threading.CancellationToken)">
            <summary>
            Asynchronously writes the end of the current JSON object or array.
            </summary>
            <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None"/>.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation.</returns>
            <remarks>Because BSON documents are written as a single unit, only <see cref="M:Newtonsoft.Json.Bson.BsonDataWriter.FlushAsync(System.Threading.CancellationToken)"/>,
            <see cref="M:Newtonsoft.Json.Bson.BsonDataWriter.CloseAsync(System.Threading.CancellationToken)"/> and the final <see cref="M:Newtonsoft.Json.Bson.BsonDataWriter.WriteEndAsync(System.Threading.CancellationToken)"/>,
            <see cref="M:Newtonsoft.Json.Bson.BsonDataWriter.WriteEndArrayAsync(System.Threading.CancellationToken)"/> or <see cref="M:Newtonsoft.Json.Bson.BsonDataWriter.WriteEndObjectAsync(System.Threading.CancellationToken)"/>
            that finishes writing the document will write asynchronously. Derived classes will not write asynchronously.</remarks>
        </member>
        <member name="M:Newtonsoft.Json.Bson.BsonDataWriter.WriteEndArrayAsync(System.Threading.CancellationToken)">
            <summary>
            Asynchronously writes the end of an array.
            </summary>
            <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None"/>.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation.</returns>
            <remarks>Because BSON documents are written as a single unit, only <see cref="M:Newtonsoft.Json.Bson.BsonDataWriter.FlushAsync(System.Threading.CancellationToken)"/>,
            <see cref="M:Newtonsoft.Json.Bson.BsonDataWriter.CloseAsync(System.Threading.CancellationToken)"/> and the final <see cref="M:Newtonsoft.Json.Bson.BsonDataWriter.WriteEndAsync(System.Threading.CancellationToken)"/>,
            <see cref="M:Newtonsoft.Json.Bson.BsonDataWriter.WriteEndArrayAsync(System.Threading.CancellationToken)"/> or <see cref="M:Newtonsoft.Json.Bson.BsonDataWriter.WriteEndObjectAsync(System.Threading.CancellationToken)"/>
            that finishes writing the document will write asynchronously. Derived classes will not write asynchronously.</remarks>
        </member>
        <member name="M:Newtonsoft.Json.Bson.BsonDataWriter.WriteEndObjectAsync(System.Threading.CancellationToken)">
            <summary>
            Asynchronously writes the end of a JSON object.
            </summary>
            <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None"/>.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation.</returns>
            <remarks>Because BSON documents are written as a single unit, only <see cref="M:Newtonsoft.Json.Bson.BsonDataWriter.FlushAsync(System.Threading.CancellationToken)"/>,
            <see cref="M:Newtonsoft.Json.Bson.BsonDataWriter.CloseAsync(System.Threading.CancellationToken)"/> and the final <see cref="M:Newtonsoft.Json.Bson.BsonDataWriter.WriteEndAsync(System.Threading.CancellationToken)"/>,
            <see cref="M:Newtonsoft.Json.Bson.BsonDataWriter.WriteEndArrayAsync(System.Threading.CancellationToken)"/> or <see cref="M:Newtonsoft.Json.Bson.BsonDataWriter.WriteEndObjectAsync(System.Threading.CancellationToken)"/>
            that finishes writing the document will write asynchronously. Derived classes will not write asynchronously.</remarks>
        </member>
        <member name="M:Newtonsoft.Json.Bson.BsonDataWriter.CloseAsync(System.Threading.CancellationToken)">
            <summary>
            Asynchronously closes this writer.
            If <see cref="P:Newtonsoft.Json.JsonWriter.CloseOutput"/> is set to <c>true</c>, the destination is also closed.
            </summary>
            <param name="cancellationToken">The token to monitor for cancellation requests. The default value is <see cref="P:System.Threading.CancellationToken.None"/>.</param>
            <returns>A <see cref="T:System.Threading.Tasks.Task"/> that represents the asynchronous operation.</returns>
            <remarks>Because BSON documents are written as a single unit, only <see cref="M:Newtonsoft.Json.Bson.BsonDataWriter.FlushAsync(System.Threading.CancellationToken)"/>,
            <see cref="M:Newtonsoft.Json.Bson.BsonDataWriter.CloseAsync(System.Threading.CancellationToken)"/> and the final <see cref="M:Newtonsoft.Json.Bson.BsonDataWriter.WriteEndAsync(System.Threading.CancellationToken)"/>,
            <see cref="M:Newtonsoft.Json.Bson.BsonDataWriter.WriteEndArrayAsync(System.Threading.CancellationToken)"/> or <see cref="M:Newtonsoft.Json.Bson.BsonDataWriter.WriteEndObjectAsync(System.Threading.CancellationToken)"/>
            that finishes writing the document will write asynchronously. Derived classes will not write asynchronously.</remarks>
        </member>
        <member name="P:Newtonsoft.Json.Bson.BsonDataWriter.DateTimeKindHandling">
            <summary>
            Gets or sets the <see cref="T:System.DateTimeKind" /> used when writing <see cref="T:System.DateTime"/> values to BSON.
            When set to <see cref="F:System.DateTimeKind.Unspecified" /> no conversion will occur.
            </summary>
            <value>The <see cref="T:System.DateTimeKind" /> used when writing <see cref="T:System.DateTime"/> values to BSON.</value>
        </member>
        <member name="M:Newtonsoft.Json.Bson.BsonDataWriter.#ctor(System.IO.Stream)">
            <summary>
            Initializes a new instance of the <see cref="T:Newtonsoft.Json.Bson.BsonDataWriter"/> class.
            </summary>
            <param name="stream">The <see cref="T:System.IO.Stream"/> to write to.</param>
        </member>
        <member name="M:Newtonsoft.Json.Bson.BsonDataWriter.#ctor(System.IO.BinaryWriter)">
            <summary>
            Initializes a new instance of the <see cref="T:Newtonsoft.Json.Bson.BsonDataWriter"/> class.
            </summary>
            <param name="writer">The <see cref="T:System.IO.BinaryWriter"/> to write to.</param>
        </member>
        <member name="M:Newtonsoft.Json.Bson.BsonDataWriter.Flush">
            <summary>
            Flushes whatever is in the buffer to the underlying <see cref="T:System.IO.Stream"/> and also flushes the underlying stream.
            </summary>
        </member>
        <member name="M:Newtonsoft.Json.Bson.BsonDataWriter.WriteEnd(Newtonsoft.Json.JsonToken)">
            <summary>
            Writes the end.
            </summary>
            <param name="token">The token.</param>
        </member>
        <member name="M:Newtonsoft.Json.Bson.BsonDataWriter.WriteComment(System.String)">
            <summary>
            Writes a comment <c>/*...*/</c> containing the specified text.
            </summary>
            <param name="text">Text to place inside the comment.</param>
        </member>
        <member name="M:Newtonsoft.Json.Bson.BsonDataWriter.WriteStartConstructor(System.String)">
            <summary>
            Writes the start of a constructor with the given name.
            </summary>
            <param name="name">The name of the constructor.</param>
        </member>
        <member name="M:Newtonsoft.Json.Bson.BsonDataWriter.WriteRaw(System.String)">
            <summary>
            Writes raw JSON.
            </summary>
            <param name="json">The raw JSON to write.</param>
        </member>
        <member name="M:Newtonsoft.Json.Bson.BsonDataWriter.WriteRawValue(System.String)">
            <summary>
            Writes raw JSON where a value is expected and updates the writer's state.
            </summary>
            <param name="json">The raw JSON to write.</param>
        </member>
        <member name="M:Newtonsoft.Json.Bson.BsonDataWriter.WriteStartArray">
            <summary>
            Writes the beginning of a JSON array.
            </summary>
        </member>
        <member name="M:Newtonsoft.Json.Bson.BsonDataWriter.WriteStartObject">
            <summary>
            Writes the beginning of a JSON object.
            </summary>
        </member>
        <member name="M:Newtonsoft.Json.Bson.BsonDataWriter.WritePropertyName(System.String)">
            <summary>
            Writes the property name of a name/value pair on a JSON object.
            </summary>
            <param name="name">The name of the property.</param>
        </member>
        <member name="M:Newtonsoft.Json.Bson.BsonDataWriter.Close">
            <summary>
            Closes this writer.
            If <see cref="P:Newtonsoft.Json.JsonWriter.CloseOutput"/> is set to <c>true</c>, the underlying <see cref="T:System.IO.Stream"/> is also closed.
            If <see cref="P:Newtonsoft.Json.JsonWriter.AutoCompleteOnClose"/> is set to <c>true</c>, the JSON is auto-completed.
            </summary>
        </member>
        <member name="M:Newtonsoft.Json.Bson.BsonDataWriter.WriteValue(System.Object)">
            <summary>
            Writes a <see cref="T:System.Object"/> value.
            An error will raised if the value cannot be written as a single JSON token.
            </summary>
            <param name="value">The <see cref="T:System.Object"/> value to write.</param>
        </member>
        <member name="M:Newtonsoft.Json.Bson.BsonDataWriter.WriteNull">
            <summary>
            Writes a null value.
            </summary>
        </member>
        <member name="M:Newtonsoft.Json.Bson.BsonDataWriter.WriteUndefined">
            <summary>
            Writes an undefined value.
            </summary>
        </member>
        <member name="M:Newtonsoft.Json.Bson.BsonDataWriter.WriteValue(System.String)">
            <summary>
            Writes a <see cref="T:System.String"/> value.
            </summary>
            <param name="value">The <see cref="T:System.String"/> value to write.</param>
        </member>
        <member name="M:Newtonsoft.Json.Bson.BsonDataWriter.WriteValue(System.Int32)">
            <summary>
            Writes a <see cref="T:System.Int32"/> value.
            </summary>
            <param name="value">The <see cref="T:System.Int32"/> value to write.</param>
        </member>
        <member name="M:Newtonsoft.Json.Bson.BsonDataWriter.WriteValue(System.UInt32)">
            <summary>
            Writes a <see cref="T:System.UInt32"/> value.
            </summary>
            <param name="value">The <see cref="T:System.UInt32"/> value to write.</param>
        </member>
        <member name="M:Newtonsoft.Json.Bson.BsonDataWriter.WriteValue(System.Int64)">
            <summary>
            Writes a <see cref="T:System.Int64"/> value.
            </summary>
            <param name="value">The <see cref="T:System.Int64"/> value to write.</param>
        </member>
        <member name="M:Newtonsoft.Json.Bson.BsonDataWriter.WriteValue(System.UInt64)">
            <summary>
            Writes a <see cref="T:System.UInt64"/> value.
            </summary>
            <param name="value">The <see cref="T:System.UInt64"/> value to write.</param>
        </member>
        <member name="M:Newtonsoft.Json.Bson.BsonDataWriter.WriteValue(System.Single)">
            <summary>
            Writes a <see cref="T:System.Single"/> value.
            </summary>
            <param name="value">The <see cref="T:System.Single"/> value to write.</param>
        </member>
        <member name="M:Newtonsoft.Json.Bson.BsonDataWriter.WriteValue(System.Double)">
            <summary>
            Writes a <see cref="T:System.Double"/> value.
            </summary>
            <param name="value">The <see cref="T:System.Double"/> value to write.</param>
        </member>
        <member name="M:Newtonsoft.Json.Bson.BsonDataWriter.WriteValue(System.Boolean)">
            <summary>
            Writes a <see cref="T:System.Boolean"/> value.
            </summary>
            <param name="value">The <see cref="T:System.Boolean"/> value to write.</param>
        </member>
        <member name="M:Newtonsoft.Json.Bson.BsonDataWriter.WriteValue(System.Int16)">
            <summary>
            Writes a <see cref="T:System.Int16"/> value.
            </summary>
            <param name="value">The <see cref="T:System.Int16"/> value to write.</param>
        </member>
        <member name="M:Newtonsoft.Json.Bson.BsonDataWriter.WriteValue(System.UInt16)">
            <summary>
            Writes a <see cref="T:System.UInt16"/> value.
            </summary>
            <param name="value">The <see cref="T:System.UInt16"/> value to write.</param>
        </member>
        <member name="M:Newtonsoft.Json.Bson.BsonDataWriter.WriteValue(System.Char)">
            <summary>
            Writes a <see cref="T:System.Char"/> value.
            </summary>
            <param name="value">The <see cref="T:System.Char"/> value to write.</param>
        </member>
        <member name="M:Newtonsoft.Json.Bson.BsonDataWriter.WriteValue(System.Byte)">
            <summary>
            Writes a <see cref="T:System.Byte"/> value.
            </summary>
            <param name="value">The <see cref="T:System.Byte"/> value to write.</param>
        </member>
        <member name="M:Newtonsoft.Json.Bson.BsonDataWriter.WriteValue(System.SByte)">
            <summary>
            Writes a <see cref="T:System.SByte"/> value.
            </summary>
            <param name="value">The <see cref="T:System.SByte"/> value to write.</param>
        </member>
        <member name="M:Newtonsoft.Json.Bson.BsonDataWriter.WriteValue(System.Decimal)">
            <summary>
            Writes a <see cref="T:System.Decimal"/> value.
            </summary>
            <param name="value">The <see cref="T:System.Decimal"/> value to write.</param>
        </member>
        <member name="M:Newtonsoft.Json.Bson.BsonDataWriter.WriteValue(System.DateTime)">
            <summary>
            Writes a <see cref="T:System.DateTime"/> value.
            </summary>
            <param name="value">The <see cref="T:System.DateTime"/> value to write.</param>
        </member>
        <member name="M:Newtonsoft.Json.Bson.BsonDataWriter.WriteValue(System.DateTimeOffset)">
            <summary>
            Writes a <see cref="T:System.DateTimeOffset"/> value.
            </summary>
            <param name="value">The <see cref="T:System.DateTimeOffset"/> value to write.</param>
        </member>
        <member name="M:Newtonsoft.Json.Bson.BsonDataWriter.WriteValue(System.Byte[])">
            <summary>
            Writes a <see cref="T:System.Byte"/>[] value.
            </summary>
            <param name="value">The <see cref="T:System.Byte"/>[] value to write.</param>
        </member>
        <member name="M:Newtonsoft.Json.Bson.BsonDataWriter.WriteValue(System.Guid)">
            <summary>
            Writes a <see cref="T:System.Guid"/> value.
            </summary>
            <param name="value">The <see cref="T:System.Guid"/> value to write.</param>
        </member>
        <member name="M:Newtonsoft.Json.Bson.BsonDataWriter.WriteValue(System.TimeSpan)">
            <summary>
            Writes a <see cref="T:System.TimeSpan"/> value.
            </summary>
            <param name="value">The <see cref="T:System.TimeSpan"/> value to write.</param>
        </member>
        <member name="M:Newtonsoft.Json.Bson.BsonDataWriter.WriteValue(System.Uri)">
            <summary>
            Writes a <see cref="T:System.Uri"/> value.
            </summary>
            <param name="value">The <see cref="T:System.Uri"/> value to write.</param>
        </member>
        <member name="M:Newtonsoft.Json.Bson.BsonDataWriter.WriteObjectId(System.Byte[])">
            <summary>
            Writes a <see cref="T:System.Byte"/>[] value that represents a BSON object id.
            </summary>
            <param name="value">The Object ID value to write.</param>
        </member>
        <member name="M:Newtonsoft.Json.Bson.BsonDataWriter.WriteRegex(System.String,System.String)">
            <summary>
            Writes a BSON regex.
            </summary>
            <param name="pattern">The regex pattern.</param>
            <param name="options">The regex options.</param>
        </member>
        <member name="T:Newtonsoft.Json.Bson.Converters.BsonDataObjectIdConverter">
            <summary>
            Converts a <see cref="T:Newtonsoft.Json.Bson.BsonDataObjectId"/> to and from JSON and BSON.
            </summary>
        </member>
        <member name="M:Newtonsoft.Json.Bson.Converters.BsonDataObjectIdConverter.WriteJson(Newtonsoft.Json.JsonWriter,System.Object,Newtonsoft.Json.JsonSerializer)">
            <summary>
            Writes the JSON representation of the object.
            </summary>
            <param name="writer">The <see cref="T:Newtonsoft.Json.JsonWriter"/> to write to.</param>
            <param name="value">The value.</param>
            <param name="serializer">The calling serializer.</param>
        </member>
        <member name="M:Newtonsoft.Json.Bson.Converters.BsonDataObjectIdConverter.ReadJson(Newtonsoft.Json.JsonReader,System.Type,System.Object,Newtonsoft.Json.JsonSerializer)">
            <summary>
            Reads the JSON representation of the object.
            </summary>
            <param name="reader">The <see cref="T:Newtonsoft.Json.JsonReader"/> to read from.</param>
            <param name="objectType">Type of the object.</param>
            <param name="existingValue">The existing value of object being read.</param>
            <param name="serializer">The calling serializer.</param>
            <returns>The object value.</returns>
        </member>
        <member name="M:Newtonsoft.Json.Bson.Converters.BsonDataObjectIdConverter.CanConvert(System.Type)">
            <summary>
            Determines whether this instance can convert the specified object type.
            </summary>
            <param name="objectType">Type of the object.</param>
            <returns>
            	<c>true</c> if this instance can convert the specified object type; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="T:Newtonsoft.Json.Bson.Converters.BsonDataRegexConverter">
            <summary>
            Converts a <see cref="T:System.Text.RegularExpressions.Regex"/> to and from BSON.
            </summary>
        </member>
        <member name="M:Newtonsoft.Json.Bson.Converters.BsonDataRegexConverter.WriteJson(Newtonsoft.Json.JsonWriter,System.Object,Newtonsoft.Json.JsonSerializer)">
            <summary>
            Writes the JSON representation of the object.
            </summary>
            <param name="writer">The <see cref="T:Newtonsoft.Json.JsonWriter"/> to write to.</param>
            <param name="value">The value.</param>
            <param name="serializer">The calling serializer.</param>
        </member>
        <member name="M:Newtonsoft.Json.Bson.Converters.BsonDataRegexConverter.ReadJson(Newtonsoft.Json.JsonReader,System.Type,System.Object,Newtonsoft.Json.JsonSerializer)">
            <summary>
            Reads the JSON representation of the object.
            </summary>
            <param name="reader">The <see cref="T:Newtonsoft.Json.JsonReader"/> to read from.</param>
            <param name="objectType">Type of the object.</param>
            <param name="existingValue">The existing value of object being read.</param>
            <param name="serializer">The calling serializer.</param>
            <returns>The object value.</returns>
        </member>
        <member name="M:Newtonsoft.Json.Bson.Converters.BsonDataRegexConverter.CanConvert(System.Type)">
            <summary>
            Determines whether this instance can convert the specified object type.
            </summary>
            <param name="objectType">Type of the object.</param>
            <returns>
            	<c>true</c> if this instance can convert the specified object type; otherwise, <c>false</c>.
            </returns>
        </member>
    </members>
</doc>
