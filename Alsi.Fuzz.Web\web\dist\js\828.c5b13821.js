"use strict";(self["webpackChunkfuzz_web"]=self["webpackChunkfuzz_web"]||[]).push([[828],{2971:function(e,t,s){s.d(t,{A:function(){return c}});var i=s(6768),a=s(4232),n=s(1021),l=(0,i.pM)({__name:"CaseStateTag",props:{state:{}},setup(e){const t=e,s=(0,i.EW)((()=>{switch(t.state){case n.si.Success:return"success";case n.si.Running:return"warning";case n.si.Failure:return"danger";case n.si.Pending:default:return"info"}})),l=e=>{switch(e){case n.si.Running:return"Running";case n.si.Pending:return"Not Run";case n.si.Success:return"Passed";case n.si.Failure:return"Failed";default:return"Unknown"}},r=(0,i.EW)((()=>l(t.state)));return(e,t)=>{const n=(0,i.g2)("el-tag");return(0,i.uX)(),(0,i.Wv)(n,{type:s.value,size:"small",style:{"min-width":"60px"}},{default:(0,i.k6)((()=>[(0,i.eW)((0,a.v_)(r.value),1)])),_:1},8,["type"])}}});const r=l;var c=r},7823:function(e,t,s){s.d(t,{A:function(){return A}});var i=s(6768),a=s(4232),n=s(144),l=s(1219),r=s(1021);const c=e=>{switch(e){case r.si.Success:return"success";case r.si.Running:return"primary";case r.si.Failure:return"danger";case r.si.Pending:default:return"info"}};var o=s(2971);const d={key:0,class:"loading"},h={key:1,class:"case-detail-content"},u={class:"basic-info"},m={class:"info-grid"},v={class:"info-item"},p={class:"value"},g={class:"info-item"},f={class:"value"},C={class:"info-item"},b={class:"value"},k={class:"info-item"},x={class:"value status-combined"},y={key:0,class:"info-item full-width"},S=["title"],w={key:1,class:"info-item full-width"},L=["title"],H={class:"steps-section"},I={key:0,class:"no-steps"},E={class:"step-content"},R={class:"step-row"},_={class:"step-left"},N={class:"step-timestamp"},W=["title"],F=["title"],z={class:"step-right"},M={class:"dialog-footer"};var X=(0,i.pM)({__name:"CaseDetailDialog",props:{visible:{type:Boolean},testResultId:{},caseResultId:{}},emits:["update:visible","close"],setup(e,{emit:t}){const s=e,X=t,P=(0,n.KR)(s.visible),T=(0,n.KR)(null),A=(0,n.KR)([]),V=(0,n.KR)(!1);(0,i.wB)((()=>s.visible),(e=>{P.value=e,e&&s.testResultId&&s.caseResultId&&$()})),(0,i.wB)((()=>P.value),(e=>{X("update:visible",e),e||X("close")}));const $=async()=>{if(s.testResultId&&s.caseResultId){V.value=!0;try{const[e,t]=await Promise.all([r.GQ.getCaseResult(s.testResultId,s.caseResultId),r.GQ.getCaseSteps(s.testResultId,s.caseResultId)]);T.value=e.data,A.value=t.data}catch(e){console.error("Failed to load case data:",e),l.nk.error("Failed to load case details")}finally{V.value=!1}}else l.nk.warning("Missing required parameters")},B=()=>{P.value=!1,T.value=null,A.value=[]},K=e=>{if(!e)return"N/A";try{const t=new Date(e);return t.toLocaleString()}catch(t){return e}},D=e=>{if(!e&&0!==e)return"N/A";const t=e/1e6;return`${t.toFixed(6)}`};return(0,i.sV)((()=>{P.value&&s.testResultId&&s.caseResultId&&$()})),(e,t)=>{const s=(0,i.g2)("el-skeleton"),l=(0,i.g2)("el-empty"),r=(0,i.g2)("el-timeline-item"),X=(0,i.g2)("el-timeline"),$=(0,i.g2)("el-button"),O=(0,i.g2)("el-dialog");return(0,i.uX)(),(0,i.Wv)(O,{modelValue:P.value,"onUpdate:modelValue":t[0]||(t[0]=e=>P.value=e),title:`${T.value?.name||T.value?.sequenceName||""}`,width:"60%","destroy-on-close":""},{footer:(0,i.k6)((()=>[(0,i.Lk)("span",M,[(0,i.bF)($,{onClick:B},{default:(0,i.k6)((()=>t[9]||(t[9]=[(0,i.eW)("Close")]))),_:1})])])),default:(0,i.k6)((()=>[V.value?((0,i.uX)(),(0,i.CE)("div",d,[(0,i.bF)(s,{rows:10,animated:""})])):((0,i.uX)(),(0,i.CE)("div",h,[(0,i.Lk)("div",u,[t[7]||(t[7]=(0,i.Lk)("h4",null,"Information",-1)),(0,i.Lk)("div",m,[(0,i.Lk)("div",v,[t[1]||(t[1]=(0,i.Lk)("div",{class:"label"},"Case Name:",-1)),(0,i.Lk)("div",p,(0,a.v_)(T.value?.name),1)]),(0,i.Lk)("div",g,[t[2]||(t[2]=(0,i.Lk)("div",{class:"label"},"Sequence Name:",-1)),(0,i.Lk)("div",f,(0,a.v_)(T.value?.sequenceName),1)]),(0,i.Lk)("div",C,[t[3]||(t[3]=(0,i.Lk)("div",{class:"label"},"Start Time:",-1)),(0,i.Lk)("div",b,(0,a.v_)(K(T.value?.begin)),1)]),(0,i.Lk)("div",k,[t[4]||(t[4]=(0,i.Lk)("div",{class:"label"},"End Time / Status:",-1)),(0,i.Lk)("div",x,[(0,i.eW)((0,a.v_)(K(T.value?.end))+" ",1),(0,i.bF)(o.A,{state:T.value?.state||"",class:"status-tag"},null,8,["state"])])]),T.value?.parameter?((0,i.uX)(),(0,i.CE)("div",y,[t[5]||(t[5]=(0,i.Lk)("div",{class:"label"},"Parameter:",-1)),(0,i.Lk)("div",{class:"value",title:T.value?.parameter},(0,a.v_)(T.value?.parameter),9,S)])):(0,i.Q3)("",!0),T.value?.detail?((0,i.uX)(),(0,i.CE)("div",w,[t[6]||(t[6]=(0,i.Lk)("div",{class:"label"},"Detail:",-1)),(0,i.Lk)("div",{class:"value",title:T.value?.detail},(0,a.v_)(T.value.detail),9,L)])):(0,i.Q3)("",!0)])]),(0,i.Lk)("div",H,[t[8]||(t[8]=(0,i.Lk)("h4",null,"Steps",-1)),0===A.value.length?((0,i.uX)(),(0,i.CE)("div",I,[(0,i.bF)(l,{description:"No steps available"})])):((0,i.uX)(),(0,i.Wv)(X,{key:1},{default:(0,i.k6)((()=>[((0,i.uX)(!0),(0,i.CE)(i.FK,null,(0,i.pI)(A.value,(e=>((0,i.uX)(),(0,i.Wv)(r,{key:e.id,type:(0,n.R1)(c)(e.state),hollow:"Success"!==e.state},{default:(0,i.k6)((()=>[(0,i.Lk)("div",E,[(0,i.Lk)("div",R,[(0,i.Lk)("div",_,[(0,i.Lk)("span",N,(0,a.v_)(D(e.timestamp)),1),(0,i.Lk)("span",{class:"step-name",title:e.name},(0,a.v_)(e.name),9,W),e.detail?((0,i.uX)(),(0,i.CE)("span",{key:0,title:e.detail,class:"step-detail-inline"},(0,a.v_)(e.detail),9,F)):(0,i.Q3)("",!0)]),(0,i.Lk)("div",z,[(0,i.bF)(o.A,{state:e.state},null,8,["state"])])])])])),_:2},1032,["type","hollow"])))),128))])),_:1}))])]))])),_:1},8,["modelValue","title"])}}}),P=s(1241);const T=(0,P.A)(X,[["__scopeId","data-v-a931aa76"]]);var A=T},9386:function(e,t,s){s.d(t,{A:function(){return v}});var i=s(6768),a=s(144);
/**
 * js-booster - High-performance frontend library
 * VirtualScroll - Virtual scrolling implementation
 * @version 1.1.1
 * <AUTHOR>
 * @license MIT
 */
class n{constructor(e){this.container=e.container,this.items=e.items||[],this.itemHeight=e.itemHeight||20,this.bufferSize=e.bufferSize||10,this.customRenderItem=e.renderItem,this.customRenderHeader=e.renderHeader,this.maxHeight=e.maxHeight||2684e4,this.visibleStartIndex=0,this.visibleEndIndex=0,this.scrollContainer=null,this.contentWrapper=null,this.contentContainer=null,this.totalHeight=this.items.length*this.itemHeight,this.heightScale=1,this.totalHeight>this.maxHeight&&(this.heightScale=this.maxHeight/this.totalHeight),this.initialize()}initialize(){if(this.container.innerHTML="",this.scrollContainer=document.createElement("div"),Object.assign(this.scrollContainer.style,{flex:"1",overflow:"auto",position:"relative",minHeight:"0",height:"100%",boxSizing:"border-box"}),this.customRenderHeader){const e=this.customRenderHeader();e&&this.scrollContainer.appendChild(e)}this.contentWrapper=document.createElement("div"),Object.assign(this.contentWrapper.style,{position:"relative",width:"100%"});const e=this.totalHeight*this.heightScale;this.contentWrapper.style.height=`${e}px`,this.contentContainer=document.createElement("div"),Object.assign(this.contentContainer.style,{position:"absolute",width:"100%",left:"0"}),this.scrollContainer.addEventListener("scroll",this.handleScroll.bind(this)),this.contentWrapper.appendChild(this.contentContainer),this.scrollContainer.appendChild(this.contentWrapper),this.container.appendChild(this.scrollContainer),this.renderVisibleItems(0,Math.min(100,this.items.length))}handleScroll(){const e=this.scrollContainer.scrollTop,t=this.scrollContainer.clientHeight,s=e/this.heightScale,i=Math.max(0,Math.floor(s/this.itemHeight)-this.bufferSize),a=Math.min(this.items.length,Math.ceil((s+t/this.heightScale)/this.itemHeight)+this.bufferSize);i===this.visibleStartIndex&&a===this.visibleEndIndex||(this.renderVisibleItems(i,a),this.visibleStartIndex=i,this.visibleEndIndex=a)}renderVisibleItems(e,t){this.contentContainer.innerHTML="",this.contentContainer.style.transform=`translateY(${e*this.itemHeight*this.heightScale}px)`;for(let s=e;s<t;s++){const e=this.items[s];if(this.customRenderItem){const t=this.customRenderItem(e,s);t&&(t.style.height=this.itemHeight*this.heightScale+"px",t.style.boxSizing="border-box",t.style.width="100%",this.contentContainer.appendChild(t))}else{const t=document.createElement("div");Object.assign(t.style,{height:this.itemHeight*this.heightScale+"px",width:"100%",boxSizing:"border-box",padding:"8px",borderBottom:"1px solid #eee"}),t.textContent=JSON.stringify(e),this.contentContainer.appendChild(t)}}}updateItems(e){this.items=e||[],this.totalHeight=this.items.length*this.itemHeight,this.heightScale=1,this.totalHeight>this.maxHeight&&(this.heightScale=this.maxHeight/this.totalHeight),this.contentWrapper&&(this.contentWrapper.style.height=this.totalHeight*this.heightScale+"px"),this.visibleStartIndex=0,this.visibleEndIndex=0,this.handleScroll()}scrollToIndex(e){e>=0&&e<this.items.length&&(this.scrollContainer.scrollTop=e*this.itemHeight*this.heightScale)}destroy(){this.scrollContainer&&this.scrollContainer.removeEventListener("scroll",this.handleScroll),this.container&&(this.container.innerHTML=""),this.items=null,this.container=null,this.scrollContainer=null,this.contentWrapper=null,this.contentContainer=null}refresh(){this.handleScroll()}getScrollContainer(){return this.scrollContainer}}
/**
 * js-booster - High-performance frontend library
 * @version 1.1.1
 * <AUTHOR>
 * @license MIT
 */"undefined"!==typeof window&&(window.JsBooster={VirtualScroll:n});var l=s(1420);const r={class:"cases-list-container case-list"},c=36,o=20,d=40;var h=(0,i.pM)({__name:"CaseList",props:{cases:{}},emits:["view-detail"],setup(e,{emit:t}){const s=e,h=t,u=(0,a.KR)(null),m=(0,a.KR)(null);let v=null;const p=()=>{if(!m.value)return;m.value.innerHTML="";const e=document.createElement("div");e.className="header-row",e.style.height=`${d}px`;const t=document.createElement("div");t.textContent="ID",t.className="header-cell-id",e.appendChild(t);const s=document.createElement("div");s.textContent="Name",s.className="header-cell-name",e.appendChild(s);const i=document.createElement("div");i.textContent="Parameter",i.className="header-cell-param",e.appendChild(i);const a=document.createElement("div");a.textContent="Detail",a.className="header-cell-detail",e.appendChild(a);const n=document.createElement("div");n.textContent="Status",n.className="header-cell-status",e.appendChild(n),m.value.appendChild(e)},g=()=>{u.value&&s.cases.length&&(p(),v&&v.destroy(),v=new n({container:u.value,items:s.cases,itemHeight:c,bufferSize:o,renderItem:(e,t)=>{const i=document.createElement("div");i.className="case-row",i.onclick=()=>h("view-detail",e),i.style.height=`${c}px`,i.style.lineHeight=`${c}px`,i.style.borderBottom=t===s.cases.length-1?"none":"1px solid #ebeef5",i.style.backgroundColor=e.id%2===0?"#fff":"#fafafa",i.onmouseover=()=>{i.style.backgroundColor="#f5f7fa"},i.onmouseout=()=>{i.style.backgroundColor=e.id%2===0?"#fff":"#fafafa",i.style.borderBottom=t===s.cases.length-1?"none":"1px solid #ebeef5"};const a=document.createElement("div");a.className="case-row-content";const n=document.createElement("div");n.textContent=`#${e.id}`,n.className="case-cell-id",a.appendChild(n);const l=document.createElement("div");l.textContent=e.name,l.className="case-cell-name",a.appendChild(l);const r=document.createElement("div");r.textContent=e.parameter,r.title=e.parameter,r.className="case-cell-param",a.appendChild(r);const o=document.createElement("div");o.textContent=e.detail||"-",o.title=e.detail||"",o.className="case-cell-detail",a.appendChild(o);const d=document.createElement("div");d.className="case-cell-status";const u=C(e.state),m=b(e.state),v=document.createElement("span");return v.className=`el-tag el-tag--${u} el-tag--small case-status-tag`,v.textContent=m,d.appendChild(v),a.appendChild(d),i.appendChild(a),i}}))},f=()=>{v?v.updateItems(s.cases):(0,i.dY)((()=>{g()}))},C=e=>{switch(e){case l.si.Success:return"success";case l.si.Running:return"warning";case l.si.Failure:return"danger";case l.si.Pending:default:return"info"}},b=e=>{switch(e){case l.si.Success:return"Passed";case l.si.Failure:return"Failed";case l.si.Running:return"Running";case l.si.Pending:return"Pending";default:return"Unknown"}};return(0,i.wB)((()=>s.cases),((e,t)=>{e===t&&e?.length===t?.length||(0,i.dY)((()=>{f()}))}),{flush:"post"}),(0,i.sV)((()=>{(0,i.dY)((()=>{g()}))})),(0,i.hi)((()=>{v&&(v.destroy(),v=null)})),(e,t)=>((0,i.uX)(),(0,i.CE)("div",r,[(0,i.Lk)("div",{class:"cases-header",ref_key:"headerContainer",ref:m},null,512),(0,i.Lk)("div",{ref_key:"casesContainer",ref:u,class:"cases-content"},null,512)]))}}),u=s(1241);const m=(0,u.A)(h,[["__scopeId","data-v-795f583a"]]);var v=m}}]);
//# sourceMappingURL=828.c5b13821.js.map