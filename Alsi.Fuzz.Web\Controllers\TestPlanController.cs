using Alsi.App.Database;
using Alsi.App.Desktop.Utils;
using Alsi.Common.Utils;
using Alsi.Fuzz.Core.Models.TestPlans;
using Alsi.Fuzz.Core.Service;
using Alsi.Fuzz.Core.Service.Tester;
using Alsi.Fuzz.Core.Storage;
using Alsi.Fuzz.Web.Dto;
using System;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Web.Http;

namespace Alsi.Fuzz.Web.Controllers
{
    public class TestPlanController : WebControllerBase
    {
        private readonly ITestPlanService _testPlanService;

        public TestPlanController()
        {
            var historyService = new TestPlanHistoryService();
            var testPlanStorage = new TestPlanStorage();
            _testPlanService = new TestPlanService(testPlanStorage, historyService);
        }

        [HttpGet]
        public async Task<IHttpActionResult> OpenInExplorer()
        {
            if (!UiUtils.SelectFile(out var filePath, "Fuzz Test Plan", "*.fzp"))
            {
                return BadRequest("UserCanceled");
            }

            if (!filePath.EndsWith(".fzp"))
            {
                return BadRequest("InvalidFileFormat");
            }

            var testPlan = await InternalLoadAsync(filePath);
            FileLocker.LockFile(filePath);
            return Ok(CreateTestPlanDto(filePath, testPlan));
        }

        [HttpPost]
        [ActionName("create")]
        public async Task<IHttpActionResult> Create([FromBody] CreateTestPlanRequest request)
        {
            var testPlan = await _testPlanService.CreateAsync(request.Description);
            var folder = request.Folder;
            if (!Directory.Exists(folder))
            {
                throw new Exception($"Can't find directory: {folder}");
            }

            var fileName = $"{request.Name}.fzp";
            var existedNames = Directory.GetFileSystemEntries(folder).Select(x => Path.GetFileName(x)).ToArray();
            var newFileName = PathUtils.GetAlternativeFileName(fileName, existedNames);
            var filePath = Path.Combine(folder, newFileName);

            await _testPlanService.SaveAsync(testPlan, filePath);

            var testPlanDto = await Open(filePath);
            return Ok(testPlanDto);
        }

        [HttpPost]
        [ActionName("open")]
        public async Task<IHttpActionResult> OpenByPath([FromBody] OpenTestPlanRequest request)
        {
            if (string.IsNullOrEmpty(request.Path) || !File.Exists(request.Path))
            {
                throw new Exception($"Can't find file: {request.Path}");
            }

            if (!request.Path.EndsWith(".fzp"))
            {
                throw new Exception($"The fuzz test plan file should has '.fzp' extension: {request.Path}");
            }

            var testPlanDto = await Open(request.Path);
            return Ok(testPlanDto);
        }

        private async Task<TestPlanDto> Open(string path)
        {
            if (!path.Equals(TestPlanManager.Instance.GetCurrentPlanPath(), StringComparison.OrdinalIgnoreCase))
            {
                if (TesterManager.Instance.IsRunning)
                {
                    throw new Exception($"The tester is running, can't open test plan: {Path.GetFileNameWithoutExtension(path)}");
                }
            }

            var testPlan = await InternalLoadAsync(path);
            FileLocker.LockFile(path);
            return CreateTestPlanDto(path, testPlan);
        }

        [HttpPost]
        [ActionName("close")]
        public IHttpActionResult Close()
        {
            try
            {
                FileLocker.UnlockFile();
                TestPlanManager.Instance.ClearCurrentPlan();
                WindowEnv.SetTitle("Fuzz");
                return Ok();
            }
            catch (Exception ex)
            {
                return InternalServerError(ex);
            }
        }

        [HttpGet]
        [ActionName("current")]
        public IHttpActionResult GetCurrentPlan()
        {
            var currentPlan = TestPlanManager.Instance.GetCurrentPlan();
            if (currentPlan == null)
            {
                return NotFound();
            }
            return Ok(CreateTestPlanDto(TestPlanManager.Instance.GetCurrentPlanPath(), currentPlan));
        }

        [HttpPost]
        [ActionName("checkFileExists")]
        public IHttpActionResult CheckFileExists([FromBody] CheckFileExistsRequest request)
        {
            if (string.IsNullOrEmpty(request.Path))
            {
                return BadRequest("Path cannot be empty");
            }

            bool exists = File.Exists(request.Path);
            return Ok(new { exists });
        }

        [HttpPost]
        [ActionName("updateBasicInfo")]
        public async Task<IHttpActionResult> UpdateBasicInfo([FromBody] UpdateBasicInfoRequest request)
        {
            try
            {
                var currentPlan = TestPlanManager.Instance.GetCurrentPlan();
                if (currentPlan == null)
                {
                    return NotFound();
                }
                var path = TestPlanManager.Instance.GetCurrentPlanPath();
                FileLocker.UnlockFile();
                var testPlan = await _testPlanService.UpdateBasicInfoAsync(path, request.Description);
                FileLocker.LockFile(path);
                return Ok(CreateTestPlanDto(path, testPlan));
            }
            catch (Exception ex)
            {
                return InternalServerError(ex);
            }
        }

        private async Task<TestPlan> InternalLoadAsync(string path)
        {
            var testPlan = await _testPlanService.LoadAsync(path);
            var name = Path.GetFileNameWithoutExtension(path);
            WindowEnv.SetTitle("Fuzz - " + name);
            TestPlanManager.Instance.SetCurrentPlan(testPlan, path);
            return testPlan;
        }

        private TestPlanDto CreateTestPlanDto(string path, TestPlan testPlan)
        {
            var dto = _mapper.Map<TestPlanDto>(testPlan);
            dto.Manifest.Name = Path.GetFileNameWithoutExtension(path);
            dto.Path = path;
            return dto;
        }
    }

    public class CreateTestPlanRequest
    {
        public string Name { get; set; }
        public string Description { get; set; }
        public string Folder { get; set; }
    }

    public class OpenTestPlanRequest
    {
        public string Path { get; set; }
    }

    public class UpdateBasicInfoRequest
    {
        public string Description { get; set; }
    }

    public class CheckFileExistsRequest
    {
        public string Path { get; set; }
    }
}
