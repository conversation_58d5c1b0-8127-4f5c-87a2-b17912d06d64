namespace Alsi.Fuzz.Core.Service.Results
{
    public interface ITestResultWriterService
    {
        ResultContext ResultContext { get; }

        void Begin(TestResult testResult);
        void End();

        CaseResult[] GetCaseResults();
        void BatchAddCaseResult(CaseResult[] caseResults);
        void AddOrUpdateCaseResult(CaseResult caseResult);
        void AddCaseStep(CaseStep caseStep);
        void UpdateCaseStep(CaseStep caseStep);
    }
}
