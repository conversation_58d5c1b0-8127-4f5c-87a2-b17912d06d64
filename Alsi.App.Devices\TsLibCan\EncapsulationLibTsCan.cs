using Alsi.App.Devices.Core;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using TsMaster;
using static Alsi.App.Devices.TsLibCan.TsLibApi;

namespace Alsi.App.Devices.TsLibCan
{
    public class EncapsulationLibTsCan
    {
        private static Dictionary<IntPtr, TCANQueueEvent_Win32> receiveCANCallBackDictionary = new Dictionary<IntPtr, TCANQueueEvent_Win32>();
        private static Dictionary<IntPtr, TCANFDQueueEvent_Win32> receiveCANFDCallBackDictionary = new Dictionary<IntPtr, TCANFDQueueEvent_Win32>();
        private static Dictionary<IntPtr, GCHandle> receiveCanCallBackHandleDictionary = new Dictionary<IntPtr, GCHandle>();
        private static Dictionary<IntPtr, GCHandle> receiveCanfdCallBackHandleDictionary = new Dictionary<IntPtr, GCHandle>();

        public static bool filterErrorFrame = false;
        public static List<CanFrame> ErrorFrameList = new List<CanFrame>();
        public delegate void SendCanDataToTrace(IntPtr deviceHandle, CanFrame dataFrame);
        public static event SendCanDataToTrace smttEvent;

        public static bool isConnected = false;

        public static Dictionary<string, IntPtr> dict = new Dictionary<string, IntPtr>();

        public static void Start(ref IntPtr deviceHandle, string deviceSerial, int tosunChannel, ChannelConfig config)
        {
            // 设置为自动进行总线数据统计
            TsCANApi.tscan_set_auto_calc_bus_statistics(true);
            TsCANApi.tscan_clear_can_bus_statistic();

            // 连接设备
            deviceHandle = IntPtr.Zero;
            uint ret = 0;
            if (dict.ContainsKey(deviceSerial))
            {
                deviceHandle = dict[deviceSerial];
            }
            else
            {
                ret = TsCANApi.tscan_connect(deviceSerial, ref deviceHandle);
                if (ret == (uint)TSCAN_DEF.IDX_ERR_OK || ret == (uint)TSCAN_DEF.IDX_ERR_ALREADY_CONNECTED)
                {
                    dict[deviceSerial] = deviceHandle;
                    isConnected = true;
                }
                else
                {
                    isConnected = false;
                }

                var localDeviceHandle = deviceHandle;

                var receiveCANCallBack = new TCANQueueEvent_Win32((ref TLIBCAN AData) => ReceivedCANMsgCallBack(localDeviceHandle, ref AData));
                receiveCANCallBackDictionary[deviceHandle] = receiveCANCallBack;
                GC.KeepAlive(receiveCANCallBackDictionary[deviceHandle]);
                receiveCanCallBackHandleDictionary[deviceHandle] = GCHandle.Alloc(receiveCANCallBackDictionary[deviceHandle]);
                ret = TsCANApi.tscan_register_event_can(deviceHandle, receiveCANCallBack);
                CheckRet(ret, nameof(TsCANApi.tscan_register_event_can) + $"({deviceHandle})");

                var receiveCANFDCallBack = new TCANFDQueueEvent_Win32((ref TLIBCANFD AData) => ReceivedCANFDMsgCallBack(localDeviceHandle, ref AData));
                receiveCANFDCallBackDictionary[deviceHandle] = receiveCANFDCallBack;
                GC.KeepAlive(receiveCANFDCallBackDictionary[deviceHandle]);
                receiveCanfdCallBackHandleDictionary[deviceHandle] = GCHandle.Alloc(receiveCANFDCallBackDictionary[deviceHandle]);
                ret = TsCANApi.tscan_register_event_canfd(deviceHandle, receiveCANFDCallBack);
                CheckRet(ret, nameof(TsCANApi.tscan_register_event_canfd) + $"({deviceHandle})");
            }

            _config = config;
            if (config.CommunicationType == CommunicationType.Can)
            {
                SetCanBaudrate(deviceHandle, tosunChannel);
            }
            else if (config.CommunicationType == CommunicationType.CanFd)
            {
                SetCanFdBaudrate(deviceHandle, tosunChannel);
            }
            else
            {
                throw new NotImplementedException($"Unknown {nameof(config.CommunicationType)}");
            }

            ErrorFrameList.Clear();
        }

        public static void Stop(IntPtr deviceHandle, string deviceSerial)
        {

            if (dict.ContainsKey(deviceSerial))
            {
                dict.Remove(deviceSerial);
            }
            if (TsCANApi.tscan_disconnect_by_handle(deviceHandle) == (uint)TSCAN_DEF.IDX_ERR_OK)
            {
                isConnected = false;
            }
            else
            {
                isConnected = true;
            }
            if (receiveCANCallBackDictionary.ContainsKey(deviceHandle))
            {
                var receiveCANCallBack = receiveCANCallBackDictionary[deviceHandle];
                var ret = TsCANApi.tscan_unregister_event_can(deviceHandle, receiveCANCallBack);
                CheckRet(ret, nameof(TsCANApi.tscan_unregister_event_can) + $"({deviceHandle})");
                receiveCANCallBackDictionary.Remove(deviceHandle);
            }

            if (receiveCANFDCallBackDictionary.ContainsKey(deviceHandle))
            {
                var receiveCANFDCallBack = receiveCANFDCallBackDictionary[deviceHandle];
                var ret = TsCANApi.tscan_unregister_event_canfd(deviceHandle, receiveCANFDCallBack);
                CheckRet(ret, nameof(TsCANApi.tscan_unregister_event_canfd) + $"({deviceHandle})");
                receiveCANFDCallBackDictionary.Remove(deviceHandle);
            }
            if (receiveCanCallBackHandleDictionary.ContainsKey(deviceHandle))
            {
                if (receiveCanCallBackHandleDictionary[deviceHandle].IsAllocated)
                {
                    receiveCanCallBackHandleDictionary[deviceHandle].Free();
                }
            }
            if (receiveCanfdCallBackHandleDictionary.ContainsKey(deviceHandle))
            {
                if (receiveCanfdCallBackHandleDictionary[deviceHandle].IsAllocated)
                {
                    receiveCanfdCallBackHandleDictionary[deviceHandle].Free();
                }
            }
        }

        public static void ReceivedCANMsgCallBack(IntPtr deviceHandle, ref TLIBCAN AData)
        {
            var dataFrame = new CanFrame(
                AData.FIdentifier,
                AData.FData,
                AData.FTimeUS,
                AData.FDLC,
                AData.FIdxChn,
                AData.FProperties,
                false,
                AData.FIsExt);

            ReceiveDataFrame(deviceHandle, dataFrame);
        }

        private static void ReceiveDataFrame(IntPtr deviceHandle, CanFrame dataFrame)
        {
            if (!filterErrorFrame)
            {
                smttEvent?.Invoke(deviceHandle, dataFrame);
                return;
            }

            if (dataFrame.IsErrorFrame)
            {
                if (ErrorFrameList.Count == 0)
                {
                    ErrorFrameList.Add(dataFrame);
                    smttEvent?.Invoke(deviceHandle, dataFrame);
                }
                else if (ErrorFrameList.Count == 1)
                {
                    ErrorFrameList.Add(dataFrame);
                }
                else
                {
                    ErrorFrameList[1] = dataFrame;
                }
            }
            else
            {
                if (ErrorFrameList.Count == 2)
                {
                    smttEvent?.Invoke(deviceHandle, ErrorFrameList[1]);
                }

                ErrorFrameList.Clear();
                smttEvent?.Invoke(deviceHandle, dataFrame);
            }
        }

        public static DeviceChannel[] GetDeviceChannels()
        {
            uint deviceCount = 0;
            var ret = TsCANApi.tscan_scan_devices(ref deviceCount);
            if (ret != 0)
            {
                // 没获取到设备
                AppEnv.Logger.Info($"Failed to call {nameof(TsCANApi.tscan_scan_devices)}: {ret}");
                return Array.Empty<DeviceChannel>();
            }

            var deviceChannels = new List<DeviceChannel>();
            for (uint i = 0; i < deviceCount; i++)
            {
                var factory = string.Empty;
                var product = string.Empty;
                var serial = string.Empty;

                ret = TsCANApi.GetDeviceInfo(i, ref factory, ref product, ref serial);
                if (ret != 0)
                {
                    //没有获取到设备信息
                    AppEnv.Logger.Info($"Failed to call {nameof(TsCANApi.GetDeviceInfo)}: {ret}");
                    continue;
                }
                if (string.IsNullOrWhiteSpace(product))
                {
                    continue;
                }
                if (!TosunConsts.LinChannels.ContainsKey(product) &&
                    !TosunConsts.CanChannels.ContainsKey(product))
                {
                    var deviceName = string.Empty;
                    if (product.ToLower().Contains("canfd"))
                    {
                        var beforeIndex = product.ToLower().IndexOf("canfd");
                        deviceName = product.Substring(0, beforeIndex).TrimEnd();
                        var canChannels = GetTosunCanChannels(product);
                        var channels = Enumerable.Range(0, canChannels);
                        foreach (var channel in channels)
                        {
                            var deviceChannel = new DeviceChannel();
                            deviceChannel.CommunicationType = CommunicationType.CanFd;
                            deviceChannel.Manufacturer = Manufacturer.Tosun;
                            deviceChannel.TsLibCanChannelInfo = new TsLibCanChannelInfo
                            {
                                TosunChanel = channel,
                                TosunDisplayChanel = channel + 1,
                            };

                            deviceChannel.DeviceFactory = factory;
                            deviceChannel.DeviceType = $"{deviceName} CANFD (Chn {deviceChannel.TsLibCanChannelInfo.TosunDisplayChanel})";
                            deviceChannel.DeviceSerial = serial;
                            deviceChannels.Add(deviceChannel);
                        }
                    }
                    if (product.ToLower().Contains("lin"))
                    {
                        var canChannels = GetTosunLinChannels(product);
                        var channels = Enumerable.Range(0, canChannels);
                        if (!string.IsNullOrWhiteSpace(deviceName))
                        {
                            var name1Index = product.ToLower().IndexOf("lin");
                            var name2Index = product.ToLower().IndexOf("canfd");

                            if (name1Index != -1 && name1Index <= name2Index)
                            {
                                deviceName = product.Substring(0, name1Index);
                            }
                            else if (name2Index != -1 && name2Index < name1Index)
                            {
                                deviceName = product.Substring(0, name2Index).TrimEnd();
                            }

                        }
                        foreach (var channel in channels)
                        {
                            var deviceChannel = new DeviceChannel();
                            deviceChannel.CommunicationType = CommunicationType.Lin;
                            deviceChannel.Manufacturer = Manufacturer.Tosun;
                            deviceChannel.TsLibCanChannelInfo = new TsLibCanChannelInfo
                            {
                                TosunChanel = channel,
                                TosunDisplayChanel = channel + 1,
                            };

                            deviceChannel.DeviceFactory = factory;

                            deviceChannel.DeviceType = $"{deviceName} LIN (Chn {deviceChannel.TsLibCanChannelInfo.TosunDisplayChanel})";
                            deviceChannel.DeviceSerial = serial;
                            deviceChannels.Add(deviceChannel);
                        }
                    }

                    if (product.ToLower().Contains("can") && !product.ToLower().Contains("canfd"))
                    {
                        var beforeIndex = product.ToLower().IndexOf("can");
                        deviceName = product.Substring(0, beforeIndex).TrimEnd();
                        var canChannels = GetTosunCanChannels(product);
                        var channels = Enumerable.Range(0, canChannels);
                        foreach (var channel in channels)
                        {
                            var deviceChannel = new DeviceChannel();
                            deviceChannel.CommunicationType = CommunicationType.Can;
                            deviceChannel.Manufacturer = Manufacturer.Tosun;
                            deviceChannel.TsLibCanChannelInfo = new TsLibCanChannelInfo
                            {
                                TosunChanel = channel,
                                TosunDisplayChanel = channel + 1,
                            };

                            deviceChannel.DeviceFactory = factory;
                            deviceChannel.DeviceType = $"{deviceName} CAN (Chn {deviceChannel.TsLibCanChannelInfo.TosunDisplayChanel})";
                            deviceChannel.DeviceSerial = serial;
                            deviceChannels.Add(deviceChannel);
                        }
                    }
                }
                else
                {
                    var channelCount = TsLibApi.GetChannelCount(product);
                    var channels = Enumerable.Range(0, channelCount);
                    foreach (var channel in channels)
                    {
                        var deviceChannel = new DeviceChannel();
                        if (product.ToUpper().Split(new[] { ' ' }).Contains("LIN"))
                        {
                            deviceChannel.CommunicationType = CommunicationType.Lin;
                        }
                        else if (product.ToUpper().Contains("CANFD"))
                        {
                            deviceChannel.CommunicationType = CommunicationType.CanFd;
                        }
                        else
                        {
                            deviceChannel.CommunicationType = CommunicationType.Can;
                        }

                        deviceChannel.Manufacturer = Manufacturer.Tosun;
                        deviceChannel.TsLibCanChannelInfo = new TsLibCanChannelInfo
                        {
                            TosunChanel = channel,
                            TosunDisplayChanel = channel + 1,
                        };

                        deviceChannel.DeviceFactory = factory;
                        deviceChannel.DeviceType = $"{product}(Chn {deviceChannel.TsLibCanChannelInfo.TosunDisplayChanel})";
                        deviceChannel.DeviceSerial = serial;
                        deviceChannels.Add(deviceChannel);
                    }
                }
            }

            return deviceChannels.ToArray();
        }

        private static int GetTosunCanChannels(string product)
        {
            var channelCount = 1;
            if (product.ToLower().Contains("canfd"))
            {
                int startIndex = product.ToLower().IndexOf("canfd");
                int endIndex = product.IndexOf('.');

                // 检查条件：确保 startIndex 在 endIndex 之前，并且两个字符都存在  
                if (startIndex >= 0 && endIndex > startIndex)
                {
                    // 截取并返回两个字符之间的字符串  
                    var canChannelsStr = product.Substring(startIndex, endIndex - startIndex);
                    var canfdStr = canChannelsStr.ToUpper().Replace("CANFD", "");
                    var isSuccess = int.TryParse(canfdStr, out var canChannels);
                    if (isSuccess)
                    {
                        return canChannels;
                    }
                }
            }

            return channelCount;
        }

        private static int GetTosunLinChannels(string product)
        {
            var channelCount = 1;
            if (product.ToLower().Contains("lin"))
            {
                int startIndex = product.ToLower().IndexOf("lin");
                if (startIndex != -1)
                {
                    var linChannelsStr = product.Substring(startIndex);
                    var linCount = linChannelsStr.ToUpper().Replace("LIN", "");
                    var isSuccess = int.TryParse(linCount, out var linChannels);
                    if (isSuccess)
                    {
                        return linChannels;
                    }
                }
            }
            return channelCount;
        }

        public static void SetSelfAck(IntPtr deviceHandle, int tosunChannel, bool enableTesterSelfAck)
        {
            var controllerMode = enableTesterSelfAck ? TLIBCANFDControllerMode.lfdmNormal : TLIBCANFDControllerMode.lfdmACKOff;

            var retCanFD = TsCANApi.tscan_config_canfd_by_baudrate(
                deviceHandle,
                (CHANNEL_INDEX)tosunChannel,
                _config.CanFdArbitrationBitRate / 1000,
                _config.CanFdDataBitRate / 1000,
                TLIBCANFDControllerType.lfdtISOCAN,
                controllerMode,
                0);
            CheckRet(retCanFD, nameof(TsCANApi.tscan_config_canfd_by_baudrate) + $"({deviceHandle}, {tosunChannel})");
        }

        private static ChannelConfig _config = new ChannelConfig();

        private static uint SetCanBaudrate(IntPtr deviceHandle, int tosunChannel)
        {
            uint a120OhmConnected = 0;
            uint ret = TsCANApi.tscan_config_can_by_baudrate(
                deviceHandle,
                (CHANNEL_INDEX)tosunChannel,
                _config.CanBitrate / 1000,
                a120OhmConnected);
            CheckRet(ret, nameof(TsCANApi.tscan_config_can_by_baudrate) + $"({deviceHandle}, {tosunChannel})");
            return ret;
        }

        private static uint SetCanFdBaudrate(IntPtr deviceHandle, int tosunChannel)
        {
            uint a120OhmConnected = 0;
            var retCanFD = TsCANApi.tscan_config_canfd_by_baudrate(
                deviceHandle,
                (CHANNEL_INDEX)tosunChannel,
                _config.CanFdArbitrationBitRate / 1000,
                _config.CanFdDataBitRate / 1000,
                TLIBCANFDControllerType.lfdtISOCAN,
                TLIBCANFDControllerMode.lfdmNormal,
                a120OhmConnected);
            CheckRet(retCanFD, nameof(TsCANApi.tscan_config_canfd_by_baudrate) + $"({deviceHandle}, {tosunChannel})");
            return retCanFD;
        }

        public static void SendCanFrameData(IntPtr deviceHandle, int tosunChannel, int canId, byte dlc, byte[] data, bool rtr, bool isExt)
        {
            if (!isConnected)
            {
                return;
            }
            var msg = new TLIBCAN((CHANNEL_INDEX)tosunChannel, canId, true, isExt, rtr, dlc, data);
            var ret = TsCANApi.tscan_transmit_can_async(deviceHandle, ref msg);
            CheckRet(ret, nameof(TsCANApi.tscan_transmit_can_async) + $"({deviceHandle}, {tosunChannel})", false);
        }

        public static void SendCanFDFrameData(IntPtr deviceHandle, int tosunChannel, int canId, byte dlc, byte[] data, bool rtr, bool isExt)
        {
            var list = new List<byte>();
            list.AddRange(data);
            while (list.Count < dlc)
            {
                list.Add(0xCC);
            }
            if (!isConnected) return;
            TLIBCANFD msg = new TLIBCANFD((CHANNEL_INDEX)tosunChannel, canId, false, isExt, rtr, dlc, list.ToArray());
            msg.FIsFD = true;
            msg.FIsBRS = true;
            var ret = TsCANApi.tscan_transmit_canfd_async(deviceHandle, ref msg);
            CheckRet(ret, nameof(TsCANApi.tscan_transmit_canfd_async) + $"({deviceHandle}, {tosunChannel})", false);
        }

        public static void ReceivedCANFDMsgCallBack(IntPtr deviceHandle, ref TLIBCANFD AData)
        {
            string binaryString = Convert.ToString(AData.FFDProperties, 2);
            char firstBit = binaryString[0]; //0: 普通 CAN报文；1: CANFD报文

            // 注：CANFD报文中的错误帧，仍然需要转发
            // 使用同星的 CANFD 设备时，错误帧是通过 CANFD 回调接收的
            if (firstBit == '0' && AData.FIdentifier != CanFrame.ErrorFrameId)
            {
                return;
            }

            var dataFrame = new CanFrame(
                AData.FIdentifier,
                // TS LIB CANFD 中返回的数据中，应根据 DLC 截取数据
                AData.FData.Take(TLIBCANFD.DLC_DATA_BYTE_CNT[AData.FDLC]).ToArray(),
                AData.FTimeUS,
                AData.FDLC,
                AData.FIdxChn,
                AData.FProperties,
                AData.FIsFD,
                AData.FIsExt);
            dataFrame.IsBrs = AData.FIsBRS;
            dataFrame.IsEsi = AData.FIsESI;
            ReceiveDataFrame(deviceHandle, dataFrame);
        }

        public static void OperateLog(string message)
        {
            AppEnv.Logger.Info($"TsLibCan -> {message}");
        }

        public static object StartCyclicSendAframeCanData(IntPtr deviceHandle, int channel, int aId, byte[] data, float periodMS)
        {
            if (!isConnected)
            {
                OperateLog("Not Connected!");
                return null;
            }

            AppEnv.Logger.Info($"{nameof(TsCANApi.tscan_add_cyclic_msg_can)} -> {channel} -> {aId}");

            var tlibCan = new TLIBCAN((CHANNEL_INDEX)channel, aId, true, false, false, 8, data);

            var ret = TsCANApi.tscan_add_cyclic_msg_can(deviceHandle, ref tlibCan, periodMS);
            CheckRet(ret, nameof(TsCANApi.tscan_add_cyclic_msg_can) + $"({deviceHandle}, {channel})");
            return tlibCan;
        }

        public static void StopCyclicSendAframeCanData(IntPtr deviceHandle, ref TLIBCAN tlibCan)
        {
            if (!isConnected)
            {
                OperateLog("Not Connected!");
                return;
            }

            AppEnv.Logger.Info($"{nameof(TsCANApi.tscan_delete_cyclic_msg_can)} -> {tlibCan.FIdxChn} -> {tlibCan.FIdentifier}");

            var ret = TsCANApi.tscan_delete_cyclic_msg_can(deviceHandle, ref tlibCan);
            CheckRet(ret, nameof(TsCANApi.tscan_delete_cyclic_msg_can) + $"({deviceHandle})");
        }
    }
}
