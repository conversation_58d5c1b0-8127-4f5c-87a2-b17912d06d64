using Alsi.App.Database;
using Alsi.App.Database.Midwares;
using Alsi.Fuzz.Core.Models.History;
using Alsi.Fuzz.Core.Models.TestPlans;
using System;
using System.Threading.Tasks;

namespace Alsi.Fuzz.Core.Service
{
    public class TestPlanHistoryService : ITestPlanHistoryService
    {
        private readonly DbContext _dbContext;
        private IFreeSql _appSql => _dbContext.FreeSql;

        public TestPlanHistoryService(DbContext dbContext = null)
        {
            _dbContext = dbContext ?? DbEnv.AppDbContext;
        }

        public async Task RecordAccessAsync(TestPlan plan, string filePath)
        {
            // 检查是否存在相同路径的记录
            var existingRecord = await _appSql.Select<TestPlanHistory>()
                .Where(x => x.FilePath == filePath && !x.IsDeleted)
                .FirstAsync();

            if (existingRecord != null)
            {
                // 更新现有记录
                existingRecord.LastAccessTime = DateTime.Now;
                existingRecord.LastModified = plan.Manifest.Modified;

                await _appSql.Update<TestPlanHistory>()
                    .SetSource(existingRecord)
                    .ExecuteAffrowsAsync();
            }
            else
            {
                // 创建新记录
                var history = new TestPlanHistory
                {
                    Id = Guid.NewGuid(),
                    FilePath = filePath,
                    LastAccessTime = DateTime.Now,
                    LastModified = plan.Manifest.Modified,
                    IsDeleted = false
                };

                await _appSql.Insert(history).ExecuteInsertedAsync();
            }
        }

        public async Task<TestPlanHistory[]> GetRecentHistoryAsync(int count = 10)
        {
            var items = await _appSql.Select<TestPlanHistory>()
                .Where(x => !x.IsDeleted)
                .OrderByDescending(x => x.LastAccessTime)
                .Take(count)
                .ToListAsync();
            return items.ToArray();
        }

        public async Task ClearHistoryAsync()
        {
            await _appSql.Update<TestPlanHistory>()
                .Where(x => !x.IsDeleted)
                .Set(x => x.IsDeleted, true)
                .ExecuteAffrowsAsync();
        }

        public async Task DeleteRecordAsync(string filePath)
        {
            if (string.IsNullOrEmpty(filePath))
            {
                throw new ArgumentNullException(nameof(filePath));
            }

            // 查找记录
            var record = await _appSql.Select<TestPlanHistory>()
                .Where(x => x.FilePath == filePath && !x.IsDeleted)
                .FirstAsync();

            if (record != null)
            {
                // 标记为已删除
                record.IsDeleted = true;
                await _appSql.Update<TestPlanHistory>()
                    .SetSource(record)
                    .ExecuteAffrowsAsync();
            }
        }
    }
}
