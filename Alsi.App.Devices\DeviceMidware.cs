using Alsi.App.Core;
using Alsi.Common.Utils;
using System;
using System.IO;

namespace Alsi.App.Devices
{
    public class DeviceMidware : IBuildAppMidware
    {
        public string Name => "Device";

        private static void ConfigureNativeDllSearchPath()
        {
            // 首先设置 DLL 搜索策略
            NativeDllUtils.ConfigureSearchPolicy();

            // 添加DLL文件夹到搜索路径
            string basePath = AppDomain.CurrentDomain.BaseDirectory;
            string[] folderNames = new[] { "libTsCan", "vector", "blf" };

            foreach (var folderName in folderNames)
            {
                var dllPath = Path.Combine(basePath, "x64", folderName);
                NativeDllUtils.AddSearchPath(dllPath);
            }
        }

        public void Build(WebHostApp webHostApp)
        {
            ConfigureNativeDllSearchPath();
        }
    }
}
