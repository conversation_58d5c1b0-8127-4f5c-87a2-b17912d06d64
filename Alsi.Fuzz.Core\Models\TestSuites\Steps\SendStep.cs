using System.Xml.Serialization;

namespace Alsi.Fuzz.Core.Models.TestSuites.Steps
{
    public class SendStep : StepBase
    {
        [XmlElement("frame")]
        public Frame Frame { get; set; }

        private int? _cycleMs;

        [XmlIgnore]
        public int? CycleMs
        {
            get { return _cycleMs; }
            set { _cycleMs = value; }
        }

        [XmlAttribute("cycle-ms")]
        public string CycleMsString
        {
            get => _cycleMs.HasValue ? _cycleMs.Value.ToString() : null;
            set => _cycleMs = string.IsNullOrEmpty(value) ? (int?)null : int.Parse(value);
        }
    }
}
