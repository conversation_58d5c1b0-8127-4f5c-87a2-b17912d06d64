{"version": 3, "file": "js/644.d36ffcbf.js", "mappings": "iNAGA,MAAMA,EAAa,CAAEC,MAAO,2BACtBC,EAAa,CAAED,MAAO,qBACtBE,EAAa,CAAEF,MAAO,cACtBG,EAAa,CAAEH,MAAO,sBACtBI,EAAa,CAAEJ,MAAO,aACtBK,EAAa,CAAEL,MAAO,uBACtBM,EAAa,CAAEN,MAAO,kBACtBO,EAAa,CACjBC,IAAK,EACLR,MAAO,uBAEHS,EAAa,CAAED,IAAK,GACpBE,EAAc,CAClBF,IAAK,EACLR,MAAO,oBAEHW,EAAc,CAAEX,MAAO,gBACvBY,EAAc,CAAEZ,MAAO,2BACvBa,EAAc,CAAEb,MAAO,gBAO7B,OAA4Bc,EAAAA,EAAAA,IAAiB,CAC3CC,OAAQ,eACRC,KAAAA,CAAMC,GC6CR,MAAMC,EAAQC,EAAAA,EAAgBC,WACxBC,GAAWC,EAAAA,EAAAA,KAAS,IAAMJ,EAAMK,cAChCC,GAAUF,EAAAA,EAAAA,KAAS,IAAMJ,EAAMO,YAE/BC,GAAYC,EAAAA,EAAAA,KAAI,GAChBC,GAAkBD,EAAAA,EAAAA,IAAI,IACtBE,GAAWF,EAAAA,EAAAA,KAAI,GAEfG,EAAkBC,gBAChBZ,EAAAA,EAAgBa,gBAAgB,EAGlCC,EAAkBC,IACtB,IAAKA,EAAU,MAAO,IACtB,MAAMC,EAAOD,aAAoBE,KAAOF,EAAW,IAAIE,KAAKF,GAC5D,OAAOC,EAAKE,gBAAgB,EAGxBC,EAAeA,KACnBV,EAAgBW,MAAQlB,EAASkB,OAAOC,SAASC,aAAe,GAChEf,EAAUa,OAAQ,CAAI,EAGlBG,EAAgBA,KACpBhB,EAAUa,OAAQ,CAAK,EAGnBI,EAAkBZ,UACtB,GAAKV,EAASkB,MAAd,CAEAV,EAASU,OAAQ,EACjB,UACQpB,EAAAA,EAAgByB,gBAAgBhB,EAAgBW,OACtDb,EAAUa,OAAQ,C,CAClB,QACAV,EAASU,OAAQ,C,CAPQ,C,EAWvBM,EAAcA,KAClBf,GAAiB,EDpCnB,OCuCAgB,EAAAA,EAAAA,KAAU,KACRhB,GAAiB,IDxCZ,CAACiB,EAAUC,KAChB,MAAMC,GAAuBC,EAAAA,EAAAA,IAAkB,aACzCC,GAAoBD,EAAAA,EAAAA,IAAkB,UACtCE,GAAyBF,EAAAA,EAAAA,IAAkB,eAC3CG,GAAsBH,EAAAA,EAAAA,IAAkB,YACxCI,GAAsBJ,EAAAA,EAAAA,IAAkB,YACxCK,GAAwBL,EAAAA,EAAAA,IAAkB,cAC1CM,GAAkCN,EAAAA,EAAAA,IAAkB,wBACpDO,GAA6BP,EAAAA,EAAAA,IAAkB,mBAC/CQ,GAAqBC,EAAAA,EAAAA,IAAkB,WAE7C,OAAOC,EAAAA,EAAAA,MAAiBC,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAoB,MAAO/D,EAAY,EAC3EgE,EAAAA,EAAAA,IAAoB,MAAO9D,EAAY,EACrC8D,EAAAA,EAAAA,IAAoB,MAAO7D,EAAY,EACrC6D,EAAAA,EAAAA,IAAoB,MAAO5D,EAAY,EACrC4D,EAAAA,EAAAA,IAAoB,KAAM,MAAMC,EAAAA,EAAAA,IAAiB3C,EAASkB,OAAOC,SAASyB,MAAO,IACjFC,EAAAA,EAAAA,IAAajB,EAAsB,CACjCkB,KAAM,UACNC,QAASvB,EACTwB,MAAMC,EAAAA,EAAAA,IAAOC,EAAAA,SACbC,OAAQ,GACRC,KAAM,SACL,KAAM,EAAG,CAAC,cAGjBV,EAAAA,EAAAA,IAAoB,MAAO3D,EAAY,EACrC8D,EAAAA,EAAAA,IAAaf,EAAmB,CAAEsB,KAAM,SAAW,CACjDC,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBC,EAAAA,EAAAA,IAAiB,aAAcZ,EAAAA,EAAAA,IAAiB/B,EAAeZ,EAASkB,OAAOC,SAASqC,UAAW,MAErGC,EAAG,KAELZ,EAAAA,EAAAA,IAAaf,EAAmB,CAC9BsB,KAAM,QACNN,KAAM,QACL,CACDO,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBC,EAAAA,EAAAA,IAAiB,cAAeZ,EAAAA,EAAAA,IAAiB/B,EAAeZ,EAASkB,OAAOC,SAASuC,WAAY,MAEvGD,EAAG,SAITf,EAAAA,EAAAA,IAAoB,MAAO1D,EAAY,EACrC0D,EAAAA,EAAAA,IAAoB,MAAOzD,EAAY,CACrC0C,EAAO,KAAOA,EAAO,IAAKe,EAAAA,EAAAA,IAAoB,KAAM,KAAM,eAAgB,IACxErC,EAAUa,OAcRyC,EAAAA,EAAAA,IAAoB,IAAI,KAbvBnB,EAAAA,EAAAA,OAAcoB,EAAAA,EAAAA,IAAahC,EAAsB,CAChDzC,IAAK,EACL2D,KAAM,UACNe,KAAM,GACNT,KAAM,QACNL,QAAS9B,EACT+B,MAAMC,EAAAA,EAAAA,IAAOa,EAAAA,OACZ,CACDT,SAASC,EAAAA,EAAAA,KAAS,IAAM3B,EAAO,KAAOA,EAAO,GAAK,EAChD4B,EAAAA,EAAAA,IAAiB,cAEnBE,EAAG,GACF,EAAG,CAAC,YAGZtD,EAAQe,QACJsB,EAAAA,EAAAA,OAAcoB,EAAAA,EAAAA,IAAa7B,EAAwB,CAClD5C,IAAK,EACL4E,KAAM,EACNC,SAAU,QAEXxB,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAoBwB,EAAAA,GAAW,CAAE9E,IAAK,GAAK,CACtDkB,EAAUa,QAUPsB,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAoB,MAAOpD,EAAa,EACrDwD,EAAAA,EAAAA,IAAaZ,EAAqB,CAChCiC,WAAY3D,EAAgBW,MAC5B,sBAAuBS,EAAO,KAAOA,EAAO,GAAMwC,GAAkB5D,EAAiBW,MAAQiD,GAC7FrB,KAAM,WACNiB,KAAM,EACNK,YAAa,8BACbC,UAAW,MACX,kBAAmB,IAClB,KAAM,EAAG,CAAC,gBACb3B,EAAAA,EAAAA,IAAoB,MAAOpD,EAAa,EACtCuD,EAAAA,EAAAA,IAAajB,EAAsB,CACjCkB,KAAM,UACNC,QAASzB,EACTnB,QAASK,EAASU,OACjB,CACDmC,SAASC,EAAAA,EAAAA,KAAS,IAAM3B,EAAO,KAAOA,EAAO,GAAK,EAChD4B,EAAAA,EAAAA,IAAiB,cAEnBE,EAAG,GACF,EAAG,CAAC,aACPZ,EAAAA,EAAAA,IAAajB,EAAsB,CAAEmB,QAAS1B,GAAiB,CAC7DgC,SAASC,EAAAA,EAAAA,KAAS,IAAM3B,EAAO,KAAOA,EAAO,GAAK,EAChD4B,EAAAA,EAAAA,IAAiB,cAEnBE,EAAG,WAlCRjB,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAoB,MAAOvD,EAAY,CACnDc,EAASkB,OAAOC,SAASC,cACrBoB,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAoB,IAAKrD,GAAYuD,EAAAA,EAAAA,IAAiB3C,EAASkB,OAAOC,SAASC,aAAc,MAC3GoB,EAAAA,EAAAA,OAAcoB,EAAAA,EAAAA,IAAa5B,EAAqB,CAC/C7C,IAAK,EACLiC,YAAa,iBACb,aAAc,WAgCzB,QAETsB,EAAAA,EAAAA,IAAoB,MAAOnD,EAAa,CACtCoC,EAAO,KAAOA,EAAO,IAAKe,EAAAA,EAAAA,IAAoB,KAAM,KAAM,0BAA2B,KACrFG,EAAAA,EAAAA,IAAaT,EAA4B,CACvCkC,OAAQ,GACRC,OAAQ,GACP,CACDlB,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBT,EAAAA,EAAAA,IAAaV,EAAiC,CAAEqC,MAAO,aAAe,CACpEnB,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBT,EAAAA,EAAAA,IAAaX,EAAuB,CAClCuC,QAASzE,EAASkB,OAAOwD,KACzBC,UAAW,OACV,CACDtB,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBZ,EAAAA,EAAAA,IAAoB,MAAOlD,GAAamD,EAAAA,EAAAA,IAAiB3C,EAASkB,OAAOwD,MAAO,MAElFjB,EAAG,GACF,EAAG,CAAC,eAETA,EAAG,OAGPA,EAAG,SAGJ,CACH,CAACpB,EAAoBlC,EAAQe,QAC7B,CAEJ,I,UEpNA,MAAM0D,GAA2B,OAAgB,EAAQ,CAAC,CAAC,YAAY,qBAEvE,O", "sources": ["webpack://fuzz-web/./src/views/testplan/BasicSetting.vue?9517", "webpack://fuzz-web/./src/views/testplan/BasicSetting.vue", "webpack://fuzz-web/./src/views/testplan/BasicSetting.vue?06f2"], "sourcesContent": ["import { defineComponent as _defineComponent } from 'vue'\nimport { toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, unref as _unref, resolveComponent as _resolveComponent, createVNode as _createVNode, createTextVNode as _createTextVNode, withCtx as _withCtx, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, createElementBlock as _createElementBlock, Fragment as _Fragment, resolveDirective as _resolveDirective, withDirectives as _withDirectives } from \"vue\"\n\nconst _hoisted_1 = { class: \"basic-setting-container\" }\nconst _hoisted_2 = { class: \"plan-name-section\" }\nconst _hoisted_3 = { class: \"plan-title\" }\nconst _hoisted_4 = { class: \"title-with-refresh\" }\nconst _hoisted_5 = { class: \"plan-meta\" }\nconst _hoisted_6 = { class: \"description-section\" }\nconst _hoisted_7 = { class: \"section-header\" }\nconst _hoisted_8 = {\n  key: 0,\n  class: \"description-content\"\n}\nconst _hoisted_9 = { key: 0 }\nconst _hoisted_10 = {\n  key: 1,\n  class: \"description-edit\"\n}\nconst _hoisted_11 = { class: \"edit-actions\" }\nconst _hoisted_12 = { class: \"additional-info-section\" }\nconst _hoisted_13 = { class: \"path-display\" }\n\nimport { ref, onMounted, computed } from 'vue';\r\nimport { Edit, Refresh } from '@element-plus/icons-vue';\r\nimport { testPlanService } from '@/services/testPlanService';\r\n\r\n\nexport default /*@__PURE__*/_defineComponent({\n  __name: 'BasicSetting',\n  setup(__props) {\n\r\nconst state = testPlanService.getState();\r\nconst testPlan = computed(() => state.currentPlan);\r\nconst loading = computed(() => state.isLoading);\r\n\r\nconst isEditing = ref(false);\r\nconst editDescription = ref('');\r\nconst isSaving = ref(false);\r\n\r\nconst loadCurrentPlan = async () => {\r\n  await testPlanService.getCurrentPlan();\r\n};\r\n\r\nconst formatDateTime = (dateTime?: string | Date) => {\r\n  if (!dateTime) return '-';\r\n  const date = dateTime instanceof Date ? dateTime : new Date(dateTime);\r\n  return date.toLocaleString();\r\n};\r\n\r\nconst startEditing = () => {\r\n  editDescription.value = testPlan.value?.manifest.description || '';\r\n  isEditing.value = true;\r\n};\r\n\r\nconst cancelEditing = () => {\r\n  isEditing.value = false;\r\n};\r\n\r\nconst saveDescription = async () => {\r\n  if (!testPlan.value) return;\r\n  \r\n  isSaving.value = true;\r\n  try {\r\n    await testPlanService.updateBasicInfo(editDescription.value);\r\n    isEditing.value = false;\r\n  } finally {\r\n    isSaving.value = false;\r\n  }\r\n};\r\n\r\nconst refreshData = () => {\r\n  loadCurrentPlan();\r\n};\r\n\r\nonMounted(() => {\r\n  loadCurrentPlan();\r\n});\r\n\nreturn (_ctx: any,_cache: any) => {\n  const _component_el_button = _resolveComponent(\"el-button\")!\n  const _component_el_tag = _resolveComponent(\"el-tag\")!\n  const _component_el_skeleton = _resolveComponent(\"el-skeleton\")!\n  const _component_el_empty = _resolveComponent(\"el-empty\")!\n  const _component_el_input = _resolveComponent(\"el-input\")!\n  const _component_el_tooltip = _resolveComponent(\"el-tooltip\")!\n  const _component_el_descriptions_item = _resolveComponent(\"el-descriptions-item\")!\n  const _component_el_descriptions = _resolveComponent(\"el-descriptions\")!\n  const _directive_loading = _resolveDirective(\"loading\")!\n\n  return _withDirectives((_openBlock(), _createElementBlock(\"div\", _hoisted_1, [\n    _createElementVNode(\"div\", _hoisted_2, [\n      _createElementVNode(\"div\", _hoisted_3, [\n        _createElementVNode(\"div\", _hoisted_4, [\n          _createElementVNode(\"h2\", null, _toDisplayString(testPlan.value?.manifest.name), 1),\n          _createVNode(_component_el_button, {\n            type: \"primary\",\n            onClick: refreshData,\n            icon: _unref(Refresh),\n            circle: \"\",\n            size: \"small\"\n          }, null, 8, [\"icon\"])\n        ])\n      ]),\n      _createElementVNode(\"div\", _hoisted_5, [\n        _createVNode(_component_el_tag, { size: \"small\" }, {\n          default: _withCtx(() => [\n            _createTextVNode(\"Created: \" + _toDisplayString(formatDateTime(testPlan.value?.manifest.created)), 1)\n          ]),\n          _: 1\n        }),\n        _createVNode(_component_el_tag, {\n          size: \"small\",\n          type: \"info\"\n        }, {\n          default: _withCtx(() => [\n            _createTextVNode(\"Modified: \" + _toDisplayString(formatDateTime(testPlan.value?.manifest.modified)), 1)\n          ]),\n          _: 1\n        })\n      ])\n    ]),\n    _createElementVNode(\"div\", _hoisted_6, [\n      _createElementVNode(\"div\", _hoisted_7, [\n        _cache[2] || (_cache[2] = _createElementVNode(\"h4\", null, \"Description\", -1)),\n        (!isEditing.value)\n          ? (_openBlock(), _createBlock(_component_el_button, {\n              key: 0,\n              type: \"primary\",\n              text: \"\",\n              size: \"small\",\n              onClick: startEditing,\n              icon: _unref(Edit)\n            }, {\n              default: _withCtx(() => _cache[1] || (_cache[1] = [\n                _createTextVNode(\" Edit \")\n              ])),\n              _: 1\n            }, 8, [\"icon\"]))\n          : _createCommentVNode(\"\", true)\n      ]),\n      (loading.value)\n        ? (_openBlock(), _createBlock(_component_el_skeleton, {\n            key: 0,\n            rows: 3,\n            animated: \"\"\n          }))\n        : (_openBlock(), _createElementBlock(_Fragment, { key: 1 }, [\n            (!isEditing.value)\n              ? (_openBlock(), _createElementBlock(\"div\", _hoisted_8, [\n                  (testPlan.value?.manifest.description)\n                    ? (_openBlock(), _createElementBlock(\"p\", _hoisted_9, _toDisplayString(testPlan.value?.manifest.description), 1))\n                    : (_openBlock(), _createBlock(_component_el_empty, {\n                        key: 1,\n                        description: \"No description\",\n                        \"image-size\": 100\n                      }))\n                ]))\n              : (_openBlock(), _createElementBlock(\"div\", _hoisted_10, [\n                  _createVNode(_component_el_input, {\n                    modelValue: editDescription.value,\n                    \"onUpdate:modelValue\": _cache[0] || (_cache[0] = ($event: any) => ((editDescription).value = $event)),\n                    type: \"textarea\",\n                    rows: 3,\n                    placeholder: \"Enter test plan description\",\n                    maxlength: \"500\",\n                    \"show-word-limit\": \"\"\n                  }, null, 8, [\"modelValue\"]),\n                  _createElementVNode(\"div\", _hoisted_11, [\n                    _createVNode(_component_el_button, {\n                      type: \"primary\",\n                      onClick: saveDescription,\n                      loading: isSaving.value\n                    }, {\n                      default: _withCtx(() => _cache[3] || (_cache[3] = [\n                        _createTextVNode(\" Save \")\n                      ])),\n                      _: 1\n                    }, 8, [\"loading\"]),\n                    _createVNode(_component_el_button, { onClick: cancelEditing }, {\n                      default: _withCtx(() => _cache[4] || (_cache[4] = [\n                        _createTextVNode(\"Cancel\")\n                      ])),\n                      _: 1\n                    })\n                  ])\n                ]))\n          ], 64))\n    ]),\n    _createElementVNode(\"div\", _hoisted_12, [\n      _cache[5] || (_cache[5] = _createElementVNode(\"h4\", null, \"Additional Information\", -1)),\n      _createVNode(_component_el_descriptions, {\n        border: \"\",\n        column: 1\n      }, {\n        default: _withCtx(() => [\n          _createVNode(_component_el_descriptions_item, { label: \"File Path\" }, {\n            default: _withCtx(() => [\n              _createVNode(_component_el_tooltip, {\n                content: testPlan.value?.path,\n                placement: \"top\"\n              }, {\n                default: _withCtx(() => [\n                  _createElementVNode(\"div\", _hoisted_13, _toDisplayString(testPlan.value?.path), 1)\n                ]),\n                _: 1\n              }, 8, [\"content\"])\n            ]),\n            _: 1\n          })\n        ]),\n        _: 1\n      })\n    ])\n  ])), [\n    [_directive_loading, loading.value]\n  ])\n}\n}\n\n})", "<template>\r\n  <div class=\"basic-setting-container\" v-loading=\"loading\">\r\n    <div class=\"plan-name-section\">\r\n      <div class=\"plan-title\">\r\n        <div class=\"title-with-refresh\">\r\n          <h2>{{ testPlan?.manifest.name }}</h2>\r\n          <el-button type=\"primary\" @click=\"refreshData\" :icon=\"Refresh\" circle size=\"small\" />\r\n        </div>\r\n      </div>\r\n      <div class=\"plan-meta\">\r\n        <el-tag size=\"small\">Created: {{ formatDateTime(testPlan?.manifest.created) }}</el-tag>\r\n        <el-tag size=\"small\" type=\"info\">Modified: {{ formatDateTime(testPlan?.manifest.modified) }}</el-tag>\r\n      </div>\r\n    </div>\r\n    \r\n    <div class=\"description-section\">\r\n      <div class=\"section-header\">\r\n        <h4>Description</h4>\r\n        <el-button \r\n          v-if=\"!isEditing\" \r\n          type=\"primary\" \r\n          text \r\n          size=\"small\" \r\n          @click=\"startEditing\"\r\n          :icon=\"Edit\"\r\n        >\r\n          Edit\r\n        </el-button>\r\n      </div>\r\n      \r\n      <el-skeleton :rows=\"3\" animated v-if=\"loading\"></el-skeleton>\r\n      \r\n      <template v-else>\r\n        <div v-if=\"!isEditing\" class=\"description-content\">\r\n          <p v-if=\"testPlan?.manifest.description\">{{ testPlan?.manifest.description }}</p>\r\n          <el-empty v-else description=\"No description\" :image-size=\"100\"></el-empty>\r\n        </div>\r\n        \r\n        <div v-else class=\"description-edit\">\r\n          <el-input\r\n            v-model=\"editDescription\"\r\n            type=\"textarea\"\r\n            :rows=\"3\"\r\n            placeholder=\"Enter test plan description\"\r\n            maxlength=\"500\"\r\n            show-word-limit\r\n          />\r\n          <div class=\"edit-actions\">\r\n            <el-button type=\"primary\" @click=\"saveDescription\" :loading=\"isSaving\">\r\n              Save\r\n            </el-button>\r\n            <el-button @click=\"cancelEditing\">Cancel</el-button>\r\n          </div>\r\n        </div>\r\n      </template>\r\n    </div>\r\n    \r\n    <div class=\"additional-info-section\">\r\n      <h4>Additional Information</h4>\r\n      <el-descriptions border :column=\"1\">\r\n        <el-descriptions-item label=\"File Path\">\r\n          <el-tooltip :content=\"testPlan?.path\" placement=\"top\">\r\n            <div class=\"path-display\">{{ testPlan?.path }}</div>\r\n          </el-tooltip>\r\n        </el-descriptions-item>\r\n      </el-descriptions>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { ref, onMounted, computed } from 'vue';\r\nimport { Edit, Refresh } from '@element-plus/icons-vue';\r\nimport { testPlanService } from '@/services/testPlanService';\r\n\r\nconst state = testPlanService.getState();\r\nconst testPlan = computed(() => state.currentPlan);\r\nconst loading = computed(() => state.isLoading);\r\n\r\nconst isEditing = ref(false);\r\nconst editDescription = ref('');\r\nconst isSaving = ref(false);\r\n\r\nconst loadCurrentPlan = async () => {\r\n  await testPlanService.getCurrentPlan();\r\n};\r\n\r\nconst formatDateTime = (dateTime?: string | Date) => {\r\n  if (!dateTime) return '-';\r\n  const date = dateTime instanceof Date ? dateTime : new Date(dateTime);\r\n  return date.toLocaleString();\r\n};\r\n\r\nconst startEditing = () => {\r\n  editDescription.value = testPlan.value?.manifest.description || '';\r\n  isEditing.value = true;\r\n};\r\n\r\nconst cancelEditing = () => {\r\n  isEditing.value = false;\r\n};\r\n\r\nconst saveDescription = async () => {\r\n  if (!testPlan.value) return;\r\n  \r\n  isSaving.value = true;\r\n  try {\r\n    await testPlanService.updateBasicInfo(editDescription.value);\r\n    isEditing.value = false;\r\n  } finally {\r\n    isSaving.value = false;\r\n  }\r\n};\r\n\r\nconst refreshData = () => {\r\n  loadCurrentPlan();\r\n};\r\n\r\nonMounted(() => {\r\n  loadCurrentPlan();\r\n});\r\n</script>\r\n\r\n<style scoped>\r\n.basic-setting-container {\r\n  margin: 0 auto;\r\n  background-color: #ffffff;\r\n  border-radius: 8px;\r\n  flex: 1;\r\n  padding: 20px;\r\n}\r\n\r\n.plan-name-section {\r\n  margin-bottom: 25px;\r\n  border-bottom: 1px solid #ebeef5;\r\n  padding-bottom: 15px;\r\n}\r\n\r\n.plan-title {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.title-with-refresh {\r\n  display: inline-flex;\r\n  align-items: center;\r\n  gap: 10px;\r\n}\r\n\r\n.plan-title h2 {\r\n  margin: 0;\r\n  font-size: 1.5rem;\r\n  font-weight: 500;\r\n  color: #303133;\r\n}\r\n\r\n.plan-meta {\r\n  display: flex;\r\n  gap: 10px;\r\n}\r\n\r\n.section-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 15px;\r\n}\r\n\r\n.section-header h4 {\r\n  margin: 0;\r\n  font-size: 1rem;\r\n  font-weight: 500;\r\n}\r\n\r\n.description-section {\r\n  margin-bottom: 25px;\r\n}\r\n\r\n.description-content {\r\n  min-height: 60px;\r\n  padding: 10px;\r\n  background-color: #f8f8f8;\r\n  border-radius: 4px;\r\n}\r\n\r\n.description-content p {\r\n  margin: 0;\r\n  white-space: pre-line;\r\n  line-height: 1.5;\r\n}\r\n\r\n.description-edit {\r\n  margin-bottom: 10px;\r\n}\r\n\r\n.edit-actions {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  gap: 10px;\r\n  margin-top: 15px;\r\n}\r\n\r\n.additional-info-section {\r\n  margin-top: 25px;\r\n}\r\n\r\n.additional-info-section h4 {\r\n  margin: 0 0 15px 0;\r\n  font-size: 1rem;\r\n  font-weight: 500;\r\n}\r\n\r\n.path-display {\r\n  white-space: nowrap;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  max-width: 100%;\r\n}\r\n\r\n@media (max-width: 767px) {\r\n  .basic-setting-container {\r\n    padding: 10px;\r\n  }\r\n  \r\n  .plan-meta {\r\n    flex-direction: column;\r\n    gap: 5px;\r\n  }\r\n}\r\n</style>\r\n", "import script from \"./BasicSetting.vue?vue&type=script&setup=true&lang=ts\"\nexport * from \"./BasicSetting.vue?vue&type=script&setup=true&lang=ts\"\n\nimport \"./BasicSetting.vue?vue&type=style&index=0&id=6ed11c04&scoped=true&lang=css\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-6ed11c04\"]])\n\nexport default __exports__"], "names": ["_hoisted_1", "class", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_hoisted_5", "_hoisted_6", "_hoisted_7", "_hoisted_8", "key", "_hoisted_9", "_hoisted_10", "_hoisted_11", "_hoisted_12", "_hoisted_13", "_defineComponent", "__name", "setup", "__props", "state", "testPlanService", "getState", "testPlan", "computed", "currentPlan", "loading", "isLoading", "isEditing", "ref", "editDescription", "isSaving", "loadCurrentPlan", "async", "getCurrentPlan", "formatDateTime", "dateTime", "date", "Date", "toLocaleString", "startEditing", "value", "manifest", "description", "cancelEditing", "saveDescription", "updateBasicInfo", "refreshData", "onMounted", "_ctx", "_cache", "_component_el_button", "_resolveComponent", "_component_el_tag", "_component_el_skeleton", "_component_el_empty", "_component_el_input", "_component_el_tooltip", "_component_el_descriptions_item", "_component_el_descriptions", "_directive_loading", "_resolveDirective", "_withDirectives", "_openBlock", "_createElementBlock", "_createElementVNode", "_toDisplayString", "name", "_createVNode", "type", "onClick", "icon", "_unref", "Refresh", "circle", "size", "default", "_withCtx", "_createTextVNode", "created", "_", "modified", "_createCommentVNode", "_createBlock", "text", "Edit", "rows", "animated", "_Fragment", "modelValue", "$event", "placeholder", "maxlength", "border", "column", "label", "content", "path", "placement", "__exports__"], "sourceRoot": ""}