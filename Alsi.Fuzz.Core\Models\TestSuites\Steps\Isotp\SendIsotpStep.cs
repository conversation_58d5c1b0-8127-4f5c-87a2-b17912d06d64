using System.Xml.Serialization;

namespace Alsi.Fuzz.Core.Models.TestSuites.Steps.Isotp
{
    public class SendIsotpStep : StepBase
    {
        [XmlAttribute("hex-payload")]
        public string HexPayload { get; set; }

        private int? _repeatPayload;

        [XmlIgnore]
        public int? RepeatPayload
        {
            get { return _repeatPayload; }
            set { _repeatPayload = value; }
        }

        [XmlAttribute("repeat-payload")]
        public string RepeatPayloadString
        {
            get => _repeatPayload.HasValue ? _repeatPayload.Value.ToString() : null;
            set => _repeatPayload = string.IsNullOrEmpty(value) ? (int?)null : int.Parse(value);
        }
    }
}
