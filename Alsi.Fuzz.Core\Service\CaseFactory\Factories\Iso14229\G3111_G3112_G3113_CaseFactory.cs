using Alsi.Fuzz.Core.Service.CaseFactory.Core;
using Alsi.Fuzz.Core.Service.CaseFactory.Factories.Iso14229.Consts;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Alsi.Fuzz.Core.Service.CaseFactory.Factories.Iso14229
{
    public class G3111_G3112_G3113_CaseFactory : CaseFactoryBase
    {
        public override CaseMutation[] Generate(MutationOptions options)
        {
            if (options.Coverage == CoverageType.High)
            {
                return GenerateByIsoSubfunctions(options);
            }
            else if (options.Coverage == CoverageType.Normal)
            {
                return GenerateByXmlSubfunctions(options);
            }

            return Array.Empty<CaseMutation>();
        }

        private CaseMutation[] GenerateByXmlSubfunctions(MutationOptions options)
        {
            var xmlServices = options.XmlServices;
            var supportedXmlServicesWithSubfunction = xmlServices
                // 仅遍历支持的服务
                .Where(x => x.SubfunctionId.HasValue && x.IsSupported)
                // 仅遍历有 Subfunction 的服务
                .Where(x => IsoUdsConsts.Services.FirstOrDefault(service => x.Id == service.Id)?.Subfunctions.Any() == true)
                .ToArray();

            var caseMutations = new List<CaseMutation>();

            foreach (var xmlService in supportedXmlServicesWithSubfunction)
            {
                var sid = xmlService.Id;
                var subfunctionId = xmlService.SubfunctionId.Value;
                var data2kLength = xmlService.Parameter2k.Length;

                var payload = new List<byte> { sid, subfunctionId };
                payload.AddRange(xmlService.Parameter2k);

                // G111 - 当前 Subfunction、参数、正确的 DataLength
                var caseMutation = CreateCaseMutation($"G3111-Sid{sid:X2}-Subfunc{subfunctionId:X2}", payload, payload.Count);
                caseMutations.Add(caseMutation);

                // G112 - 当前 Subfunction、当前参数、DataLength - 1
                for (var payloadLength = 1; payloadLength < payload.Count - 1; payloadLength++)
                {
                    caseMutation = CreateCaseMutation($"G3112-Sid{sid:X2}-Subfunc{subfunctionId:X2}", payload, payloadLength);
                    caseMutations.Add(caseMutation);
                }

                // G113 - 当前 Subfunction、当前参数、DataLength + 1
                var randomByte = new byte[] { 0, 0x55, 0xAA, 0xCC, 0xFF };
                {
                    var payloadBytes = payload.ToList();
                    payloadBytes.Insert(0, randomByte[options.Random.Next(0, 5)]);
                    caseMutation = CreateCaseMutation($"G3113-Sid{sid:X2}-Subfunc{subfunctionId:X2}", payloadBytes.ToArray(), payloadBytes.Count);
                    caseMutations.Add(caseMutation);
                }
                {
                    var payloadBytes = payload.ToList();
                    payloadBytes.Add(randomByte[options.Random.Next(0, 5)]);
                    caseMutation = CreateCaseMutation($"G3113-Sid{sid:X2}-Subfunc{subfunctionId:X2}", payloadBytes.ToArray(), payloadBytes.Count);
                    caseMutations.Add(caseMutation);
                }
            }

            return caseMutations.ToArray();
        }

        private CaseMutation[] GenerateByIsoSubfunctions(MutationOptions options)
        {
            var caseMutations = new List<CaseMutation>();

            foreach (var service in IsoUdsConsts.Services)
            {
                var sid = service.Id;

                foreach (var subfunction in service.Subfunctions)
                {
                    var data2kLength = subfunction.Length - 2;

                    var payload = new List<byte> { sid, subfunction.Id };
                    payload.AddRange(RandomBytes(data2kLength));

                    // G111 - 当前 Subfunction、自己的参数、正确的 DataLength
                    var caseMutation = CreateCaseMutation($"G3111-Sid{sid:X2}-Subfunc{subfunction.Id:X2}", payload, payload.Count);
                    caseMutations.Add(caseMutation);

                    // G112 - 当前 Subfunction、自己的参数、DataLength - 1
                    for (var payloadLength = 1; payloadLength < payload.Count - 1; payloadLength++)
                    {
                        caseMutation = CreateCaseMutation($"G3112-Sid{sid:X2}-Subfunc{subfunction.Id:X2}", payload, payloadLength);
                        caseMutations.Add(caseMutation);
                    }

                    // G113 - 当前 Subfunction、自己的参数、DataLength + 1
                    var randomByte = new byte[] { 0, 0x55, 0xAA, 0xCC, 0xFF };
                    {
                        var payloadBytes = payload.ToList();
                        payloadBytes.Insert(0, randomByte[options.Random.Next(0, 5)]);
                        caseMutation = CreateCaseMutation($"G3113-Sid{sid:X2}-Subfunc{subfunction.Id:X2}", payloadBytes, payloadBytes.Count);
                        caseMutations.Add(caseMutation);
                    }
                    {
                        var payloadBytes = payload.ToList();
                        payloadBytes.Add(randomByte[options.Random.Next(0, 5)]);
                        caseMutation = CreateCaseMutation($"G3113-Sid{sid:X2}-Subfunc{subfunction.Id:X2}", payloadBytes, payloadBytes.Count);
                        caseMutations.Add(caseMutation);
                    }
                }
            }

            return caseMutations.ToArray();
        }
    }
}
