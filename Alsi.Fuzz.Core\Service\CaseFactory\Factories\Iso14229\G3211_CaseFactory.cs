using Alsi.Fuzz.Core.Service.CaseFactory.Core;
using System.Collections.Generic;
using System.Linq;

namespace Alsi.Fuzz.Core.Service.CaseFactory.Factories.Iso14229
{
    public class G3211_CaseFactory : CaseFactoryBase
    {
        public override CaseMutation[] Generate(MutationOptions options)
        {
            var caseMutations = new List<CaseMutation>();
            var xmlServices = options.XmlServices;

            // 仅遍历支持的服务
            var supportedXmlServices = xmlServices
                .Where(x => x.IsSupported)
                .ToArray();

            foreach (var supportedXmlService in supportedXmlServices)
            {
                var sid = supportedXmlService.Id;

                for (var parameter2 = 0; parameter2 <= 0xFF; parameter2++)
                {
                    var payload = new List<byte> { 0x7F, sid, (byte)parameter2 };
                    var caseMutation = CaseMutation.Create($"G3211-Sid{sid:X2}-Parameter2{parameter2:X2}")
                        .MutatePayload(payload.ToArray())
                        .MutatePayloadLength(payload.Count);
                    caseMutations.Add(caseMutation);
                }
            }

            return caseMutations.ToArray();
        }
    }
}
