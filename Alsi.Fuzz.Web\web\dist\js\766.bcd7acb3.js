"use strict";(self["webpackChunkfuzz_web"]=self["webpackChunkfuzz_web"]||[]).push([[766],{2766:function(e,t,a){a.r(t),a.d(t,{default:function(){return m}});a(4114);var n=a(6768),l=a(4232),s=a(1387),u=a(1219),d=a(144),i=a(7477);const r={class:"side-nav-wrapper"};var c={__name:"SideNav",setup(e){const t=(0,d.KR)(!1),a=(0,s.lq)(),u=()=>{t.value=!t.value};return(e,s)=>{const c=(0,n.g2)("el-icon"),o=(0,n.g2)("el-menu-item"),k=(0,n.g2)("el-menu");return(0,n.uX)(),(0,n.CE)("div",r,[(0,n.bF)(k,{collapse:t.value,"collapse-transition":!1,class:(0,l.C4)(["side-nav",{"side-nav-collapsed":t.value}]),router:"","default-active":(0,d.R1)(a).path},{default:(0,n.k6)((()=>[(0,n.bF)(o,{index:"/test-plan"},{default:(0,n.k6)((()=>[(0,n.bF)(c,null,{default:(0,n.k6)((()=>[(0,n.bF)((0,d.R1)(i.Document))])),_:1}),s[0]||(s[0]=(0,n.Lk)("span",null,"Basic Setting",-1))])),_:1}),(0,n.bF)(o,{index:"/test-plan/hardware"},{default:(0,n.k6)((()=>[(0,n.bF)(c,null,{default:(0,n.k6)((()=>[(0,n.bF)((0,d.R1)(i.Monitor))])),_:1}),s[1]||(s[1]=(0,n.Lk)("span",null,"Hardware Setting",-1))])),_:1}),(0,n.bF)(o,{index:"/test-plan/case-setting"},{default:(0,n.k6)((()=>[(0,n.bF)(c,null,{default:(0,n.k6)((()=>[(0,n.bF)((0,d.R1)(i.Setting))])),_:1}),s[2]||(s[2]=(0,n.Lk)("span",null,"Case Setting",-1))])),_:1}),(0,n.bF)(o,{index:"/test-plan/sequence-setting"},{default:(0,n.k6)((()=>[(0,n.bF)(c,null,{default:(0,n.k6)((()=>[(0,n.bF)((0,d.R1)(i.Setting))])),_:1}),s[3]||(s[3]=(0,n.Lk)("span",null,"Sequence Setting",-1))])),_:1}),(0,n.bF)(o,{index:"/test-plan/interoperation"},{default:(0,n.k6)((()=>[(0,n.bF)(c,null,{default:(0,n.k6)((()=>[(0,n.bF)((0,d.R1)(i.Connection))])),_:1}),s[4]||(s[4]=(0,n.Lk)("span",null,"Interoperation",-1))])),_:1}),(0,n.bF)(o,{index:"/test-plan/test-cases"},{default:(0,n.k6)((()=>[(0,n.bF)(c,null,{default:(0,n.k6)((()=>[(0,n.bF)((0,d.R1)(i.List))])),_:1}),s[5]||(s[5]=(0,n.Lk)("span",null,"Test Cases",-1))])),_:1}),(0,n.bF)(o,{index:"/test-plan/test-run"},{default:(0,n.k6)((()=>[(0,n.bF)(c,null,{default:(0,n.k6)((()=>[(0,n.bF)((0,d.R1)(i.VideoPlay))])),_:1}),s[6]||(s[6]=(0,n.Lk)("span",null,"Test Run",-1))])),_:1}),(0,n.bF)(o,{index:"/test-plan/test-results"},{default:(0,n.k6)((()=>[(0,n.bF)(c,null,{default:(0,n.k6)((()=>[(0,n.bF)((0,d.R1)(i.DataAnalysis))])),_:1}),s[7]||(s[7]=(0,n.Lk)("span",null,"Results",-1))])),_:1})])),_:1},8,["collapse","class","default-active"]),(0,n.Lk)("div",{class:"sidebar-toggle",onClick:u},[(0,n.bF)(c,{size:16},{default:(0,n.k6)((()=>[((0,n.uX)(),(0,n.Wv)((0,n.$y)(t.value?"ArrowRight":"ArrowLeft")))])),_:1})])])}}},o=a(1241);const k=(0,o.A)(c,[["__scopeId","data-v-70c75d22"]]);var p=k,f=a(2616);const b={class:"testplan-container"},F={class:"panel-header"},v={class:"title-section"},_={class:"plan-content"},g={class:"side-menu"},L={class:"content-area"};var R=(0,n.pM)({__name:"TestPlanView",setup(e){const t=(0,s.rd)();f.f.setRouter(t);const a=f.f.getState(),d=(0,n.EW)((()=>a.currentPlan)),i=e=>{document.title=e?`FUZZ - ${e}`:"FUZZ"};return(0,n.sV)((async()=>{try{const e=await f.f.getCurrentPlan();e?i(e.manifest.name):(u.nk.error("No test plan is currently open"),t.push("/"))}catch(e){u.nk.error("Failed to load test plan"),t.push("/")}})),(e,t)=>{const a=(0,n.g2)("router-view"),s=(0,n.g2)("el-card");return(0,n.uX)(),(0,n.CE)("div",b,[(0,n.bF)(s,{class:"testplan-card"},{header:(0,n.k6)((()=>[(0,n.Lk)("div",F,[(0,n.Lk)("div",v,[(0,n.Lk)("h2",null,(0,l.v_)(d.value?.manifest.name),1)])])])),default:(0,n.k6)((()=>[(0,n.Lk)("div",_,[(0,n.Lk)("div",g,[(0,n.bF)(p)]),(0,n.Lk)("div",L,[(0,n.bF)(a)])])])),_:1})])}}});const h=(0,o.A)(R,[["__scopeId","data-v-f1364f7e"]]);var m=h}}]);
//# sourceMappingURL=766.bcd7acb3.js.map