using Alsi.Fuzz.Core.Service;
using System.Linq;
using System.Web.Http;

namespace Alsi.Fuzz.Web.Controllers
{
    public class TestSuiteController : WebControllerBase
    {
        private readonly BuiltInTestSuiteService _testSuiteService;

        public TestSuiteController()
        {
            _testSuiteService = new BuiltInTestSuiteService();
        }

        [HttpGet]
        [ActionName("builtin")]
        public IHttpActionResult GetBuiltInTestSuites()
        {
            var testSuites = _testSuiteService.Get();
            return Ok(testSuites);
        }

        [HttpGet]
        [ActionName("xml")]
        public IHttpActionResult GetSequenceContent(string suiteName, string packageName)
        {
            var sequenceConfig = TestPlanManager.Instance.GetCurrentPlan()
                .Config.SequenceConfigList.FirstOrDefault(x => x.TestSuiteName == suiteName && x.SequencePackageName == packageName);

            if (sequenceConfig != null)
            {
                return Ok(sequenceConfig.SequencePackageXml);
            }

            return GetBuiltInSequenceContent(suiteName, packageName);
        }

        [HttpGet]
        [ActionName("builtin-xml")]
        public IHttpActionResult GetBuiltInSequenceContent(string suiteName, string packageName)
        {
            var content = _testSuiteService.GetBuiltInXml(suiteName, packageName);
            return Ok(content);
        }
    }
}
