"use strict";(self["webpackChunkfuzz_web"]=self["webpackChunkfuzz_web"]||[]).push([[558],{4999:function(e,s,a){a.r(s),a.d(s,{default:function(){return F}});a(4114),a(8111),a(7588);var t=a(6768),l=a(4232),n=a(144),u=a(1219),i=a(1021),o=a(6741),c=a(7823),r=a(6256);const d={class:"test-run-container"},v={class:"toolbar"},p={class:"action-buttons"},g={class:"content-area"},k={class:"test-cases-panel"},f={class:"card-header"},R={key:0,class:"case-count"},h={key:0,class:"loading-container"},y={key:1,class:"empty-container"},b={key:2,class:"case-list-wrapper"};var C=(0,t.pM)({__name:"TestRun",setup(e){const s=(0,n.KR)([]),a=(0,n.KR)(!0),C=(0,n.KR)(!1),m=(0,n.KR)(!1),w=(0,n.KR)(!1),F=(0,n.KR)(!1),_=(0,n.KR)(!1),S=(0,n.KR)({processState:i.si.Pending,currentOperation:"",testResult:{id:"",resultFolderName:"",testType:"",creationTime:"",totalCount:0,successCount:0,failureCount:0},caseResults:[]}),x=(0,n.KR)(""),T=(0,n.KR)(!1),E=(0,n.KR)(null),L=(0,n.KR)(null),W=(0,n.KR)(!0),I=(0,n.KR)([]);let K=0;const Q=(0,n.KR)(new Map);let z=null;const P=(0,t.EW)((()=>S.value.processState===i.si.Running)),X=(0,t.EW)((()=>S.value.processState===i.si.Paused)),M=(0,t.EW)((()=>(console.log("displayedCases = computed(() => begin."),(P.value||X.value)&&I.value?.length>0?(console.log("displayedCases = computed(() => end - running cases."),I.value):(console.log("displayedCases = computed(() => end - saved cases."),s.value)))),G=async()=>{a.value=!0;try{const e=await i.GQ.getSavedCases();s.value=e.data}catch(e){console.error("获取保存的测试用例失败:",e),u.nk.error("Failed to fetch saved test cases")}finally{a.value=!1}},A=()=>{G()},N=async()=>{if(0!==s.value.length){C.value=!0;try{await i.GQ.startTest(),_.value=!0,u.nk.success("Test execution started"),await V(),H()}catch(e){console.error("启动测试失败:",e),u.nk.error("Failed to start test execution")}finally{C.value=!1}}else u.nk.warning("No test cases available to execute")},O=async()=>{w.value=!0;try{await i.GQ.pauseTest(),u.nk.success("Test execution paused"),await V()}catch(e){console.error("暂停测试失败:",e),u.nk.error("Failed to pause test execution")}finally{w.value=!1}},D=async()=>{F.value=!0;try{await i.GQ.resumeTest(),u.nk.success("Test execution resumed"),await V()}catch(e){console.error("恢复测试失败:",e),u.nk.error("Failed to resume test execution")}finally{F.value=!1}},U=async()=>{m.value=!0;try{await i.GQ.stopTest(),u.nk.success("Test execution stopped"),await V()}catch(e){console.error("停止测试失败:",e),u.nk.error("Failed to stop test execution")}finally{m.value=!1}},V=async()=>{try{let e;if(W.value)e=await i.GQ.getTestStatus(),W.value=!1,S.value=e.data,I.value=e.data.caseResults||[],j(),K=B(I.value);else{const s=Math.floor(K/100)+1;e=await i.GQ.getTestStatusPaged({pageNumber:s,pageSize:100}),S.value.processState=e.data.processState,S.value.currentOperation=e.data.currentOperation,e.data.pagedCaseResult?.items&&(q(e.data.pagedCaseResult.items),K=B(I.value))}x.value=(new Date).toLocaleString(),(0,i.xh)(S.value)&&z&&(A(),J())}catch(e){console.error("获取测试状态失败:",e)}},B=e=>{console.log("findFirstPendingIndex begin");for(let s=0;s<e.length;s++)if(e[s].state===i.si.Pending)return console.log("findFirstPendingIndex end"),s;return console.log("findFirstPendingIndex end"),e.length},j=()=>{console.log("rebuildIndexMap begin"),Q.value.clear(),I.value.forEach(((e,s)=>{Q.value.set(e.id,s)})),console.log("rebuildIndexMap end, map size:",Q.value.size)},q=e=>{if(console.log("updateCaseResults begin, updating",e.length,"cases"),!I.value||0===I.value.length)return I.value=e,j(),void console.log("updateCaseResults end - initial load");const s=[];let a=0,t=0;for(const l of e){const e=Q.value.get(l.id);if(void 0!==e&&e<I.value.length)if(I.value[e].id===l.id)s.push({index:e,case:l}),a++;else{console.warn("Index map inconsistency detected, rebuilding..."),j();const e=Q.value.get(l.id);void 0!==e&&e<I.value.length?(s.push({index:e,case:l}),a++):t++}else t++}s.forEach((({index:e,case:s})=>{I.value[e]=s})),console.log("updateCaseResults end - map hits:",a,"misses:",t)},H=()=>{J(),W.value=!0,Q.value.clear(),z=window.setInterval(V,300)},J=()=>{z&&(clearInterval(z),z=null)},Y=e=>{L.value=e.testResultId,E.value=e.id,T.value=!0},Z=()=>{T.value=!1,E.value=null};return(0,t.sV)((()=>{G(),V().then((()=>{P.value&&(_.value=!0,H())}))})),(0,t.hi)((()=>{J()})),(e,n)=>{const u=(0,t.g2)("el-button"),i=(0,t.g2)("el-skeleton"),x=(0,t.g2)("el-empty"),W=(0,t.g2)("el-card");return(0,t.uX)(),(0,t.CE)("div",d,[(0,t.Lk)("div",v,[n[5]||(n[5]=(0,t.Lk)("h3",null,"Test Execution",-1)),(0,t.Lk)("div",p,[P.value||X.value?(0,t.Q3)("",!0):((0,t.uX)(),(0,t.Wv)(u,{key:0,type:"success",size:"small",loading:C.value,onClick:N,disabled:0===s.value.length},{default:(0,t.k6)((()=>n[1]||(n[1]=[(0,t.eW)(" Start ")]))),_:1},8,["loading","disabled"])),P.value&&!X.value?((0,t.uX)(),(0,t.Wv)(u,{key:1,type:"warning",size:"small",loading:w.value,onClick:O},{default:(0,t.k6)((()=>n[2]||(n[2]=[(0,t.eW)(" Pause ")]))),_:1},8,["loading"])):(0,t.Q3)("",!0),X.value?((0,t.uX)(),(0,t.Wv)(u,{key:2,type:"info",size:"small",loading:F.value,onClick:D},{default:(0,t.k6)((()=>n[3]||(n[3]=[(0,t.eW)(" Resume ")]))),_:1},8,["loading"])):(0,t.Q3)("",!0),P.value||X.value?((0,t.uX)(),(0,t.Wv)(u,{key:3,type:"danger",size:"small",loading:m.value,onClick:U},{default:(0,t.k6)((()=>n[4]||(n[4]=[(0,t.eW)(" Stop ")]))),_:1},8,["loading"])):(0,t.Q3)("",!0)])]),(0,t.bF)(o.A,{"run-status":S.value,visible:_.value},null,8,["run-status","visible"]),(0,t.Lk)("div",g,[(0,t.Lk)("div",k,[(0,t.bF)(W,{shadow:"never",class:"test-cases-card"},{header:(0,t.k6)((()=>[(0,t.Lk)("div",f,[n[6]||(n[6]=(0,t.Lk)("span",null,"Test Cases",-1)),M.value.length>0?((0,t.uX)(),(0,t.CE)("span",R,(0,l.v_)(M.value.length)+" cases",1)):(0,t.Q3)("",!0)])])),default:(0,t.k6)((()=>[a.value?((0,t.uX)(),(0,t.CE)("div",h,[(0,t.bF)(i,{rows:10,animated:""})])):0===M.value.length?((0,t.uX)(),(0,t.CE)("div",y,[(0,t.bF)(x,{description:"No test cases found"})])):((0,t.uX)(),(0,t.CE)("div",b,[(0,t.bF)(r.A,{cases:M.value,onViewDetail:Y},null,8,["cases"])]))])),_:1})])]),(0,t.bF)(c.A,{visible:T.value,"onUpdate:visible":n[0]||(n[0]=e=>T.value=e),testResultId:L.value,caseResultId:E.value,onClose:Z},null,8,["visible","testResultId","caseResultId"])])}}}),m=a(1241);const w=(0,m.A)(C,[["__scopeId","data-v-55a32910"]]);var F=w},6741:function(e,s,a){a.d(s,{A:function(){return m}});var t=a(6768),l=a(144),n=a(4232),u=a(7477),i=a(1021),o=(0,t.pM)({__name:"TestStateTag",props:{state:{}},setup(e){const s=e,a=(0,t.EW)((()=>{switch(s.state){case i.si.Success:return"success";case i.si.Running:return"warning";case i.si.Failure:return"danger";case i.si.Pending:default:return"info"}})),l=e=>{switch(e){case i.si.Running:return"Running";case i.si.Pending:return"Not Run";case i.si.Success:return"Completed";case i.si.Failure:return"Faulted";case i.si.Paused:return"Paused";default:return"Unknown"}},u=(0,t.EW)((()=>l(s.state)));return(e,s)=>{const l=(0,t.g2)("el-tag");return(0,t.uX)(),(0,t.Wv)(l,{type:a.value,size:"small"},{default:(0,t.k6)((()=>[(0,t.eW)((0,n.v_)(u.value),1)])),_:1},8,["type"])}}});const c=o;var r=c;const d={key:0,class:"test-monitor"},v={class:"status-area"},p={class:"compact-status"},g={class:"status-header-inline"},k={key:0,class:"stats-row"},f={class:"stat-item success"},R={class:"stat-item failure"},h={class:"stat-item total"};var y=(0,t.pM)({__name:"TestMonitor",props:{runStatus:{},visible:{type:Boolean}},setup(e){const s=e,a=(0,t.EW)((()=>s.runStatus.testResult?.totalCount||0)),i=(0,t.EW)((()=>(s.runStatus.testResult?.successCount||0)+(s.runStatus.testResult?.failureCount||0)));return(e,s)=>{const o=(0,t.g2)("el-icon"),c=(0,t.g2)("el-progress");return e.visible?((0,t.uX)(),(0,t.CE)("div",d,[(0,t.Lk)("div",v,[(0,t.Lk)("div",p,[(0,t.Lk)("div",g,[a.value>0?((0,t.uX)(),(0,t.CE)("div",k,[(0,t.Lk)("div",f,[(0,t.bF)(o,null,{default:(0,t.k6)((()=>[(0,t.bF)((0,l.R1)(u.CircleCheckFilled))])),_:1}),(0,t.Lk)("span",null,(0,n.v_)(e.runStatus.testResult?.successCount||0),1)]),(0,t.Lk)("div",R,[(0,t.bF)(o,null,{default:(0,t.k6)((()=>[(0,t.bF)((0,l.R1)(u.CircleCloseFilled))])),_:1}),(0,t.Lk)("span",null,(0,n.v_)(e.runStatus.testResult?.failureCount||0),1)]),(0,t.Lk)("div",h,[(0,t.bF)(o,null,{default:(0,t.k6)((()=>[(0,t.bF)((0,l.R1)(u.InfoFilled))])),_:1}),(0,t.Lk)("span",null,(0,n.v_)(a.value||0),1)])])):(0,t.Q3)("",!0),(0,t.bF)(r,{state:e.runStatus.processState},null,8,["state"])]),(0,t.bF)(c,{percentage:a.value>0?Math.round(i.value/a.value*100):0,"stroke-width":8},null,8,["percentage"])])])])):(0,t.Q3)("",!0)}}}),b=a(1241);const C=(0,b.A)(y,[["__scopeId","data-v-ebdce83c"]]);var m=C}}]);
//# sourceMappingURL=558.4c0a52aa.js.map