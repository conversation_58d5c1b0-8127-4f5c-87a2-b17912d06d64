{"version": 3, "file": "js/67.cd8395db.js", "mappings": "mMAOA,GAA4BA,EAAAA,EAAAA,IAAiB,CAC3CC,OAAQ,eACRC,MAAO,CACLC,MAAO,CAAC,GAEVC,KAAAA,CAAMC,GCFR,MAAMH,EAAQG,EAIRC,GAAUC,EAAAA,EAAAA,KAAoD,KAClE,OAAQL,EAAMC,OACZ,KAAKK,EAAAA,GAAeC,QAClB,MAAO,UACT,KAAKD,EAAAA,GAAeE,QAClB,MAAO,UACT,KAAKF,EAAAA,GAAeG,QAClB,MAAO,SACT,KAAKH,EAAAA,GAAeI,QACpB,QACE,MAAO,O,IAIPC,EAAoBV,IACxB,OAAQA,GACN,KAAKK,EAAAA,GAAeE,QAClB,MAAO,UACT,KAAKF,EAAAA,GAAeI,QAClB,MAAO,UACT,KAAKJ,EAAAA,GAAeC,QAClB,MAAO,YACT,KAAKD,EAAAA,GAAeG,QAClB,MAAO,UACT,KAAKH,EAAAA,GAAeM,OAClB,MAAO,SACT,QACE,MAAO,U,EAIPC,GAAYR,EAAAA,EAAAA,KAAS,IAClBM,EAAiBX,EAAMC,SDKhC,MAAO,CAACa,EAAUC,KAChB,MAAMC,GAAoBC,EAAAA,EAAAA,IAAkB,UAE5C,OAAQC,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAaH,EAAmB,CACpDI,KAAMhB,EAAQiB,MACdC,KAAM,SACL,CACDC,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtBC,EAAAA,EAAAA,KAAiBC,EAAAA,EAAAA,IAAiBb,EAAUQ,OAAQ,MAEtDM,EAAG,GACF,EAAG,CAAC,QAAQ,CAEjB,IE7DA,MAAMC,EAAc,EAEpB,QCFA,MAAMC,EAAa,CACjBC,IAAK,EACLC,MAAO,gBAEHC,EAAa,CAAED,MAAO,eACtBE,EAAa,CAAEF,MAAO,kBACtBG,EAAa,CAAEH,MAAO,wBACtBI,EAAa,CACjBL,IAAK,EACLC,MAAO,aAEHK,EAAa,CAAEL,MAAO,qBACtBM,EAAa,CAAEN,MAAO,qBACtBO,EAAa,CAAEP,MAAO,mBAS5B,OAA4BjC,EAAAA,EAAAA,IAAiB,CAC3CC,OAAQ,cACRC,MAAO,CACLuC,UAAW,CAAC,EACZC,QAAS,CAAEpB,KAAMqB,UAEnBvC,KAAAA,CAAMC,GCgBR,MAAMH,EAAQG,EAKRuC,GAAarC,EAAAA,EAAAA,KAAS,IACnBL,EAAMuC,UAAUI,YAAYD,YAAc,IAG7CE,GAAiBvC,EAAAA,EAAAA,KAAS,KACtBL,EAAMuC,UAAUI,YAAYE,cAAgB,IAAM7C,EAAMuC,UAAUI,YAAYG,cAAgB,KDdxG,MAAO,CAAChC,EAAUC,KAChB,MAAMgC,GAAqB9B,EAAAA,EAAAA,IAAkB,WACvC+B,GAAyB/B,EAAAA,EAAAA,IAAkB,eAEjD,OAAQH,EAAK0B,UACRtB,EAAAA,EAAAA,OAAc+B,EAAAA,EAAAA,IAAoB,MAAOpB,EAAY,EACpDqB,EAAAA,EAAAA,IAAoB,MAAOlB,EAAY,EACrCkB,EAAAA,EAAAA,IAAoB,MAAOjB,EAAY,EACrCiB,EAAAA,EAAAA,IAAoB,MAAOhB,EAAY,CACpCQ,EAAWrB,MAAQ,IACfH,EAAAA,EAAAA,OAAc+B,EAAAA,EAAAA,IAAoB,MAAOd,EAAY,EACpDe,EAAAA,EAAAA,IAAoB,MAAOd,EAAY,EACrCe,EAAAA,EAAAA,IAAaJ,EAAoB,KAAM,CACrCxB,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtB2B,EAAAA,EAAAA,KAAaC,EAAAA,EAAAA,IAAOC,EAAAA,uBAEtB1B,EAAG,KAELuB,EAAAA,EAAAA,IAAoB,OAAQ,MAAMxB,EAAAA,EAAAA,IAAiBZ,EAAKyB,UAAUI,YAAYE,cAAgB,GAAI,MAEpGK,EAAAA,EAAAA,IAAoB,MAAOb,EAAY,EACrCc,EAAAA,EAAAA,IAAaJ,EAAoB,KAAM,CACrCxB,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtB2B,EAAAA,EAAAA,KAAaC,EAAAA,EAAAA,IAAOE,EAAAA,uBAEtB3B,EAAG,KAELuB,EAAAA,EAAAA,IAAoB,OAAQ,MAAMxB,EAAAA,EAAAA,IAAiBZ,EAAKyB,UAAUI,YAAYG,cAAgB,GAAI,MAEpGI,EAAAA,EAAAA,IAAoB,MAAOZ,EAAY,EACrCa,EAAAA,EAAAA,IAAaJ,EAAoB,KAAM,CACrCxB,SAASC,EAAAA,EAAAA,KAAS,IAAM,EACtB2B,EAAAA,EAAAA,KAAaC,EAAAA,EAAAA,IAAOG,EAAAA,gBAEtB5B,EAAG,KAELuB,EAAAA,EAAAA,IAAoB,OAAQ,MAAMxB,EAAAA,EAAAA,IAAiBgB,EAAWrB,OAAS,GAAI,SAG/EmC,EAAAA,EAAAA,IAAoB,IAAI,IAC5BL,EAAAA,EAAAA,IAAaM,EAAc,CACzBxD,MAAOa,EAAKyB,UAAUmB,cACrB,KAAM,EAAG,CAAC,aAEfP,EAAAA,EAAAA,IAAaH,EAAwB,CACnCW,WAAYjB,EAAWrB,MAAQ,EAAIuC,KAAKC,MAAMjB,EAAevB,MAAQqB,EAAWrB,MAAQ,KAAO,EAC/F,eAAgB,GACf,KAAM,EAAG,CAAC,uBAInBmC,EAAAA,EAAAA,IAAoB,IAAI,EAAK,CAEnC,I,UE1FA,MAAM,GAA2B,OAAgB,EAAQ,CAAC,CAAC,YAAY,qBAEvE,O,+KCLA,MAAM3B,EAAa,CAAEE,MAAO,sBACtBC,EAAa,CAAED,MAAO,WACtBE,EAAa,CAAEF,MAAO,kBACtBG,EAAa,CAAEH,MAAO,gBACtBI,EAAa,CAAEJ,MAAO,oBACtBK,EAAa,CAAEL,MAAO,eACtBM,EAAa,CACjBP,IAAK,EACLC,MAAO,cAEHO,EAAa,CACjBR,IAAK,EACLC,MAAO,qBAEH+B,EAAa,CACjBhC,IAAK,EACLC,MAAO,mBAEHgC,EAAc,CAClBjC,IAAK,EACLC,MAAO,qBCoHHiC,EAAiB,GDpGvB,OAA4BlE,EAAAA,EAAAA,IAAiB,CAC3CC,OAAQ,UACRG,KAAAA,CAAMC,GCwDR,MAAM8D,GAAiBC,EAAAA,EAAAA,IAAkB,IACnCC,GAAUD,EAAAA,EAAAA,KAAI,GACdE,GAAWF,EAAAA,EAAAA,KAAI,GACfG,GAAWH,EAAAA,EAAAA,KAAI,GACfI,GAAUJ,EAAAA,EAAAA,KAAI,GACdK,GAAWL,EAAAA,EAAAA,KAAI,GACfM,GAAiBN,EAAAA,EAAAA,KAAI,GACrB3B,GAAY2B,EAAAA,EAAAA,IAAoB,CACpCR,aAAcpD,EAAAA,GAAeI,QAC7B+D,iBAAkB,GAClB9B,WAAY,CACV+B,GAAI,GACJC,iBAAkB,GAClBC,SAAU,GACVC,aAAc,GACdnC,WAAY,EACZG,aAAc,EACdC,aAAc,GAEhBgC,YAAa,KAETC,GAAcb,EAAAA,EAAAA,IAAI,IAGlBc,GAAsBd,EAAAA,EAAAA,KAAI,GAC1Be,GAAiBf,EAAAA,EAAAA,IAAmB,MACpCgB,GAAehB,EAAAA,EAAAA,IAAmB,MAGlCiB,GAAejB,EAAAA,EAAAA,KAAI,GAEnBkB,GAAWC,EAAAA,EAAAA,IAAyB,IAC1C,IAAIC,EAAmB,EAGvB,MAAMC,GAAerB,EAAAA,EAAAA,IAAyB,IAAIsB,KAG5CC,GAAgBvB,EAAAA,EAAAA,IAAI,GAG1B,IAAIwB,EAAqC,KAIrCC,EAAoC,KAGxC,MAAMC,GAAYvF,EAAAA,EAAAA,KAAS,IAClBkC,EAAUlB,MAAMqC,eAAiBpD,EAAAA,GAAeE,UAGnDqF,GAAWxF,EAAAA,EAAAA,KAAS,IACjBkC,EAAUlB,MAAMqC,eAAiBpD,EAAAA,GAAeM,SAGnDkF,GAAiBzF,EAAAA,EAAAA,KAAS,KAE9BoF,EAAcpE,MAEd0E,QAAQC,IAAI,2CAEPJ,EAAUvE,OAASwE,EAASxE,QAAU+D,EAAS/D,OAAO4E,OAAS,GAClEF,QAAQC,IAAI,wDACLZ,EAAS/D,QAGlB0E,QAAQC,IAAI,sDACL/B,EAAe5C,UAIlB6E,EAAsBC,UAC1BhC,EAAQ9C,OAAQ,EAChB,IACE,MAAM+E,QAAiBC,EAAAA,GAAOC,gBAC9BrC,EAAe5C,MAAQ+E,EAASG,I,CAIhC,MAAOC,GACPT,QAAQS,MAAM,eAAgBA,GAC9BC,EAAAA,GAAUD,MAAM,mC,CAChB,QACArC,EAAQ9C,OAAQ,C,GAKdqF,EAAmBA,KACvBR,GAAqB,EAIjBS,EAAqBR,UACzB,GAAoC,IAAhClC,EAAe5C,MAAM4E,OAAzB,CAKA7B,EAAS/C,OAAQ,EACjB,UACQgF,EAAAA,GAAOO,YACbpC,EAAenD,OAAQ,EACvBoF,EAAAA,GAAUI,QAAQ,gCAGZC,IACNC,G,CACA,MAAOP,GACPT,QAAQS,MAAM,UAAWA,GACzBC,EAAAA,GAAUD,MAAM,iC,CAChB,QACApC,EAAS/C,OAAQ,C,OAjBjBoF,EAAAA,GAAUO,QAAQ,qC,EAsBhBC,EAAqBd,UACzB7B,EAAQjD,OAAQ,EAChB,UACQgF,EAAAA,GAAOa,YACbT,EAAAA,GAAUI,QAAQ,+BAGZC,G,CACN,MAAON,GACPT,QAAQS,MAAM,UAAWA,GACzBC,EAAAA,GAAUD,MAAM,iC,CAChB,QACAlC,EAAQjD,OAAQ,C,GAKd8F,EAAsBhB,UAC1B5B,EAASlD,OAAQ,EACjB,UACQgF,EAAAA,GAAOe,aACbX,EAAAA,GAAUI,QAAQ,gCAGZC,G,CACN,MAAON,GACPT,QAAQS,MAAM,UAAWA,GACzBC,EAAAA,GAAUD,MAAM,kC,CAChB,QACAjC,EAASlD,OAAQ,C,GAKfgG,EAAoBlB,UACxB9B,EAAShD,OAAQ,EACjB,UACQgF,EAAAA,GAAOiB,WACbb,EAAAA,GAAUI,QAAQ,gCAGZC,G,CACN,MAAON,GACPT,QAAQS,MAAM,UAAWA,GACzBC,EAAAA,GAAUD,MAAM,gC,CAChB,QACAnC,EAAShD,OAAQ,C,GAKfyF,EAAkBX,UACtB,IACE,IAAIC,EAEJ,GAAIjB,EAAa9D,MAEf+E,QAAiBC,EAAAA,GAAOkB,gBACxBpC,EAAa9D,OAAQ,EAErBkB,EAAUlB,MAAQ+E,EAASG,KAC3BnB,EAAS/D,MAAQ+E,EAASG,KAAKzB,aAAe,GAG9C0C,IAGAlC,EAAmBmC,EAAsBrC,EAAS/D,WAC7C,CAEL,MAAMqG,EAAa9D,KAAK+D,MAAMrC,EAAmB,KAAO,EACxDc,QAAiBC,EAAAA,GAAOuB,mBAAmB,CACzCF,aACAG,SAAU,MAIZtF,EAAUlB,MAAMqC,aAAe0C,EAASG,KAAK7C,aAC7CnB,EAAUlB,MAAMoD,iBAAmB2B,EAASG,KAAK9B,iBAI7C2B,EAASG,KAAKuB,iBAAiBC,QACjCC,EAAkB5B,EAASG,KAAKuB,gBAAgBC,OAChDzC,EAAmBmC,EAAsBrC,EAAS/D,O,CAOtD0D,EAAY1D,OAAQ,IAAI4G,MAAOC,kBAG3BC,EAAAA,EAAAA,IAAkB5F,EAAUlB,QAAUsE,IACxCe,IACA0B,K,CAGF,MAAO5B,GACPT,QAAQS,MAAM,YAAaA,E,GAKzBiB,EAAyBY,IAC7BtC,QAAQC,IAAI,+BACZ,IAAK,IAAIsC,EAAI,EAAGA,EAAID,EAAMpC,OAAQqC,IAChC,GAAID,EAAMC,GAAGrI,QAAUK,EAAAA,GAAeI,QAEpC,OADAqF,QAAQC,IAAI,6BACLsC,EAIX,OADAvC,QAAQC,IAAI,6BACLqC,EAAMpC,MAAM,EAIfuB,EAAkBA,KACtBzB,QAAQC,IAAI,yBACZT,EAAalE,MAAMkH,QACnBnD,EAAS/D,MAAMmH,SAAQ,CAACC,EAAsBC,KAC5CnD,EAAalE,MAAMsH,IAAIF,EAAS/D,GAAIgE,EAAM,IAE5C3C,QAAQC,IAAI,iCAAkCT,EAAalE,MAAMC,KAAK,EAIlEsH,EAAyBA,KACzBlD,GACFmD,aAAanD,GAEfA,EAAsBoD,OAAOC,YAAW,MACtCC,EAAAA,EAAAA,IAAW5D,GACXK,EAAcpE,QACdqE,EAAsB,IAAI,GACzB1B,EAAe,EAIdgE,EAAqBiB,IAGzB,GAFAlD,QAAQC,IAAI,oCAAqCiD,EAAahD,OAAQ,UAEjEb,EAAS/D,OAAmC,IAA1B+D,EAAS/D,MAAM4E,OAMpC,OALAb,EAAS/D,MAAQ4H,EACjBzB,IAEAoB,SACA7C,QAAQC,IAAI,wCAKd,MAAMkD,EAAoD,GAC1D,IAAIC,EAAU,EACVC,EAAY,EAEhB,IAAK,MAAMC,KAAWJ,EAAc,CAClC,MAAMP,EAAQnD,EAAalE,MAAMiI,IAAID,EAAQ3E,IAC7C,QAAc6E,IAAVb,GAAuBA,EAAQtD,EAAS/D,MAAM4E,OAEhD,GAAIb,EAAS/D,MAAMqH,GAAOhE,KAAO2E,EAAQ3E,GACvCwE,EAAQM,KAAK,CAACd,QAAOe,KAAMJ,IAC3BF,QACK,CAELpD,QAAQ2D,KAAK,mDACblC,IACA,MAAMmC,EAAWpE,EAAalE,MAAMiI,IAAID,EAAQ3E,SAC/B6E,IAAbI,GAA0BA,EAAWvE,EAAS/D,MAAM4E,QACtDiD,EAAQM,KAAK,CAACd,MAAOiB,EAAUF,KAAMJ,IACrCF,KAEAC,G,MAIJA,G,CAKJF,EAAQV,SAAQ,EAAEE,QAAOe,KAAMG,MAC7BxE,EAAS/D,MAAMqH,GAASkB,CAAW,IAIjCV,EAAQjD,OAAS,GACnB2C,IAGF7C,QAAQC,IAAI,oCAAqCmD,EAAS,UAAWC,EAAW,WAAYF,EAAQjD,OAAO,EAIvGc,EAAqBA,KAEzBqB,KAGAjD,EAAa9D,OAAQ,EAGrBkE,EAAalE,MAAMkH,QAEnB5C,EAAqBmD,OAAOe,YAAY/C,EAAiB,IAAI,EAIzDsB,GAAoBA,KACpBzC,IACFmE,cAAcnE,GACdA,EAAqB,K,EAKnBoE,GAAkBC,IACtB9E,EAAa7D,MAAQ2I,EAAW9E,aAChCD,EAAe5D,MAAQ2I,EAAWtF,GAClCM,EAAoB3D,OAAQ,CAAI,EAI5B4I,GAAoBA,KACxBjF,EAAoB3D,OAAQ,EAC5B4D,EAAe5D,MAAQ,IAAI,EDlC7B,OCwCA6I,EAAAA,EAAAA,KAAU,KACRhE,IACAY,IAAkBqD,MAAK,KAEjBvE,EAAUvE,QACZmD,EAAenD,OAAQ,EACvB0F,I,GAEF,KAIJqD,EAAAA,EAAAA,KAAY,KACVhC,IAAmB,IDrDd,CAACtH,EAAUC,KAChB,MAAMsJ,GAAuBpJ,EAAAA,EAAAA,IAAkB,aACzCqJ,GAAyBrJ,EAAAA,EAAAA,IAAkB,eAC3CsJ,GAAsBtJ,EAAAA,EAAAA,IAAkB,YACxCuJ,GAAqBvJ,EAAAA,EAAAA,IAAkB,WAE7C,OAAQC,EAAAA,EAAAA,OAAc+B,EAAAA,EAAAA,IAAoB,MAAOpB,EAAY,EAC3DqB,EAAAA,EAAAA,IAAoB,MAAOlB,EAAY,CACrCjB,EAAO,KAAOA,EAAO,IAAKmC,EAAAA,EAAAA,IAAoB,KAAM,KAAM,kBAAmB,KAC7EA,EAAAA,EAAAA,IAAoB,MAAOjB,EAAY,CACnC2D,EAAUvE,OAAUwE,EAASxE,OAc3BmC,EAAAA,EAAAA,IAAoB,IAAI,KAbvBtC,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAakJ,EAAsB,CAChDvI,IAAK,EACLV,KAAM,UACNE,KAAM,QACN6C,QAASC,EAAS/C,MAClBoJ,QAAS9D,EACT+D,SAA0C,IAAhCzG,EAAe5C,MAAM4E,QAC9B,CACD1E,SAASC,EAAAA,EAAAA,KAAS,IAAMT,EAAO,KAAOA,EAAO,GAAK,EAChDU,EAAAA,EAAAA,IAAiB,eAEnBE,EAAG,GACF,EAAG,CAAC,UAAW,cAErBiE,EAAUvE,QAAUwE,EAASxE,QACzBH,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAakJ,EAAsB,CAChDvI,IAAK,EACLV,KAAM,UACNE,KAAM,QACN6C,QAASG,EAAQjD,MACjBoJ,QAASxD,GACR,CACD1F,SAASC,EAAAA,EAAAA,KAAS,IAAMT,EAAO,KAAOA,EAAO,GAAK,EAChDU,EAAAA,EAAAA,IAAiB,eAEnBE,EAAG,GACF,EAAG,CAAC,cACP6B,EAAAA,EAAAA,IAAoB,IAAI,GAC3BqC,EAASxE,QACLH,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAakJ,EAAsB,CAChDvI,IAAK,EACLV,KAAM,OACNE,KAAM,QACN6C,QAASI,EAASlD,MAClBoJ,QAAStD,GACR,CACD5F,SAASC,EAAAA,EAAAA,KAAS,IAAMT,EAAO,KAAOA,EAAO,GAAK,EAChDU,EAAAA,EAAAA,IAAiB,gBAEnBE,EAAG,GACF,EAAG,CAAC,cACP6B,EAAAA,EAAAA,IAAoB,IAAI,GAC3BoC,EAAUvE,OAASwE,EAASxE,QACxBH,EAAAA,EAAAA,OAAcC,EAAAA,EAAAA,IAAakJ,EAAsB,CAChDvI,IAAK,EACLV,KAAM,SACNE,KAAM,QACN6C,QAASE,EAAShD,MAClBoJ,QAASpD,GACR,CACD9F,SAASC,EAAAA,EAAAA,KAAS,IAAMT,EAAO,KAAOA,EAAO,GAAK,EAChDU,EAAAA,EAAAA,IAAiB,cAEnBE,EAAG,GACF,EAAG,CAAC,cACP6B,EAAAA,EAAAA,IAAoB,IAAI,QAGhCL,EAAAA,EAAAA,IAAawH,EAAAA,EAAa,CACxB,aAAcpI,EAAUlB,MACxBmB,QAASgC,EAAenD,OACvB,KAAM,EAAG,CAAC,aAAc,aAC3B6B,EAAAA,EAAAA,IAAoB,MAAOhB,EAAY,EACrCgB,EAAAA,EAAAA,IAAoB,MAAOf,EAAY,EACrCgB,EAAAA,EAAAA,IAAaqH,EAAoB,CAC/BI,OAAQ,QACR7I,MAAO,mBACN,CACD8I,QAAQrJ,EAAAA,EAAAA,KAAS,IAAM,EACrB0B,EAAAA,EAAAA,IAAoB,MAAOd,EAAY,CACrCrB,EAAO,KAAOA,EAAO,IAAKmC,EAAAA,EAAAA,IAAoB,OAAQ,KAAM,cAAe,IAC1E4C,EAAezE,MAAM4E,OAAS,IAC1B/E,EAAAA,EAAAA,OAAc+B,EAAAA,EAAAA,IAAoB,OAAQZ,GAAYX,EAAAA,EAAAA,IAAiBoE,EAAezE,MAAM4E,QAAU,SAAU,KACjHzC,EAAAA,EAAAA,IAAoB,IAAI,QAGhCjC,SAASC,EAAAA,EAAAA,KAAS,IAAM,CACrB2C,EAAQ9C,QACJH,EAAAA,EAAAA,OAAc+B,EAAAA,EAAAA,IAAoB,MAAOX,EAAY,EACpDa,EAAAA,EAAAA,IAAamH,EAAwB,CACnCQ,KAAM,GACNC,SAAU,QAGmB,IAAhCjF,EAAezE,MAAM4E,SACnB/E,EAAAA,EAAAA,OAAc+B,EAAAA,EAAAA,IAAoB,MAAOa,EAAY,EACpDX,EAAAA,EAAAA,IAAaoH,EAAqB,CAAES,YAAa,6BAElD9J,EAAAA,EAAAA,OAAc+B,EAAAA,EAAAA,IAAoB,MAAOc,EAAa,EACrDZ,EAAAA,EAAAA,IAAa8H,EAAAA,EAAU,CACrB5C,MAAOvC,EAAezE,MACtB6J,aAAcnB,IACb,KAAM,EAAG,CAAC,gBAGvBpI,EAAG,SAITwB,EAAAA,EAAAA,IAAagI,EAAAA,EAAkB,CAC7B3I,QAASwC,EAAoB3D,MAC7B,mBAAoBN,EAAO,KAAOA,EAAO,GAAMqK,GAAkBpG,EAAqB3D,MAAQ+J,GAC9FlG,aAAcA,EAAa7D,MAC3BgK,aAAcpG,EAAe5D,MAC7BiK,QAASrB,IACR,KAAM,EAAG,CAAC,UAAW,eAAgB,kBACxC,CAEJ,I,UExgBA,MAAMrI,GAA2B,OAAgB,EAAQ,CAAC,CAAC,YAAY,qBAEvE,O", "sources": ["webpack://fuzz-web/./src/components/common/TestStateTag.vue?fb59", "webpack://fuzz-web/./src/components/common/TestStateTag.vue", "webpack://fuzz-web/./src/components/common/TestStateTag.vue?d49a", "webpack://fuzz-web/./src/components/test/TestMonitor.vue?60d6", "webpack://fuzz-web/./src/components/test/TestMonitor.vue", "webpack://fuzz-web/./src/components/test/TestMonitor.vue?602e", "webpack://fuzz-web/./src/views/testplan/TestRun.vue?0daa", "webpack://fuzz-web/./src/views/testplan/TestRun.vue", "webpack://fuzz-web/./src/views/testplan/TestRun.vue?f1b4"], "sourcesContent": ["import { defineComponent as _defineComponent } from 'vue'\nimport { toDisplayString as _toDisplayString, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, openBlock as _openBlock, createBlock as _createBlock } from \"vue\"\n\nimport { computed } from 'vue';\r\nimport { ExecutionState } from '@/api/appApi';\r\n\r\n\nexport default /*@__PURE__*/_defineComponent({\n  __name: 'TestStateTag',\n  props: {\n    state: {}\n  },\n  setup(__props: any) {\n\r\nconst props = __props;\r\n\r\nconst tagType = computed<'success' | 'warning' | 'danger' | 'info'>(() => {\r\n  switch (props.state) {\r\n    case ExecutionState.Success:\r\n      return 'success';\r\n    case ExecutionState.Running:\r\n      return 'warning';\r\n    case ExecutionState.Failure:\r\n      return 'danger';\r\n    case ExecutionState.Pending:\r\n    default:\r\n      return 'info';\r\n  }\r\n});\r\n\r\nconst getTestStateName = (state: string): 'Not Run' | 'Running' | 'Completed' | 'Faulted' | 'Paused' | 'Unknown' => {\r\n  switch (state) {\r\n    case ExecutionState.Running:\r\n      return 'Running';\r\n    case ExecutionState.Pending:\r\n      return 'Not Run';\r\n    case ExecutionState.Success:\r\n      return 'Completed';\r\n    case ExecutionState.Failure:\r\n      return 'Faulted';\r\n    case ExecutionState.Paused:\r\n      return 'Paused';\r\n    default:\r\n      return 'Unknown';\r\n  }\r\n};\r\n\r\nconst stateName = computed(() => {\r\n  return getTestStateName(props.state);\r\n});\r\n\nreturn (_ctx: any,_cache: any) => {\n  const _component_el_tag = _resolveComponent(\"el-tag\")!\n\n  return (_openBlock(), _createBlock(_component_el_tag, {\n    type: tagType.value,\n    size: \"small\"\n  }, {\n    default: _withCtx(() => [\n      _createTextVNode(_toDisplayString(stateName.value), 1)\n    ]),\n    _: 1\n  }, 8, [\"type\"]))\n}\n}\n\n})", "<template>\r\n  <el-tag :type=\"tagType\" size=\"small\">\r\n    {{ stateName }}\r\n  </el-tag>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { computed, defineProps } from 'vue';\r\nimport { ExecutionState } from '@/api/appApi';\r\n\r\nconst props = defineProps<{\r\n  state: string;\r\n}>();\r\n\r\nconst tagType = computed<'success' | 'warning' | 'danger' | 'info'>(() => {\r\n  switch (props.state) {\r\n    case ExecutionState.Success:\r\n      return 'success';\r\n    case ExecutionState.Running:\r\n      return 'warning';\r\n    case ExecutionState.Failure:\r\n      return 'danger';\r\n    case ExecutionState.Pending:\r\n    default:\r\n      return 'info';\r\n  }\r\n});\r\n\r\nconst getTestStateName = (state: string): 'Not Run' | 'Running' | 'Completed' | 'Faulted' | 'Paused' | 'Unknown' => {\r\n  switch (state) {\r\n    case ExecutionState.Running:\r\n      return 'Running';\r\n    case ExecutionState.Pending:\r\n      return 'Not Run';\r\n    case ExecutionState.Success:\r\n      return 'Completed';\r\n    case ExecutionState.Failure:\r\n      return 'Faulted';\r\n    case ExecutionState.Paused:\r\n      return 'Paused';\r\n    default:\r\n      return 'Unknown';\r\n  }\r\n};\r\n\r\nconst stateName = computed(() => {\r\n  return getTestStateName(props.state);\r\n});\r\n</script>\r\n", "import script from \"./TestStateTag.vue?vue&type=script&setup=true&lang=ts\"\nexport * from \"./TestStateTag.vue?vue&type=script&setup=true&lang=ts\"\n\nconst __exports__ = script;\n\nexport default __exports__", "import { defineComponent as _defineComponent } from 'vue'\nimport { unref as _unref, createVNode as _createVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, toDisplayString as _toDisplayString, createElementVNode as _createElementVNode, openBlock as _openBlock, createElementBlock as _createElementBlock, createCommentVNode as _createCommentVNode } from \"vue\"\n\nconst _hoisted_1 = {\n  key: 0,\n  class: \"test-monitor\"\n}\nconst _hoisted_2 = { class: \"status-area\" }\nconst _hoisted_3 = { class: \"compact-status\" }\nconst _hoisted_4 = { class: \"status-header-inline\" }\nconst _hoisted_5 = {\n  key: 0,\n  class: \"stats-row\"\n}\nconst _hoisted_6 = { class: \"stat-item success\" }\nconst _hoisted_7 = { class: \"stat-item failure\" }\nconst _hoisted_8 = { class: \"stat-item total\" }\n\nimport { computed } from 'vue';\r\nimport { CircleCheckFilled, CircleCloseFilled, InfoFilled } from '@element-plus/icons-vue';\r\nimport { TesterSnapshot } from '@/api/appApi';\r\nimport TestStateTag from '@/components/common/TestStateTag.vue';\r\n\r\n// 组件属性定义\r\n\nexport default /*@__PURE__*/_defineComponent({\n  __name: 'TestMonitor',\n  props: {\n    runStatus: {},\n    visible: { type: Boolean }\n  },\n  setup(__props: any) {\n\r\nconst props = __props;\r\n\r\nconst totalCount = computed(() => {\r\n  return props.runStatus.testResult?.totalCount || 0;\r\n});\r\n\r\nconst completedCount = computed(() => {\r\n  return (props.runStatus.testResult?.successCount || 0) + (props.runStatus.testResult?.failureCount || 0);\r\n});\r\n\nreturn (_ctx: any,_cache: any) => {\n  const _component_el_icon = _resolveComponent(\"el-icon\")!\n  const _component_el_progress = _resolveComponent(\"el-progress\")!\n\n  return (_ctx.visible)\n    ? (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [\n        _createElementVNode(\"div\", _hoisted_2, [\n          _createElementVNode(\"div\", _hoisted_3, [\n            _createElementVNode(\"div\", _hoisted_4, [\n              (totalCount.value > 0)\n                ? (_openBlock(), _createElementBlock(\"div\", _hoisted_5, [\n                    _createElementVNode(\"div\", _hoisted_6, [\n                      _createVNode(_component_el_icon, null, {\n                        default: _withCtx(() => [\n                          _createVNode(_unref(CircleCheckFilled))\n                        ]),\n                        _: 1\n                      }),\n                      _createElementVNode(\"span\", null, _toDisplayString(_ctx.runStatus.testResult?.successCount || 0), 1)\n                    ]),\n                    _createElementVNode(\"div\", _hoisted_7, [\n                      _createVNode(_component_el_icon, null, {\n                        default: _withCtx(() => [\n                          _createVNode(_unref(CircleCloseFilled))\n                        ]),\n                        _: 1\n                      }),\n                      _createElementVNode(\"span\", null, _toDisplayString(_ctx.runStatus.testResult?.failureCount || 0), 1)\n                    ]),\n                    _createElementVNode(\"div\", _hoisted_8, [\n                      _createVNode(_component_el_icon, null, {\n                        default: _withCtx(() => [\n                          _createVNode(_unref(InfoFilled))\n                        ]),\n                        _: 1\n                      }),\n                      _createElementVNode(\"span\", null, _toDisplayString(totalCount.value || 0), 1)\n                    ])\n                  ]))\n                : _createCommentVNode(\"\", true),\n              _createVNode(TestStateTag, {\n                state: _ctx.runStatus.processState\n              }, null, 8, [\"state\"])\n            ]),\n            _createVNode(_component_el_progress, {\n              percentage: totalCount.value > 0 ? Math.round(completedCount.value / totalCount.value * 100) : 0,\n              \"stroke-width\": 8\n            }, null, 8, [\"percentage\"])\n          ])\n        ])\n      ]))\n    : _createCommentVNode(\"\", true)\n}\n}\n\n})", "<template>\r\n  <div class=\"test-monitor\" v-if=\"visible\">\r\n    <!-- 优化后的紧凑测试状态区域 -->\r\n    <div class=\"status-area\">\r\n      <div class=\"compact-status\">\r\n        <!-- 内联状态头部 -->\r\n        <div class=\"status-header-inline\">\r\n          <div class=\"stats-row\" v-if=\"totalCount > 0\">\r\n            <div class=\"stat-item success\">\r\n              <el-icon>\r\n                <CircleCheckFilled />\r\n              </el-icon>\r\n              <span>{{ runStatus.testResult?.successCount || 0 }}</span>\r\n            </div>\r\n            <div class=\"stat-item failure\">\r\n              <el-icon>\r\n                <CircleCloseFilled />\r\n              </el-icon>\r\n              <span>{{ runStatus.testResult?.failureCount || 0 }}</span>\r\n            </div>\r\n\r\n            <div class=\"stat-item total\">\r\n              <el-icon>\r\n                <InfoFilled />\r\n              </el-icon>\r\n              <span>{{ totalCount || 0 }}</span>\r\n            </div>\r\n          </div>\r\n\r\n          <TestStateTag :state=\"runStatus.processState\" />\r\n        </div>\r\n\r\n        <!-- 进度条 -->\r\n        <el-progress :percentage=\"totalCount > 0 ? Math.round(completedCount / totalCount * 100) : 0\" :stroke-width=\"8\">\r\n        </el-progress>\r\n      </div>\r\n    </div>\r\n  </div>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\nimport { defineProps, computed } from 'vue';\r\nimport { CircleCheckFilled, CircleCloseFilled, InfoFilled } from '@element-plus/icons-vue';\r\nimport { TesterSnapshot } from '@/api/appApi';\r\nimport TestStateTag from '@/components/common/TestStateTag.vue';\r\n\r\n// 组件属性定义\r\nconst props = defineProps<{\r\n  runStatus: TesterSnapshot;\r\n  visible: boolean;\r\n}>();\r\n\r\nconst totalCount = computed(() => {\r\n  return props.runStatus.testResult?.totalCount || 0;\r\n});\r\n\r\nconst completedCount = computed(() => {\r\n  return (props.runStatus.testResult?.successCount || 0) + (props.runStatus.testResult?.failureCount || 0);\r\n});\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.test-monitor {\r\n  width: 100%;\r\n  margin-bottom: 12px;\r\n}\r\n\r\n/* 状态区域样式 */\r\n.status-area {\r\n  padding: 0 12px;\r\n\r\n  .compact-status {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 8px;\r\n  }\r\n\r\n  .status-header-inline {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n  }\r\n\r\n  .progress-numbers {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 4px;\r\n  }\r\n\r\n  .stats-row {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 10px;\r\n  }\r\n\r\n  .stat-item {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 4px;\r\n\r\n    &.success {\r\n      color: var(--el-color-success);\r\n    }\r\n\r\n    &.failure {\r\n      color: var(--el-color-danger);\r\n    }\r\n\r\n    &.total {\r\n      color: var(--el-color-primary);\r\n    }\r\n  }\r\n}\r\n</style>\r\n", "import script from \"./TestMonitor.vue?vue&type=script&setup=true&lang=ts\"\nexport * from \"./TestMonitor.vue?vue&type=script&setup=true&lang=ts\"\n\nimport \"./TestMonitor.vue?vue&type=style&index=0&id=ebdce83c&scoped=true&lang=scss\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-ebdce83c\"]])\n\nexport default __exports__", "import { defineComponent as _defineComponent } from 'vue'\nimport { createElementVNode as _createElementVNode, createTextVNode as _createTextVNode, resolveComponent as _resolveComponent, withCtx as _withCtx, openBlock as _openBlock, createBlock as _createBlock, createCommentVNode as _createCommentVNode, createVNode as _createVNode, toDisplayString as _toDisplayString, createElementBlock as _createElementBlock } from \"vue\"\n\nconst _hoisted_1 = { class: \"test-run-container\" }\nconst _hoisted_2 = { class: \"toolbar\" }\nconst _hoisted_3 = { class: \"action-buttons\" }\nconst _hoisted_4 = { class: \"content-area\" }\nconst _hoisted_5 = { class: \"test-cases-panel\" }\nconst _hoisted_6 = { class: \"card-header\" }\nconst _hoisted_7 = {\n  key: 0,\n  class: \"case-count\"\n}\nconst _hoisted_8 = {\n  key: 0,\n  class: \"loading-container\"\n}\nconst _hoisted_9 = {\n  key: 1,\n  class: \"empty-container\"\n}\nconst _hoisted_10 = {\n  key: 2,\n  class: \"case-list-wrapper\"\n}\n\nimport { ref, onMounted, onUnmounted, computed, shallowRef, triggerRef } from 'vue';\r\nimport { ElMessage } from 'element-plus';\r\nimport { appApi, TesterSnapshot, ExecutionState, isTesterCompleted } from '@/api/appApi';\r\nimport { CaseResult } from '@/api/interoperationApi';\r\nimport TestMonitor from '@/components/test/TestMonitor.vue';\r\nimport CaseDetailDialog from '@/components/test/CaseDetailDialog.vue';\r\nimport CaseList from '@/components/test/CaseList.vue';\r\n\r\n// 状态变量\r\nconst DEBOUNCE_DELAY = 50; // 50ms防抖延迟\r\n\r\n// 状态轮询定时器\r\n\nexport default /*@__PURE__*/_defineComponent({\n  __name: 'TestRun',\n  setup(__props) {\n\r\n/* eslint-disable */\r\nconst savedTestCases = ref<CaseResult[]>([]);\r\nconst loading = ref(true);\r\nconst starting = ref(false);\r\nconst stopping = ref(false);\r\nconst pausing = ref(false);\r\nconst resuming = ref(false);\r\nconst hasEverStarted = ref(false);\r\nconst runStatus = ref<TesterSnapshot>({\r\n  processState: ExecutionState.Pending,\r\n  currentOperation: '',\r\n  testResult: {\r\n    id: '',\r\n    resultFolderName: '',\r\n    testType: '',\r\n    creationTime: '',\r\n    totalCount: 0,\r\n    successCount: 0,\r\n    failureCount: 0\r\n  },\r\n  caseResults: []\r\n});\r\nconst lastUpdated = ref('');\r\n\r\n// 用例详情对话框相关\r\nconst detailDialogVisible = ref(false);\r\nconst selectedCaseId = ref<number | null>(null);\r\nconst testResultId = ref<string | null>(null);\r\n\r\n// 无需虚拟滚动相关变量，已移至 CaseList 组件\r\nconst isFirstFetch = ref(true);\r\n// 性能优化：使用shallowRef避免深度响应式\r\nconst allCases = shallowRef<CaseResult[]>([]);\r\nlet lastPendingIndex = 0;\r\n\r\n// 性能优化：维护一个ID到索引的映射表\r\nconst caseIndexMap = ref<Map<number, number>>(new Map());\r\n\r\n// 性能优化：用于强制触发视图更新的计数器\r\nconst updateCounter = ref(0);\r\n\r\n// 性能优化：防抖更新机制\r\nlet updateDebounceTimer: number | null = null;\r\nlet statusPollingTimer: number | null = null;\r\n\r\n// 计算属性\r\nconst isRunning = computed(() => {\r\n  return runStatus.value.processState === ExecutionState.Running;\r\n});\r\n\r\nconst isPaused = computed(() => {\r\n  return runStatus.value.processState === ExecutionState.Paused;\r\n});\r\n\r\nconst displayedCases = computed(() => {\r\n  // 依赖updateCounter确保在数据更新时重新计算\r\n  updateCounter.value;\r\n\r\n  console.log('displayedCases = computed(() => begin.');\r\n  // 如果测试正在运行，显示优化后的allCases中的用例\r\n  if ((isRunning.value || isPaused.value) && allCases.value?.length > 0) {\r\n    console.log('displayedCases = computed(() => end - running cases.');\r\n    return allCases.value;\r\n  }\r\n  // 否则显示保存的测试用例\r\n  console.log('displayedCases = computed(() => end - saved cases.');\r\n  return savedTestCases.value;\r\n});\r\n\r\n// 获取保存的测试用例\r\nconst fetchSavedTestCases = async () => {\r\n  loading.value = true;\r\n  try {\r\n    const response = await appApi.getSavedCases();\r\n    savedTestCases.value = response.data;\r\n\r\n    // 数据加载完成，无需额外操作\r\n    // 虚拟滚动已移至 CaseList 组件\r\n  } catch (error) {\r\n    console.error('获取保存的测试用例失败:', error);\r\n    ElMessage.error('Failed to fetch saved test cases');\r\n  } finally {\r\n    loading.value = false;\r\n  }\r\n};\r\n\r\n// 刷新测试用例列表\r\nconst refreshTestCases = () => {\r\n  fetchSavedTestCases();\r\n};\r\n\r\n// 开始测试执行\r\nconst startTestExecution = async () => {\r\n  if (savedTestCases.value.length === 0) {\r\n    ElMessage.warning('No test cases available to execute');\r\n    return;\r\n  }\r\n\r\n  starting.value = true;\r\n  try {\r\n    await appApi.startTest();\r\n    hasEverStarted.value = true;\r\n    ElMessage.success('Test execution started');\r\n\r\n    // 立即获取状态并开始轮询\r\n    await fetchTestStatus();\r\n    startStatusPolling();\r\n  } catch (error) {\r\n    console.error('启动测试失败:', error);\r\n    ElMessage.error('Failed to start test execution');\r\n  } finally {\r\n    starting.value = false;\r\n  }\r\n};\r\n\r\n// 暂停测试执行\r\nconst pauseTestExecution = async () => {\r\n  pausing.value = true;\r\n  try {\r\n    await appApi.pauseTest();\r\n    ElMessage.success('Test execution paused');\r\n\r\n    // 立即更新状态\r\n    await fetchTestStatus();\r\n  } catch (error) {\r\n    console.error('暂停测试失败:', error);\r\n    ElMessage.error('Failed to pause test execution');\r\n  } finally {\r\n    pausing.value = false;\r\n  }\r\n};\r\n\r\n// 恢复测试执行\r\nconst resumeTestExecution = async () => {\r\n  resuming.value = true;\r\n  try {\r\n    await appApi.resumeTest();\r\n    ElMessage.success('Test execution resumed');\r\n\r\n    // 立即更新状态\r\n    await fetchTestStatus();\r\n  } catch (error) {\r\n    console.error('恢复测试失败:', error);\r\n    ElMessage.error('Failed to resume test execution');\r\n  } finally {\r\n    resuming.value = false;\r\n  }\r\n};\r\n\r\n// 停止测试执行\r\nconst stopTestExecution = async () => {\r\n  stopping.value = true;\r\n  try {\r\n    await appApi.stopTest();\r\n    ElMessage.success('Test execution stopped');\r\n\r\n    // 立即更新状态\r\n    await fetchTestStatus();\r\n  } catch (error) {\r\n    console.error('停止测试失败:', error);\r\n    ElMessage.error('Failed to stop test execution');\r\n  } finally {\r\n    stopping.value = false;\r\n  }\r\n};\r\n\r\n// 获取测试状态\r\nconst fetchTestStatus = async () => {\r\n  try {\r\n    let response;\r\n\r\n    if (isFirstFetch.value) {\r\n      // 第一次获取全部数据\r\n      response = await appApi.getTestStatus();\r\n      isFirstFetch.value = false;\r\n\r\n      runStatus.value = response.data;\r\n      allCases.value = response.data.caseResults || [];\r\n\r\n      // 性能优化：建立索引映射\r\n      rebuildIndexMap();\r\n\r\n      // 找到第一个Pending状态的索引\r\n      lastPendingIndex = findFirstPendingIndex(allCases.value);\r\n    } else {\r\n      // 后续只获取状态可能变化的用例\r\n      const pageNumber = Math.floor(lastPendingIndex / 100) + 1;\r\n      response = await appApi.getTestStatusPaged({\r\n        pageNumber,\r\n        pageSize: 100\r\n      });\r\n\r\n      // 更新基本状态\r\n      runStatus.value.processState = response.data.processState;\r\n      runStatus.value.currentOperation = response.data.currentOperation;\r\n      // runStatus.value.testResult = response.data.testResult;\r\n\r\n      // 更新用例状态\r\n      if (response.data.pagedCaseResult?.items) {\r\n        updateCaseResults(response.data.pagedCaseResult.items);\r\n        lastPendingIndex = findFirstPendingIndex(allCases.value);\r\n      }\r\n\r\n      // 将更新后的所有用例赋值给runStatus\r\n      // runStatus.value.caseResults = allCases.value;\r\n    }\r\n\r\n    lastUpdated.value = new Date().toLocaleString();\r\n\r\n    // 测试完成时停止轮询\r\n    if (isTesterCompleted(runStatus.value) && statusPollingTimer) {\r\n      refreshTestCases();\r\n      stopStatusPolling();\r\n    }\r\n\r\n  } catch (error) {\r\n    console.error('获取测试状态失败:', error);\r\n  }\r\n};\r\n\r\n// 查找第一个Pending状态的用例索引\r\nconst findFirstPendingIndex = (cases: CaseResult[]) => {\r\n  console.log('findFirstPendingIndex begin');\r\n  for (let i = 0; i < cases.length; i++) {\r\n    if (cases[i].state === ExecutionState.Pending) {\r\n      console.log('findFirstPendingIndex end');\r\n      return i;\r\n    }\r\n  }\r\n  console.log('findFirstPendingIndex end');\r\n  return cases.length;\r\n};\r\n\r\n// 性能优化：重建索引映射表\r\nconst rebuildIndexMap = () => {\r\n  console.log('rebuildIndexMap begin');\r\n  caseIndexMap.value.clear();\r\n  allCases.value.forEach((caseItem: CaseResult, index: number) => {\r\n    caseIndexMap.value.set(caseItem.id, index);\r\n  });\r\n  console.log('rebuildIndexMap end, map size:', caseIndexMap.value.size);\r\n};\r\n\r\n// 性能优化：防抖触发视图更新\r\nconst debouncedTriggerUpdate = () => {\r\n  if (updateDebounceTimer) {\r\n    clearTimeout(updateDebounceTimer);\r\n  }\r\n  updateDebounceTimer = window.setTimeout(() => {\r\n    triggerRef(allCases);\r\n    updateCounter.value++;\r\n    updateDebounceTimer = null;\r\n  }, DEBOUNCE_DELAY);\r\n};\r\n\r\n// 更新用例结果 - 性能优化版本\r\nconst updateCaseResults = (updatedCases: CaseResult[]) => {\r\n  console.log('updateCaseResults begin, updating', updatedCases.length, 'cases');\r\n\r\n  if (!allCases.value || allCases.value.length === 0) {\r\n    allCases.value = updatedCases;\r\n    rebuildIndexMap();\r\n    // 防抖触发响应式更新\r\n    debouncedTriggerUpdate();\r\n    console.log('updateCaseResults end - initial load');\r\n    return;\r\n  }\r\n\r\n  // 批量更新：收集所有需要更新的索引和数据\r\n  const updates: Array<{index: number, case: CaseResult}> = [];\r\n  let mapHits = 0;\r\n  let mapMisses = 0;\r\n\r\n  for (const updated of updatedCases) {\r\n    const index = caseIndexMap.value.get(updated.id);\r\n    if (index !== undefined && index < allCases.value.length) {\r\n      // 验证索引是否仍然有效（防止数组结构变化导致的不一致）\r\n      if (allCases.value[index].id === updated.id) {\r\n        updates.push({index, case: updated});\r\n        mapHits++;\r\n      } else {\r\n        // 索引映射失效，需要重建\r\n        console.warn('Index map inconsistency detected, rebuilding...');\r\n        rebuildIndexMap();\r\n        const newIndex = caseIndexMap.value.get(updated.id);\r\n        if (newIndex !== undefined && newIndex < allCases.value.length) {\r\n          updates.push({index: newIndex, case: updated});\r\n          mapHits++;\r\n        } else {\r\n          mapMisses++;\r\n        }\r\n      }\r\n    } else {\r\n      mapMisses++;\r\n    }\r\n  }\r\n\r\n  // 一次性应用所有更新，避免逐个触发响应式\r\n  updates.forEach(({index, case: updatedCase}) => {\r\n    allCases.value[index] = updatedCase;\r\n  });\r\n\r\n  // 只有在有更新时才防抖触发响应式更新\r\n  if (updates.length > 0) {\r\n    debouncedTriggerUpdate();\r\n  }\r\n\r\n  console.log('updateCaseResults end - map hits:', mapHits, 'misses:', mapMisses, 'updates:', updates.length);\r\n};\r\n\r\n// 开始状态轮询 - 保持300毫秒间隔\r\nconst startStatusPolling = () => {\r\n  // 清除可能存在的轮询定时器\r\n  stopStatusPolling();\r\n\r\n  // 重置首次获取标志\r\n  isFirstFetch.value = true;\r\n\r\n  // 清空索引映射，将在首次获取时重建\r\n  caseIndexMap.value.clear();\r\n\r\n  statusPollingTimer = window.setInterval(fetchTestStatus, 300);\r\n};\r\n\r\n// 停止状态轮询\r\nconst stopStatusPolling = () => {\r\n  if (statusPollingTimer) {\r\n    clearInterval(statusPollingTimer);\r\n    statusPollingTimer = null;\r\n  }\r\n};\r\n\r\n// 查看用例详情\r\nconst viewCaseDetail = (caseResult: CaseResult) => {\r\n  testResultId.value = caseResult.testResultId;\r\n  selectedCaseId.value = caseResult.id;\r\n  detailDialogVisible.value = true;\r\n};\r\n\r\n// 关闭详情对话框\r\nconst closeDetailDialog = () => {\r\n  detailDialogVisible.value = false;\r\n  selectedCaseId.value = null;\r\n};\r\n\r\n// 虚拟滚动相关代码已移至 CaseList 组件\r\n\r\n// 组件挂载时获取保存的测试用例和测试状态\r\nonMounted(() => {\r\n  fetchSavedTestCases();\r\n  fetchTestStatus().then(() => {\r\n    // 如果测试正在运行，开始轮询\r\n    if (isRunning.value) {\r\n      hasEverStarted.value = true;\r\n      startStatusPolling();\r\n    }\r\n  });\r\n});\r\n\r\n// 组件卸载时停止轮询\r\nonUnmounted(() => {\r\n  stopStatusPolling();\r\n});\r\n\nreturn (_ctx: any,_cache: any) => {\n  const _component_el_button = _resolveComponent(\"el-button\")!\n  const _component_el_skeleton = _resolveComponent(\"el-skeleton\")!\n  const _component_el_empty = _resolveComponent(\"el-empty\")!\n  const _component_el_card = _resolveComponent(\"el-card\")!\n\n  return (_openBlock(), _createElementBlock(\"div\", _hoisted_1, [\n    _createElementVNode(\"div\", _hoisted_2, [\n      _cache[5] || (_cache[5] = _createElementVNode(\"h3\", null, \"Test Execution\", -1)),\n      _createElementVNode(\"div\", _hoisted_3, [\n        (!isRunning.value && !isPaused.value)\n          ? (_openBlock(), _createBlock(_component_el_button, {\n              key: 0,\n              type: \"success\",\n              size: \"small\",\n              loading: starting.value,\n              onClick: startTestExecution,\n              disabled: savedTestCases.value.length === 0\n            }, {\n              default: _withCtx(() => _cache[1] || (_cache[1] = [\n                _createTextVNode(\" Start \")\n              ])),\n              _: 1\n            }, 8, [\"loading\", \"disabled\"]))\n          : _createCommentVNode(\"\", true),\n        (isRunning.value && !isPaused.value)\n          ? (_openBlock(), _createBlock(_component_el_button, {\n              key: 1,\n              type: \"warning\",\n              size: \"small\",\n              loading: pausing.value,\n              onClick: pauseTestExecution\n            }, {\n              default: _withCtx(() => _cache[2] || (_cache[2] = [\n                _createTextVNode(\" Pause \")\n              ])),\n              _: 1\n            }, 8, [\"loading\"]))\n          : _createCommentVNode(\"\", true),\n        (isPaused.value)\n          ? (_openBlock(), _createBlock(_component_el_button, {\n              key: 2,\n              type: \"info\",\n              size: \"small\",\n              loading: resuming.value,\n              onClick: resumeTestExecution\n            }, {\n              default: _withCtx(() => _cache[3] || (_cache[3] = [\n                _createTextVNode(\" Resume \")\n              ])),\n              _: 1\n            }, 8, [\"loading\"]))\n          : _createCommentVNode(\"\", true),\n        (isRunning.value || isPaused.value)\n          ? (_openBlock(), _createBlock(_component_el_button, {\n              key: 3,\n              type: \"danger\",\n              size: \"small\",\n              loading: stopping.value,\n              onClick: stopTestExecution\n            }, {\n              default: _withCtx(() => _cache[4] || (_cache[4] = [\n                _createTextVNode(\" Stop \")\n              ])),\n              _: 1\n            }, 8, [\"loading\"]))\n          : _createCommentVNode(\"\", true)\n      ])\n    ]),\n    _createVNode(TestMonitor, {\n      \"run-status\": runStatus.value,\n      visible: hasEverStarted.value\n    }, null, 8, [\"run-status\", \"visible\"]),\n    _createElementVNode(\"div\", _hoisted_4, [\n      _createElementVNode(\"div\", _hoisted_5, [\n        _createVNode(_component_el_card, {\n          shadow: \"never\",\n          class: \"test-cases-card\"\n        }, {\n          header: _withCtx(() => [\n            _createElementVNode(\"div\", _hoisted_6, [\n              _cache[6] || (_cache[6] = _createElementVNode(\"span\", null, \"Test Cases\", -1)),\n              (displayedCases.value.length > 0)\n                ? (_openBlock(), _createElementBlock(\"span\", _hoisted_7, _toDisplayString(displayedCases.value.length) + \" cases\", 1))\n                : _createCommentVNode(\"\", true)\n            ])\n          ]),\n          default: _withCtx(() => [\n            (loading.value)\n              ? (_openBlock(), _createElementBlock(\"div\", _hoisted_8, [\n                  _createVNode(_component_el_skeleton, {\n                    rows: 10,\n                    animated: \"\"\n                  })\n                ]))\n              : (displayedCases.value.length === 0)\n                ? (_openBlock(), _createElementBlock(\"div\", _hoisted_9, [\n                    _createVNode(_component_el_empty, { description: \"No test cases found\" })\n                  ]))\n                : (_openBlock(), _createElementBlock(\"div\", _hoisted_10, [\n                    _createVNode(CaseList, {\n                      cases: displayedCases.value,\n                      onViewDetail: viewCaseDetail\n                    }, null, 8, [\"cases\"])\n                  ]))\n          ]),\n          _: 1\n        })\n      ])\n    ]),\n    _createVNode(CaseDetailDialog, {\n      visible: detailDialogVisible.value,\n      \"onUpdate:visible\": _cache[0] || (_cache[0] = ($event: any) => ((detailDialogVisible).value = $event)),\n      testResultId: testResultId.value,\n      caseResultId: selectedCaseId.value,\n      onClose: closeDetailDialog\n    }, null, 8, [\"visible\", \"testResultId\", \"caseResultId\"])\n  ]))\n}\n}\n\n})", "<template>\r\n  <div class=\"test-run-container\">\r\n    <div class=\"toolbar\">\r\n      <h3>Test Execution</h3>\r\n      <div class=\"action-buttons\">\r\n        <!-- 开始按钮：在测试未运行且未暂停时显示 -->\r\n        <el-button\r\n          v-if=\"!isRunning && !isPaused\"\r\n          type=\"success\"\r\n          size=\"small\"\r\n          :loading=\"starting\"\r\n          @click=\"startTestExecution\"\r\n          :disabled=\"savedTestCases.length === 0\">\r\n          Start\r\n        </el-button>\r\n\r\n        <!-- 暂停按钮：只在测试运行且未暂停时显示 -->\r\n        <el-button\r\n          v-if=\"isRunning && !isPaused\"\r\n          type=\"warning\"\r\n          size=\"small\"\r\n          :loading=\"pausing\"\r\n          @click=\"pauseTestExecution\">\r\n          Pause\r\n        </el-button>\r\n\r\n        <!-- 恢复按钮：只在测试已暂停时显示 -->\r\n        <el-button\r\n          v-if=\"isPaused\"\r\n          type=\"info\"\r\n          size=\"small\"\r\n          :loading=\"resuming\"\r\n          @click=\"resumeTestExecution\">\r\n          Resume\r\n        </el-button>\r\n\r\n        <!-- 停止按钮：在测试运行或暂停时都显示 -->\r\n        <el-button\r\n          v-if=\"isRunning || isPaused\"\r\n          type=\"danger\"\r\n          size=\"small\"\r\n          :loading=\"stopping\"\r\n          @click=\"stopTestExecution\">\r\n          Stop\r\n        </el-button>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 使用新的TestMonitor组件 -->\r\n    <TestMonitor :run-status=\"runStatus\" :visible=\"hasEverStarted\" />\r\n\r\n    <div class=\"content-area\">\r\n      <div class=\"test-cases-panel\">\r\n        <el-card shadow=\"never\" class=\"test-cases-card\">\r\n          <template #header>\r\n            <div class=\"card-header\">\r\n              <span>Test Cases</span>\r\n              <span v-if=\"displayedCases.length > 0\" class=\"case-count\">{{ displayedCases.length }} cases</span>\r\n            </div>\r\n          </template>\r\n          <div v-if=\"loading\" class=\"loading-container\">\r\n            <el-skeleton :rows=\"10\" animated />\r\n          </div>\r\n          <div v-else-if=\"displayedCases.length === 0\" class=\"empty-container\">\r\n            <el-empty description=\"No test cases found\" />\r\n          </div>\r\n          <div v-else class=\"case-list-wrapper\">\r\n            <CaseList\r\n              :cases=\"displayedCases\"\r\n              @view-detail=\"viewCaseDetail\"\r\n            />\r\n          </div>\r\n        </el-card>\r\n      </div>\r\n    </div>\r\n\r\n    <!-- 添加用例详情对话框组件 -->\r\n    <CaseDetailDialog\r\n      v-model:visible=\"detailDialogVisible\"\r\n      :testResultId=\"testResultId\"\r\n      :caseResultId=\"selectedCaseId\"\r\n      @close=\"closeDetailDialog\"\r\n    />\r\n  </div>\r\n</template>\r\n\r\n<script setup lang=\"ts\">\r\n/* eslint-disable */\r\nimport { ref, onMounted, onUnmounted, computed, shallowRef, triggerRef } from 'vue';\r\nimport { ElMessage } from 'element-plus';\r\nimport { appApi, TesterSnapshot, ExecutionState, isTesterCompleted } from '@/api/appApi';\r\nimport { CaseResult } from '@/api/interoperationApi';\r\nimport TestMonitor from '@/components/test/TestMonitor.vue';\r\nimport CaseDetailDialog from '@/components/test/CaseDetailDialog.vue';\r\nimport CaseList from '@/components/test/CaseList.vue';\r\n\r\n// 状态变量\r\nconst savedTestCases = ref<CaseResult[]>([]);\r\nconst loading = ref(true);\r\nconst starting = ref(false);\r\nconst stopping = ref(false);\r\nconst pausing = ref(false);\r\nconst resuming = ref(false);\r\nconst hasEverStarted = ref(false);\r\nconst runStatus = ref<TesterSnapshot>({\r\n  processState: ExecutionState.Pending,\r\n  currentOperation: '',\r\n  testResult: {\r\n    id: '',\r\n    resultFolderName: '',\r\n    testType: '',\r\n    creationTime: '',\r\n    totalCount: 0,\r\n    successCount: 0,\r\n    failureCount: 0\r\n  },\r\n  caseResults: []\r\n});\r\nconst lastUpdated = ref('');\r\n\r\n// 用例详情对话框相关\r\nconst detailDialogVisible = ref(false);\r\nconst selectedCaseId = ref<number | null>(null);\r\nconst testResultId = ref<string | null>(null);\r\n\r\n// 无需虚拟滚动相关变量，已移至 CaseList 组件\r\nconst isFirstFetch = ref(true);\r\n// 性能优化：使用shallowRef避免深度响应式\r\nconst allCases = shallowRef<CaseResult[]>([]);\r\nlet lastPendingIndex = 0;\r\n\r\n// 性能优化：维护一个ID到索引的映射表\r\nconst caseIndexMap = ref<Map<number, number>>(new Map());\r\n\r\n// 性能优化：用于强制触发视图更新的计数器\r\nconst updateCounter = ref(0);\r\n\r\n// 性能优化：防抖更新机制\r\nlet updateDebounceTimer: number | null = null;\r\nconst DEBOUNCE_DELAY = 50; // 50ms防抖延迟\r\n\r\n// 状态轮询定时器\r\nlet statusPollingTimer: number | null = null;\r\n\r\n// 计算属性\r\nconst isRunning = computed(() => {\r\n  return runStatus.value.processState === ExecutionState.Running;\r\n});\r\n\r\nconst isPaused = computed(() => {\r\n  return runStatus.value.processState === ExecutionState.Paused;\r\n});\r\n\r\nconst displayedCases = computed(() => {\r\n  // 依赖updateCounter确保在数据更新时重新计算\r\n  updateCounter.value;\r\n\r\n  console.log('displayedCases = computed(() => begin.');\r\n  // 如果测试正在运行，显示优化后的allCases中的用例\r\n  if ((isRunning.value || isPaused.value) && allCases.value?.length > 0) {\r\n    console.log('displayedCases = computed(() => end - running cases.');\r\n    return allCases.value;\r\n  }\r\n  // 否则显示保存的测试用例\r\n  console.log('displayedCases = computed(() => end - saved cases.');\r\n  return savedTestCases.value;\r\n});\r\n\r\n// 获取保存的测试用例\r\nconst fetchSavedTestCases = async () => {\r\n  loading.value = true;\r\n  try {\r\n    const response = await appApi.getSavedCases();\r\n    savedTestCases.value = response.data;\r\n\r\n    // 数据加载完成，无需额外操作\r\n    // 虚拟滚动已移至 CaseList 组件\r\n  } catch (error) {\r\n    console.error('获取保存的测试用例失败:', error);\r\n    ElMessage.error('Failed to fetch saved test cases');\r\n  } finally {\r\n    loading.value = false;\r\n  }\r\n};\r\n\r\n// 刷新测试用例列表\r\nconst refreshTestCases = () => {\r\n  fetchSavedTestCases();\r\n};\r\n\r\n// 开始测试执行\r\nconst startTestExecution = async () => {\r\n  if (savedTestCases.value.length === 0) {\r\n    ElMessage.warning('No test cases available to execute');\r\n    return;\r\n  }\r\n\r\n  starting.value = true;\r\n  try {\r\n    await appApi.startTest();\r\n    hasEverStarted.value = true;\r\n    ElMessage.success('Test execution started');\r\n\r\n    // 立即获取状态并开始轮询\r\n    await fetchTestStatus();\r\n    startStatusPolling();\r\n  } catch (error) {\r\n    console.error('启动测试失败:', error);\r\n    ElMessage.error('Failed to start test execution');\r\n  } finally {\r\n    starting.value = false;\r\n  }\r\n};\r\n\r\n// 暂停测试执行\r\nconst pauseTestExecution = async () => {\r\n  pausing.value = true;\r\n  try {\r\n    await appApi.pauseTest();\r\n    ElMessage.success('Test execution paused');\r\n\r\n    // 立即更新状态\r\n    await fetchTestStatus();\r\n  } catch (error) {\r\n    console.error('暂停测试失败:', error);\r\n    ElMessage.error('Failed to pause test execution');\r\n  } finally {\r\n    pausing.value = false;\r\n  }\r\n};\r\n\r\n// 恢复测试执行\r\nconst resumeTestExecution = async () => {\r\n  resuming.value = true;\r\n  try {\r\n    await appApi.resumeTest();\r\n    ElMessage.success('Test execution resumed');\r\n\r\n    // 立即更新状态\r\n    await fetchTestStatus();\r\n  } catch (error) {\r\n    console.error('恢复测试失败:', error);\r\n    ElMessage.error('Failed to resume test execution');\r\n  } finally {\r\n    resuming.value = false;\r\n  }\r\n};\r\n\r\n// 停止测试执行\r\nconst stopTestExecution = async () => {\r\n  stopping.value = true;\r\n  try {\r\n    await appApi.stopTest();\r\n    ElMessage.success('Test execution stopped');\r\n\r\n    // 立即更新状态\r\n    await fetchTestStatus();\r\n  } catch (error) {\r\n    console.error('停止测试失败:', error);\r\n    ElMessage.error('Failed to stop test execution');\r\n  } finally {\r\n    stopping.value = false;\r\n  }\r\n};\r\n\r\n// 获取测试状态\r\nconst fetchTestStatus = async () => {\r\n  try {\r\n    let response;\r\n\r\n    if (isFirstFetch.value) {\r\n      // 第一次获取全部数据\r\n      response = await appApi.getTestStatus();\r\n      isFirstFetch.value = false;\r\n\r\n      runStatus.value = response.data;\r\n      allCases.value = response.data.caseResults || [];\r\n\r\n      // 性能优化：建立索引映射\r\n      rebuildIndexMap();\r\n\r\n      // 找到第一个Pending状态的索引\r\n      lastPendingIndex = findFirstPendingIndex(allCases.value);\r\n    } else {\r\n      // 后续只获取状态可能变化的用例\r\n      const pageNumber = Math.floor(lastPendingIndex / 100) + 1;\r\n      response = await appApi.getTestStatusPaged({\r\n        pageNumber,\r\n        pageSize: 100\r\n      });\r\n\r\n      // 更新基本状态\r\n      runStatus.value.processState = response.data.processState;\r\n      runStatus.value.currentOperation = response.data.currentOperation;\r\n      // runStatus.value.testResult = response.data.testResult;\r\n\r\n      // 更新用例状态\r\n      if (response.data.pagedCaseResult?.items) {\r\n        updateCaseResults(response.data.pagedCaseResult.items);\r\n        lastPendingIndex = findFirstPendingIndex(allCases.value);\r\n      }\r\n\r\n      // 将更新后的所有用例赋值给runStatus\r\n      // runStatus.value.caseResults = allCases.value;\r\n    }\r\n\r\n    lastUpdated.value = new Date().toLocaleString();\r\n\r\n    // 测试完成时停止轮询\r\n    if (isTesterCompleted(runStatus.value) && statusPollingTimer) {\r\n      refreshTestCases();\r\n      stopStatusPolling();\r\n    }\r\n\r\n  } catch (error) {\r\n    console.error('获取测试状态失败:', error);\r\n  }\r\n};\r\n\r\n// 查找第一个Pending状态的用例索引\r\nconst findFirstPendingIndex = (cases: CaseResult[]) => {\r\n  console.log('findFirstPendingIndex begin');\r\n  for (let i = 0; i < cases.length; i++) {\r\n    if (cases[i].state === ExecutionState.Pending) {\r\n      console.log('findFirstPendingIndex end');\r\n      return i;\r\n    }\r\n  }\r\n  console.log('findFirstPendingIndex end');\r\n  return cases.length;\r\n};\r\n\r\n// 性能优化：重建索引映射表\r\nconst rebuildIndexMap = () => {\r\n  console.log('rebuildIndexMap begin');\r\n  caseIndexMap.value.clear();\r\n  allCases.value.forEach((caseItem: CaseResult, index: number) => {\r\n    caseIndexMap.value.set(caseItem.id, index);\r\n  });\r\n  console.log('rebuildIndexMap end, map size:', caseIndexMap.value.size);\r\n};\r\n\r\n// 性能优化：防抖触发视图更新\r\nconst debouncedTriggerUpdate = () => {\r\n  if (updateDebounceTimer) {\r\n    clearTimeout(updateDebounceTimer);\r\n  }\r\n  updateDebounceTimer = window.setTimeout(() => {\r\n    triggerRef(allCases);\r\n    updateCounter.value++;\r\n    updateDebounceTimer = null;\r\n  }, DEBOUNCE_DELAY);\r\n};\r\n\r\n// 更新用例结果 - 性能优化版本\r\nconst updateCaseResults = (updatedCases: CaseResult[]) => {\r\n  console.log('updateCaseResults begin, updating', updatedCases.length, 'cases');\r\n\r\n  if (!allCases.value || allCases.value.length === 0) {\r\n    allCases.value = updatedCases;\r\n    rebuildIndexMap();\r\n    // 防抖触发响应式更新\r\n    debouncedTriggerUpdate();\r\n    console.log('updateCaseResults end - initial load');\r\n    return;\r\n  }\r\n\r\n  // 批量更新：收集所有需要更新的索引和数据\r\n  const updates: Array<{index: number, case: CaseResult}> = [];\r\n  let mapHits = 0;\r\n  let mapMisses = 0;\r\n\r\n  for (const updated of updatedCases) {\r\n    const index = caseIndexMap.value.get(updated.id);\r\n    if (index !== undefined && index < allCases.value.length) {\r\n      // 验证索引是否仍然有效（防止数组结构变化导致的不一致）\r\n      if (allCases.value[index].id === updated.id) {\r\n        updates.push({index, case: updated});\r\n        mapHits++;\r\n      } else {\r\n        // 索引映射失效，需要重建\r\n        console.warn('Index map inconsistency detected, rebuilding...');\r\n        rebuildIndexMap();\r\n        const newIndex = caseIndexMap.value.get(updated.id);\r\n        if (newIndex !== undefined && newIndex < allCases.value.length) {\r\n          updates.push({index: newIndex, case: updated});\r\n          mapHits++;\r\n        } else {\r\n          mapMisses++;\r\n        }\r\n      }\r\n    } else {\r\n      mapMisses++;\r\n    }\r\n  }\r\n\r\n  // 一次性应用所有更新，避免逐个触发响应式\r\n  updates.forEach(({index, case: updatedCase}) => {\r\n    allCases.value[index] = updatedCase;\r\n  });\r\n\r\n  // 只有在有更新时才防抖触发响应式更新\r\n  if (updates.length > 0) {\r\n    debouncedTriggerUpdate();\r\n  }\r\n\r\n  console.log('updateCaseResults end - map hits:', mapHits, 'misses:', mapMisses, 'updates:', updates.length);\r\n};\r\n\r\n// 开始状态轮询 - 保持300毫秒间隔\r\nconst startStatusPolling = () => {\r\n  // 清除可能存在的轮询定时器\r\n  stopStatusPolling();\r\n\r\n  // 重置首次获取标志\r\n  isFirstFetch.value = true;\r\n\r\n  // 清空索引映射，将在首次获取时重建\r\n  caseIndexMap.value.clear();\r\n\r\n  statusPollingTimer = window.setInterval(fetchTestStatus, 300);\r\n};\r\n\r\n// 停止状态轮询\r\nconst stopStatusPolling = () => {\r\n  if (statusPollingTimer) {\r\n    clearInterval(statusPollingTimer);\r\n    statusPollingTimer = null;\r\n  }\r\n};\r\n\r\n// 查看用例详情\r\nconst viewCaseDetail = (caseResult: CaseResult) => {\r\n  testResultId.value = caseResult.testResultId;\r\n  selectedCaseId.value = caseResult.id;\r\n  detailDialogVisible.value = true;\r\n};\r\n\r\n// 关闭详情对话框\r\nconst closeDetailDialog = () => {\r\n  detailDialogVisible.value = false;\r\n  selectedCaseId.value = null;\r\n};\r\n\r\n// 虚拟滚动相关代码已移至 CaseList 组件\r\n\r\n// 组件挂载时获取保存的测试用例和测试状态\r\nonMounted(() => {\r\n  fetchSavedTestCases();\r\n  fetchTestStatus().then(() => {\r\n    // 如果测试正在运行，开始轮询\r\n    if (isRunning.value) {\r\n      hasEverStarted.value = true;\r\n      startStatusPolling();\r\n    }\r\n  });\r\n});\r\n\r\n// 组件卸载时停止轮询\r\nonUnmounted(() => {\r\n  stopStatusPolling();\r\n});\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n.test-run-container {\r\n  display: flex;\r\n  flex-direction: column;\r\n  flex: 1;\r\n  padding: 20px;\r\n}\r\n\r\n/* 顶部工具栏样式 */\r\n.toolbar {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 8px 12px;\r\n  border-bottom: 1px solid #dcdfe6;\r\n  margin-bottom: 12px;\r\n\r\n  h3 {\r\n    margin: 0;\r\n    font-size: 16px;\r\n    font-weight: 500;\r\n    color: #303133;\r\n  }\r\n\r\n  .action-buttons {\r\n    display: flex;\r\n    gap: 8px;\r\n  }\r\n}\r\n\r\n/* 内容区域样式 */\r\n.content-area {\r\n  flex: 1;\r\n  min-height: 0;\r\n  display: flex;\r\n  flex-direction: column;\r\n}\r\n\r\n.test-cases-panel {\r\n  flex: 1;\r\n  min-height: 0;\r\n}\r\n\r\n.test-cases-card {\r\n  height: 100%;\r\n  display: flex;\r\n  flex-direction: column;\r\n  box-shadow: none;\r\n\r\n  :deep(.el-card__header) {\r\n    padding: 12px 16px;\r\n    border-bottom: 2px solid #f0f0f0;\r\n  }\r\n\r\n  :deep(.el-card__body) {\r\n    flex: 1;\r\n    overflow: hidden;\r\n    padding: 0;\r\n  }\r\n}\r\n\r\n.card-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n\r\n  .case-count {\r\n    font-size: 12px;\r\n    color: #909399;\r\n  }\r\n}\r\n\r\n.loading-container,\r\n.empty-container {\r\n  height: 100%;\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  padding: 20px;\r\n}\r\n\r\n.case-list-wrapper {\r\n  height: 100%;\r\n}\r\n</style>\r\n", "import script from \"./TestRun.vue?vue&type=script&setup=true&lang=ts\"\nexport * from \"./TestRun.vue?vue&type=script&setup=true&lang=ts\"\n\nimport \"./TestRun.vue?vue&type=style&index=0&id=47d5a254&scoped=true&lang=scss\"\n\nimport exportComponent from \"../../../node_modules/vue-loader/dist/exportHelper.js\"\nconst __exports__ = /*#__PURE__*/exportComponent(script, [['__scopeId',\"data-v-47d5a254\"]])\n\nexport default __exports__"], "names": ["_defineComponent", "__name", "props", "state", "setup", "__props", "tagType", "computed", "ExecutionState", "Success", "Running", "Failure", "Pending", "getTestStateName", "Paused", "stateName", "_ctx", "_cache", "_component_el_tag", "_resolveComponent", "_openBlock", "_createBlock", "type", "value", "size", "default", "_withCtx", "_createTextVNode", "_toDisplayString", "_", "__exports__", "_hoisted_1", "key", "class", "_hoisted_2", "_hoisted_3", "_hoisted_4", "_hoisted_5", "_hoisted_6", "_hoisted_7", "_hoisted_8", "runStatus", "visible", "Boolean", "totalCount", "testResult", "completedCount", "successCount", "failureCount", "_component_el_icon", "_component_el_progress", "_createElementBlock", "_createElementVNode", "_createVNode", "_unref", "CircleCheckFilled", "CircleCloseFilled", "InfoFilled", "_createCommentVNode", "TestStateTag", "processState", "percentage", "Math", "round", "_hoisted_9", "_hoisted_10", "DEBOUNCE_DELAY", "savedTestCases", "ref", "loading", "starting", "stopping", "pausing", "resuming", "hasEverStarted", "currentOperation", "id", "resultFolderName", "testType", "creationTime", "caseResults", "lastUpdated", "detailDialogVisible", "selectedCaseId", "testResultId", "isFirstFetch", "allCases", "shallowRef", "lastPendingIndex", "caseIndexMap", "Map", "updateCounter", "updateDebounceTimer", "statusPollingTimer", "isRunning", "isPaused", "displayedCases", "console", "log", "length", "fetchSavedTestCases", "async", "response", "appApi", "getSavedCases", "data", "error", "ElMessage", "refreshTestCases", "startTestExecution", "startTest", "success", "fetchTestStatus", "startStatusPolling", "warning", "pauseTestExecution", "pauseTest", "resumeTestExecution", "resumeTest", "stopTestExecution", "stopTest", "getTestStatus", "rebuildIndexMap", "findFirstPendingIndex", "pageNumber", "floor", "getTestStatusPaged", "pageSize", "pagedCaseResult", "items", "updateCaseResults", "Date", "toLocaleString", "isTesterCompleted", "stopStatusPolling", "cases", "i", "clear", "for<PERSON>ach", "caseItem", "index", "set", "debouncedTriggerUpdate", "clearTimeout", "window", "setTimeout", "triggerRef", "updatedCases", "updates", "mapHits", "mapMisses", "updated", "get", "undefined", "push", "case", "warn", "newIndex", "updatedCase", "setInterval", "clearInterval", "viewCaseDetail", "caseResult", "closeDetailDialog", "onMounted", "then", "onUnmounted", "_component_el_button", "_component_el_skeleton", "_component_el_empty", "_component_el_card", "onClick", "disabled", "TestMonitor", "shadow", "header", "rows", "animated", "description", "CaseList", "onViewDetail", "CaseDetailDialog", "$event", "caseResultId", "onClose"], "sourceRoot": ""}