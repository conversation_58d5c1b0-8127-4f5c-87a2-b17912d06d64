using Alsi.App;
using Alsi.Fuzz.Core.Contracts.Tester;
using Alsi.Fuzz.Core.Service.Results;
using Alsi.Fuzz.Core.Storage;
using Alsi.Fuzz.Tester.Testers;
using System;

namespace Alsi.Fuzz.Tester
{
    internal static class TesterEnv
    {
        public static PackageTester PackageTester { get; set; }

        public static TesterSnapshot Snapshot { get; private set; } = new TesterSnapshot
        {
            ProcessState = ExecutionState.Pending
        };

        public static ITestResultWriterService ResultWriter = new TestResultWriterService();

        public static DataLogStorage DataLogStorage { get; private set; }

        // 测试器开始
        public static void BeginTester(TestResult testResult)
        {
            Snapshot.TestResult = testResult;
            Snapshot.ProcessState = ExecutionState.Running;

            ResultWriter.Begin(testResult);
            Snapshot.CaseResults = ResultWriter.GetCaseResults();

            testResult.Begin = DateTime.Now;
            testResult.TotalCount = Snapshot.CaseResults.Length;

            // 初始化日志存储
            DataLogStorage = new DataLogStorage();
            DataLogStorage.Initialize(ResultWriter.ResultContext.DataLogPath);
        }

        public static void EndTester(TestResult testResult)
        {
            testResult.End = DateTime.Now;

            ResultWriter.End();

            DataLogStorage?.Dispose();
            DataLogStorage = null;
        }

        // 更新状态
        public static void Update(string currentOperation)
        {
            Snapshot.CurrentOperation = currentOperation;
            AppEnv.Logger.Info(currentOperation);
        }
    }
}
