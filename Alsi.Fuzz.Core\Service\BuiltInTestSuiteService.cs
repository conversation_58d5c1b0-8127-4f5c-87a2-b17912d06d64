using Alsi.Fuzz.Core.Models.TestSuites;
using Alsi.Fuzz.Core.Utils;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;

namespace Alsi.Fuzz.Core.Service
{
    public class BuiltInTestSuiteService
    {
        private static readonly List<TestSuite> _builtInTestSuites = new List<TestSuite>();
        private static readonly Dictionary<string, string> _suiteResourceMap = new Dictionary<string, string>(StringComparer.OrdinalIgnoreCase);
        private static bool _isLoaded;

        public BuiltInTestSuiteService()
        {
            Load();
        }

        public IReadOnlyList<TestSuite> Get()
        {
            return _builtInTestSuites.AsReadOnly();
        }

        public TestSuite GetByName(string suiteName)
        {
            return _builtInTestSuites.Find(x => x.Name.Equals(suiteName, StringComparison.OrdinalIgnoreCase));
        }

        public string GetBuiltInXml(string suiteName, string packageName)
        {
            var testSuite = GetByName(suiteName);
            if (testSuite == null)
                throw new InvalidOperationException($"Test suite '{suiteName}' not found");

            var package = testSuite.Packages.FirstOrDefault(p =>
                p.Name?.Equals(packageName, StringComparison.OrdinalIgnoreCase) == true);

            if (package == null)
                throw new InvalidOperationException($"Package '{packageName}' not found in test suite '{suiteName}'");

            if (!_suiteResourceMap.TryGetValue(packageName, out var resourceName))
                throw new InvalidOperationException($"Resource mapping not found for test suite '{suiteName}'");

            return LoadEmbeddedResource(resourceName);
        }

        private void Load()
        {
            if (_isLoaded)
                return;

            lock (_builtInTestSuites)
            {
                if (_isLoaded)
                    return;

                var assembly = Assembly.GetExecutingAssembly();
                var resourceNames = assembly.GetManifestResourceNames()
                    .Where(x => x.Contains(".sequences-") && x.EndsWith(".xml"))
                    .ToList();

                var canBusPackages = new List<SequencePackage>();
                var canFdPackages = new List<SequencePackage>();

                foreach (var resourceName in resourceNames)
                {
                    var content = LoadEmbeddedResource(resourceName);
                    var package = SequencePackageUtils.LoadFromString(content);

                    canFdPackages.Add(package);
                    canBusPackages.Add(package);

                    _suiteResourceMap[package.Name] = resourceName;
                }

                if (canBusPackages.Any())
                {
                    var canBusSuite = new TestSuite
                    {
                        Name = "CAN-Bus Test Suite",
                        Version = "1.0",
                        Packages = canBusPackages
                    };
                    _builtInTestSuites.Add(canBusSuite);
                }

                if (canFdPackages.Any())
                {
                    var canFdSuite = new TestSuite
                    {
                        Name = "CAN-FD Test Suite",
                        Version = "1.0",
                        Packages = canFdPackages
                    };
                    _builtInTestSuites.Add(canFdSuite);
                }

                _isLoaded = true;
            }
        }

        private string LoadEmbeddedResource(string resourceName)
        {
            var assembly = Assembly.GetExecutingAssembly();
            using (var stream = assembly.GetManifestResourceStream(resourceName))
            {
                if (stream == null)
                    throw new InvalidOperationException($"Resource {resourceName} not found");
                using (var reader = new StreamReader(stream))
                {
                    return reader.ReadToEnd();
                }
            }
        }
    }
}
