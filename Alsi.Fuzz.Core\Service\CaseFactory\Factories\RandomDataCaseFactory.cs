using Alsi.App.Devices.Core;
using Alsi.Common.Utils;
using Alsi.Fuzz.Core.Service.CaseFactory.Core;
using Alsi.Fuzz.Core.Utils;
using System.Collections.Generic;
using System.Linq;

namespace Alsi.Fuzz.Core.Service.CaseFactory.Factories
{
    public class HsRandomDataCaseFactory : RandomDataCaseFactory
    {
        public override CommunicationType CommunicationType => CommunicationType.Can;
    }

    public class FdRandomDataCaseFactory : RandomDataCaseFactory
    {
        public override CommunicationType CommunicationType => CommunicationType.CanFd;
    }

    public abstract class RandomDataCaseFactory : ICaseFactory
    {
        public int Group => 1;
        public abstract CommunicationType CommunicationType { get; }

        public CaseMutation[] Generate(MutationOptions options)
        {
            var list = new List<CaseMutation>();
            foreach (var id in options.WhiteListIds)
            {
                var dataList = new List<byte[]>();
                if (options.Coverage == CoverageType.High)
                {
                    dataList = new LhsSampler().GenerateByteBasedSamples(options.Dlc);
                }
                else if (options.Coverage == CoverageType.Normal)
                {
                    dataList = new LhsSampler().GenerateBitBasedSamples(options.Dlc);
                }

                dataList = dataList.OrderBy(x => x.ToHex()).ToList();
                for (var i = 0; i < dataList.Count; i++)
                {
                    byte[] data = dataList[i];
                    var name = $"G{Group}-ID{id.ToHex()}-LHS{i + 1}/{dataList.Count}";
                    var caseMutation = CaseMutation.Create(name).MutateId(id).MutateData(data);
                    list.Add(caseMutation);
                }
            }

            return list.ToArray();
        }
    }
}
