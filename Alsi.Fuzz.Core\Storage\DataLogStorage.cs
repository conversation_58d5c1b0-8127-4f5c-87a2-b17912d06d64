using Alsi.App;
using Alsi.App.Devices.Core;
using Alsi.Common.Log;
using System;
using System.IO;

namespace Alsi.Fuzz.Core.Storage
{
    /// <summary>
    /// 提供CAN/CANFD日志的存储和检索功能，对接BlfLogReader和BlfLogWriter
    /// </summary>
    public class DataLogStorage : IDisposable
    {
        private readonly BlfLogWriter _writer;
        private string _currentLogPath;
        private bool _isDisposed;

        /// <summary>
        /// 获取当前日志文件路径
        /// </summary>
        public string CurrentLogPath => _currentLogPath;

        /// <summary>
        /// 是否已初始化并准备好写入
        /// </summary>
        public bool IsInitialized => !string.IsNullOrEmpty(_currentLogPath) && BlfLogWriter.IsCreateFile;

        /// <summary>
        /// 创建日志存储实例
        /// </summary>
        public DataLogStorage()
        {
            _writer = new BlfLogWriter();
        }

        /// <summary>
        /// 初始化日志存储，准备写入日志
        /// </summary>
        /// <param name="logDirectory">日志目录</param>
        /// <param name="logFileName">日志文件名（可选），不指定则自动生成</param>
        /// <returns>日志文件完整路径</returns>
        public string Initialize(string logPath)
        {
            if (_isDisposed)
            {
                throw new ObjectDisposedException(nameof(DataLogStorage));
            }

            _currentLogPath = logPath;

            // 初始化写入器
            _writer.Initialize(_currentLogPath);

            return _currentLogPath;
        }

        /// <summary>
        /// 写入单个CAN帧到日志
        /// </summary>
        /// <param name="frame">要写入的CAN帧</param>
        public void WriteFrame(CanFrame frame)
        {
            if (_isDisposed)
            {
                throw new ObjectDisposedException(nameof(DataLogStorage));
            }

            if (!IsInitialized)
            {
                throw new InvalidOperationException("LogStorage not initialized. Call Initialize() first.");
            }

            BlfLogWriter.WriteLog(frame);
        }

        /// <summary>
        /// 批量写入多个CAN帧到日志
        /// </summary>
        /// <param name="frames">要写入的CAN帧数组</param>
        public void WriteFrames(CanFrame[] frames)
        {
            if (_isDisposed)
            {
                throw new ObjectDisposedException(nameof(DataLogStorage));
            }

            if (!IsInitialized)
            {
                throw new InvalidOperationException("LogStorage not initialized. Call Initialize() first.");
            }

            _writer.Log(frames);
        }

        /// <summary>
        /// 从BLF日志文件中读取所有CAN帧
        /// </summary>
        /// <param name="logFilePath">日志文件路径</param>
        /// <returns>读取的CAN帧数组</returns>
        public static CanFrame[] ReadFrames(string logFilePath)
        {
            if (!File.Exists(logFilePath))
            {
                throw new FileNotFoundException("Log file not found", logFilePath);
            }

            return BlfLogReader.ReadBlfFile(logFilePath);
        }

        /// <summary>
        /// 关闭当前日志文件
        /// </summary>
        public void Close()
        {
            if (!IsInitialized) return;

            _writer.Release();
            _currentLogPath = null;
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (_isDisposed) return;

            try
            {
                Close();
            }
            catch (Exception)
            {
                // 忽略异常，确保不会阻止其他资源的释放
            }

            _isDisposed = true;
            GC.SuppressFinalize(this);
        }
    }
}
