<template>
  <div class="test-run-container">
    <div class="toolbar">
      <h3>Test Execution</h3>
      <div class="action-buttons">
        <!-- 开始按钮：在测试未运行且未暂停时显示 -->
        <el-button
          v-if="!isRunning && !isPaused"
          type="success"
          size="small"
          :loading="starting"
          @click="startTestExecution"
          :disabled="savedTestCases.length === 0">
          Start
        </el-button>

        <!-- 暂停按钮：只在测试运行且未暂停时显示 -->
        <el-button
          v-if="isRunning && !isPaused"
          type="warning"
          size="small"
          :loading="pausing"
          @click="pauseTestExecution">
          Pause
        </el-button>

        <!-- 恢复按钮：只在测试已暂停时显示 -->
        <el-button
          v-if="isPaused"
          type="info"
          size="small"
          :loading="resuming"
          @click="resumeTestExecution">
          Resume
        </el-button>

        <!-- 停止按钮：在测试运行或暂停时都显示 -->
        <el-button
          v-if="isRunning || isPaused"
          type="danger"
          size="small"
          :loading="stopping"
          @click="stopTestExecution">
          Stop
        </el-button>
      </div>
    </div>

    <!-- 使用新的TestMonitor组件 -->
    <TestMonitor :run-status="runStatus" :visible="hasEverStarted" />

    <div class="content-area">
      <div class="test-cases-panel">
        <el-card shadow="never" class="test-cases-card">
          <template #header>
            <div class="card-header">
              <span>Test Cases</span>
              <span v-if="displayedCases.length > 0" class="case-count">{{ displayedCases.length }} cases</span>
            </div>
          </template>
          <div v-if="loading" class="loading-container">
            <el-skeleton :rows="10" animated />
          </div>
          <div v-else-if="displayedCases.length === 0" class="empty-container">
            <el-empty description="No test cases found" />
          </div>
          <div v-else class="case-list-wrapper">
            <CaseList
              :cases="displayedCases"
              @view-detail="viewCaseDetail"
            />
          </div>
        </el-card>
      </div>
    </div>

    <!-- 添加用例详情对话框组件 -->
    <CaseDetailDialog
      v-model:visible="detailDialogVisible"
      :testResultId="testResultId"
      :caseResultId="selectedCaseId"
      @close="closeDetailDialog"
    />
  </div>
</template>

<script setup lang="ts">
/* eslint-disable */
import { ref, onMounted, onUnmounted, computed } from 'vue';
import { ElMessage } from 'element-plus';
import { appApi, TesterSnapshot, ExecutionState, isTesterCompleted } from '@/api/appApi';
import { CaseResult } from '@/api/interoperationApi';
import TestMonitor from '@/components/test/TestMonitor.vue';
import CaseDetailDialog from '@/components/test/CaseDetailDialog.vue';
import CaseList from '@/components/test/CaseList.vue';

// 状态变量
const savedTestCases = ref<CaseResult[]>([]);
const loading = ref(true);
const starting = ref(false);
const stopping = ref(false);
const pausing = ref(false);
const resuming = ref(false);
const hasEverStarted = ref(false);
const runStatus = ref<TesterSnapshot>({
  processState: ExecutionState.Pending,
  currentOperation: '',
  testResult: {
    id: '',
    resultFolderName: '',
    testType: '',
    creationTime: '',
    totalCount: 0,
    successCount: 0,
    failureCount: 0
  },
  caseResults: []
});
const lastUpdated = ref('');

// 用例详情对话框相关
const detailDialogVisible = ref(false);
const selectedCaseId = ref<number | null>(null);
const testResultId = ref<string | null>(null);

// 无需虚拟滚动相关变量，已移至 CaseList 组件
const isFirstFetch = ref(true);
const allCases = ref<CaseResult[]>([]);
let lastPendingIndex = 0;

// 性能优化：维护一个ID到索引的映射表
const caseIndexMap = ref<Map<number, number>>(new Map());

// 状态轮询定时器
let statusPollingTimer: number | null = null;

// 计算属性
const isRunning = computed(() => {
  return runStatus.value.processState === ExecutionState.Running;
});

const isPaused = computed(() => {
  return runStatus.value.processState === ExecutionState.Paused;
});

const displayedCases = computed(() => {
  console.log('displayedCases = computed(() => begin.');
  // 如果测试正在运行，显示优化后的allCases中的用例
  if ((isRunning.value || isPaused.value) && allCases.value?.length > 0) {
    console.log('displayedCases = computed(() => end - running cases.');
    return allCases.value;
  }
  // 否则显示保存的测试用例
  console.log('displayedCases = computed(() => end - saved cases.');
  return savedTestCases.value;
});

// 获取保存的测试用例
const fetchSavedTestCases = async () => {
  loading.value = true;
  try {
    const response = await appApi.getSavedCases();
    savedTestCases.value = response.data;

    // 数据加载完成，无需额外操作
    // 虚拟滚动已移至 CaseList 组件
  } catch (error) {
    console.error('获取保存的测试用例失败:', error);
    ElMessage.error('Failed to fetch saved test cases');
  } finally {
    loading.value = false;
  }
};

// 刷新测试用例列表
const refreshTestCases = () => {
  fetchSavedTestCases();
};

// 开始测试执行
const startTestExecution = async () => {
  if (savedTestCases.value.length === 0) {
    ElMessage.warning('No test cases available to execute');
    return;
  }

  starting.value = true;
  try {
    await appApi.startTest();
    hasEverStarted.value = true;
    ElMessage.success('Test execution started');

    // 立即获取状态并开始轮询
    await fetchTestStatus();
    startStatusPolling();
  } catch (error) {
    console.error('启动测试失败:', error);
    ElMessage.error('Failed to start test execution');
  } finally {
    starting.value = false;
  }
};

// 暂停测试执行
const pauseTestExecution = async () => {
  pausing.value = true;
  try {
    await appApi.pauseTest();
    ElMessage.success('Test execution paused');

    // 立即更新状态
    await fetchTestStatus();
  } catch (error) {
    console.error('暂停测试失败:', error);
    ElMessage.error('Failed to pause test execution');
  } finally {
    pausing.value = false;
  }
};

// 恢复测试执行
const resumeTestExecution = async () => {
  resuming.value = true;
  try {
    await appApi.resumeTest();
    ElMessage.success('Test execution resumed');

    // 立即更新状态
    await fetchTestStatus();
  } catch (error) {
    console.error('恢复测试失败:', error);
    ElMessage.error('Failed to resume test execution');
  } finally {
    resuming.value = false;
  }
};

// 停止测试执行
const stopTestExecution = async () => {
  stopping.value = true;
  try {
    await appApi.stopTest();
    ElMessage.success('Test execution stopped');

    // 立即更新状态
    await fetchTestStatus();
  } catch (error) {
    console.error('停止测试失败:', error);
    ElMessage.error('Failed to stop test execution');
  } finally {
    stopping.value = false;
  }
};

// 获取测试状态
const fetchTestStatus = async () => {
  try {
    let response;

    if (isFirstFetch.value) {
      // 第一次获取全部数据
      response = await appApi.getTestStatus();
      isFirstFetch.value = false;

      runStatus.value = response.data;
      allCases.value = response.data.caseResults || [];

      // 性能优化：建立索引映射
      rebuildIndexMap();

      // 找到第一个Pending状态的索引
      lastPendingIndex = findFirstPendingIndex(allCases.value);
    } else {
      // 后续只获取状态可能变化的用例
      const pageNumber = Math.floor(lastPendingIndex / 100) + 1;
      response = await appApi.getTestStatusPaged({
        pageNumber,
        pageSize: 100
      });

      // 更新基本状态
      runStatus.value.processState = response.data.processState;
      runStatus.value.currentOperation = response.data.currentOperation;
      // runStatus.value.testResult = response.data.testResult;

      // 更新用例状态
      if (response.data.pagedCaseResult?.items) {
        updateCaseResults(response.data.pagedCaseResult.items);
        lastPendingIndex = findFirstPendingIndex(allCases.value);
      }

      // 将更新后的所有用例赋值给runStatus
      // runStatus.value.caseResults = allCases.value;
    }

    lastUpdated.value = new Date().toLocaleString();

    // 测试完成时停止轮询
    if (isTesterCompleted(runStatus.value) && statusPollingTimer) {
      refreshTestCases();
      stopStatusPolling();
    }

  } catch (error) {
    console.error('获取测试状态失败:', error);
  }
};

// 查找第一个Pending状态的用例索引
const findFirstPendingIndex = (cases: CaseResult[]) => {
  console.log('findFirstPendingIndex begin');
  for (let i = 0; i < cases.length; i++) {
    if (cases[i].state === ExecutionState.Pending) {
      console.log('findFirstPendingIndex end');
      return i;
    }
  }
  console.log('findFirstPendingIndex end');
  return cases.length;
};

// 性能优化：重建索引映射表
const rebuildIndexMap = () => {
  console.log('rebuildIndexMap begin');
  caseIndexMap.value.clear();
  allCases.value.forEach((caseItem, index) => {
    caseIndexMap.value.set(caseItem.id, index);
  });
  console.log('rebuildIndexMap end, map size:', caseIndexMap.value.size);
};

// 更新用例结果 - 性能优化版本
const updateCaseResults = (updatedCases: CaseResult[]) => {
  console.log('updateCaseResults begin, updating', updatedCases.length, 'cases');

  if (!allCases.value || allCases.value.length === 0) {
    allCases.value = updatedCases;
    rebuildIndexMap();
    console.log('updateCaseResults end - initial load');
    return;
  }

  // 批量更新：收集所有需要更新的索引和数据
  const updates: Array<{index: number, case: CaseResult}> = [];
  let mapHits = 0;
  let mapMisses = 0;

  for (const updated of updatedCases) {
    const index = caseIndexMap.value.get(updated.id);
    if (index !== undefined && index < allCases.value.length) {
      // 验证索引是否仍然有效（防止数组结构变化导致的不一致）
      if (allCases.value[index].id === updated.id) {
        updates.push({index, case: updated});
        mapHits++;
      } else {
        // 索引映射失效，需要重建
        console.warn('Index map inconsistency detected, rebuilding...');
        rebuildIndexMap();
        const newIndex = caseIndexMap.value.get(updated.id);
        if (newIndex !== undefined && newIndex < allCases.value.length) {
          updates.push({index: newIndex, case: updated});
          mapHits++;
        } else {
          mapMisses++;
        }
      }
    } else {
      mapMisses++;
    }
  }

  // 一次性应用所有更新，减少响应式触发次数
  updates.forEach(({index, case: updatedCase}) => {
    allCases.value[index] = updatedCase;
  });

  console.log('updateCaseResults end - map hits:', mapHits, 'misses:', mapMisses);
};

// 开始状态轮询 - 保持300毫秒间隔
const startStatusPolling = () => {
  // 清除可能存在的轮询定时器
  stopStatusPolling();

  // 重置首次获取标志
  isFirstFetch.value = true;

  // 清空索引映射，将在首次获取时重建
  caseIndexMap.value.clear();

  statusPollingTimer = window.setInterval(fetchTestStatus, 300);
};

// 停止状态轮询
const stopStatusPolling = () => {
  if (statusPollingTimer) {
    clearInterval(statusPollingTimer);
    statusPollingTimer = null;
  }
};

// 查看用例详情
const viewCaseDetail = (caseResult: CaseResult) => {
  testResultId.value = caseResult.testResultId;
  selectedCaseId.value = caseResult.id;
  detailDialogVisible.value = true;
};

// 关闭详情对话框
const closeDetailDialog = () => {
  detailDialogVisible.value = false;
  selectedCaseId.value = null;
};

// 虚拟滚动相关代码已移至 CaseList 组件

// 组件挂载时获取保存的测试用例和测试状态
onMounted(() => {
  fetchSavedTestCases();
  fetchTestStatus().then(() => {
    // 如果测试正在运行，开始轮询
    if (isRunning.value) {
      hasEverStarted.value = true;
      startStatusPolling();
    }
  });
});

// 组件卸载时停止轮询
onUnmounted(() => {
  stopStatusPolling();
});
</script>

<style scoped lang="scss">
.test-run-container {
  display: flex;
  flex-direction: column;
  flex: 1;
  padding: 20px;
}

/* 顶部工具栏样式 */
.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  border-bottom: 1px solid #dcdfe6;
  margin-bottom: 12px;

  h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
    color: #303133;
  }

  .action-buttons {
    display: flex;
    gap: 8px;
  }
}

/* 内容区域样式 */
.content-area {
  flex: 1;
  min-height: 0;
  display: flex;
  flex-direction: column;
}

.test-cases-panel {
  flex: 1;
  min-height: 0;
}

.test-cases-card {
  height: 100%;
  display: flex;
  flex-direction: column;
  box-shadow: none;

  :deep(.el-card__header) {
    padding: 12px 16px;
    border-bottom: 2px solid #f0f0f0;
  }

  :deep(.el-card__body) {
    flex: 1;
    overflow: hidden;
    padding: 0;
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .case-count {
    font-size: 12px;
    color: #909399;
  }
}

.loading-container,
.empty-container {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
}

.case-list-wrapper {
  height: 100%;
}
</style>
