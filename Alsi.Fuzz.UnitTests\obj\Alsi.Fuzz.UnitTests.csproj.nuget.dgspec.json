{"format": 1, "restore": {"D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.UnitTests\\Alsi.Fuzz.UnitTests.csproj": {}}, "projects": {"D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.App.Database\\Alsi.App.Database.csproj": {"restore": {"projectUniqueName": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.App.Database\\Alsi.App.Database.csproj", "projectName": "Alsi.App.Database", "projectPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.App.Database\\Alsi.App.Database.csproj", "UsingMicrosoftNETSdk": false, "frameworks": {"net462": {"projectReferences": {"D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.App\\Alsi.App.csproj": {"projectPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.App\\Alsi.App.csproj"}}}}}, "frameworks": {"net462": {}}}, "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.App\\Alsi.App.csproj": {"restore": {"projectUniqueName": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.App\\Alsi.App.csproj", "projectName": "Alsi.App", "projectPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.App\\Alsi.App.csproj", "UsingMicrosoftNETSdk": false, "frameworks": {"net462": {"projectReferences": {}}}}, "frameworks": {"net462": {}}}, "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.Common.Utils\\Alsi.Common.Utils.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.Common.Utils\\Alsi.Common.Utils.csproj", "projectName": "Alsi.Common.Utils", "projectPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.Common.Utils\\Alsi.Common.Utils.csproj", "packagesPath": "D:\\nuget_packages", "outputPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.Common.Utils\\obj\\", "projectStyle": "PackageReference", "crossTargeting": true, "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net462", "netstandard2.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net462": {"targetAlias": "net462", "projectReferences": {}}, "netstandard2.0": {"targetAlias": "netstandard2.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net462": {"targetAlias": "net462", "dependencies": {"Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}, "netstandard2.0": {"targetAlias": "netstandard2.0", "dependencies": {"NETStandard.Library": {"suppressParent": "All", "target": "Package", "version": "[2.0.3, )", "autoReferenced": true}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}, "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.App.Devices\\Alsi.App.Devices.csproj": {"restore": {"projectUniqueName": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.App.Devices\\Alsi.App.Devices.csproj", "projectName": "Alsi.App.Devices", "projectPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.App.Devices\\Alsi.App.Devices.csproj", "UsingMicrosoftNETSdk": false, "frameworks": {"net462": {"projectReferences": {"D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.App\\Alsi.App.csproj": {"projectPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.App\\Alsi.App.csproj"}, "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.Common.Utils\\Alsi.Common.Utils.csproj": {"projectPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.Common.Utils\\Alsi.Common.Utils.csproj"}}}}}, "frameworks": {"net462": {}}}, "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Core\\Alsi.Fuzz.Core.csproj": {"restore": {"projectUniqueName": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Core\\Alsi.Fuzz.Core.csproj", "projectName": "Alsi.Fuzz.Core", "projectPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Core\\Alsi.Fuzz.Core.csproj", "UsingMicrosoftNETSdk": false, "frameworks": {"net462": {"projectReferences": {"D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.App.Database\\Alsi.App.Database.csproj": {"projectPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.App.Database\\Alsi.App.Database.csproj"}, "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.App\\Alsi.App.csproj": {"projectPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.App\\Alsi.App.csproj"}, "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.Common.Utils\\Alsi.Common.Utils.csproj": {"projectPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.Common.Utils\\Alsi.Common.Utils.csproj"}, "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.App.Devices\\Alsi.App.Devices.csproj": {"projectPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.App.Devices\\Alsi.App.Devices.csproj"}}}}}, "frameworks": {"net462": {}}}, "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.UnitTests\\Alsi.Fuzz.UnitTests.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.UnitTests\\Alsi.Fuzz.UnitTests.csproj", "projectName": "Alsi.Fuzz.UnitTests", "projectPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.UnitTests\\Alsi.Fuzz.UnitTests.csproj", "packagesPath": "D:\\nuget_packages", "outputPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.UnitTests\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net6.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "projectReferences": {"D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.App.Database\\Alsi.App.Database.csproj": {"projectPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.App.Database\\Alsi.App.Database.csproj"}, "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.App\\Alsi.App.csproj": {"projectPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.App\\Alsi.App.csproj"}, "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.Common.Utils\\Alsi.Common.Utils.csproj": {"projectPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Common\\Alsi.Common.Utils\\Alsi.Common.Utils.csproj"}, "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.App.Devices\\Alsi.App.Devices.csproj": {"projectPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.App.Devices\\Alsi.App.Devices.csproj"}, "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Core\\Alsi.Fuzz.Core.csproj": {"projectPath": "D:\\src\\005_TAB\\2、Src\\1、Source Code\\Alsi.Fuzz\\Alsi.Fuzz.Core\\Alsi.Fuzz.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net6.0": {"targetAlias": "net6.0", "dependencies": {"FreeSql.Provider.Sqlite": {"target": "Package", "version": "[3.5.106, )"}, "Microsoft.NET.Test.Sdk": {"target": "Package", "version": "[17.5.0, )"}, "Shouldly": {"target": "Package", "version": "[4.3.0, )"}, "Xunit.SkippableFact": {"target": "Package", "version": "[1.5.23, )"}, "coverlet.collector": {"target": "Package", "version": "[3.2.0, )"}, "xunit": {"target": "Package", "version": "[2.9.3, )"}, "xunit.runner.visualstudio": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[3.0.2, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203\\RuntimeIdentifierGraph.json"}}}}}