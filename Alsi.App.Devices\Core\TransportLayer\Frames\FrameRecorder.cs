using System.Collections.Generic;
using System.Linq;

namespace Alsi.App.Devices.Core.TransportLayer.Frames
{
    public class FrameRecorder
    {
        private readonly object dataFrameLock = new object();

        public void Clear()
        {
            diagFrames = new List<IDiagFrame>();
        }

        public void Add(IDiagFrame diagFrame)
        {
            lock (dataFrameLock)
            {
                while (diagFrames.Count > 10000)
                {
                    diagFrames.RemoveAt(0);
                }

                diagFrames.Add(diagFrame);
            }
        }

        public IDiagFrame[] GetDiagFrames()
        {
            lock (dataFrameLock)
            {
                return diagFrames.ToArray();
            }
        }

        public TDiagFrame[] GetDiagFrames<TDiagFrame>(bool isTx)
            where TDiagFrame : IDiagFrame
        {
            lock (dataFrameLock)
            {
                return diagFrames
                    .Where(x => x.IsTx == isTx && x is TDiagFrame)
                    .Select(x => (TDiagFrame)x).ToArray();
            }
        }

        private List<IDiagFrame> diagFrames = new List<IDiagFrame>();
    }
}
