using System.Collections.Generic;
using System.Linq;

namespace Alsi.App.Devices.Core.TransportLayer.Frames
{
    public class SingleFrame : IDiagFrame
    {
        public SingleFrame(byte[] data, bool isTx, bool isCanfd)
        {
            Data = data;
            IsTx = isTx;
            IsCanfd = isCanfd;
        }

        public bool IsTx { get; }
        public byte[] Data { get; }

        public int Length => IsCanfd && Data.Length > 8 ? Data[1] : Data[0] & 0xF;
        public byte[] Payload => IsCanfd && Data.Length > 8 ? Data.Skip(2).Take(Length).ToArray() : Data.Skip(1).Take(Length).ToArray();
        public bool IsCanfd { get; }
        public bool IsDiagnosticEnd { get; internal set; }

        public static bool TryBuild(byte[] payload, int payloadLength, bool isCanfd, out SingleFrame singleFrame)
        {
            singleFrame = null;
            if (isCanfd)
            {
                // CANFD 单帧最大有效载荷: 62 字节
                if (payload.Length > 62)
                {
                    return false;
                }
            }
            else
            {
                // CAN 单帧最大有效载荷: 7 字节
                if (payload.Length > 7)
                {
                    return false;
                }
            }
            var data = new List<byte>();
            if (isCanfd && payloadLength >= 8)
            {
                data.Add(0x00);
            }
            data.Add((byte)payloadLength);
            data.AddRange(payload);

            singleFrame = new SingleFrame(data.ToArray(), true, isCanfd);
            return true;
        }

        public static bool TryMatch(byte[] data, bool isTx, bool isCanFd, out SingleFrame singleFrame)
        {
            singleFrame = null;
            if (data[0] >> 4 == 0)
            {
                singleFrame = new SingleFrame(data, isTx, isCanFd);
                return true;
            }
            return false;
        }
    }
}
