using Alsi.App.Devices;
using Alsi.App.Devices.Core;
using System;
using System.Threading;
using System.Threading.Tasks;

namespace Alsi.Fuzz.Core.Service
{
    /// <summary>
    /// 设备扫描器，提供异步定期扫描设备功能
    /// </summary>
    public class DeviceScanner : IDisposable
    {
        private readonly TimeSpan _scanInterval;
        private CancellationTokenSource _cancellationTokenSource;
        private Task _scanningTask;
        private bool _isScanning;
        private bool _isDisposed;

        /// <summary>
        /// 设备扫描完成事件
        /// </summary>
        public event EventHandler<DevicesScannedEventArgs> DevicesScanned;

        /// <summary>
        /// 初始化设备扫描器
        /// </summary>
        /// <param name="scanInterval">扫描间隔时间</param>
        public DeviceScanner(TimeSpan scanInterval)
        {
            _scanInterval = scanInterval;
        }

        /// <summary>
        /// 启动设备扫描
        /// </summary>
        public void Start()
        {
            if (_isScanning)
                return;

            _isScanning = true;
            _cancellationTokenSource = new CancellationTokenSource();
            _scanningTask = Task.Run(() => DeviceScannerLoopAsync(_cancellationTokenSource.Token));
        }

        /// <summary>
        /// 停止设备扫描
        /// </summary>
        public void Stop()
        {
            if (!_isScanning)
                return;

            _isScanning = false;
            _cancellationTokenSource?.Cancel();
            
            try
            {
                _scanningTask?.Wait(TimeSpan.FromSeconds(1));
            }
            catch (AggregateException)
            {
                // 任务取消异常，可以忽略
            }
            
            _cancellationTokenSource?.Dispose();
            _cancellationTokenSource = null;
            _scanningTask = null;
        }

        /// <summary>
        /// 设备扫描循环
        /// </summary>
        private async Task DeviceScannerLoopAsync(CancellationToken token)
        {
            while (!token.IsCancellationRequested)
            {
                try
                {
                    // 执行设备扫描
                    var deviceChannels = await ScanDevicesAsync();
                    
                    // 触发设备扫描完成事件
                    OnDevicesScanned(deviceChannels);
                    
                    // 等待指定间隔（如2秒）
                    await Task.Delay(_scanInterval, token);
                }
                catch (OperationCanceledException) when (token.IsCancellationRequested)
                {
                    // 正常取消，退出循环
                    break;
                }
                catch (Exception ex)
                {
                    // 记录异常，但继续循环
                    System.Diagnostics.Debug.WriteLine($"设备扫描异常: {ex.Message}");
                    
                    try
                    {
                        // 避免因异常导致的高频循环
                        await Task.Delay(_scanInterval, token);
                    }
                    catch (OperationCanceledException) when (token.IsCancellationRequested)
                    {
                        break;
                    }
                }
            }
        }

        /// <summary>
        /// 执行设备扫描
        /// </summary>
        private Task<DeviceChannel[]> ScanDevicesAsync()
        {
            // 调用设备扫描API
            var deviceChannels = DeviceEnv.GetDeviceChannels();
            return Task.FromResult(deviceChannels);
        }

        /// <summary>
        /// 触发设备扫描完成事件
        /// </summary>
        protected virtual void OnDevicesScanned(DeviceChannel[] deviceChannels)
        {
            DevicesScanned?.Invoke(this, new DevicesScannedEventArgs(deviceChannels));
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        protected virtual void Dispose(bool disposing)
        {
            if (!_isDisposed)
            {
                if (disposing)
                {
                    Stop();
                }

                _isDisposed = true;
            }
        }
    }
}
