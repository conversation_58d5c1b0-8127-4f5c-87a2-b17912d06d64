using FreeSql.DataAnnotations;
using System;

namespace Alsi.Fuzz.Core.Service.Results
{
    public class CaseStep
    {
        [Column(IsIdentity = true, IsPrimary = true)]
        public Guid Id { get; set; }
        public string Name { get; set; }
        public int CaseResultId { get; set; }
        public ulong Timestamp { get; set; }
        public ulong? FrameTimestamp { get; set; }
        public ExecutionState State { get; set; }
        public DateTime? Begin { get; set; }
        public DateTime? End { get; set; }
        public string Detail { get; set; }
    }
}
