import axios, { AxiosResponse } from 'axios'
import { USE_MOCK, mockApi } from '@/mock/mockApi'

// 定义历史记录类型
export interface TestPlanHistory {
  id: string;
  filePath: string;
  planName: string;
  lastAccessTime: string;
  lastModified: string;
  isDeleted: boolean;
}

const BASE_URL = '/api/testPlanHistory'

export const testPlanHistoryApi = {
  // 获取测试计划历史记录
  get: (): Promise<AxiosResponse<TestPlanHistory[]>> => {
    if (USE_MOCK) {
      return mockApi.testPlanHistory.get();
    }
    return axios.get(`${BASE_URL}/get`);
  },

  // 清空历史记录
  clear: (): Promise<AxiosResponse<void>> => {
    if (USE_MOCK) {
      return mockApi.testPlanHistory.clear();
    }
    return axios.delete(`${BASE_URL}/clear`);
  },

  // 删除单个历史记录
  deleteRecord: (filePath: string): Promise<AxiosResponse<void>> => {
    if (USE_MOCK) {
      // 模拟实现，可以根据需要调整
      if (mockApi.testPlanHistory.deleteRecord) {
        return mockApi.testPlanHistory.deleteRecord(filePath);
      } else {
        // 创建一个符合AxiosResponse类型的对象
        return Promise.resolve({
          data: undefined,
          status: 200,
          statusText: 'OK',
          headers: {},
          config: { headers: {} } as any
        });
      }
    }
    return axios.delete(`${BASE_URL}/deleteRecord`, { data: { filePath } });
  }
}

export default testPlanHistoryApi
