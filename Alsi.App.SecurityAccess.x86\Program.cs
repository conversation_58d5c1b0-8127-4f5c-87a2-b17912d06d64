using System;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;

namespace Alsi.App.SecurityAccess.x86
{
    internal class Program
    {
        static int Main(string[] args)
        {
            try
            {
                if (args.Length < 3)
                {
                    Console.Error.WriteLine("Alsi.App.SecurityAccess.x86.exe [dll-path] [seed-hex] [security-level] [variant]");
                    return 1;
                }

                // 解析参数
                var dllPath = args[0];
                if (!File.Exists(dllPath))
                {
                    throw new Exception($"Can't find dll path: {dllPath}");
                }

                SetDefaultDllDirectories(LOAD_LIBRARY_SEARCH_DEFAULT_DIRS | LOAD_LIBRARY_SEARCH_USER_DIRS);

                var dllFolder = Directory.GetParent(dllPath).FullName;
                var handle = AddDllDirectory(dllFolder);
                if (handle == IntPtr.Zero)
                {
                    var error = Marshal.GetLastWin32Error();
                    throw new Win32Exception(error, "Failed to add DLL directory: " + dllFolder);
                }

                // 从十六进制字符串解析种子字节数组
                byte[] seedArray = HexStringToByteArray(args[1]);
                uint securityLevel = uint.Parse(args[2]);
                string variant = string.Empty;

                // 准备输出缓冲区
                byte[] keyArray = new byte[10240]; // 根据需要调整大小
                uint outSize = 0;

                // 调用DLL
                VKeyGenResultEx result = KeyGenAlgo.GenerateKeyEx(
                    seedArray, (uint)seedArray.Length, securityLevel,
                    variant, keyArray, (uint)keyArray.Length, ref outSize);

                // 输出结果
                Console.WriteLine($"RESULT:{(int)result}");
                if (result == VKeyGenResultEx.KGRE_Ok)
                {
                    var keyBytes = keyArray.Take((int)outSize).ToArray();
                    // 输出十六进制格式的密钥
                    Console.WriteLine($"KEY:{BitConverter.ToString(keyBytes).Replace("-", "")}");
                }

                return (int)result;
            }
            catch (Exception ex)
            {
                Console.Error.WriteLine($"ERROR:{ex.Message}");
                return 999;
            }
        }

        // 将十六进制字符串转换为字节数组
        private static byte[] HexStringToByteArray(string hex)
        {
            if (string.IsNullOrEmpty(hex))
                return new byte[0];

            if (hex.Length % 2 != 0)
            {
                throw new ArgumentException("十六进制字符串必须具有偶数长度");
            }

            byte[] bytes = new byte[hex.Length / 2];
            for (int i = 0; i < hex.Length; i += 2)
            {
                bytes[i / 2] = Convert.ToByte(hex.Substring(i, 2), 16);
            }
            return bytes;
        }

        // 常量定义
        private const uint LOAD_LIBRARY_SEARCH_DEFAULT_DIRS = 0x00001000;
        private const uint LOAD_LIBRARY_SEARCH_USER_DIRS = 0x00000400;

        [DllImport("kernel32.dll", CharSet = CharSet.Unicode, SetLastError = true)]
        private static extern IntPtr AddDllDirectory(string lpPathName);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern bool SetDefaultDllDirectories(uint directoryFlags);

        private enum VKeyGenResultEx
        {
            KGRE_Ok = 0,
            KGRE_BufferToSmall = 1,
            KGRE_SecurityLevelInvalid = 2,
            KGRE_VariantInvalid = 3,
            KGRE_UnspecifiedError = 4
        }

        private class KeyGenAlgo
        {
            [DllImport("SecurityAccess.dll", CallingConvention = CallingConvention.Cdecl)]
            public static extern VKeyGenResultEx GenerateKeyEx(
                byte[] iSeedArray, uint iSeedArraySize, uint iSecurityLevel,
                string iVariant,
                byte[] ioKeyArray, uint iKeyArraySize, ref uint oSize
            );
        }
    }
}
