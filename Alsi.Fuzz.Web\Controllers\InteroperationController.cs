using Alsi.Fuzz.Core.Contracts.Tester;
using Alsi.Fuzz.Core.Service;
using Alsi.Fuzz.Core.Service.Tester;
using System;
using System.Threading.Tasks;
using System.Web.Http;

namespace Alsi.Fuzz.Web.Controllers
{
    public class InteroperationController : WebControllerBase
    {
        private static StatusPollingService StatusPollingService { get; set; } = new StatusPollingService();

        [HttpPost]
        [ActionName("start")]
        public async Task<IHttpActionResult> Start()
        {
            if (StatusPollingService.IsTesterRunning)
            {
                return Ok();
            }

            StatusPollingService.TesterSnapshot = new TesterSnapshot();

            var testerManager = TesterManager.Instance;
            // 启动Tester进程并传递参数
            var success = await testerManager.StartAsync();
            if (!success)
            {
                throw new Exception("Failed to start tester");
            }

            var apiClient = testerManager.GetApiClient();
            if (apiClient == null)
            {
                throw new Exception("The API client is null");
            }

            // 等待 tester 启动
            await testerManager.WaitTesterAsync(expectIsRunning: true);

            // 通过API启动互操作测试
            var apiResponse = await apiClient.StartInteroperationTestAsync();
            if (!apiResponse.Success)
            {
                throw new Exception(apiResponse.Message);
            }

            await StatusPollingService.UpdateStatusAsync(true);
            StatusPollingService.StartStatusPolling();

            return Ok();
        }

        [HttpPost]
        [ActionName("stop")]
        public async Task<IHttpActionResult> Stop()
        {
            await TesterManager.Instance.GetApiClient().StopAsync();
            await StatusPollingService.UpdateStatusAsync(false);
            StatusPollingService.StopStatusPolling();
            await TesterManager.Instance.GetApiClient().ExitAsync();
            return Ok();
        }

        [HttpGet]
        [ActionName("status")]
        public IHttpActionResult GetStatus()
        {
            return Ok(StatusPollingService.TesterSnapshot);
        }
    }
}
