using Alsi.App.Devices.Core.TransportLayer.Frames;
using System.Threading;

namespace Alsi.App.Devices.Core.TransportLayer
{
    public class TpContext
    {
        public TpContext(Request request, DiagParams diagParams)
        {
            Request = request;
            DiagParams = diagParams;
        }

        public Request Request { get; }
        public DiagParams DiagParams { get; }
        public FrameRecorder Recorder { get; } = new FrameRecorder();

        public ResponseStore ResponseContext { get; } = new ResponseStore();

        private SemaphoreSlim semaphore = new SemaphoreSlim(0, 10);

        private int waitTimes = 1;

        public bool Wait(int timeoutMs)
        {
            var isSemaphoreEntered = false;
            var waitP2Time = true;
            while (waitTimes > 0)
            {
                // 收到 0x78 之后，等待时间为 P2ServerExt
                isSemaphoreEntered = semaphore.Wait(waitP2Time ? timeoutMs : DiagParams.P2ExtTime);
                waitP2Time = false;
                if (!isSemaphoreEntered)
                {
                    return isSemaphoreEntered;
                }

                Interlocked.Decrement(ref waitTimes);
            }
            return isSemaphoreEntered;
        }

        public void OnResponse(bool is0x78)
        {
            if (is0x78)
            {
                Interlocked.Increment(ref waitTimes);
            }

            semaphore.Release();
        }
    }
}
