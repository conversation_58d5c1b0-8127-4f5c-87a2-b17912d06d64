using System.IO;

namespace Alsi.Fuzz.Core.Service
{
    public static class FileLocker
    {
        private static FileStream _currentLock;

        public static void LockFile(string filePath)
        {
            UnlockFile();

            _currentLock = new FileStream(
                filePath,
                FileMode.Open,
                FileAccess.Read,
                FileShare.Read
            );
        }

        public static void UnlockFile()
        {
            if (_currentLock != null)
            {
                _currentLock.Close();
                _currentLock.Dispose();
                _currentLock = null;
            }
        }
    }
}
