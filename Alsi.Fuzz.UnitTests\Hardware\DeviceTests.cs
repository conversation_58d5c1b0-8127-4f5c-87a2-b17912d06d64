﻿using Alsi.App.Devices;
using Alsi.App.Devices.Core;
using Alsi.App.Devices.TsLibCan;
using Alsi.App.Devices.Vector;
using Alsi.Common.Utils;
using Shouldly;
using System.Text;
using Xunit.Abstractions;

namespace Alsi.Fuzz.UnitTests.Hardware
{
    public class DeviceTests : UnitTestBase
    {
        private readonly ITestOutputHelper _testOutputHelper;

        public DeviceTests(ITestOutputHelper testOutputHelper)
            : base()
        {
            _testOutputHelper = testOutputHelper;
        }

        [Fact]
        public void DiscoverDevices_ValidateDeviceChannelsData()
        {
            // Arrange & Act
            var deviceChannels = DeviceEnv.GetDeviceChannels();

            // 生成测试结果输出
            var outputBuilder = new StringBuilder();
            outputBuilder.AppendLine("设备发现测试结果:");

            // Assert
            deviceChannels.ShouldNotBeNull();
            outputBuilder.AppendLine($"发现设备总数: {deviceChannels.Length}");

            if (deviceChannels.Length > 0)
            {
                // 如果有设备被发现，验证每个设备通道的属性
                outputBuilder.AppendLine("设备列表:");
                int deviceIndex = 1;

                foreach (var channel in deviceChannels)
                {
                    outputBuilder.AppendLine($"设备 #{deviceIndex++}:");
                    outputBuilder.AppendLine($"  名称: {channel.Name}");
                    outputBuilder.AppendLine($"  制造商: {channel.Manufacturer}");
                    outputBuilder.AppendLine($"  通信类型: {channel.CommunicationType}");
                    outputBuilder.AppendLine($"  设备类型: {channel.DeviceType}");
                    outputBuilder.AppendLine($"  设备厂商: {channel.DeviceFactory}");
                    outputBuilder.AppendLine($"  序列号: {channel.DeviceSerial}");

                    // 输出特定制造商信息
                    switch (channel.Manufacturer)
                    {
                        case Manufacturer.Vector:
                            outputBuilder.AppendLine($"  Vector设备信息: {(channel.VectorInfo != null ? JsonUtils.Serialize(channel.VectorInfo) : "未提供")}");
                            break;
                        case Manufacturer.Tosun:
                            outputBuilder.AppendLine($"  同星设备信息: {(channel.TsLibCanChannelInfo != null ? JsonUtils.Serialize(channel.TsLibCanChannelInfo) : "未提供")}");
                            break;
                    }

                    // 基本属性不应为空
                    channel.DeviceType.ShouldNotBeNullOrEmpty("设备类型不应为空");
                    channel.DeviceSerial.ShouldNotBeNullOrEmpty("设备序列号不应为空");
                    channel.DeviceFactory.ShouldNotBeNullOrEmpty("设备厂商不应为空");

                    // 名称应该符合预期格式
                    channel.Name.ShouldBe($"{channel.DeviceType} {channel.DeviceSerial}");

                    // 根据制造商检查特定属性
                    switch (channel.Manufacturer)
                    {
                        case Manufacturer.Vector:
                            // Vector设备应该有Vector相关信息
                            if (channel.VectorInfo != null)
                            {
                                // 可以添加对VectorInfo的具体验证
                                channel.TsLibCanChannelInfo.ShouldBeNull("Vector设备不应含有同星通道信息");
                            }
                            break;

                        case Manufacturer.Tosun:
                            // 同星设备应该有TsLibCanChannel信息
                            if (channel.TsLibCanChannelInfo != null)
                            {
                                // 可以添加对TsLibCanChannel对信息的具体验证
                                channel.VectorInfo.ShouldBeNull("同星设备不应含有Vector信息");
                            }
                            break;

                        default:
                            // 对于其他制造商的处理
                            break;
                    }

                    // 通信类型验证
                    Enum.IsDefined(typeof(CommunicationType), channel.CommunicationType)
                        .ShouldBeTrue($"无效的通信类型: {channel.CommunicationType}");
                }
            }
            else
            {
                // 如果没有设备被发现，记录此情况（不作为测试失败）
                outputBuilder.AppendLine("没有发现设备通道，可能是测试环境中没有连接任何设备");
            }

            // 输出测试结果到测试输出窗口
            _testOutputHelper.WriteLine(outputBuilder.ToString());
        }

        [Fact]
        public void DeviceChannel_PropertiesTest()
        {
            // Arrange
            var deviceChannel = new DeviceChannel
            {
                Manufacturer = Manufacturer.Vector,
                CommunicationType = CommunicationType.Can,
                DeviceType = "VN1630",
                DeviceFactory = "Vector",
                DeviceSerial = "123456"
            };

            // Act & Assert
            deviceChannel.Name.ShouldBe("VN1630 123456");
            deviceChannel.Manufacturer.ShouldBe(Manufacturer.Vector);
            deviceChannel.CommunicationType.ShouldBe(CommunicationType.Can);
            deviceChannel.ToString().ShouldBe("VN1630 123456");
        }

        [Fact]
        public void CreateDeviceChannel_ShouldCreateValidChannel()
        {
            // Arrange & Act
            var channel = new DeviceChannel
            {
                Manufacturer = Manufacturer.Tosun,
                CommunicationType = CommunicationType.Can,
                DeviceType = "TC1001",
                DeviceFactory = "Tosun",
                DeviceSerial = "ABC123"
            };

            // Assert
            channel.ShouldNotBeNull();
            channel.Name.ShouldBe("TC1001 ABC123");
            channel.Manufacturer.ShouldBe(Manufacturer.Tosun);
        }

        [Fact]
        public void VectorDeviceChannel_ShouldContainVectorInfo()
        {
            // Arrange
            var channel = new DeviceChannel
            {
                Manufacturer = Manufacturer.Vector,
                CommunicationType = CommunicationType.Can,
                DeviceType = "VN1630",
                DeviceFactory = "Vector",
                DeviceSerial = "123456",
                VectorInfo = new VectorInfo()
            };

            // Act & Assert
            channel.VectorInfo.ShouldNotBeNull();
            channel.TsLibCanChannelInfo.ShouldBeNull();
        }

        [Fact]
        public void TosunDeviceChannel_ShouldContainTosunInfo()
        {
            // Arrange
            var channel = new DeviceChannel
            {
                Manufacturer = Manufacturer.Tosun,
                CommunicationType = CommunicationType.Can,
                DeviceType = "TC1001",
                DeviceFactory = "Tosun",
                DeviceSerial = "789012",
                TsLibCanChannelInfo = new TsLibCanChannelInfo()
            };

            // Act & Assert
            channel.TsLibCanChannelInfo.ShouldNotBeNull();
            channel.VectorInfo.ShouldBeNull();
        }
    }
}